import{_ as F,H as v,c as W}from"./app.js";import{c as M,b as s,o as a,j as e,l as u,F as j,p as E,n as z,t as i,h as D,k as C,e as b,s as p,E as X,K as I,r as y,x as Y}from"./vendor.js";import{_ as Z}from"./BaseModal.js";import{S as V}from"./StandardButton.js";import{_ as ee}from"./PageHeader.js";import{_ as te}from"./FilterBar.js";import{_ as re}from"./StatsGrid.js";import{_ as ae}from"./AlertsSection.js";const se={class:"flex justify-center mb-1"},le={class:"flex space-x-0.5"},ie={class:"text-center"},ne={key:1,class:"text-xs text-gray-400 dark:text-gray-600"},oe={key:0,class:"mt-1 flex justify-center space-x-1"},de={key:0,class:"text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-1 rounded"},ce=["title"],ue={key:2,class:"text-xs text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900 px-1 rounded",title:"Valutato dal manager"},ge={key:3,class:"text-xs text-purple-700 dark:text-purple-300 bg-purple-100 dark:bg-purple-900 px-1 rounded",title:"Auto-valutato"},me={__name:"SkillCell",props:{userSkill:{type:Object,required:!0},skill:{type:Object,required:!0}},emits:["click"],setup(t,{emit:x}){const g=t,l=M(()=>{const c=g.userSkill.proficiency_level;if(c===0)return"bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700";const m="hover:shadow-md transform hover:scale-105";switch(c){case 1:return`${m} bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/30`;case 2:return`${m} bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 hover:bg-orange-100 dark:hover:bg-orange-900/30`;case 3:return`${m} bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 hover:bg-yellow-100 dark:hover:bg-yellow-900/30`;case 4:return`${m} bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900/30`;case 5:return`${m} bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/30`;default:return`${m} bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700`}}),k=M(()=>{switch(g.userSkill.proficiency_level){case 1:return"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200";case 2:return"bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200";case 3:return"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200";case 4:return"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200";case 5:return"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200";default:return"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"}});return(c,m)=>(a(),s("div",{onClick:m[0]||(m[0]=f=>c.$emit("click")),class:z(["skill-cell cursor-pointer rounded-lg p-2 transition-all duration-200",l.value])},[e("div",se,[e("div",le,[(a(),s(j,null,E(5,f=>e("svg",{key:f,class:z(["w-3 h-3",f<=t.userSkill.proficiency_level?"text-yellow-400":"text-gray-300 dark:text-gray-600"]),fill:"currentColor",viewBox:"0 0 20 20"},m[1]||(m[1]=[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]),2)),64))])]),e("div",ie,[t.userSkill.proficiency_level>0?(a(),s("span",{key:0,class:z(["text-xs font-medium px-1.5 py-0.5 rounded",k.value])},i(t.userSkill.proficiency_level),3)):(a(),s("span",ne,"-"))]),t.userSkill.proficiency_level>0?(a(),s("div",oe,[t.userSkill.years_experience>0?(a(),s("span",de,i(t.userSkill.years_experience)+"y ",1)):u("",!0),t.userSkill.is_certified?(a(),s("span",{key:1,class:"text-xs text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900 px-1 rounded",title:t.userSkill.certification_name||"Certificato"}," ✓ ",8,ce)):u("",!0),t.userSkill.manager_assessed?(a(),s("span",ue," M ")):t.userSkill.self_assessed?(a(),s("span",ge," S ")):u("",!0)])):u("",!0)],2))}},be=F(me,[["__scopeId","data-v-4c10f004"]]),ke={key:0,class:"space-y-6"},xe={class:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4"},ye={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ve={class:"flex items-center text-lg font-medium text-gray-900 dark:text-white mb-2"},fe={key:0,class:"text-sm text-gray-600 dark:text-gray-400"},pe={key:1,class:"mt-2"},he={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-200"},_e={class:"flex items-center text-lg font-medium text-gray-900 dark:text-white mb-2"},we={key:0,class:"text-sm text-gray-600 dark:text-gray-400"},Se={key:1,class:"text-sm text-gray-600 dark:text-gray-400"},$e={class:"bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4"},ze={class:"flex items-center justify-between mb-4"},Ce={class:"flex items-center space-x-4"},Le={class:"flex items-center"},Be={class:"flex-1 mx-4"},De={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Me={class:"text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-3 rounded-md"},je={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ee={class:"bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4"},Ae={class:"flex items-center text-md font-medium text-gray-900 dark:text-white mb-3"},Ne={class:"space-y-2"},Oe={class:"flex justify-between"},Te={class:"text-sm font-medium text-gray-900 dark:text-white"},Ue={class:"flex justify-between"},Fe={class:"bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4"},Pe={class:"flex items-center text-md font-medium text-gray-900 dark:text-white mb-3"},qe={key:0,class:"space-y-2"},Ie={class:"flex items-center"},Ve={key:0,class:"ml-6"},Re={class:"text-sm font-medium text-gray-900 dark:text-white mt-1"},He={key:1,class:"flex items-center"},Qe={key:0,class:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"},Ke={class:"flex items-center text-md font-medium text-gray-900 dark:text-white mb-3"},Ge={class:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm"},Je={class:"text-center"},We={class:"text-lg font-bold text-blue-600 dark:text-blue-400"},Xe={class:"text-center"},Ye={class:"text-lg font-bold text-green-600 dark:text-green-400"},Ze={class:"text-center"},et={class:"text-lg font-bold text-purple-600 dark:text-purple-400"},tt={key:1,class:"bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4"},rt={class:"flex items-center text-md font-medium text-gray-900 dark:text-white mb-3"},at={class:"space-y-2"},st={key:0,class:"text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-3 rounded-md"},lt={key:1,class:"text-xs text-gray-500 dark:text-gray-400"},it={__name:"SkillDetailModal",props:{isOpen:{type:Boolean,default:!1},user:{type:Object,default:null},skill:{type:Object,default:null},userSkill:{type:Object,default:null},skillStats:{type:Object,default:null}},emits:["close","edit"],setup(t,{emit:x}){const g=t,l=d=>({0:"Nessuna competenza",1:"1 - Principiante",2:"2 - Base",3:"3 - Intermedio",4:"4 - Avanzato",5:"5 - Esperto"})[d]||"Non definito",k=d=>({0:"Nessuna esperienza o conoscenza in questa competenza.",1:"Conoscenza basilare teorica, richiede supervisione costante per applicazione pratica.",2:"Competenza di base con alcune applicazioni pratiche, occasionale supervisione necessaria.",3:"Competenza solida con capacità di lavorare autonomamente nella maggior parte delle situazioni.",4:"Competenza avanzata con capacità di mentoring e risoluzione di problemi complessi.",5:"Esperto riconosciuto con capacità di innovazione e leadership in questa competenza."})[d]||"Livello non definito.",c=d=>({0:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",1:"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-200",2:"bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-200",3:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200",4:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-200",5:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200"})[d]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",m=d=>({0:"bg-gray-300 dark:bg-gray-600",1:"bg-red-400 dark:bg-red-500",2:"bg-orange-400 dark:bg-orange-500",3:"bg-yellow-400 dark:bg-yellow-500",4:"bg-blue-400 dark:bg-blue-500",5:"bg-green-400 dark:bg-green-500"})[d]||"bg-gray-300 dark:bg-gray-600",f=()=>{var d,r;return(d=g.userSkill)!=null&&d.manager_assessed?"Manager":(r=g.userSkill)!=null&&r.self_assessed?"Auto-valutazione":"Non valutato"},L=()=>{var d,r;return(d=g.userSkill)!=null&&d.manager_assessed?"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-200":(r=g.userSkill)!=null&&r.self_assessed?"bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"},B=d=>{if(!d)return"-";try{const r=new Date(d);return isNaN(r.getTime())?"-":r.toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"})}catch{return"-"}};return(d,r)=>{var A,N;return a(),D(Z,{"is-open":t.isOpen,title:`${((A=t.skill)==null?void 0:A.name)||"Competenza"} - ${((N=t.user)==null?void 0:N.full_name)||"Dipendente"}`,size:"lg",onClose:r[2]||(r[2]=w=>d.$emit("close"))},{actions:C(()=>[b(V,{variant:"secondary",onClick:r[0]||(r[0]=w=>d.$emit("close"))},{default:C(()=>r[17]||(r[17]=[p(" Chiudi ")])),_:1,__:[17]}),b(V,{variant:"primary",onClick:r[1]||(r[1]=w=>d.$emit("edit",{user:t.user,skill:t.skill,userSkill:t.userSkill}))},{default:C(()=>r[18]||(r[18]=[p(" Modifica Competenza ")])),_:1,__:[18]})]),default:C(()=>[t.skill&&t.user&&t.userSkill?(a(),s("div",ke,[e("div",xe,[e("div",ye,[e("div",null,[e("h3",ve,[b(v,{name:"star",size:"md",class:"text-yellow-500 mr-2"}),p(" "+i(t.skill.name),1)]),t.skill.description?(a(),s("p",fe,i(t.skill.description),1)):u("",!0),t.skill.category?(a(),s("div",pe,[e("span",he,i(t.skill.category),1)])):u("",!0)]),e("div",null,[e("h3",_e,[b(v,{name:"user",size:"md",class:"text-green-500 mr-2"}),p(" "+i(t.user.full_name),1)]),t.user.position?(a(),s("p",we,i(t.user.position),1)):u("",!0),t.user.department?(a(),s("p",Se,i(t.user.department),1)):u("",!0)])])]),e("div",$e,[r[4]||(r[4]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-3"}," Livello di Competenza ",-1)),e("div",ze,[e("div",Ce,[e("div",Le,[(a(),s(j,null,E(5,w=>e("svg",{key:w,class:z(["w-6 h-6",w<=t.userSkill.proficiency_level?"text-yellow-400":"text-gray-300 dark:text-gray-600"]),fill:"currentColor",viewBox:"0 0 20 20"},r[3]||(r[3]=[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]),2)),64))]),e("span",{class:z(["px-3 py-1 rounded-full text-sm font-medium",c(t.userSkill.proficiency_level)])},i(l(t.userSkill.proficiency_level)),3)]),e("div",Be,[e("div",De,[e("div",{class:z(["h-2 rounded-full transition-all duration-300",m(t.userSkill.proficiency_level)]),style:X({width:`${t.userSkill.proficiency_level/5*100}%`})},null,6)])])]),e("p",Me,i(k(t.userSkill.proficiency_level)),1)]),e("div",je,[e("div",Ee,[e("h4",Ae,[b(v,{name:"clock",size:"sm",class:"text-orange-500 mr-2"}),r[5]||(r[5]=p(" Esperienza "))]),e("div",Ne,[e("div",Oe,[r[6]||(r[6]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Anni di esperienza:",-1)),e("span",Te,i(t.userSkill.years_experience>0?`${t.userSkill.years_experience} anni`:"Non specificato"),1)]),e("div",Ue,[r[7]||(r[7]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Tipo valutazione:",-1)),e("span",{class:z(["text-sm font-medium px-2 py-1 rounded",L()])},i(f()),3)])])]),e("div",Fe,[e("h4",Pe,[b(v,{name:"academic-cap",size:"sm",class:"text-purple-500 mr-2"}),r[8]||(r[8]=p(" Certificazione "))]),t.userSkill.is_certified?(a(),s("div",qe,[e("div",Ie,[b(v,{name:"check-circle",size:"sm",class:"text-green-500 mr-2"}),r[9]||(r[9]=e("span",{class:"text-sm text-green-700 dark:text-green-300 font-medium"},"Certificato",-1))]),t.userSkill.certification_name?(a(),s("div",Ve,[r[10]||(r[10]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Nome certificazione:",-1)),e("p",Re,i(t.userSkill.certification_name),1)])):u("",!0)])):(a(),s("div",He,[b(v,{name:"x-circle",size:"sm",class:"text-gray-400 mr-2"}),r[11]||(r[11]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Nessuna certificazione",-1))]))])]),t.skillStats?(a(),s("div",Qe,[e("h4",Ke,[b(v,{name:"chart-bar",size:"sm",class:"text-blue-500 mr-2"}),r[12]||(r[12]=p(" Statistiche Competenza nel Team "))]),e("div",Ge,[e("div",Je,[e("div",We,i(t.skillStats.total_users||0),1),r[13]||(r[13]=e("div",{class:"text-gray-600 dark:text-gray-400"},"Dipendenti con questa skill",-1))]),e("div",Xe,[e("div",Ye,i(t.skillStats.avg_level||0),1),r[14]||(r[14]=e("div",{class:"text-gray-600 dark:text-gray-400"},"Livello medio team",-1))]),e("div",Ze,[e("div",et,i(t.skillStats.certified_count||0),1),r[15]||(r[15]=e("div",{class:"text-gray-600 dark:text-gray-400"},"Dipendenti certificati",-1))])])])):u("",!0),t.userSkill.notes||t.userSkill.last_updated?(a(),s("div",tt,[e("h4",rt,[b(v,{name:"document-text",size:"sm",class:"text-gray-500 mr-2"}),r[16]||(r[16]=p(" Note Aggiuntive "))]),e("div",at,[t.userSkill.notes?(a(),s("div",st,i(t.userSkill.notes),1)):u("",!0),t.userSkill.last_updated?(a(),s("div",lt," Ultimo aggiornamento: "+i(B(t.userSkill.last_updated)),1)):u("",!0)])])):u("",!0)])):u("",!0)]),_:1},8,["is-open","title"])}}},nt={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"},ot={class:"overflow-x-auto"},dt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ct={class:"bg-gray-50 dark:bg-gray-700"},ut={scope:"col",class:"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600"},gt={class:"flex flex-col items-center"},mt={class:"mb-1"},bt={class:"text-xs text-gray-400 dark:text-gray-500 normal-case"},kt={key:0,class:"mt-1 flex items-center space-x-1"},xt={class:"text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-1 rounded"},yt={class:"text-xs text-gray-400"},vt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ft={class:"sticky left-0 z-10 bg-white dark:bg-gray-800 px-6 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-600"},pt={class:"flex items-center"},ht={class:"w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3"},_t={class:"text-sm font-medium text-gray-900 dark:text-white"},wt={class:"text-sm text-gray-500 dark:text-gray-400"},St={class:"text-xs text-gray-400 dark:text-gray-500"},$t={class:"text-xs text-gray-500"},zt={__name:"SkillsMatrixTable",props:{users:{type:Array,required:!0},skills:{type:Array,required:!0},userColumnLabel:{type:String,default:"Dipendente"},showSkillStats:{type:Boolean,default:!0},getUserSkill:{type:Function,default:(t,x)=>{var g;return((g=t.skills)==null?void 0:g.find(l=>l.skill_id===x))||{skill_id:x,proficiency_level:0,years_experience:0,is_certified:!1,certification_name:null,self_assessed:!1,manager_assessed:!1}}}},setup(t){return(x,g)=>(a(),s("div",nt,[e("div",ot,[e("table",dt,[e("thead",ct,[e("tr",null,[e("th",ut,i(t.userColumnLabel),1),(a(!0),s(j,null,E(t.skills,l=>(a(),s("th",{key:l.id,scope:"col",class:"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-24"},[e("div",gt,[e("span",mt,i(l.name),1),e("span",bt,i(l.category),1),t.showSkillStats?(a(),s("div",kt,[e("span",xt,i(l.total_users),1),e("span",yt," avg: "+i(l.avg_level),1)])):u("",!0)])]))),128))])]),e("tbody",vt,[(a(!0),s(j,null,E(t.users,l=>(a(),s("tr",{key:l.user_id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",ft,[I(x.$slots,"user-cell",{user:l},()=>[e("div",pt,[e("div",ht,[b(v,{name:"user",size:"md",class:"text-gray-500 dark:text-gray-400"})]),e("div",null,[e("div",_t,i(l.full_name),1),e("div",wt,i(l.position||"Dipendente"),1),e("div",St,i(l.department||"N/A"),1)])])],!0)]),(a(!0),s(j,null,E(t.skills,k=>(a(),s("td",{key:`${l.user_id}-${k.id}`,class:"px-3 py-4 text-center"},[I(x.$slots,"skill-cell",{user:l,skill:k,userSkill:t.getUserSkill(l,k.id)},()=>{var c;return[e("div",$t,i(((c=t.getUserSkill(l,k.id))==null?void 0:c.proficiency_level)||"-"),1)]},!0)]))),128))]))),128))])])])]))}},Ct=F(zt,[["__scopeId","data-v-831bfdfc"]]),Lt={class:"space-y-6"},Bt=["disabled"],Dt={key:1,class:"flex justify-center items-center py-12"},Mt={key:4,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12"},jt={class:"text-center"},Et={__name:"SkillsMatrix",setup(t){const x=y(!1),g=y(null),l=y([]),k=y([]),c=y(null),m=y({departments:[],categories:[]}),f=y(!1),L=y(null),B=y(null),d=y(null),r=y({department_id:"",category:"",min_level:"",max_level:""}),A=M(()=>[{id:"department_id",label:"Dipartimento",value:r.value.department_id,placeholder:"Tutti i dipartimenti",options:m.value.departments.map(n=>({value:n.id,label:n.name}))},{id:"category",label:"Categoria",value:r.value.category,placeholder:"Tutte le categorie",options:m.value.categories.map(n=>({value:n,label:n}))},{id:"min_level",label:"Livello Minimo",value:r.value.min_level,placeholder:"Qualsiasi",options:[{value:"1",label:"1 - Principiante"},{value:"2",label:"2 - Base"},{value:"3",label:"3 - Intermedio"},{value:"4",label:"4 - Avanzato"},{value:"5",label:"5 - Esperto"}]},{id:"max_level",label:"Livello Massimo",value:r.value.max_level,placeholder:"Qualsiasi",options:[{value:"1",label:"1 - Principiante"},{value:"2",label:"2 - Base"},{value:"3",label:"3 - Intermedio"},{value:"4",label:"4 - Avanzato"},{value:"5",label:"5 - Esperto"}]}]),N=M(()=>{var n;return c.value?[{id:"users",label:"Dipendenti",value:c.value.total_users,format:"number",icon:"users",iconBgColor:"bg-blue-100 dark:bg-blue-900",iconColor:"text-blue-600 dark:text-blue-400"},{id:"skills",label:"Competenze",value:c.value.total_skills,format:"number",icon:"star",iconBgColor:"bg-green-100 dark:bg-green-900",iconColor:"text-green-600 dark:text-green-400"},{id:"avg_skills",label:"Media Skills/Utente",value:c.value.avg_skills_per_user,format:"number",icon:"bars-3",iconBgColor:"bg-purple-100 dark:bg-purple-900",iconColor:"text-purple-600 dark:text-purple-400"},{id:"advanced",label:"Competenze Avanzate",value:((n=c.value.skill_coverage)==null?void 0:n.advanced)||0,format:"number",icon:"arrow-trending-up",iconBgColor:"bg-orange-100 dark:bg-orange-900",iconColor:"text-orange-600 dark:text-orange-400"}]:[]}),w=M(()=>g.value?[{id:"error",type:"error",title:"Errore nel caricamento",message:g.value}]:[]),R=(n,o)=>{r.value[n]=o,O()},H=()=>{r.value={department_id:"",category:"",min_level:"",max_level:""},O()},O=async()=>{x.value=!0,g.value=null;try{const n=new URLSearchParams;Object.entries(r.value).forEach(([S,$])=>{$&&n.append(S,$)});const o=await W.get(`/api/personnel/skills-matrix?${n}`);if(o.data.success)l.value=o.data.data.matrix||[],k.value=o.data.data.skills_summary||[],c.value=o.data.data.stats||{},m.value=o.data.data.filters||{departments:[],categories:[]};else throw new Error(o.data.message||"Errore nel caricamento della matrice competenze")}catch(n){console.error("Error loading skills matrix:",n),g.value=n.message}finally{x.value=!1}},T=(n,o)=>n.skills.find(S=>S.skill_id===o)||{skill_id:o,proficiency_level:0,years_experience:0,is_certified:!1,certification_name:null,self_assessed:!1,manager_assessed:!1},Q=(n,o)=>{L.value=n,B.value=o,d.value=T(n,o.id),f.value=!0},P=()=>{f.value=!1,L.value=null,B.value=null,d.value=null},K=n=>{console.log("Edit skill:",n),P()},G=()=>{if(!l.value.length||!k.value.length)return;const n=["Dipendente","Dipartimento","Posizione",...k.value.map(_=>_.name)],o=l.value.map(_=>{const U=k.value.map(J=>T(_,J.id).proficiency_level||0);return[_.full_name,_.department||"",_.position||"",...U]}),S=[n,...o].map(_=>_.map(U=>`"${U}"`).join(",")).join(`
`),$=new Blob([S],{type:"text/csv;charset=utf-8;"}),h=document.createElement("a"),q=URL.createObjectURL($);h.setAttribute("href",q),h.setAttribute("download",`skills-matrix-${new Date().toISOString().split("T")[0]}.csv`),h.style.visibility="hidden",document.body.appendChild(h),h.click(),document.body.removeChild(h)};return Y(()=>{O()}),(n,o)=>(a(),s("div",Lt,[b(ee,{title:"Matrice Competenze",subtitle:"Panoramica delle competenze del team con livelli di proficiency",icon:"star","icon-color":"text-green-600"},{actions:C(()=>[e("button",{onClick:G,disabled:x.value,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"},[b(v,{name:"arrow-down-tray",size:"sm",class:"mr-2"}),o[0]||(o[0]=p(" Esporta CSV "))],8,Bt)]),_:1}),b(te,{"select-filters":A.value,onFilterChange:R,onClearFilters:H},null,8,["select-filters"]),c.value?(a(),D(re,{key:0,stats:N.value},null,8,["stats"])):u("",!0),x.value?(a(),s("div",Dt,o[1]||(o[1]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):g.value?(a(),D(ae,{key:2,alerts:w.value},null,8,["alerts"])):l.value.length>0?(a(),D(Ct,{key:3,users:l.value,skills:k.value,"get-user-skill":T,"user-column-label":"Dipendente","show-skill-stats":!0},{"skill-cell":C(({user:S,skill:$,userSkill:h})=>[b(be,{"user-skill":h,skill:$,onClick:q=>Q(S,$)},null,8,["user-skill","skill","onClick"])]),_:1},8,["users","skills"])):x.value?u("",!0):(a(),s("div",Mt,[e("div",jt,[b(v,{name:"light-bulb",size:"2xl",class:"mx-auto text-gray-400"}),o[2]||(o[2]=e("h3",{class:"mt-4 text-lg font-medium text-gray-900 dark:text-white"},"Nessuna competenza trovata",-1)),o[3]||(o[3]=e("p",{class:"mt-2 text-sm text-gray-500 dark:text-gray-400"}," Prova a modificare i filtri o aggiungi competenze ai dipendenti ",-1))])])),f.value?(a(),D(it,{key:5,"is-open":f.value,user:L.value,skill:B.value,"user-skill":d.value,onClose:P,onEdit:K},null,8,["is-open","user","skill","user-skill"])):u("",!0)]))}},Vt=F(Et,[["__scopeId","data-v-29b662a2"]]);export{Vt as default};
