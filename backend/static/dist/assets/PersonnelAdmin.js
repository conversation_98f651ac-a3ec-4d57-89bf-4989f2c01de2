import{_ as Z,H as p,c as ne}from"./app.js";import{S as O}from"./StandardButton.js";import{_ as ue}from"./PageHeader.js";import{_ as me}from"./AlertsSection.js";import{T as ce}from"./TabNavigation.js";import{r as k,c as q,w as ge,x as W,b as i,o as s,j as e,l as C,e as m,B as P,C as A,H as G,F as B,p as H,t as u,Q as le,s as T,h as N,n as Q,A as se,D as pe,f as xe,k as L,E as re,q as ye}from"./vendor.js";import{P as ie}from"./Pagination.js";import{A as de}from"./ActionButtonGroup.js";/* empty css                                                             */const ve={class:"space-y-6"},be={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},fe={class:"flex-1 max-w-lg"},ke={class:"relative"},we={class:"flex items-center space-x-3"},he=["value"],_e={class:"flex items-center space-x-2 text-sm"},$e={key:0,class:"flex justify-center items-center h-64"},ze={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},Ce={class:"flex"},Ee={class:"text-sm text-red-700 dark:text-red-300 mt-1"},Te={key:2,class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6"},De={class:"flex items-center justify-between"},Ue={class:"flex items-center"},Pe={class:"text-sm font-medium text-blue-800 dark:text-blue-200"},Se={key:0,class:"font-normal"},je={key:0,class:"text-xs text-blue-600 dark:text-blue-400 mt-1"},Ve={key:0,class:"flex items-center space-x-2"},Me={key:3,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Ne={class:"overflow-x-auto"},Ae={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Ie={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Re={class:"px-6 py-4 whitespace-nowrap"},Le={class:"flex items-center"},Oe={class:"flex-shrink-0 w-10 h-10"},Be={class:"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},He=["src","alt"],Fe={class:"ml-4"},Ge={class:"text-sm font-medium text-gray-900 dark:text-white"},qe={class:"text-sm text-gray-500 dark:text-gray-400"},Je={key:0,class:"text-xs text-gray-400 dark:text-gray-500"},Ye={class:"px-6 py-4 whitespace-nowrap"},Qe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Ke={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},We={class:"space-y-1"},Xe={key:0},Ze={key:1},et={class:"px-6 py-4 whitespace-nowrap"},tt={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},rt={key:4,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},st={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},at={__name:"UsersManagement",emits:["user-created","user-updated","user-deleted"],setup(S,{emit:I}){const $=I,w=k([]),z=k([]),v=k(!1),c=k(null),o=k(""),D=k(""),E=k(""),d=k(!1),t=k(1),r=k(10),_=q(()=>{let f=w.value;if(o.value.trim()){const h=o.value.toLowerCase();f=f.filter(a=>{var g,U,K;return((g=a.full_name)==null?void 0:g.toLowerCase().includes(h))||((U=a.email)==null?void 0:U.toLowerCase().includes(h))||a.position&&a.position.toLowerCase().includes(h)||a.department&&((K=a.department.name)==null?void 0:K.toLowerCase().includes(h))})}return D.value&&(f=f.filter(h=>h.department_id==D.value)),E.value&&(f=f.filter(h=>h.role===E.value)),f}),j=q(()=>{const f=(t.value-1)*r.value,h=f+r.value;return _.value.slice(f,h)}),M=q(()=>Math.ceil(_.value.length/r.value)),R=q(()=>o.value.trim()!==""||D.value!==""||E.value!==""||d.value),V=async()=>{v.value=!0,c.value=null;try{const f=new URLSearchParams;f.append("per_page","all"),d.value||f.append("is_active","true");const h=`/api/personnel/users?${f.toString()}`;console.log("Loading users with URL:",h);const a=await fetch(h,{credentials:"include"});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const g=await a.json();if(g.success)w.value=g.data.users||[];else throw new Error(g.message||"Errore nel caricamento utenti")}catch(f){console.error("Error loading users:",f),c.value=f.message}finally{v.value=!1}},x=async()=>{try{const f=await fetch("/api/personnel/departments",{credentials:"include"});if(!f.ok)throw new Error(`HTTP ${f.status}: ${f.statusText}`);const h=await f.json();h.success&&(z.value=h.data.departments||[])}catch(f){console.error("Error loading departments:",f)}},b=f=>{console.log("Edit user:",f)},l=async f=>{if(confirm(`Sei sicuro di voler resettare la password di ${f.full_name}?`))try{const h=await fetch(`/api/personnel/admin/users/${f.id}/reset-password`,{method:"POST",credentials:"include"});if(!h.ok)throw new Error(`HTTP ${h.status}: ${h.statusText}`);const a=await h.json();if(a.success)alert(`Password resettata per ${f.full_name}. Nuova password temporanea: ${a.data.temporary_password}`);else throw new Error(a.message||"Errore nel reset password")}catch(h){console.error("Error resetting password:",h),alert("Errore nel reset della password: "+h.message)}},n=f=>[{key:"reset-password",label:"Reset Password",icon:"key",danger:!1},{key:"toggle-status",label:f.is_active?"Disattiva":"Riattiva",icon:f.is_active?"no-symbol":"check-circle",danger:f.is_active}],y=(f,h)=>{f==="reset-password"?l(h):f==="toggle-status"&&J(h)},J=async f=>{const h=f.is_active?"disattivare":"riattivare";if(confirm(`Sei sicuro di voler ${h} ${f.full_name}?`))try{if(f.is_active){const a=await fetch(`/api/personnel/admin/users/${f.id}`,{method:"DELETE",credentials:"include"});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);(await a.json()).success&&(f.is_active=!1,$("user-deleted",f))}else{const a=await fetch(`/api/personnel/users/${f.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({is_active:!0})});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);(await a.json()).success&&(f.is_active=!0,$("user-updated",f))}}catch(a){console.error("Error toggling user status:",a),alert("Errore nell'operazione: "+a.message)}},F=f=>{t.value=f},Y=()=>{o.value="",D.value="",E.value="",d.value=!1,V()},ee=f=>({admin:"Admin",manager:"Manager",employee:"Dipendente"})[f]||f,te=f=>({full_time:"Tempo pieno",part_time:"Part-time",contractor:"Consulente",intern:"Stagista"})[f]||f,X=f=>f?new Date(f).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"",ae=f=>{if(!f)return!1;const h=new Date,g=new Date(f)-h,U=Math.ceil(g/(1e3*60*60*24));return U<=90&&U>=0};return ge([o,D,E,r],()=>{t.value=1}),W(async()=>{await Promise.all([V(),x()])}),(f,h)=>(s(),i("div",ve,[e("div",be,[e("div",fe,[e("div",ke,[m(p,{name:"search",size:"xs",class:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),P(e("input",{"onUpdate:modelValue":h[0]||(h[0]=a=>o.value=a),type:"text",placeholder:"Cerca dipendenti...",class:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,o.value]])])]),e("div",we,[P(e("select",{"onUpdate:modelValue":h[1]||(h[1]=a=>D.value=a),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[h[5]||(h[5]=e("option",{value:""},"Tutti i dipartimenti",-1)),(s(!0),i(B,null,H(z.value,a=>(s(),i("option",{key:a.id,value:a.id},u(a.name),9,he))),128))],512),[[G,D.value]]),P(e("select",{"onUpdate:modelValue":h[2]||(h[2]=a=>E.value=a),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},h[6]||(h[6]=[e("option",{value:""},"Tutti i ruoli",-1),e("option",{value:"admin"},"Admin",-1),e("option",{value:"manager"},"Manager",-1),e("option",{value:"employee"},"Dipendente",-1)]),512),[[G,E.value]]),e("label",_e,[P(e("input",{type:"checkbox","onUpdate:modelValue":h[3]||(h[3]=a=>d.value=a),onChange:V,class:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"},null,544),[[le,d.value]]),h[7]||(h[7]=e("span",{class:"text-gray-700 dark:text-gray-300"},"Mostra disattivati",-1))]),P(e("select",{"onUpdate:modelValue":h[4]||(h[4]=a=>r.value=a),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},h[8]||(h[8]=[e("option",{value:10},"10 per pagina",-1),e("option",{value:20},"20 per pagina",-1),e("option",{value:50},"50 per pagina",-1),e("option",{value:100},"100 per pagina",-1)]),512),[[G,r.value]]),m(O,{onClick:V,disabled:v.value,loading:v.value,variant:"primary",size:"sm",icon:"arrow-path"},null,8,["disabled","loading"])])]),v.value&&!w.value.length?(s(),i("div",$e,h[9]||(h[9]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):c.value?(s(),i("div",ze,[e("div",Ce,[m(p,{name:"error",size:"sm",color:"text-red-400",class:"mr-2 mt-0.5"}),e("div",null,[h[10]||(h[10]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento",-1)),e("p",Ee,u(c.value),1)])])])):C("",!0),_.value.length>0?(s(),i("div",Te,[e("div",De,[e("div",Ue,[m(p,{name:"info",size:"sm",color:"text-blue-600 dark:text-blue-400",class:"mr-2"}),e("div",null,[e("p",Pe,[T(u(_.value.length)+" dipendenti trovati ",1),R.value?(s(),i("span",Se," (filtrati da "+u(w.value.length)+" totali) ",1)):C("",!0)]),M.value>1?(s(),i("p",je," Visualizzazione pagina "+u(t.value)+" di "+u(M.value)+" • "+u(r.value)+" per pagina ",1)):C("",!0)])]),R.value?(s(),i("div",Ve,[m(O,{onClick:Y,variant:"outline-primary",size:"xs",icon:"x-mark",text:"Rimuovi Filtri"})])):C("",!0)])])):C("",!0),j.value.length>0?(s(),i("div",Me,[e("div",Ne,[e("table",Ae,[h[11]||(h[11]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendente "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ruolo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipartimento "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Contratto "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",Ie,[(s(!0),i(B,null,H(j.value,a=>{var g,U,K;return s(),i("tr",{key:a.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",Re,[e("div",Le,[e("div",Oe,[e("div",Be,[a.profile_image?(s(),i("img",{key:0,src:a.profile_image,alt:a.full_name,class:"w-10 h-10 rounded-full object-cover"},null,8,He)):(s(),N(p,{key:1,name:"user",size:"sm",color:"text-gray-600 dark:text-gray-300"}))])]),e("div",Fe,[e("div",Ge,u(a.full_name),1),e("div",qe,u(a.email),1),a.position?(s(),i("div",Je,u(a.position),1)):C("",!0)])])]),e("td",Ye,[e("span",{class:Q(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",a.role==="admin"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":a.role==="manager"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},u(ee(a.role)),3)]),e("td",Qe,u(((g=a.department)==null?void 0:g.name)||"Nessun dipartimento"),1),e("td",Ke,[e("div",We,[a.hire_date?(s(),i("div",Xe," Assunto: "+u(X(a.hire_date)),1)):C("",!0),(U=a.profile)!=null&&U.employment_type?(s(),i("div",Ze,u(te(a.profile.employment_type)),1)):C("",!0),(K=a.profile)!=null&&K.contract_end_date?(s(),i("div",{key:2,class:Q(ae(a.profile.contract_end_date)?"text-red-600 dark:text-red-400 font-medium":"")}," Scade: "+u(X(a.profile.contract_end_date)),3)):C("",!0)])]),e("td",et,[e("span",{class:Q(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",a.is_active?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"])},u(a.is_active?"Attivo":"Disattivato"),3)]),e("td",tt,[m(de,{onView:()=>f.$router.push(`/app/personnel/${a.id}`),onEdit:oe=>b(a),onAction:oe=>y(oe,a),"more-actions":n(a),"show-delete":!1},null,8,["onView","onEdit","onAction","more-actions"])])])}),128))])])]),M.value>1?(s(),N(ie,{key:0,"current-page":t.value,"total-pages":M.value,total:_.value.length,"per-page":r.value,"results-label":"dipendenti",onPageChange:F},null,8,["current-page","total-pages","total","per-page"])):C("",!0)])):(s(),i("div",rt,[m(p,{name:"users",size:"2xl",color:"text-gray-400",class:"mx-auto"}),h[12]||(h[12]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dipendente trovato",-1)),e("p",st,u(R.value?"Prova a modificare i filtri di ricerca.":"Inizia creando il primo dipendente."),1)]))]))}},ot=Z(at,[["__scopeId","data-v-9fbbc913"]]),nt={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},lt={class:"text-lg font-medium text-gray-900 dark:text-white"},it={class:"mt-6"},dt={class:"grid grid-cols-1 gap-4"},ut={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},mt=["value"],ct=["value"],gt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},pt={key:0,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},xt={class:"flex"},yt={class:"text-sm text-red-700 dark:text-red-300 mt-1"},vt={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},bt=["disabled"],ft={__name:"DepartmentModal",props:{department:{type:Object,default:null},managers:{type:Array,default:()=>[]},departments:{type:Array,default:()=>[]}},emits:["close","saved"],setup(S,{emit:I}){const $=S,w=I,z=k(!1),v=k(null),c=k({name:"",description:"",manager_id:"",parent_id:"",budget:null,code:""}),o=q(()=>$.department?$.departments.filter(E=>E.id!==$.department.id&&E.parent_id!==$.department.id):$.departments),D=async()=>{z.value=!0,v.value=null;try{const E={...c.value};Object.keys(E).forEach(j=>{E[j]===""&&(E[j]=null)});const d=$.department?`/api/personnel/departments/${$.department.id}`:"/api/personnel/departments",t=$.department?"PUT":"POST",r=await fetch(d,{method:t,headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(E)});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const _=await r.json();if(_.success)w("saved",_.data.department);else throw new Error(_.message||"Errore nel salvataggio dipartimento")}catch(E){console.error("Error saving department:",E),v.value=E.message}finally{z.value=!1}};return W(()=>{$.department&&(c.value={name:$.department.name||"",description:$.department.description||"",manager_id:$.department.manager_id||"",parent_id:$.department.parent_id||"",budget:$.department.budget||null,code:$.department.code||""})}),(E,d)=>(s(),i("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:d[9]||(d[9]=t=>E.$emit("close"))},[e("div",{class:"relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:d[8]||(d[8]=se(()=>{},["stop"]))},[e("div",nt,[e("h3",lt,u(S.department?"Modifica Dipartimento":"Nuovo Dipartimento"),1),e("button",{onClick:d[0]||(d[0]=t=>E.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[m(p,{name:"close",size:"lg"})])]),e("div",it,[e("form",{onSubmit:se(D,["prevent"]),class:"space-y-6"},[e("div",null,[d[12]||(d[12]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Base",-1)),e("div",dt,[e("div",null,[d[10]||(d[10]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome Dipartimento * ",-1)),P(e("input",{"onUpdate:modelValue":d[1]||(d[1]=t=>c.value.name=t),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,c.value.name]])]),e("div",null,[d[11]||(d[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),P(e("textarea",{"onUpdate:modelValue":d[2]||(d[2]=t=>c.value.description=t),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,c.value.description]])])])]),e("div",null,[d[17]||(d[17]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Gestione",-1)),e("div",ut,[e("div",null,[d[14]||(d[14]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Manager ",-1)),P(e("select",{"onUpdate:modelValue":d[3]||(d[3]=t=>c.value.manager_id=t),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[d[13]||(d[13]=e("option",{value:""},"Seleziona manager",-1)),(s(!0),i(B,null,H(S.managers,t=>(s(),i("option",{key:t.id,value:t.id},u(t.full_name),9,mt))),128))],512),[[G,c.value.manager_id]])]),e("div",null,[d[16]||(d[16]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Dipartimento Padre ",-1)),P(e("select",{"onUpdate:modelValue":d[4]||(d[4]=t=>c.value.parent_id=t),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[d[15]||(d[15]=e("option",{value:""},"Nessun padre (dipartimento principale)",-1)),(s(!0),i(B,null,H(o.value,t=>(s(),i("option",{key:t.id,value:t.id},u(t.name),9,ct))),128))],512),[[G,c.value.parent_id]])])])]),e("div",null,[d[20]||(d[20]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Budget",-1)),e("div",gt,[e("div",null,[d[18]||(d[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Budget Annuale (€) ",-1)),P(e("input",{"onUpdate:modelValue":d[5]||(d[5]=t=>c.value.budget=t),type:"number",step:"100",min:"0",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,c.value.budget,void 0,{number:!0}]])]),e("div",null,[d[19]||(d[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Codice Dipartimento ",-1)),P(e("input",{"onUpdate:modelValue":d[6]||(d[6]=t=>c.value.code=t),type:"text",placeholder:"es. IT, HR, SALES",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,c.value.code]])])])]),v.value?(s(),i("div",pt,[e("div",xt,[m(p,{name:"error",size:"sm",color:"text-red-400",class:"mr-2 mt-0.5"}),e("div",null,[d[21]||(d[21]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel salvataggio",-1)),e("p",yt,u(v.value),1)])])])):C("",!0),e("div",vt,[e("button",{type:"button",onClick:d[7]||(d[7]=t=>E.$emit("close")),class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Annulla "),e("button",{type:"submit",disabled:z.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[z.value?(s(),N(p,{key:0,name:"loading",size:"sm",color:"text-white",class:"-ml-1 mr-3 inline"})):C("",!0),T(" "+u(z.value?"Salvataggio...":S.department?"Aggiorna":"Crea"),1)],8,bt)])],32)])])]))}},kt=Z(ft,[["__scopeId","data-v-3eb204ea"]]),wt={class:"space-y-6"},ht={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},_t={class:"text-lg font-medium text-gray-900 dark:text-white flex items-center"},$t={class:"mt-4 sm:mt-0 flex items-center space-x-3"},zt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Ct={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Et={class:"p-5"},Tt={class:"flex items-center"},Dt={class:"flex-shrink-0"},Ut={class:"ml-5 w-0 flex-1"},Pt={class:"text-lg font-medium text-gray-900 dark:text-white"},St={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},jt={class:"p-5"},Vt={class:"flex items-center"},Mt={class:"flex-shrink-0"},Nt={class:"ml-5 w-0 flex-1"},At={class:"text-lg font-medium text-gray-900 dark:text-white"},It={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Rt={class:"p-5"},Lt={class:"flex items-center"},Ot={class:"flex-shrink-0"},Bt={class:"ml-5 w-0 flex-1"},Ht={class:"text-lg font-medium text-gray-900 dark:text-white"},Ft={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Gt={class:"p-5"},qt={class:"flex items-center"},Jt={class:"flex-shrink-0"},Yt={class:"ml-5 w-0 flex-1"},Qt={class:"text-lg font-medium text-gray-900 dark:text-white"},Kt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},Wt={class:"flex flex-col lg:flex-row gap-4"},Xt={class:"flex-1"},Zt={class:"relative"},er={class:"lg:w-48"},tr=["disabled"],rr={key:0,class:"flex items-center gap-2 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"},sr={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},ar={key:1,class:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"},or={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},nr={class:"overflow-x-auto"},lr={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ir={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},dr={class:"px-6 py-4 whitespace-nowrap"},ur={class:"flex items-center"},mr={class:"flex-shrink-0 w-6 h-6"},cr={class:"w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded flex items-center justify-center"},gr={class:"ml-3"},pr={class:"text-sm font-medium text-gray-900 dark:text-white"},xr={key:0,class:"text-xs text-gray-500 dark:text-gray-400 truncate max-w-xs"},yr={key:1,class:"text-xs text-gray-400 dark:text-gray-500"},vr={class:"px-6 py-4 whitespace-nowrap"},br={key:0,class:"flex items-center"},fr={class:"flex-shrink-0 w-6 h-6"},kr={class:"w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},wr={class:"ml-2"},hr={class:"text-sm font-medium text-gray-900 dark:text-white"},_r={class:"text-xs text-gray-500 dark:text-gray-400 truncate max-w-xs"},$r={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},zr={class:"px-6 py-4 whitespace-nowrap text-center"},Cr={class:"flex items-center justify-center"},Er={class:"text-sm font-medium text-gray-900 dark:text-white"},Tr={class:"px-6 py-4 whitespace-nowrap"},Dr={class:"text-sm text-gray-900 dark:text-white"},Ur={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Pr={key:1,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},Sr={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},jr={key:0,class:"mt-6"},Vr={key:2,class:"flex justify-center items-center h-64"},Mr={__name:"DepartmentsManagement",emits:["department-created","department-updated","department-deleted"],setup(S,{emit:I}){const $=I,w=k([]),z=k([]),v=k({}),c=k(!1),o=k(""),D=k(""),E=k(!1),d=k(!1),t=k(null),r=k(1),_=k(10),j=q(()=>{let a=w.value;if(o.value.trim()){const g=o.value.toLowerCase();a=a.filter(U=>U.name.toLowerCase().includes(g)||U.description&&U.description.toLowerCase().includes(g)||U.manager&&U.manager.full_name.toLowerCase().includes(g))}return D.value&&(D.value==="root"?a=a.filter(g=>!g.parent_id):D.value==="sub"&&(a=a.filter(g=>g.parent_id))),a}),M=q(()=>o.value.trim()!==""||D.value!==""),R=q(()=>Math.ceil(j.value.length/_.value)),V=q(()=>{const a=(r.value-1)*_.value,g=a+_.value;return j.value.slice(a,g)}),x=a=>{switch(a){case"root":return"Dipartimenti principali";case"sub":return"Sotto-dipartimenti";default:return a}};let b=null;const l=()=>{clearTimeout(b),b=setTimeout(()=>{},300)},n=()=>{r.value=1},y=()=>{o.value="",D.value="",r.value=1},J=a=>{r.value=a},F=async()=>{c.value=!0;try{const a=await fetch("/api/personnel/departments",{credentials:"include"});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const g=await a.json();if(g.success)w.value=g.data.departments||[];else throw new Error(g.message||"Errore nel caricamento dipartimenti")}catch(a){console.error("Error loading departments:",a)}finally{c.value=!1}},Y=async()=>{try{const a=await fetch("/api/personnel/orgchart",{credentials:"include"});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const g=await a.json();if(g.success){v.value=g.data.stats||{};const U=w.value.reduce((K,oe)=>K+(oe.budget||0),0);v.value.total_budget=U}}catch(a){console.error("Error loading stats:",a)}},ee=async()=>{try{const a=await fetch("/api/personnel/users?role=manager,admin",{credentials:"include"});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const g=await a.json();g.success&&(z.value=g.data.users||[])}catch(a){console.error("Error loading managers:",a)}},te=a=>{t.value={...a},d.value=!0},X=async a=>{if(a.user_count>0){alert("Impossibile eliminare un dipartimento con dipendenti assegnati");return}if(confirm(`Sei sicuro di voler eliminare il dipartimento "${a.name}"?`))try{const g=await fetch(`/api/personnel/departments/${a.id}`,{method:"DELETE",credentials:"include"});if(!g.ok)throw new Error(`HTTP ${g.status}: ${g.statusText}`);const U=await g.json();if(U.success)await F(),await Y(),$("department-deleted",a);else throw new Error(U.message||"Errore nell'eliminazione")}catch(g){console.error("Error deleting department:",g),alert("Errore nell'eliminazione: "+g.message)}},ae=()=>{E.value=!1,d.value=!1,t.value=null},f=async a=>{ae(),await F(),await Y(),t.value?$("department-updated",a):$("department-created",a)},h=a=>a?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(a):"€0";return W(async()=>{await Promise.all([F(),Y(),ee()])}),(a,g)=>(s(),i("div",wt,[e("div",ht,[e("div",null,[e("h3",_t,[m(p,{name:"building-office-2",size:"md",class:"mr-2 text-blue-600"}),g[4]||(g[4]=T(" Gestione Dipartimenti "))]),g[5]||(g[5]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci la struttura organizzativa aziendale ",-1))]),e("div",$t,[e("button",{onClick:g[0]||(g[0]=U=>E.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},[m(p,{name:"plus",size:"sm",class:"mr-2"}),g[6]||(g[6]=T(" Nuovo Dipartimento "))])])]),e("div",zt,[e("div",Ct,[e("div",Et,[e("div",Tt,[e("div",Dt,[m(p,{name:"building-office-2",size:"md",class:"text-blue-600"})]),e("div",Ut,[e("dl",null,[g[7]||(g[7]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipartimenti Totali ",-1)),e("dd",Pt,u(v.value.total_departments||0),1)])])])])]),e("div",St,[e("div",jt,[e("div",Vt,[e("div",Mt,[m(p,{name:"users",size:"md",class:"text-green-600"})]),e("div",Nt,[e("dl",null,[g[8]||(g[8]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipendenti Totali ",-1)),e("dd",At,u(v.value.total_employees||0),1)])])])])]),e("div",It,[e("div",Rt,[e("div",Lt,[e("div",Ot,[m(p,{name:"user-circle",size:"md",class:"text-purple-600"})]),e("div",Bt,[e("dl",null,[g[9]||(g[9]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Manager Assegnati ",-1)),e("dd",Ht,u(v.value.total_managers||0),1)])])])])]),e("div",Ft,[e("div",Gt,[e("div",qt,[e("div",Jt,[m(p,{name:"banknotes",size:"md",class:"text-yellow-600"})]),e("div",Yt,[e("dl",null,[g[10]||(g[10]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Budget Totale ",-1)),e("dd",Qt,u(h(v.value.total_budget||0)),1)])])])])])]),e("div",Kt,[e("div",Wt,[e("div",Xt,[e("div",Zt,[m(p,{name:"magnifying-glass",size:"md",class:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),P(e("input",{"onUpdate:modelValue":g[1]||(g[1]=U=>o.value=U),onInput:l,type:"text",placeholder:"Cerca dipartimenti...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,544),[[A,o.value]])])]),e("div",er,[P(e("select",{"onUpdate:modelValue":g[2]||(g[2]=U=>D.value=U),onChange:n,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},g[11]||(g[11]=[e("option",{value:""},"Tutti i livelli",-1),e("option",{value:"root"},"Solo dipartimenti principali",-1),e("option",{value:"sub"},"Solo sotto-dipartimenti",-1)]),544),[[G,D.value]])]),e("button",{onClick:F,disabled:c.value,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center"},[c.value?(s(),N(p,{key:0,name:"arrow-path",size:"sm",class:"mr-2 animate-spin"})):(s(),N(p,{key:1,name:"arrow-path",size:"sm",class:"mr-2"})),g[12]||(g[12]=T(" Aggiorna "))],8,tr),M.value?(s(),i("button",{key:0,onClick:y,class:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"}," Pulisci Filtri ")):C("",!0)]),M.value?(s(),i("div",rr,[g[13]||(g[13]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Filtri attivi:",-1)),o.value?(s(),i("span",sr,[m(p,{name:"magnifying-glass",size:"xs",class:"mr-1"}),T(' "'+u(o.value)+'" ',1)])):C("",!0),D.value?(s(),i("span",ar,[m(p,{name:"squares-2x2",size:"xs",class:"mr-1"}),T(" "+u(x(D.value)),1)])):C("",!0)])):C("",!0)]),V.value.length>0?(s(),i("div",or,[e("div",nr,[e("table",lr,[g[15]||(g[15]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipartimento "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Manager "),e("th",{class:"px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendenti "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Budget "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",ir,[(s(!0),i(B,null,H(V.value,U=>(s(),i("tr",{key:U.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"},[e("td",dr,[e("div",ur,[e("div",mr,[e("div",cr,[m(p,{name:"building-office-2",size:"xs",class:"text-blue-600 dark:text-blue-400"})])]),e("div",gr,[e("div",pr,u(U.name),1),U.description?(s(),i("div",xr,u(U.description),1)):C("",!0),U.parent?(s(),i("div",yr," Sotto: "+u(U.parent.name),1)):C("",!0)])])]),e("td",vr,[U.manager?(s(),i("div",br,[e("div",fr,[e("div",kr,[m(p,{name:"user",size:"xs",class:"text-gray-600 dark:text-gray-300"})])]),e("div",wr,[e("div",hr,u(U.manager.full_name),1),e("div",_r,u(U.manager.email),1)])])):(s(),i("span",$r,"Nessun manager"))]),e("td",zr,[e("div",Cr,[e("span",Er,u(U.user_count||0),1),g[14]||(g[14]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400 ml-1"},"dip.",-1))])]),e("td",Tr,[e("span",Dr,u(h(U.budget||0)),1)]),e("td",Ur,[m(de,{onView:K=>a.$router.push(`/app/personnel/departments/${U.id}`),onEdit:K=>te(U),onDelete:K=>X(U),"show-delete":U.user_count===0,"delete-message":`Sei sicuro di voler eliminare il dipartimento '${U.name}'?`},null,8,["onView","onEdit","onDelete","show-delete","delete-message"])])]))),128))])])]),R.value>1?(s(),N(ie,{key:0,"current-page":r.value,"total-pages":R.value,total:j.value.length,"per-page":_.value,"results-label":"dipartimenti",onPageChange:J},null,8,["current-page","total-pages","total","per-page"])):C("",!0)])):c.value?C("",!0):(s(),i("div",Pr,[m(p,{name:"building-office-2",size:"2xl",class:"mx-auto text-gray-400"}),g[17]||(g[17]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dipartimento trovato",-1)),e("p",Sr,u(M.value?"Prova a modificare i filtri di ricerca.":"Inizia creando il primo dipartimento."),1),M.value?C("",!0):(s(),i("div",jr,[e("button",{onClick:g[3]||(g[3]=U=>E.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},[m(p,{name:"plus",size:"sm",class:"mr-2"}),g[16]||(g[16]=T(" Crea Primo Dipartimento "))])]))])),c.value?(s(),i("div",Vr,g[18]||(g[18]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):C("",!0),E.value||d.value?(s(),N(kt,{key:3,department:t.value,managers:z.value,departments:w.value,onClose:ae,onSaved:f},null,8,["department","managers","departments"])):C("",!0)]))}},Nr={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},Ar={class:"text-lg font-medium text-gray-900 dark:text-white"},Ir={class:"mt-6"},Rr={class:"flex space-x-2"},Lr=["value"],Or={key:0,class:"mt-2"},Br={key:0,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},Hr={class:"flex"},Fr={class:"text-sm text-red-700 dark:text-red-300 mt-1"},Gr={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},qr=["disabled"],Jr={__name:"SkillModal",props:{skill:{type:Object,default:null},categories:{type:Array,default:()=>[]}},emits:["close","saved"],setup(S,{emit:I}){const $=S,w=I,z=k(!1),v=k(null),c=k(!1),o=k(""),D=k({name:"",category:"",description:""}),E=()=>{o.value.trim()&&(D.value.category=o.value.trim(),o.value="",c.value=!1)},d=async()=>{z.value=!0,v.value=null;try{const t={...D.value};Object.keys(t).forEach(R=>{t[R]===""&&(t[R]=null)});const r=$.skill?`/api/personnel/skills/${$.skill.id}`:"/api/personnel/skills",_=$.skill?"PUT":"POST",j=await fetch(r,{method:_,headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(t)});if(!j.ok)throw new Error(`HTTP ${j.status}: ${j.statusText}`);const M=await j.json();if(M.success)w("saved",M.data.skill);else throw new Error(M.message||"Errore nel salvataggio competenza")}catch(t){console.error("Error saving skill:",t),v.value=t.message}finally{z.value=!1}};return W(()=>{$.skill&&(D.value={name:$.skill.name||"",category:$.skill.category||"",description:$.skill.description||""})}),(t,r)=>(s(),i("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:r[8]||(r[8]=_=>t.$emit("close"))},[e("div",{class:"relative top-20 mx-auto p-5 border w-11/12 max-w-lg shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:r[7]||(r[7]=se(()=>{},["stop"]))},[e("div",Nr,[e("h3",Ar,u(S.skill?"Modifica Competenza":"Nuova Competenza"),1),e("button",{onClick:r[0]||(r[0]=_=>t.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[m(p,{name:"close",size:"lg"})])]),e("div",Ir,[e("form",{onSubmit:se(d,["prevent"]),class:"space-y-4"},[e("div",null,[r[9]||(r[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome Competenza * ",-1)),P(e("input",{"onUpdate:modelValue":r[1]||(r[1]=_=>D.value.name=_),type:"text",required:"",placeholder:"es. JavaScript, Project Management, Design Thinking",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,D.value.name]])]),e("div",null,[r[11]||(r[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Categoria ",-1)),e("div",Rr,[P(e("select",{"onUpdate:modelValue":r[2]||(r[2]=_=>D.value.category=_),class:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[r[10]||(r[10]=e("option",{value:""},"Seleziona categoria esistente",-1)),(s(!0),i(B,null,H(S.categories,_=>(s(),i("option",{key:_,value:_},u(_),9,Lr))),128))],512),[[G,D.value.category]]),e("button",{type:"button",onClick:r[3]||(r[3]=_=>c.value=!c.value),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"},u(c.value?"Annulla":"Nuova"),1)]),c.value?(s(),i("div",Or,[P(e("input",{"onUpdate:modelValue":r[4]||(r[4]=_=>o.value=_),type:"text",placeholder:"Nome nuova categoria",onBlur:E,onKeyup:pe(E,["enter"]),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,544),[[A,o.value]])])):C("",!0)]),e("div",null,[r[12]||(r[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),P(e("textarea",{"onUpdate:modelValue":r[5]||(r[5]=_=>D.value.description=_),rows:"3",placeholder:"Descrizione della competenza e come viene utilizzata...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,D.value.description]])]),v.value?(s(),i("div",Br,[e("div",Hr,[m(p,{name:"error",size:"sm",color:"text-red-400",class:"mr-2 mt-0.5"}),e("div",null,[r[13]||(r[13]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel salvataggio",-1)),e("p",Fr,u(v.value),1)])])])):C("",!0),e("div",Gr,[e("button",{type:"button",onClick:r[6]||(r[6]=_=>t.$emit("close")),class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Annulla "),e("button",{type:"submit",disabled:z.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[z.value?(s(),N(p,{key:0,name:"loading",size:"sm",color:"text-white",class:"-ml-1 mr-3 inline"})):C("",!0),T(" "+u(z.value?"Salvataggio...":S.skill?"Aggiorna":"Crea"),1)],8,qr)])],32)])])]))}},Yr=Z(Jr,[["__scopeId","data-v-ef3698db"]]),Qr={class:"space-y-6"},Kr={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Wr={class:"mt-4 sm:mt-0 flex items-center space-x-3"},Xr={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Zr={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},es={class:"p-5"},ts={class:"flex items-center"},rs={class:"flex-shrink-0"},ss={class:"ml-5 w-0 flex-1"},as={class:"text-lg font-medium text-gray-900 dark:text-white"},os={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},ns={class:"p-5"},ls={class:"flex items-center"},is={class:"flex-shrink-0"},ds={class:"ml-5 w-0 flex-1"},us={class:"text-lg font-medium text-gray-900 dark:text-white"},ms={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},cs={class:"p-5"},gs={class:"flex items-center"},ps={class:"flex-shrink-0"},xs={class:"ml-5 w-0 flex-1"},ys={class:"text-lg font-medium text-gray-900 dark:text-white"},vs={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},bs={class:"p-5"},fs={class:"flex items-center"},ks={class:"flex-shrink-0"},ws={class:"ml-5 w-0 flex-1"},hs={class:"text-lg font-medium text-gray-900 dark:text-white"},_s={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},$s={class:"flex-1 max-w-lg"},zs={class:"relative"},Cs={class:"flex items-center space-x-3"},Es=["value"],Ts=["disabled"],Ds={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},Us={class:"flex items-start justify-between"},Ps={class:"flex-1"},Ss={class:"text-lg font-medium text-gray-900 dark:text-white"},js={key:0,class:"text-sm text-blue-600 dark:text-blue-400 mt-1"},Vs={key:1,class:"text-sm text-gray-500 dark:text-gray-400 mt-2"},Ms={class:"mt-4 flex items-center justify-between"},Ns={class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},As={class:"flex items-center space-x-2"},Is=["onClick"],Rs=["onClick","disabled"],Ls={key:1,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},Os={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},Bs={class:"mt-6"},Hs={key:2,class:"flex justify-center items-center h-64"},Fs={__name:"SkillsManagement",emits:["skill-created","skill-updated","skill-deleted"],setup(S,{emit:I}){const $=I,w=k([]),z=k([]),v=k(!1),c=k(""),o=k(""),D=k(!1),E=k(!1),d=k(null),t=q(()=>{let b=w.value;if(c.value.trim()){const l=c.value.toLowerCase();b=b.filter(n=>n.name.toLowerCase().includes(l)||n.description&&n.description.toLowerCase().includes(l)||n.category&&n.category.toLowerCase().includes(l))}return o.value&&(b=b.filter(l=>l.category===o.value)),b}),r=q(()=>w.value.reduce((b,l)=>b+(l.user_count||0),0)),_=q(()=>w.value.filter(l=>l.user_count>0).length===0?0:3.2),j=async()=>{v.value=!0;try{const b=await fetch("/api/personnel/skills",{credentials:"include"});if(!b.ok)throw new Error(`HTTP ${b.status}: ${b.statusText}`);const l=await b.json();if(l.success)w.value=l.data.skills||[],z.value=l.data.categories||[];else throw new Error(l.message||"Errore nel caricamento competenze")}catch(b){console.error("Error loading skills:",b)}finally{v.value=!1}},M=b=>{d.value={...b},E.value=!0},R=async b=>{if(b.user_count>0){alert("Impossibile eliminare una competenza assegnata a dipendenti");return}if(confirm(`Sei sicuro di voler eliminare la competenza "${b.name}"?`))try{const l=await fetch(`/api/personnel/skills/${b.id}`,{method:"DELETE",credentials:"include"});if(!l.ok)throw new Error(`HTTP ${l.status}: ${l.statusText}`);const n=await l.json();if(n.success)await j(),$("skill-deleted",b);else throw new Error(n.message||"Errore nell'eliminazione")}catch(l){console.error("Error deleting skill:",l),alert("Errore nell'eliminazione: "+l.message)}},V=()=>{D.value=!1,E.value=!1,d.value=null},x=async b=>{V(),await j(),d.value?$("skill-updated",b):$("skill-created",b)};return W(()=>{j()}),(b,l)=>{const n=xe("router-link");return s(),i("div",Qr,[e("div",Kr,[l[6]||(l[6]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Gestione Competenze"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci il catalogo delle competenze aziendali ")],-1)),e("div",Wr,[m(n,{to:"/app/personnel/skills",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:L(()=>[m(p,{name:"external-link",size:"xs",class:"mr-2"}),l[4]||(l[4]=T(" Matrice Completa "))]),_:1,__:[4]}),e("button",{onClick:l[0]||(l[0]=y=>D.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},[m(p,{name:"add",size:"xs",class:"mr-2"}),l[5]||(l[5]=T(" Nuova Competenza "))])])]),e("div",Xr,[e("div",Zr,[e("div",es,[e("div",ts,[e("div",rs,[m(p,{name:"star",size:"lg",color:"text-blue-600"})]),e("div",ss,[e("dl",null,[l[7]||(l[7]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Competenze Totali ",-1)),e("dd",as,u(w.value.length),1)])])])])]),e("div",os,[e("div",ns,[e("div",ls,[e("div",is,[m(p,{name:"tag",size:"lg",color:"text-green-600"})]),e("div",ds,[e("dl",null,[l[8]||(l[8]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Categorie ",-1)),e("dd",us,u(z.value.length),1)])])])])]),e("div",ms,[e("div",cs,[e("div",gs,[e("div",ps,[m(p,{name:"users",size:"lg",color:"text-purple-600"})]),e("div",xs,[e("dl",null,[l[9]||(l[9]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Competenze Assegnate ",-1)),e("dd",ys,u(r.value),1)])])])])]),e("div",vs,[e("div",bs,[e("div",fs,[e("div",ks,[m(p,{name:"bolt",size:"lg",color:"text-yellow-600"})]),e("div",ws,[e("dl",null,[l[10]||(l[10]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Livello Medio ",-1)),e("dd",hs,u(_.value.toFixed(1)),1)])])])])])]),e("div",_s,[e("div",$s,[e("div",zs,[m(p,{name:"search",size:"xs",class:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),P(e("input",{"onUpdate:modelValue":l[1]||(l[1]=y=>c.value=y),type:"text",placeholder:"Cerca competenze...",class:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,c.value]])])]),e("div",Cs,[P(e("select",{"onUpdate:modelValue":l[2]||(l[2]=y=>o.value=y),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[l[11]||(l[11]=e("option",{value:""},"Tutte le categorie",-1)),(s(!0),i(B,null,H(z.value,y=>(s(),i("option",{key:y,value:y},u(y),9,Es))),128))],512),[[G,o.value]]),e("button",{onClick:j,disabled:v.value,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"},[v.value?(s(),N(p,{key:0,name:"loading",size:"xs",class:"animate-spin"})):(s(),N(p,{key:1,name:"refresh",size:"xs"}))],8,Ts)])]),t.value.length>0?(s(),i("div",Ds,[(s(!0),i(B,null,H(t.value,y=>(s(),i("div",{key:y.id,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-lg transition-shadow"},[e("div",Us,[e("div",Ps,[e("h4",Ss,u(y.name),1),y.category?(s(),i("p",js,u(y.category),1)):C("",!0),y.description?(s(),i("p",Vs,u(y.description),1)):C("",!0),e("div",Ms,[e("div",Ns,[m(p,{name:"users",size:"xs",class:"mr-1"}),T(" "+u(y.user_count)+" dipendenti ",1)]),e("div",As,[e("button",{onClick:J=>M(y),class:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"},[m(p,{name:"edit",size:"xs"})],8,Is),e("button",{onClick:J=>R(y),disabled:y.user_count>0,class:Q(y.user_count>0?"text-gray-400 cursor-not-allowed":"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300")},[m(p,{name:"delete",size:"xs"})],10,Rs)])])])])]))),128))])):v.value?C("",!0):(s(),i("div",Ls,[m(p,{name:"star",size:"2xl",color:"text-gray-400",class:"mx-auto"}),l[13]||(l[13]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna competenza trovata",-1)),e("p",Os,u(c.value||o.value?"Prova a modificare i filtri di ricerca.":"Inizia creando la prima competenza."),1),e("div",Bs,[e("button",{onClick:l[3]||(l[3]=y=>D.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},[m(p,{name:"add",size:"xs",class:"mr-2"}),l[12]||(l[12]=T(" Crea Prima Competenza "))])])])),v.value?(s(),i("div",Hs,l[14]||(l[14]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):C("",!0),D.value||E.value?(s(),N(Yr,{key:3,skill:d.value,categories:z.value,onClose:V,onSaved:x},null,8,["skill","categories"])):C("",!0)])}}},Gs={class:"space-y-6"},qs={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},Js={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ys={class:"flex items-center"},Qs={class:"flex-shrink-0"},Ks={class:"ml-4"},Ws={class:"text-2xl font-semibold text-gray-900 dark:text-white"},Xs={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Zs={class:"flex items-center"},ea={class:"flex-shrink-0"},ta={class:"ml-4"},ra={class:"text-2xl font-semibold text-gray-900 dark:text-white"},sa={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},aa={class:"flex items-center"},oa={class:"flex-shrink-0"},na={class:"ml-4"},la={class:"text-2xl font-semibold text-gray-900 dark:text-white"},ia={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},da={class:"flex items-center"},ua={class:"flex-shrink-0"},ma={class:"ml-4"},ca={class:"text-2xl font-semibold text-gray-900 dark:text-white"},ga={class:"flex flex-wrap gap-4"},pa={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},xa={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-600"},ya={class:"flex items-center justify-between"},va={class:"flex space-x-4"},ba=["value"],fa=["value"],ka={key:0,class:"flex justify-center items-center h-32"},wa={key:1,class:"overflow-x-auto"},ha={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-600"},_a={class:"bg-gray-50 dark:bg-gray-700"},$a={class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},za={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600"},Ca={class:"px-6 py-4 whitespace-nowrap"},Ea={class:"flex items-center"},Ta={class:"h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center"},Da={class:"text-sm font-medium text-primary-700 dark:text-primary-300"},Ua={class:"ml-4"},Pa={class:"text-sm font-medium text-gray-900 dark:text-white"},Sa={class:"text-sm text-gray-500 dark:text-gray-400"},ja={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Va={class:"px-6 py-4 whitespace-nowrap"},Ma={class:"flex items-center space-x-2"},Na={class:"text-sm font-medium text-gray-900 dark:text-white"},Aa={class:"w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2"},Ia={class:"px-6 py-4 whitespace-nowrap"},Ra={class:"flex items-center space-x-2"},La={class:"text-sm font-medium text-gray-900 dark:text-white"},Oa={class:"w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2"},Ba={class:"px-6 py-4 whitespace-nowrap"},Ha={key:0,class:"flex items-center space-x-1"},Fa={class:"text-sm font-medium text-yellow-600"},Ga={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},qa={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Ja={class:"flex items-center space-x-2"},Ya={key:0,class:"text-center py-12"},Qa={key:0,class:"fixed inset-0 z-50 overflow-y-auto"},Ka={class:"flex items-center justify-center min-h-screen px-4"},Wa={class:"relative bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full"},Xa={class:"flex justify-end space-x-3"},Za={key:1,class:"fixed inset-0 z-50 overflow-y-auto"},eo={class:"flex items-center justify-center min-h-screen px-4"},to={class:"relative bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full"},ro={class:"flex justify-end space-x-3"},so={__name:"PerformanceManagement",emits:["performance-updated"],setup(S){const I=ye(),$=k(!1),w=k(!1),z=k(new Date().getFullYear()),v=k(""),c=k({}),o=k([]),D=k([]),E=k(!1),d=k(!1),t=q(()=>{const l=new Date().getFullYear();return[l-2,l-1,l,l+1]}),r=q(()=>{let l=o.value;return v.value&&(l=l.filter(n=>n.department===v.value)),l}),_=async()=>{$.value=!0;try{const l=await ne.get("/api/performance/admin/stats",{params:{year:z.value}});l.data.success&&(c.value=l.data.data);const n=await ne.get("/api/personnel/users",{params:{include_performance:!0,year:z.value}});if(n.data.success){o.value=n.data.data.users||[];const y=[...new Set(o.value.map(J=>J.department).filter(Boolean))];D.value=y.sort()}}catch(l){console.error("Error loading performance data:",l)}finally{$.value=!1}},j=l=>l.full_name?l.full_name.split(" ").map(y=>y.charAt(0)).join("").substring(0,2).toUpperCase():"U",M=l=>{const n=l.performance;return!n||!n.total_goals?0:Math.round(n.goals_completed/n.total_goals*100)},R=l=>{I.push(`/app/personnel/${l.id}/performance/${z.value}`)},V=l=>{I.push(`/app/personnel/${l.id}/performance/${z.value}?action=create-goal`)},x=l=>{I.push(`/app/personnel/${l.id}/performance/${z.value}?action=create-review`)},b=async()=>{w.value=!0;try{const l=await ne.get("/api/performance/export",{params:{year:z.value,department:v.value},responseType:"blob"}),n=window.URL.createObjectURL(new Blob([l.data])),y=document.createElement("a");y.href=n,y.download=`performance-data-${z.value}.csv`,document.body.appendChild(y),y.click(),document.body.removeChild(y),window.URL.revokeObjectURL(n)}catch(l){console.error("Error exporting performance data:",l)}finally{w.value=!1}};return W(()=>{_()}),(l,n)=>(s(),i("div",Gs,[e("div",qs,[e("div",Js,[e("div",Ys,[e("div",Qs,[m(p,{name:"target",size:"lg",class:"text-blue-600"})]),e("div",Ks,[e("div",Ws,u(c.value.total_goals||0),1),n[8]||(n[8]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Obiettivi Totali",-1))])])]),e("div",Xs,[e("div",Zs,[e("div",ea,[m(p,{name:"star",size:"lg",class:"text-yellow-600"})]),e("div",ta,[e("div",ra,u(c.value.total_reviews||0),1),n[9]||(n[9]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Valutazioni",-1))])])]),e("div",sa,[e("div",aa,[e("div",oa,[m(p,{name:"document-text",size:"lg",class:"text-green-600"})]),e("div",na,[e("div",la,u(c.value.total_templates||0),1),n[10]||(n[10]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Template",-1))])])]),e("div",ia,[e("div",da,[e("div",ua,[m(p,{name:"chart-bar",size:"lg",class:"text-purple-600"})]),e("div",ma,[e("div",ca,u(c.value.avg_completion_rate||0)+"% ",1),n[11]||(n[11]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Tasso Completamento",-1))])])])]),e("div",ga,[m(O,{variant:"primary",icon:"plus",onClick:n[0]||(n[0]=y=>E.value=!0)},{default:L(()=>n[12]||(n[12]=[T(" Nuovo Template ")])),_:1,__:[12]}),m(O,{variant:"secondary",icon:"document-plus",onClick:n[1]||(n[1]=y=>d.value=!0)},{default:L(()=>n[13]||(n[13]=[T(" Assegnazione Massiva ")])),_:1,__:[13]}),m(O,{variant:"secondary",icon:"chart-line",to:"/app/personnel/performance"},{default:L(()=>n[14]||(n[14]=[T(" Dashboard Performance ")])),_:1,__:[14]}),m(O,{variant:"secondary",icon:"arrow-down-tray",onClick:b,loading:w.value},{default:L(()=>n[15]||(n[15]=[T(" Esporta Dati ")])),_:1,__:[15]},8,["loading"])]),e("div",pa,[e("div",xa,[e("div",ya,[n[17]||(n[17]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Performance Dipendenti ",-1)),e("div",va,[P(e("select",{"onUpdate:modelValue":n[2]||(n[2]=y=>z.value=y),onChange:_,class:"rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"},[(s(!0),i(B,null,H(t.value,y=>(s(),i("option",{key:y,value:y},u(y),9,ba))),128))],544),[[G,z.value]]),P(e("select",{"onUpdate:modelValue":n[3]||(n[3]=y=>v.value=y),onChange:_,class:"rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"},[n[16]||(n[16]=e("option",{value:""},"Tutti i dipartimenti",-1)),(s(!0),i(B,null,H(D.value,y=>(s(),i("option",{key:y,value:y},u(y),9,fa))),128))],544),[[G,v.value]])])])]),$.value?(s(),i("div",ka,n[18]||(n[18]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):(s(),i("div",wa,[e("table",ha,[e("thead",_a,[e("tr",null,[n[19]||(n[19]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendente ",-1)),n[20]||(n[20]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipartimento ",-1)),e("th",$a," Obiettivi "+u(z.value),1),n[21]||(n[21]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Progresso Medio ",-1)),n[22]||(n[22]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ultima Valutazione ",-1)),n[23]||(n[23]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ",-1))])]),e("tbody",za,[(s(!0),i(B,null,H(r.value,y=>{var J,F,Y,ee,te;return s(),i("tr",{key:y.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",Ca,[e("div",Ea,[e("div",Ta,[e("span",Da,u(j(y)),1)]),e("div",Ua,[e("div",Pa,u(y.full_name),1),e("div",Sa,u(y.email),1)])])]),e("td",ja,u(y.department||"-"),1),e("td",Va,[e("div",Ma,[e("span",Na,u(((J=y.performance)==null?void 0:J.goals_completed)||0)+"/"+u(((F=y.performance)==null?void 0:F.total_goals)||0),1),e("div",Aa,[e("div",{class:"bg-green-600 h-2 rounded-full",style:re({width:`${M(y)}%`})},null,4)])])]),e("td",Ia,[e("div",Ra,[e("span",La,u(((Y=y.performance)==null?void 0:Y.avg_progress)||0)+"% ",1),e("div",Oa,[e("div",{class:"bg-blue-600 h-2 rounded-full",style:re({width:`${((ee=y.performance)==null?void 0:ee.avg_progress)||0}%`})},null,4)])])]),e("td",Ba,[(te=y.performance)!=null&&te.latest_rating?(s(),i("div",Ha,[e("span",Fa,u(y.performance.latest_rating),1),m(p,{name:"star",size:"sm",class:"text-yellow-500"})])):(s(),i("span",Ga," Nessuna valutazione "))]),e("td",qa,[e("div",Ja,[m(O,{variant:"ghost",size:"sm",icon:"eye",onClick:X=>R(y)},{default:L(()=>n[24]||(n[24]=[T(" Visualizza ")])),_:2,__:[24]},1032,["onClick"]),m(O,{variant:"ghost",size:"sm",icon:"plus",onClick:X=>V(y)},{default:L(()=>n[25]||(n[25]=[T(" Obiettivo ")])),_:2,__:[25]},1032,["onClick"]),m(O,{variant:"ghost",size:"sm",icon:"star",onClick:X=>x(y)},{default:L(()=>n[26]||(n[26]=[T(" Valutazione ")])),_:2,__:[26]},1032,["onClick"])])])])}),128))])]),r.value.length===0?(s(),i("div",Ya,[m(p,{name:"users",size:"2xl",class:"text-gray-300 dark:text-gray-600 mx-auto mb-4"}),n[27]||(n[27]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"}," Nessun dipendente trovato ",-1)),n[28]||(n[28]=e("p",{class:"text-gray-600 dark:text-gray-400"}," Nessun dipendente corrisponde ai filtri selezionati ",-1))])):C("",!0)]))]),E.value?(s(),i("div",Qa,[e("div",Ka,[e("div",{class:"fixed inset-0 bg-black opacity-50",onClick:n[4]||(n[4]=y=>E.value=!1)}),e("div",Wa,[n[31]||(n[31]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Crea Template Obiettivo ",-1)),n[32]||(n[32]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400 mb-4"}," Implementazione in sviluppo - utilizzare la dashboard Performance per ora ",-1)),e("div",Xa,[m(O,{variant:"secondary",onClick:n[5]||(n[5]=y=>E.value=!1)},{default:L(()=>n[29]||(n[29]=[T(" Chiudi ")])),_:1,__:[29]}),m(O,{variant:"primary",to:"/app/personnel/performance"},{default:L(()=>n[30]||(n[30]=[T(" Vai alla Dashboard ")])),_:1,__:[30]})])])])])):C("",!0),d.value?(s(),i("div",Za,[e("div",eo,[e("div",{class:"fixed inset-0 bg-black opacity-50",onClick:n[6]||(n[6]=y=>d.value=!1)}),e("div",to,[n[35]||(n[35]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Assegnazione Massiva Obiettivi ",-1)),n[36]||(n[36]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400 mb-4"}," Implementazione in sviluppo - utilizzare la dashboard Performance per ora ",-1)),e("div",ro,[m(O,{variant:"secondary",onClick:n[7]||(n[7]=y=>d.value=!1)},{default:L(()=>n[33]||(n[33]=[T(" Chiudi ")])),_:1,__:[33]}),m(O,{variant:"primary",to:"/app/personnel/performance"},{default:L(()=>n[34]||(n[34]=[T(" Vai alla Dashboard ")])),_:1,__:[34]})])])])])):C("",!0)]))}},ao=Z(so,[["__scopeId","data-v-d9e64c01"]]),oo={class:"space-y-6"},no={key:0,class:"grid grid-cols-1 md:grid-cols-3 gap-6"},lo={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},io={class:"p-5"},uo={class:"flex items-center"},mo={class:"flex-shrink-0"},co={class:"ml-5 w-0 flex-1"},go={class:"text-lg font-medium text-gray-900 dark:text-white"},po={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},xo={class:"p-5"},yo={class:"flex items-center"},vo={class:"flex-shrink-0"},bo={class:"ml-5 w-0 flex-1"},fo={class:"text-lg font-medium text-gray-900 dark:text-white"},ko={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},wo={class:"p-5"},ho={class:"flex items-center"},_o={class:"flex-shrink-0"},$o={class:"ml-5 w-0 flex-1"},zo={class:"text-lg font-medium text-gray-900 dark:text-white"},Co={key:1,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Eo={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},To={class:"space-y-3"},Do={class:"flex items-center"},Uo={class:"text-sm text-gray-700 dark:text-gray-300"},Po={class:"flex items-center"},So={class:"text-sm font-medium text-gray-900 dark:text-white mr-2"},jo={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Vo={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Mo={class:"space-y-3 max-h-64 overflow-y-auto"},No={class:"flex items-center"},Ao={class:"text-sm text-gray-700 dark:text-gray-300 truncate"},Io={class:"flex items-center"},Ro={class:"text-sm font-medium text-gray-900 dark:text-white mr-2"},Lo={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Oo={key:2,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Bo={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ho={class:"space-y-3"},Fo={class:"flex items-center"},Go={class:"text-sm text-gray-700 dark:text-gray-300"},qo={class:"flex items-center"},Jo={class:"text-sm font-medium text-gray-900 dark:text-white mr-2"},Yo={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Qo={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ko={key:0,class:"space-y-3 max-h-64 overflow-y-auto"},Wo={class:"text-sm text-gray-700 dark:text-gray-300 truncate"},Xo={class:"text-sm font-medium text-gray-900 dark:text-white"},Zo={key:1,class:"text-center py-8"},en={class:"flex justify-center"},tn=["disabled"],rn={key:3,class:"flex justify-center items-center h-64"},sn={key:4,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},an={__name:"AnalyticsDashboard",props:{analytics:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(S,{emit:I}){const $=v=>({admin:"Admin",manager:"Manager",employee:"Dipendente"})[v]||v,w=v=>({full_time:"Tempo pieno",part_time:"Part-time",contractor:"Consulente",intern:"Stagista"})[v]||v,z=v=>v?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(v):"€0";return(v,c)=>(s(),i("div",oo,[S.analytics?(s(),i("div",no,[e("div",lo,[e("div",io,[e("div",uo,[e("div",mo,[m(p,{name:"users",size:"md",color:"text-blue-600"})]),e("div",co,[e("dl",null,[c[1]||(c[1]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipendenti Totali ",-1)),e("dd",go,u(S.analytics.overview.total_users),1)])])])])]),e("div",po,[e("div",xo,[e("div",yo,[e("div",vo,[m(p,{name:"building-office",size:"md",color:"text-green-600"})]),e("div",bo,[e("dl",null,[c[2]||(c[2]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipartimenti ",-1)),e("dd",fo,u(S.analytics.overview.total_departments),1)])])])])]),e("div",ko,[e("div",wo,[e("div",ho,[e("div",_o,[m(p,{name:"user-plus",size:"md",color:"text-purple-600"})]),e("div",$o,[e("dl",null,[c[3]||(c[3]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Assunzioni Recenti (90gg) ",-1)),e("dd",zo,u(S.analytics.overview.recent_hires),1)])])])])])])):C("",!0),S.analytics?(s(),i("div",Co,[e("div",Eo,[c[4]||(c[4]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Distribuzione per Ruolo",-1)),e("div",To,[(s(!0),i(B,null,H(S.analytics.users_by_role,o=>(s(),i("div",{key:o.role,class:"flex items-center justify-between"},[e("div",Do,[e("div",{class:Q(["w-3 h-3 rounded-full mr-3",o.role==="admin"?"bg-red-500":o.role==="manager"?"bg-blue-500":"bg-gray-500"])},null,2),e("span",Uo,u($(o.role)),1)]),e("div",Po,[e("span",So,u(o.count),1),e("div",jo,[e("div",{class:Q(["h-2 rounded-full",o.role==="admin"?"bg-red-500":o.role==="manager"?"bg-blue-500":"bg-gray-500"]),style:re({width:`${o.count/S.analytics.overview.total_users*100}%`})},null,6)])])]))),128))])]),e("div",Vo,[c[6]||(c[6]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Distribuzione per Dipartimento",-1)),e("div",Mo,[(s(!0),i(B,null,H(S.analytics.users_by_department,o=>(s(),i("div",{key:o.department,class:"flex items-center justify-between"},[e("div",No,[c[5]||(c[5]=e("div",{class:"w-3 h-3 rounded-full bg-indigo-500 mr-3"},null,-1)),e("span",Ao,u(o.department),1)]),e("div",Io,[e("span",Ro,u(o.count),1),e("div",Lo,[e("div",{class:"h-2 rounded-full bg-indigo-500",style:re({width:`${o.count/S.analytics.overview.total_users*100}%`})},null,4)])])]))),128))])])])):C("",!0),S.analytics?(s(),i("div",Oo,[e("div",Bo,[c[8]||(c[8]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Tipi di Contratto",-1)),e("div",Ho,[(s(!0),i(B,null,H(S.analytics.employment_types,o=>(s(),i("div",{key:o.type,class:"flex items-center justify-between"},[e("div",Fo,[c[7]||(c[7]=e("div",{class:"w-3 h-3 rounded-full bg-green-500 mr-3"},null,-1)),e("span",Go,u(w(o.type)),1)]),e("div",qo,[e("span",Jo,u(o.count),1),e("div",Yo,[e("div",{class:"h-2 rounded-full bg-green-500",style:re({width:`${o.count/S.analytics.overview.total_users*100}%`})},null,4)])])]))),128))])]),e("div",Qo,[c[10]||(c[10]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Stipendio Medio per Dipartimento",-1)),S.analytics.avg_salary_by_department.length>0?(s(),i("div",Ko,[(s(!0),i(B,null,H(S.analytics.avg_salary_by_department,o=>(s(),i("div",{key:o.department,class:"flex items-center justify-between"},[e("span",Wo,u(o.department),1),e("span",Xo,u(z(o.avg_salary)),1)]))),128))])):(s(),i("div",Zo,[m(p,{name:"document-text",size:"xl",color:"text-gray-400",className:"mx-auto"}),c[9]||(c[9]=e("p",{class:"mt-2 text-sm text-gray-500 dark:text-gray-400"},"Nessun dato stipendio disponibile",-1))]))])])):C("",!0),e("div",en,[e("button",{onClick:c[0]||(c[0]=o=>v.$emit("refresh")),disabled:S.loading,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[S.loading?(s(),N(p,{key:0,name:"arrow-path",size:"sm",className:"animate-spin -ml-1 mr-3"})):(s(),N(p,{key:1,name:"arrow-path",size:"sm",className:"-ml-1 mr-3"})),c[11]||(c[11]=T(" Aggiorna Dati "))],8,tn)]),S.loading&&!S.analytics?(s(),i("div",rn,c[12]||(c[12]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):C("",!0),!S.loading&&!S.analytics?(s(),i("div",sn,[m(p,{name:"chart-bar",size:"xl",color:"text-gray-400",className:"mx-auto"}),c[13]||(c[13]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dato disponibile",-1)),c[14]||(c[14]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},' Clicca su "Aggiorna Dati" per caricare le analytics. ',-1))])):C("",!0)]))}},on=Z(an,[["__scopeId","data-v-c78d2f47"]]),nn={class:"space-y-6"},ln={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},dn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},un={class:"flex items-center mb-4"},mn={class:"space-y-3"},cn=["disabled"],gn={class:"relative"},pn=["disabled"],xn={key:0,class:"mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"},yn={class:"flex items-center justify-between mb-2"},vn={class:"text-sm text-blue-600 dark:text-blue-400"},bn={class:"w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2"},fn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},kn={class:"flex items-center mb-4"},wn={class:"space-y-3"},hn=["disabled"],_n=["disabled"],$n={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},zn={class:"flex items-center mb-4"},Cn={class:"space-y-3"},En={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Tn={class:"flex items-center mb-4"},Dn={class:"space-y-3"},Un=["disabled"],Pn={class:"flex"},Sn={key:0,class:"mt-2"},jn={__name:"BulkOperations",emits:["operation-completed"],setup(S,{emit:I}){const $=I,w=k({template:!1,import:!1,export:!1,verify:!1}),z=k({show:!1,processed:0,total:0}),v=k(null),c=k(!1),o=k(!1),D=k(!1),E=k(!1),d=k(!1),t=k(!1),r=async()=>{w.value.template=!0;try{const x=["email","first_name","last_name","phone","department_id","hire_date","employment_type","salary","role","is_active"].join(",")+`
<EMAIL>,Mario,Rossi,+39 ************,1,2024-01-15,full_time,45000,employee,true
<EMAIL>,Giulia,Bianchi,+39 ************,2,2024-02-01,part_time,30000,manager,true`,b=new Blob([x],{type:"text/csv;charset=utf-8;"}),l=document.createElement("a"),n=URL.createObjectURL(b);l.setAttribute("href",n),l.setAttribute("download","template_dipendenti.csv"),l.style.visibility="hidden",document.body.appendChild(l),l.click(),document.body.removeChild(l),R("success","Template scaricato","Il template CSV è stato scaricato con successo.")}catch(V){console.error("Error downloading template:",V),R("error","Errore download","Impossibile scaricare il template.")}finally{w.value.template=!1}},_=async V=>{const x=V.target.files[0];if(x){w.value.import=!0,z.value.show=!0,z.value.processed=0;try{const b=new FormData;b.append("file",x);const l=await fetch("/api/personnel/import",{method:"POST",credentials:"include",body:b});if(!l.ok)throw new Error(`HTTP ${l.status}: ${l.statusText}`);const n=await l.json();if(n.success)R("success","Import completato",`Importati ${n.data.imported} dipendenti su ${n.data.total} righe processate.`,n.data.errors||[]),$("operation-completed","import");else throw new Error(n.message||"Errore durante l'import")}catch(b){console.error("Error importing file:",b),R("error","Errore import",b.message)}finally{w.value.import=!1,z.value.show=!1,V.target.value=""}}},j=async V=>{w.value.export=!0;try{const b=await fetch(V==="full"?"/api/personnel/export":"/api/personnel/export/contacts",{credentials:"include"});if(!b.ok)throw new Error(`HTTP ${b.status}: ${b.statusText}`);const l=b.headers.get("content-disposition");let n=`export_${V}_${new Date().toISOString().split("T")[0]}.csv`;if(l){const Y=l.match(/filename="(.+)"/);Y&&(n=Y[1])}const y=await b.blob(),J=window.URL.createObjectURL(y),F=document.createElement("a");F.href=J,F.download=n,document.body.appendChild(F),F.click(),document.body.removeChild(F),window.URL.revokeObjectURL(J),R("success","Export completato",`I dati sono stati esportati in ${n}`)}catch(x){console.error("Error exporting data:",x),R("error","Errore export",x.message)}finally{w.value.export=!1}},M=async()=>{w.value.verify=!0;try{const V=await fetch("/api/personnel/verify",{credentials:"include"});if(!V.ok)throw new Error(`HTTP ${V.status}: ${V.statusText}`);const x=await V.json();if(x.success){const b=x.data.issues||[];b.length===0?R("success","Verifica completata","Nessun problema di integrità rilevato."):R("error","Problemi rilevati",`Trovati ${b.length} problemi di integrità dati.`,b)}else throw new Error(x.message||"Errore durante la verifica")}catch(V){console.error("Error verifying data:",V),R("error","Errore verifica",V.message)}finally{w.value.verify=!1}},R=(V,x,b,l=[])=>{v.value={type:V,title:x,message:b,details:l},V==="success"&&setTimeout(()=>{v.value=null},5e3)};return(V,x)=>(s(),i("div",nn,[x[25]||(x[25]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Operazioni di Massa"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Import/Export dati e operazioni bulk ")],-1)),e("div",ln,[e("div",dn,[e("div",un,[m(p,{name:"arrow-down-tray",size:"md",color:"text-green-600",className:"mr-3"}),x[10]||(x[10]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Import Dipendenti",-1))]),x[12]||(x[12]=e("p",{class:"text-sm text-gray-500 dark:text-gray-400 mb-4"}," Importa dipendenti da file CSV con dati contrattuali completi ",-1)),e("div",mn,[e("button",{onClick:r,disabled:w.value.template,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"},[w.value.template?(s(),N(p,{key:0,name:"arrow-path",size:"sm",className:"animate-spin mr-2"})):(s(),N(p,{key:1,name:"arrow-down-tray",size:"sm",className:"mr-2"})),T(" "+u(w.value.template?"Generando...":"Scarica Template CSV"),1)],8,cn),e("div",gn,[e("input",{ref:"fileInput",type:"file",accept:".csv",onChange:_,class:"hidden"},null,544),e("button",{onClick:x[0]||(x[0]=b=>V.$refs.fileInput.click()),disabled:w.value.import,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"},[w.value.import?(s(),N(p,{key:0,name:"arrow-path",size:"sm",className:"animate-spin mr-2"})):(s(),N(p,{key:1,name:"arrow-up-tray",size:"sm",className:"mr-2"})),T(" "+u(w.value.import?"Importando...":"Carica File CSV"),1)],8,pn)]),z.value.show?(s(),i("div",xn,[e("div",yn,[x[11]||(x[11]=e("span",{class:"text-sm font-medium text-blue-800 dark:text-blue-200"},"Import in corso...",-1)),e("span",vn,u(z.value.processed)+"/"+u(z.value.total),1)]),e("div",bn,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:re({width:`${z.value.processed/z.value.total*100}%`})},null,4)])])):C("",!0)])]),e("div",fn,[e("div",kn,[m(p,{name:"arrow-up-tray",size:"md",color:"text-blue-600",className:"mr-3"}),x[13]||(x[13]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Export Dati",-1))]),x[15]||(x[15]=e("p",{class:"text-sm text-gray-500 dark:text-gray-400 mb-4"}," Esporta dati del personale in vari formati ",-1)),e("div",wn,[e("button",{onClick:x[1]||(x[1]=b=>j("full")),disabled:w.value.export,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"},[w.value.export?(s(),N(p,{key:0,name:"arrow-path",size:"sm",className:"animate-spin mr-2"})):(s(),N(p,{key:1,name:"arrow-down-tray",size:"sm",className:"mr-2"})),T(" "+u(w.value.export?"Esportando...":"Export Completo CSV"),1)],8,hn),e("button",{onClick:x[2]||(x[2]=b=>j("contacts")),disabled:w.value.export,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"},[w.value.export?(s(),N(p,{key:0,name:"arrow-path",size:"sm",className:"animate-spin mr-2"})):(s(),N(p,{key:1,name:"funnel",size:"sm",className:"mr-2"})),T(" "+u(w.value.export?"Esportando...":"Export Solo Contatti"),1)],8,_n),e("button",{onClick:x[3]||(x[3]=b=>c.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},[m(p,{name:"document",size:"sm",className:"mr-2"}),x[14]||(x[14]=T(" Export Personalizzato "))])])]),e("div",$n,[e("div",zn,[m(p,{name:"cog-6-tooth",size:"md",color:"text-purple-600",className:"mr-3"}),x[16]||(x[16]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Aggiornamenti di Massa",-1))]),x[20]||(x[20]=e("p",{class:"text-sm text-gray-500 dark:text-gray-400 mb-4"}," Applica modifiche a più dipendenti contemporaneamente ",-1)),e("div",Cn,[e("button",{onClick:x[4]||(x[4]=b=>o.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"},[m(p,{name:"building-office",size:"sm",className:"mr-2"}),x[17]||(x[17]=T(" Assegnazione Dipartimenti "))]),e("button",{onClick:x[5]||(x[5]=b=>D.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},[m(p,{name:"star",size:"sm",className:"mr-2"}),x[18]||(x[18]=T(" Assegnazione Competenze "))]),e("button",{onClick:x[6]||(x[6]=b=>E.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},[m(p,{name:"shield-check",size:"sm",className:"mr-2"}),x[19]||(x[19]=T(" Modifica Ruoli "))])])]),e("div",En,[e("div",Tn,[m(p,{name:"trash",size:"md",color:"text-red-600",className:"mr-3"}),x[21]||(x[21]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Pulizia Dati",-1))]),x[24]||(x[24]=e("p",{class:"text-sm text-gray-500 dark:text-gray-400 mb-4"}," Strumenti per la manutenzione e pulizia dei dati ",-1)),e("div",Dn,[e("button",{onClick:M,disabled:w.value.verify,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"},[w.value.verify?(s(),N(p,{key:0,name:"arrow-path",size:"sm",className:"animate-spin mr-2"})):(s(),N(p,{key:1,name:"check-circle",size:"sm",className:"mr-2"})),T(" "+u(w.value.verify?"Verificando...":"Verifica Integrità Dati"),1)],8,Un),e("button",{onClick:x[7]||(x[7]=b=>d.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 dark:border-red-600 shadow-sm text-sm font-medium rounded-md text-red-700 dark:text-red-300 bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20"},[m(p,{name:"user-minus",size:"sm",className:"mr-2"}),x[22]||(x[22]=T(" Rimuovi Utenti Inattivi "))]),e("button",{onClick:x[8]||(x[8]=b=>t.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},[m(p,{name:"arrow-path",class:"w-4 h-4 mr-2"}),x[23]||(x[23]=T(" Pulizia Avanzata "))])])])]),v.value?(s(),i("div",{key:0,class:Q(["mt-6 p-4 rounded-lg",v.value.type==="success"?"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800":"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800"])},[e("div",Pn,[v.value.type==="success"?(s(),N(p,{key:0,name:"check-circle",class:"w-5 h-5 text-green-400 mr-2 mt-0.5"})):(s(),N(p,{key:1,name:"x-circle",class:"w-5 h-5 text-red-400 mr-2 mt-0.5"})),e("div",null,[e("h3",{class:Q(["text-sm font-medium",v.value.type==="success"?"text-green-800 dark:text-green-200":"text-red-800 dark:text-red-200"])},u(v.value.title),3),e("p",{class:Q(["text-sm mt-1",v.value.type==="success"?"text-green-700 dark:text-green-300":"text-red-700 dark:text-red-300"])},u(v.value.message),3),v.value.details&&v.value.details.length>0?(s(),i("div",Sn,[e("ul",{class:Q(["text-xs space-y-1",v.value.type==="success"?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"])},[(s(!0),i(B,null,H(v.value.details,b=>(s(),i("li",{key:b},"• "+u(b),1))),128))],2)])):C("",!0)]),e("button",{onClick:x[9]||(x[9]=b=>v.value=null),class:"ml-auto"},[m(p,{name:"x-mark",class:Q(["w-4 h-4",v.value.type==="success"?"text-green-400":"text-red-400"])},null,8,["class"])])])],2)):C("",!0)]))}},Vn={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},Mn={class:"mt-6"},Nn={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},An={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},In=["value"],Rn={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ln={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},On={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Bn={class:"flex items-center space-x-4"},Hn={class:"flex items-center"},Fn={key:0,class:"mt-3"},Gn={key:0,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},qn={class:"flex"},Jn={class:"text-sm text-red-700 dark:text-red-300 mt-1"},Yn={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},Qn=["disabled"],Kn={__name:"CreateUserModal",emits:["close","user-created"],setup(S,{emit:I}){const $=I,w=k(!1),z=k(null),v=k([]),c=k(!0),o=k({first_name:"",last_name:"",username:"",email:"",phone:"",position:"",role:"employee",department_id:"",hire_date:"",employment_type:"full_time",work_location:"",weekly_hours:40,probation_end_date:"",contract_end_date:"",salary:null,salary_currency:"EUR",emergency_contact_name:"",emergency_contact_phone:"",emergency_contact_relationship:"",password:""}),D=async()=>{try{const d=await fetch("/api/personnel/departments",{credentials:"include"});if(!d.ok)throw new Error(`HTTP ${d.status}: ${d.statusText}`);const t=await d.json();t.success&&(v.value=t.data.departments||[])}catch(d){console.error("Error loading departments:",d)}},E=async()=>{w.value=!0,z.value=null;try{const d={...o.value};c.value&&delete d.password,Object.keys(d).forEach(_=>{d[_]===""&&(d[_]=null)});const t=await fetch("/api/personnel/admin/users",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(d)});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const r=await t.json();if(r.success)$("user-created",r.data.user),c.value&&r.data.temporary_password&&alert(`Utente creato con successo!
Password temporanea: ${r.data.temporary_password}`);else throw new Error(r.message||"Errore nella creazione utente")}catch(d){console.error("Error creating user:",d),z.value=d.message}finally{w.value=!1}};return W(()=>{D();const d=new Date().toISOString().split("T")[0];o.value.hire_date=d}),(d,t)=>(s(),i("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:t[24]||(t[24]=r=>d.$emit("close"))},[e("div",{class:"relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[23]||(t[23]=se(()=>{},["stop"]))},[e("div",Vn,[t[25]||(t[25]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Crea Nuovo Dipendente ",-1)),e("button",{onClick:t[0]||(t[0]=r=>d.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[m(p,{name:"close",size:"lg"})])]),e("div",Mn,[e("form",{onSubmit:se(E,["prevent"]),class:"space-y-6"},[e("div",null,[t[32]||(t[32]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Base",-1)),e("div",Nn,[e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome * ",-1)),P(e("input",{"onUpdate:modelValue":t[1]||(t[1]=r=>o.value.first_name=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,o.value.first_name]])]),e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Cognome * ",-1)),P(e("input",{"onUpdate:modelValue":t[2]||(t[2]=r=>o.value.last_name=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,o.value.last_name]])]),e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Username * ",-1)),P(e("input",{"onUpdate:modelValue":t[3]||(t[3]=r=>o.value.username=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,o.value.username]])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Email * ",-1)),P(e("input",{"onUpdate:modelValue":t[4]||(t[4]=r=>o.value.email=r),type:"email",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,o.value.email]])]),e("div",null,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Telefono ",-1)),P(e("input",{"onUpdate:modelValue":t[5]||(t[5]=r=>o.value.phone=r),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,o.value.phone]])]),e("div",null,[t[31]||(t[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Posizione ",-1)),P(e("input",{"onUpdate:modelValue":t[6]||(t[6]=r=>o.value.position=r),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,o.value.position]])])])]),e("div",null,[t[37]||(t[37]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Ruolo e Dipartimento",-1)),e("div",An,[e("div",null,[t[34]||(t[34]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ruolo ",-1)),P(e("select",{"onUpdate:modelValue":t[7]||(t[7]=r=>o.value.role=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[33]||(t[33]=[e("option",{value:"employee"},"Dipendente",-1),e("option",{value:"manager"},"Manager",-1),e("option",{value:"admin"},"Admin",-1)]),512),[[G,o.value.role]])]),e("div",null,[t[36]||(t[36]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Dipartimento ",-1)),P(e("select",{"onUpdate:modelValue":t[8]||(t[8]=r=>o.value.department_id=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[t[35]||(t[35]=e("option",{value:""},"Seleziona dipartimento",-1)),(s(!0),i(B,null,H(v.value,r=>(s(),i("option",{key:r.id,value:r.id},u(r.name),9,In))),128))],512),[[G,o.value.department_id]])])])]),e("div",null,[t[46]||(t[46]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Contrattuali",-1)),e("div",Rn,[e("div",null,[t[38]||(t[38]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Assunzione ",-1)),P(e("input",{"onUpdate:modelValue":t[9]||(t[9]=r=>o.value.hire_date=r),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,o.value.hire_date]])]),e("div",null,[t[40]||(t[40]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Contratto ",-1)),P(e("select",{"onUpdate:modelValue":t[10]||(t[10]=r=>o.value.employment_type=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[39]||(t[39]=[e("option",{value:"full_time"},"Tempo pieno",-1),e("option",{value:"part_time"},"Part-time",-1),e("option",{value:"contractor"},"Consulente",-1),e("option",{value:"intern"},"Stagista",-1)]),512),[[G,o.value.employment_type]])]),e("div",null,[t[42]||(t[42]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Modalità Lavoro ",-1)),P(e("select",{"onUpdate:modelValue":t[11]||(t[11]=r=>o.value.work_location=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[41]||(t[41]=[e("option",{value:""},"Seleziona modalità",-1),e("option",{value:"office"},"Ufficio",-1),e("option",{value:"remote"},"Remoto",-1),e("option",{value:"hybrid"},"Ibrido",-1)]),512),[[G,o.value.work_location]])]),e("div",null,[t[43]||(t[43]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ore Settimanali ",-1)),P(e("input",{"onUpdate:modelValue":t[12]||(t[12]=r=>o.value.weekly_hours=r),type:"number",step:"0.5",min:"0",max:"60",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,o.value.weekly_hours,void 0,{number:!0}]])]),e("div",null,[t[44]||(t[44]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Fine Periodo Prova ",-1)),P(e("input",{"onUpdate:modelValue":t[13]||(t[13]=r=>o.value.probation_end_date=r),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,o.value.probation_end_date]])]),e("div",null,[t[45]||(t[45]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Scadenza Contratto ",-1)),P(e("input",{"onUpdate:modelValue":t[14]||(t[14]=r=>o.value.contract_end_date=r),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,o.value.contract_end_date]])])])]),e("div",null,[t[50]||(t[50]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Economiche",-1)),e("div",Ln,[e("div",null,[t[47]||(t[47]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stipendio Annuo ",-1)),P(e("input",{"onUpdate:modelValue":t[15]||(t[15]=r=>o.value.salary=r),type:"number",step:"100",min:"0",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,o.value.salary,void 0,{number:!0}]])]),e("div",null,[t[49]||(t[49]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Valuta ",-1)),P(e("select",{"onUpdate:modelValue":t[16]||(t[16]=r=>o.value.salary_currency=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[48]||(t[48]=[e("option",{value:"EUR"},"EUR",-1),e("option",{value:"USD"},"USD",-1),e("option",{value:"GBP"},"GBP",-1)]),512),[[G,o.value.salary_currency]])])])]),e("div",null,[t[54]||(t[54]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Contatto di Emergenza",-1)),e("div",On,[e("div",null,[t[51]||(t[51]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome Contatto ",-1)),P(e("input",{"onUpdate:modelValue":t[17]||(t[17]=r=>o.value.emergency_contact_name=r),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,o.value.emergency_contact_name]])]),e("div",null,[t[52]||(t[52]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Telefono Emergenza ",-1)),P(e("input",{"onUpdate:modelValue":t[18]||(t[18]=r=>o.value.emergency_contact_phone=r),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,o.value.emergency_contact_phone]])]),e("div",null,[t[53]||(t[53]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Relazione ",-1)),P(e("input",{"onUpdate:modelValue":t[19]||(t[19]=r=>o.value.emergency_contact_relationship=r),type:"text",placeholder:"es. Coniuge, Genitore...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,o.value.emergency_contact_relationship]])])])]),e("div",null,[t[57]||(t[57]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Password",-1)),e("div",Bn,[e("label",Hn,[P(e("input",{"onUpdate:modelValue":t[20]||(t[20]=r=>c.value=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[le,c.value]]),t[55]||(t[55]=e("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"}," Genera password temporanea automaticamente ",-1))])]),c.value?C("",!0):(s(),i("div",Fn,[t[56]||(t[56]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Password Personalizzata ",-1)),P(e("input",{"onUpdate:modelValue":t[21]||(t[21]=r=>o.value.password=r),type:"password",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[A,o.value.password]])]))]),z.value?(s(),i("div",Gn,[e("div",qn,[m(p,{name:"error",size:"sm",color:"text-red-400",class:"mr-2 mt-0.5"}),e("div",null,[t[58]||(t[58]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nella creazione",-1)),e("p",Jn,u(z.value),1)])])])):C("",!0),e("div",Yn,[e("button",{type:"button",onClick:t[22]||(t[22]=r=>d.$emit("close")),class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Annulla "),e("button",{type:"submit",disabled:w.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[w.value?(s(),N(p,{key:0,name:"loading",size:"sm",color:"text-white",class:"-ml-1 mr-3 inline"})):C("",!0),T(" "+u(w.value?"Creazione...":"Crea Dipendente"),1)],8,Qn)])],32)])])]))}},Wn=Z(Kn,[["__scopeId","data-v-e74f19e2"]]),Xn={class:"space-y-6"},Zn={class:"mt-6"},el={key:0},tl={key:1},rl={key:2},sl={key:3},al={key:4},ol={key:5},nl={__name:"PersonnelAdmin",setup(S){const I=k(!1),$=k(null),w=k("users"),z=k(!1),v=k([{id:"users",name:"Gestione Utenti",icon:"users"},{id:"departments",name:"Dipartimenti",icon:"building-office-2"},{id:"skills",name:"Competenze",icon:"star"},{id:"performance",name:"Performance",icon:"chart-line"},{id:"analytics",name:"Analytics",icon:"chart-bar"},{id:"bulk",name:"Operazioni Bulk",icon:"list-bullet"}]),c=q(()=>{var r,_,j;if(!((r=$.value)!=null&&r.alerts))return[];const t=[];return((_=$.value.alerts.expiring_contracts)==null?void 0:_.length)>0&&t.push({id:"expiring-contracts",type:"error",title:"Contratti in Scadenza",message:`${$.value.alerts.expiring_contracts.length} contratti scadranno nei prossimi 90 giorni:`,items:$.value.alerts.expiring_contracts.map(M=>({id:M.user_id,full_name:M.full_name,end_date:M.contract_end_date,days_remaining:M.days_remaining})),maxItems:3,moreText:"altri contratti"}),((j=$.value.alerts.ending_probation)==null?void 0:j.length)>0&&t.push({id:"ending-probation",type:"warning",title:"Periodi di Prova in Scadenza",icon:"clock",message:`${$.value.alerts.ending_probation.length} periodi di prova termineranno nei prossimi 30 giorni:`,items:$.value.alerts.ending_probation.map(M=>({id:M.user_id,full_name:M.full_name,end_date:M.probation_end_date,days_remaining:M.days_remaining})),maxItems:3,moreText:"altri periodi di prova"}),t}),o=async()=>{I.value=!0;try{const t=await fetch("/api/personnel/admin/analytics",{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const r=await t.json();if(r.success)$.value=r.data;else throw new Error(r.message||"Errore nel caricamento analytics")}catch(t){console.error("Error loading analytics:",t)}finally{I.value=!1}},D=async()=>{try{const t=await fetch("/api/personnel/export",{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const r=await t.blob(),_=window.URL.createObjectURL(r),j=document.createElement("a");j.href=_,j.download=`personnel-export-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(j),j.click(),document.body.removeChild(j),window.URL.revokeObjectURL(_)}catch(t){console.error("Error exporting data:",t)}},E=t=>{z.value=!1,o()},d=t=>t?new Date(t).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"";return W(()=>{o()}),(t,r)=>(s(),i("div",Xn,[m(ue,{title:"Gestione Personale",subtitle:"Gestione completa del personale e dati contrattuali",icon:"cog-6-tooth"},{actions:L(()=>[m(O,{variant:"primary",icon:"plus",onClick:r[0]||(r[0]=_=>z.value=!0)},{default:L(()=>r[3]||(r[3]=[T(" Nuovo Dipendente ")])),_:1,__:[3]}),m(O,{variant:"secondary",icon:"user-group",to:"/app/personnel/allocation"},{default:L(()=>r[4]||(r[4]=[T(" Allocazione Risorse ")])),_:1,__:[4]}),m(O,{variant:"secondary",icon:"chart-bar-square",to:"/app/personnel/orgchart"},{default:L(()=>r[5]||(r[5]=[T(" Organigramma ")])),_:1,__:[5]}),m(O,{variant:"secondary",icon:"star",to:"/app/personnel/skills"},{default:L(()=>r[6]||(r[6]=[T(" Skills Matrix ")])),_:1,__:[6]}),m(O,{variant:"secondary",icon:"chart-line",to:"/app/personnel/performance"},{default:L(()=>r[7]||(r[7]=[T(" Performance Overview ")])),_:1,__:[7]}),m(O,{variant:"secondary",icon:"arrow-down-tray",disabled:I.value,onClick:D},{default:L(()=>r[8]||(r[8]=[T(" Esporta Dati ")])),_:1,__:[8]},8,["disabled"])]),_:1}),m(me,{alerts:c.value},{"alert-item":L(({item:_,alert:j})=>[e("strong",null,u(_.full_name),1),T(" - "+u(d(_.end_date))+" ("+u(_.days_remaining)+" giorni) ",1)]),_:1},8,["alerts"]),m(ce,{tabs:v.value,"active-tab":w.value,onTabChange:r[1]||(r[1]=_=>w.value=_)},null,8,["tabs","active-tab"]),e("div",Zn,[w.value==="users"?(s(),i("div",el,[m(ot,{onUserCreated:o,onUserUpdated:o,onUserDeleted:o})])):w.value==="departments"?(s(),i("div",tl,[m(Mr,{onDepartmentCreated:o,onDepartmentUpdated:o,onDepartmentDeleted:o})])):w.value==="skills"?(s(),i("div",rl,[m(Fs,{onSkillCreated:o,onSkillUpdated:o,onSkillDeleted:o})])):w.value==="performance"?(s(),i("div",sl,[m(ao,{onPerformanceUpdated:o})])):w.value==="analytics"?(s(),i("div",al,[m(on,{analytics:$.value,loading:I.value,onRefresh:o},null,8,["analytics","loading"])])):w.value==="bulk"?(s(),i("div",ol,[m(jn,{onOperationCompleted:o})])):C("",!0)]),z.value?(s(),N(Wn,{key:0,onClose:r[2]||(r[2]=_=>z.value=!1),onUserCreated:E})):C("",!0)]))}},yl=Z(nl,[["__scopeId","data-v-9467105b"]]);export{yl as default};
