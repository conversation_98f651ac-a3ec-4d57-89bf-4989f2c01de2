import{d as E,r as i,c as g}from"./vendor.js";import{c as h}from"./app.js";const k=E("projects",()=>{const u=i([]),v=i(null),o=i(!1),n=i(null),d=i(new Map),p=i({page:1,per_page:20,total:0,total_pages:0}),l=i({search:"",status:"",client:"",type:""}),w=g(()=>{let e=u.value;if(l.value.search){const r=l.value.search.toLowerCase();e=e.filter(s=>{var a,t,c,f;return((a=s.name)==null?void 0:a.toLowerCase().includes(r))||((t=s.description)==null?void 0:t.toLowerCase().includes(r))||((f=(c=s.client)==null?void 0:c.name)==null?void 0:f.toLowerCase().includes(r))})}return l.value.status&&(e=e.filter(r=>r.status===l.value.status)),l.value.client&&(e=e.filter(r=>r.client_id===l.value.client)),l.value.type&&(e=e.filter(r=>r.project_type===l.value.type)),e}),m=g(()=>{const e={};return u.value.forEach(r=>{e[r.status]||(e[r.status]=[]),e[r.status].push(r)}),e}),j=(e,r=0)=>{const s=(e==null?void 0:e.per_page)||r||10;return{page:(e==null?void 0:e.page)||1,per_page:s,total:(e==null?void 0:e.total)||r||0,total_pages:(e==null?void 0:e.total_pages)||Math.ceil(((e==null?void 0:e.total)||r||0)/s)||1}},C=async(e={})=>{var r,s;o.value=!0,n.value=null;try{const a=new URLSearchParams({page:e.page||p.value.page,per_page:e.per_page||p.value.per_page,search:e.search||l.value.search,status:e.status||l.value.status,client:e.client||l.value.client,type:e.type||l.value.type}),t=await h.get(`/api/projects?${a}`);t.data.success&&(t.data.data.items?(u.value=t.data.data.items,p.value=j(t.data.data.pagination,t.data.data.items.length)):t.data.data.projects?(u.value=t.data.data.projects,p.value=j(t.data.data.pagination,t.data.data.projects.length)):Array.isArray(t.data.data)?(u.value=t.data.data,p.value=j(null,t.data.data.length)):(u.value=[],p.value=j(null,0)))}catch(a){n.value=((s=(r=a.response)==null?void 0:r.data)==null?void 0:s.message)||"Errore nel caricamento progetti",console.error("Error fetching projects:",a)}finally{o.value=!1}},y=async(e,r=!1)=>{var s,a;if(!r&&d.value.has(e)){const t=d.value.get(e);return v.value=t,t}o.value=!0,n.value=null;try{const t=await h.get(`/api/projects/${e}`);if(t.data.success){const c=t.data.data.project;return v.value=c,d.value.set(e,c),c}}catch(t){throw n.value=((a=(s=t.response)==null?void 0:s.data)==null?void 0:a.message)||"Errore nel caricamento progetto",console.error("Error fetching project:",t),t}finally{o.value=!1}};return{projects:u,currentProject:v,loading:o,error:n,pagination:p,filters:l,filteredProjects:w,projectsByStatus:m,fetchProjects:C,fetchProject:y,createProject:async e=>{var r,s;o.value=!0,n.value=null;try{const a=await h.post("/api/projects",e);if(a.data.success){const t=a.data.data.project;return u.value.unshift(t),t}}catch(a){throw n.value=((s=(r=a.response)==null?void 0:r.data)==null?void 0:s.message)||"Errore nella creazione progetto",console.error("Error creating project:",a),a}finally{o.value=!1}},updateProject:async(e,r)=>{var s,a,t;o.value=!0,n.value=null;try{const c=await h.put(`/api/projects/${e}`,r);if(c.data.success){const f=c.data.data.project,P=u.value.findIndex(_=>_.id===e);return P!==-1&&(u.value[P]=f),((s=v.value)==null?void 0:s.id)===e&&(v.value=f),d.value.set(e,f),f}}catch(c){throw n.value=((t=(a=c.response)==null?void 0:a.data)==null?void 0:t.message)||"Errore nell'aggiornamento progetto",console.error("Error updating project:",c),c}finally{o.value=!1}},deleteProject:async e=>{var r,s,a;o.value=!0,n.value=null;try{(await h.delete(`/api/projects/${e}`)).data.success&&(u.value=u.value.filter(c=>c.id!==e),((r=v.value)==null?void 0:r.id)===e&&(v.value=null),d.value.delete(e))}catch(t){throw n.value=((a=(s=t.response)==null?void 0:s.data)==null?void 0:a.message)||"Errore nell'eliminazione progetto",console.error("Error deleting project:",t),t}finally{o.value=!1}},setFilters:e=>{l.value={...l.value,...e}},clearFilters:()=>{l.value={search:"",status:"",client:"",type:""}},setCurrentProject:e=>{v.value=e},clearCurrentProject:()=>{v.value=null},clearCache:()=>{d.value.clear()},refreshProject:async e=>await y(e,!0),getCachedProject:e=>d.value.get(e),$reset:()=>{u.value=[],v.value=null,o.value=!1,n.value=null,d.value.clear(),p.value={page:1,per_page:20,total:0,pages:0},l.value={search:"",status:"",client:"",type:""}}}});export{k as u};
