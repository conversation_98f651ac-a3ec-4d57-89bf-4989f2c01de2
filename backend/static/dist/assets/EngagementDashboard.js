import{b as n,j as e,l as u,e as l,f as H,t as a,E as q,k as j,F as z,p as N,B as F,C as G,n as B,c as x,r as M,x as O,q as U,o as r,s as I,A as J}from"./vendor.js";import{u as K}from"./engagement.js";import{_ as Q,H as W}from"./app.js";const X={name:"EngagementDashboard",components:{HeroIcon:W},setup(){const E=U(),t=K(),C=x(()=>t.userProfile),s=x(()=>t.dashboardData.active_campaigns||[]),V=x(()=>t.dashboardData.recent_points||[]),T=x(()=>t.dashboardData.available_rewards||[]),d=x(()=>t.dashboardData.leaderboard_position),y=x(()=>t.dashboardData.week_activity),v=x(()=>t.loading),m=M(null),_=M(""),g=M(!1),p=async()=>{try{await t.fetchDashboard()}catch(i){console.error("Error fetching dashboard data:",i)}},b=i=>{E.push(`/engagement/campaigns/${i}`)},h=i=>{var c;((c=C.value)==null?void 0:c.available_points)>=i.points_cost&&(m.value=i,_.value="")},f=()=>{m.value=null,_.value="",g.value=!1},w=async()=>{if(!(!m.value||g.value)){g.value=!0;try{await t.redeemReward(m.value.id),f(),await p()}catch(i){console.error("Error redeeming reward:",i)}finally{g.value=!1}}},k=i=>({login:"arrow-right-on-rectangle",create:"plus-circle",update:"pencil-square",delete:"trash",view:"eye",approve:"check-circle"})[i]||"star",P=i=>{const c={login:"Accesso alla piattaforma",create:`Creazione ${i.resource_type}`,update:`Aggiornamento ${i.resource_type}`,delete:`Eliminazione ${i.resource_type}`,view:`Visualizzazione ${i.resource_type}`,approve:`Approvazione ${i.resource_type}`};return i.description||c[i.action_type]||`Azione ${i.action_type}`},R=i=>{if(!i)return"";const c=new Date(i),o=Math.floor((new Date-c)/(1e3*60*60));return o<1?"Ora":o<24?`${o}h fa`:o<48?"Ieri":c.toLocaleDateString("it-IT",{month:"short",day:"numeric"})};return O(()=>{p()}),{selectedReward:m,redemptionNotes:_,redeeming:g,userProfile:C,activeCampaigns:s,recentPoints:V,availableRewards:T,leaderboardPosition:d,weekActivity:y,loading:v,fetchDashboardData:p,goToCampaignDetail:b,openRewardModal:h,closeRewardModal:f,redeemReward:w,getActionIcon:k,getActionDescription:P,formatDateTime:R}}},Y={class:"engagement-dashboard"},Z={key:0,class:"space-y-6"},$={class:"bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg shadow-lg p-6 text-white"},ee={class:"flex items-center justify-between"},te={class:"flex items-center space-x-4"},se={class:"bg-white bg-opacity-20 rounded-full p-3"},oe={class:"text-xl font-bold"},ae={class:"text-primary-100"},ie={class:"text-right"},ne={class:"flex items-center space-x-2"},re={class:"font-semibold"},le={key:0,class:"mt-4"},de={class:"flex justify-between text-sm text-primary-100 mb-1"},ce={class:"w-full bg-primary-400 bg-opacity-30 rounded-full h-2"},me={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ge={class:"bg-white rounded-lg shadow p-4"},xe={class:"flex items-center"},_e={class:"ml-3"},pe={class:"text-xl font-bold text-gray-900"},ue={class:"bg-white rounded-lg shadow p-4"},ye={class:"flex items-center"},fe={class:"ml-3"},ve={class:"text-xl font-bold text-gray-900"},be={class:"bg-white rounded-lg shadow p-4"},he={class:"flex items-center"},we={class:"ml-3"},ke={class:"text-xl font-bold text-gray-900"},Pe={class:"bg-white rounded-lg shadow p-4"},Re={class:"flex items-center"},Ce={class:"ml-3"},De={class:"text-xl font-bold text-gray-900"},Ae={class:"bg-white shadow rounded-lg"},je={class:"px-6 py-4 border-b border-gray-200"},ze={class:"flex items-center justify-between"},Ne={key:0,class:"p-6"},Me={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},Ie=["onClick"],Ee={class:"flex items-start justify-between mb-3"},Ve={class:"font-medium text-gray-900"},Te={key:0,class:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full"},Se={class:"text-sm text-gray-600 mb-3 line-clamp-2"},He={class:"flex items-center justify-between text-sm"},Be={class:"flex items-center text-gray-500"},Le={key:1,class:"p-6 text-center"},qe={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Fe={class:"bg-white shadow rounded-lg"},Ge={class:"px-6 py-4 border-b border-gray-200"},Oe={class:"flex items-center justify-between"},Ue={class:"p-6"},Je={key:0,class:"space-y-3"},Ke={class:"flex items-center space-x-3"},Qe={class:"flex-shrink-0"},We={class:"text-sm font-medium text-gray-900"},Xe={class:"text-xs text-gray-500"},Ye={class:"text-right"},Ze={class:"text-sm font-bold text-green-600"},$e={key:0,class:"text-xs text-gray-500"},et={key:1,class:"text-center py-4"},tt={class:"bg-white shadow rounded-lg"},st={class:"px-6 py-4 border-b border-gray-200"},ot={class:"flex items-center justify-between"},at={class:"p-6"},it={key:0,class:"space-y-3"},nt=["onClick"],rt={class:"flex items-start justify-between"},lt={class:"flex-1"},dt={class:"text-sm font-medium text-gray-900"},ct={class:"text-xs text-gray-600 mt-1 line-clamp-2"},mt={class:"flex items-center mt-2 space-x-3"},gt={class:"text-xs bg-primary-100 text-primary-800 px-2 py-1 rounded-full"},xt={class:"text-xs text-gray-500"},_t=["disabled","onClick"],pt={key:1,class:"text-center py-4"},ut={key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},yt={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},ft={class:"mt-3"},vt={class:"flex items-center justify-between mb-4"},bt={class:"mb-4"},ht={class:"font-medium text-gray-900"},wt={class:"text-sm text-gray-600 mt-1"},kt={class:"mt-3 p-3 bg-gray-50 rounded-lg"},Pt={class:"flex justify-between text-sm"},Rt={class:"font-medium"},Ct={class:"flex justify-between text-sm"},Dt={class:"font-medium"},At={class:"flex justify-between text-sm border-t border-gray-200 pt-2 mt-2"},jt={class:"font-medium"},zt={class:"mb-4"},Nt={class:"flex space-x-3"},Mt=["disabled"],It={key:0},Et={key:1},Vt={key:2,class:"fixed inset-0 bg-gray-50 bg-opacity-75 flex items-center justify-center z-50"};function Tt(E,t,C,s,V,T){var v,m,_,g,p,b,h,f,w,k,P,R,i,c,D;const d=H("HeroIcon"),y=H("router-link");return r(),n("div",Y,[t[31]||(t[31]=e("div",{class:"mb-6"},[e("h1",{class:"text-2xl font-bold text-gray-900"},"Dashboard Engagement"),e("p",{class:"mt-1 text-sm text-gray-600"}," Il tuo percorso di engagement e gamification aziendale ")],-1)),s.loading?u("",!0):(r(),n("div",Z,[e("div",$,[e("div",ee,[e("div",te,[e("div",se,[l(d,{name:"star",class:"w-8 h-8"})]),e("div",null,[e("h2",oe,a(((v=s.userProfile)==null?void 0:v.total_points)||0)+" Punti",1),e("p",ae,a(((m=s.userProfile)==null?void 0:m.available_points)||0)+" disponibili",1)])]),e("div",ie,[t[4]||(t[4]=e("div",{class:"text-sm text-primary-100"},"Livello Attuale",-1)),e("div",ne,[l(d,{name:((g=(_=s.userProfile)==null?void 0:_.current_level)==null?void 0:g.icon_name)||"user",class:"w-5 h-5"},null,8,["name"]),e("span",re,a(((b=(p=s.userProfile)==null?void 0:p.current_level)==null?void 0:b.name)||"Novizio"),1)])])]),(h=s.userProfile)!=null&&h.next_level?(r(),n("div",le,[e("div",de,[e("span",null,"Progresso verso "+a(s.userProfile.next_level.name),1),e("span",null,a(s.userProfile.points_to_next_level)+" punti mancanti",1)]),e("div",ce,[e("div",{class:"bg-white h-2 rounded-full transition-all duration-300",style:q({width:`${s.userProfile.level_progress_percentage}%`})},null,4)])])):u("",!0)]),e("div",me,[e("div",ge,[e("div",xe,[l(d,{name:"fire",class:"w-8 h-8 text-orange-500"}),e("div",_e,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-500"},"Streak",-1)),e("p",pe,a(((f=s.userProfile)==null?void 0:f.streak_days)||0),1),t[6]||(t[6]=e("p",{class:"text-xs text-gray-500"},"giorni",-1))])])]),e("div",ue,[e("div",ye,[l(d,{name:"calendar-days",class:"w-8 h-8 text-blue-500"}),e("div",fe,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-500"},"Attività Settimana",-1)),e("p",ve,a(((w=s.weekActivity)==null?void 0:w.total_points)||0),1),t[8]||(t[8]=e("p",{class:"text-xs text-gray-500"},"punti",-1))])])]),e("div",be,[e("div",he,[l(d,{name:"trophy",class:"w-8 h-8 text-yellow-500"}),e("div",we,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-500"},"Posizione",-1)),e("p",ke,a(((k=s.leaderboardPosition)==null?void 0:k.position)||"N/A"),1),t[10]||(t[10]=e("p",{class:"text-xs text-gray-500"},"classifica",-1))])])]),e("div",Pe,[e("div",Re,[l(d,{name:"gift",class:"w-8 h-8 text-purple-500"}),e("div",Ce,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-500"},"Premi Disponibili",-1)),e("p",De,a(((P=s.availableRewards)==null?void 0:P.length)||0),1),t[12]||(t[12]=e("p",{class:"text-xs text-gray-500"},"riscattabili",-1))])])])]),e("div",Ae,[e("div",je,[e("div",ze,[t[14]||(t[14]=e("h3",{class:"text-lg font-medium text-gray-900"},"Campagne Attive",-1)),l(y,{to:"/engagement/campaigns",class:"text-sm text-primary-600 hover:text-primary-900 font-medium"},{default:j(()=>t[13]||(t[13]=[I(" Vedi tutte ")])),_:1,__:[13]})])]),s.activeCampaigns.length>0?(r(),n("div",Ne,[e("div",Me,[(r(!0),n(z,null,N(s.activeCampaigns,o=>(r(),n("div",{key:o.id,class:"border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors cursor-pointer",onClick:A=>s.goToCampaignDetail(o.id)},[e("div",Ee,[e("h4",Ve,a(o.name),1),o.points_multiplier>1?(r(),n("span",Te,a(o.points_multiplier)+"x ",1)):u("",!0)]),e("p",Se,a(o.description),1),e("div",He,[e("div",Be,[l(d,{name:"clock",class:"w-4 h-4 mr-1"}),e("span",null,a(o.days_remaining)+" giorni",1)]),t[15]||(t[15]=e("span",{class:"bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium"}," Attiva ",-1))])],8,Ie))),128))])])):(r(),n("div",Le,[l(d,{name:"megaphone",class:"mx-auto h-12 w-12 text-gray-400"}),t[16]||(t[16]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Nessuna campagna attiva",-1)),t[17]||(t[17]=e("p",{class:"mt-1 text-sm text-gray-500"}," Al momento non ci sono campagne di engagement attive. ",-1))]))]),e("div",qe,[e("div",Fe,[e("div",Ge,[e("div",Oe,[t[19]||(t[19]=e("h3",{class:"text-lg font-medium text-gray-900"},"Punti Recenti",-1)),l(y,{to:"/engagement/points",class:"text-sm text-primary-600 hover:text-primary-900 font-medium"},{default:j(()=>t[18]||(t[18]=[I(" Vedi storico ")])),_:1,__:[18]})])]),e("div",Ue,[s.recentPoints.length>0?(r(),n("div",Je,[(r(!0),n(z,null,N(s.recentPoints,o=>(r(),n("div",{key:o.id,class:"flex items-center justify-between py-2"},[e("div",Ke,[e("div",Qe,[l(d,{name:s.getActionIcon(o.action_type),class:"w-5 h-5 text-gray-400"},null,8,["name"])]),e("div",null,[e("p",We,a(s.getActionDescription(o)),1),e("p",Xe,a(s.formatDateTime(o.earned_at)),1)])]),e("div",Ye,[e("span",Ze,"+"+a(o.points_earned),1),o.campaign_name?(r(),n("p",$e,a(o.campaign_name),1)):u("",!0)])]))),128))])):(r(),n("div",et,[l(d,{name:"star",class:"mx-auto h-8 w-8 text-gray-400"}),t[20]||(t[20]=e("p",{class:"mt-2 text-sm text-gray-500"},"Nessun punto recente",-1))]))])]),e("div",tt,[e("div",st,[e("div",ot,[t[22]||(t[22]=e("h3",{class:"text-lg font-medium text-gray-900"},"Premi Disponibili",-1)),l(y,{to:"/engagement/rewards",class:"text-sm text-primary-600 hover:text-primary-900 font-medium"},{default:j(()=>t[21]||(t[21]=[I(" Vedi catalogo ")])),_:1,__:[21]})])]),e("div",at,[s.availableRewards.length>0?(r(),n("div",it,[(r(!0),n(z,null,N(s.availableRewards,o=>{var A,S;return r(),n("div",{key:o.id,class:"border border-gray-200 rounded-lg p-3 hover:border-gray-300 transition-colors cursor-pointer",onClick:L=>s.openRewardModal(o)},[e("div",rt,[e("div",lt,[e("h4",dt,a(o.name),1),e("p",ct,a(o.description),1),e("div",mt,[e("span",gt,a(o.points_cost)+" punti ",1),e("span",xt,a(o.reward_type),1)])]),e("button",{disabled:((A=s.userProfile)==null?void 0:A.available_points)<o.points_cost,class:B(["ml-3 px-3 py-1 rounded text-xs font-medium",((S=s.userProfile)==null?void 0:S.available_points)>=o.points_cost?"bg-primary-600 text-white hover:bg-primary-700":"bg-gray-100 text-gray-400 cursor-not-allowed"]),onClick:J(L=>s.openRewardModal(o),["stop"])}," Riscatta ",10,_t)])],8,nt)}),128))])):(r(),n("div",pt,[l(d,{name:"gift",class:"mx-auto h-8 w-8 text-gray-400"}),t[23]||(t[23]=e("p",{class:"mt-2 text-sm text-gray-500"},"Nessun premio disponibile",-1)),t[24]||(t[24]=e("p",{class:"text-xs text-gray-400"},"Guadagna più punti per sbloccare premi!",-1))]))])])])])),s.selectedReward?(r(),n("div",ut,[e("div",yt,[e("div",ft,[e("div",vt,[t[25]||(t[25]=e("h3",{class:"text-lg font-medium text-gray-900"},"Riscatta Premio",-1)),e("button",{onClick:t[0]||(t[0]=(...o)=>s.closeRewardModal&&s.closeRewardModal(...o)),class:"text-gray-400 hover:text-gray-600"},[l(d,{name:"x-mark",class:"w-5 h-5"})])]),e("div",bt,[e("h4",ht,a(s.selectedReward.name),1),e("p",wt,a(s.selectedReward.description),1),e("div",kt,[e("div",Pt,[t[26]||(t[26]=e("span",null,"Costo:",-1)),e("span",Rt,a(s.selectedReward.points_cost)+" punti",1)]),e("div",Ct,[t[27]||(t[27]=e("span",null,"Punti disponibili:",-1)),e("span",Dt,a(((R=s.userProfile)==null?void 0:R.available_points)||0)+" punti",1)]),e("div",At,[t[28]||(t[28]=e("span",null,"Punti dopo riscatto:",-1)),e("span",jt,a((((i=s.userProfile)==null?void 0:i.available_points)||0)-s.selectedReward.points_cost)+" punti",1)])])]),e("div",zt,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Note (opzionale) ",-1)),F(e("textarea",{"onUpdate:modelValue":t[1]||(t[1]=o=>s.redemptionNotes=o),rows:"3",class:"block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Aggiungi note per il riscatto..."},null,512),[[G,s.redemptionNotes]])]),e("div",Nt,[e("button",{onClick:t[2]||(t[2]=(...o)=>s.redeemReward&&s.redeemReward(...o)),disabled:s.redeeming||((c=s.userProfile)==null?void 0:c.available_points)<s.selectedReward.points_cost,class:B(["flex-1 px-4 py-2 rounded-md text-sm font-medium",s.redeeming||((D=s.userProfile)==null?void 0:D.available_points)<s.selectedReward.points_cost?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-primary-600 text-white hover:bg-primary-700"])},[s.redeeming?(r(),n("span",It,"Riscattando...")):(r(),n("span",Et,"Conferma Riscatto"))],10,Mt),e("button",{onClick:t[3]||(t[3]=(...o)=>s.closeRewardModal&&s.closeRewardModal(...o)),class:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"}," Annulla ")])])])])):u("",!0),s.loading?(r(),n("div",Vt,t[30]||(t[30]=[e("div",{class:"bg-white p-6 rounded-lg shadow-lg flex items-center space-x-3"},[e("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"}),e("span",{class:"text-gray-900"},"Caricamento dashboard...")],-1)]))):u("",!0)])}const Lt=Q(X,[["render",Tt],["__scopeId","data-v-2251ebdd"]]);export{Lt as default};
