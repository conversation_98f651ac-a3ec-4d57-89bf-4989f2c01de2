import{r as f,x as q,b as n,j as t,e as i,k as v,v as a,B as b,H as k,I as J,F as U,p as H,t as d,l as y,o,s as _,n as K,h as A}from"./vendor.js";import{l as Q,H as u,d as W}from"./app.js";import{u as X}from"./useFormatters.js";import{S as h}from"./StandardButton.js";import{P as Y}from"./Pagination.js";import{P as Z,C as ee}from"./ClaudePromptModal.js";import"./formatters.js";import"./BaseModal.js";import"./MarkdownContent.js";const te={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},re={class:"mt-4 md:mt-0 flex space-x-3"},ae={class:"bg-white dark:bg-gray-800 shadow rounded-lg mb-6"},se={class:"p-6 border-b border-gray-200 dark:border-gray-700"},oe={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ne=["value"],ie={key:0,class:"flex justify-center py-12"},le={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6"},de={class:"flex items-center"},ue={class:"text-red-600 dark:text-red-300 mt-1"},ge={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg"},me={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},ce={class:"text-lg font-medium text-gray-900 dark:text-white"},ye={key:0,class:"text-center py-12"},pe={key:1,class:"divide-y divide-gray-200 dark:divide-gray-700"},xe={class:"flex items-start justify-between"},fe={class:"flex-1"},ve={class:"flex items-center space-x-3 mb-2"},be={class:"text-sm font-medium text-gray-900 dark:text-white"},ke={key:0,class:"px-2 py-1 text-xs font-medium rounded bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"},_e={class:"text-gray-700 dark:text-gray-300 mb-3 line-clamp-2"},he={class:"flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400"},we={class:"flex items-center space-x-1"},Pe={class:"flex items-center space-x-1"},Ce={key:0,class:"flex items-center space-x-1"},ze={key:1,class:"flex items-center space-x-1"},Ae={class:"flex items-center space-x-2 ml-4"},Se={key:2,class:"px-6 py-4 border-t border-gray-200 dark:border-gray-700"},$e={__name:"SelfHealingPatterns",setup(Ee){const m=Q(),{formatTimeAgo:$}=X(),{showToast:g}=W(),l=f({severity:"",error_type:"",has_analysis:"",auto_healable:""}),p=f(!1),w=f(!1),P=f(null),{errorPatterns:S,patternsPagination:c,filterOptions:j,loading:E,error:V,analyzingPattern:T,generatingPrompt:M,currentAnalysis:G,currentPrompt:L}=m;function O(s){return{critical:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",high:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"}[s]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}function C(s,e="Non disponibile"){return s==null||s===""||s==="Unknown"?e:s}async function z(){try{await m.fetchErrorPatterns(l.value)}catch{g("Errore nel caricamento dei pattern","error")}}async function x(){await z()}async function D(s){const e={...l.value,page:s};try{await m.fetchErrorPatterns(e)}catch{g("Errore nel cambio pagina","error")}}async function N(s){var e;try{await m.analyzePattern(s),g("Analisi AI completata","success"),((e=P.value)==null?void 0:e.id)===s&&(p.value=!0)}catch{g("Errore nell'analisi AI","error")}}function R(s){P.value=s,p.value=!0}async function B(s){try{await m.generateClaudePrompt(s),w.value=!0,g("Prompt Claude generato","success")}catch{g("Errore nella generazione del prompt","error")}}return q(()=>{z()}),(s,e)=>(o(),n("div",null,[t("div",te,[e[7]||(e[7]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"🔍 Error Patterns"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Analisi pattern errori rilevati dal sistema self-healing ")],-1)),t("div",re,[i(h,{variant:"outline",icon:"arrow-path",loading:a(E),onClick:z},{default:v(()=>e[6]||(e[6]=[_(" Aggiorna ")])),_:1,__:[6]},8,["loading"])])]),t("div",ae,[t("div",se,[e[16]||(e[16]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Filtri",-1)),t("div",oe,[t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Gravità ",-1)),b(t("select",{"onUpdate:modelValue":e[0]||(e[0]=r=>l.value.severity=r),onChange:x,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[8]||(e[8]=[J('<option value="">Tutte</option><option value="critical">Critica</option><option value="high">Alta</option><option value="medium">Media</option><option value="low">Bassa</option>',5)]),544),[[k,l.value.severity]])]),t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tipo Errore ",-1)),b(t("select",{"onUpdate:modelValue":e[1]||(e[1]=r=>l.value.error_type=r),onChange:x,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[e[10]||(e[10]=t("option",{value:""},"Tutti",-1)),(o(!0),n(U,null,H(a(j).error_types,r=>(o(),n("option",{key:r,value:r},d(r),9,ne))),128))],544),[[k,l.value.error_type]])]),t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Analisi AI ",-1)),b(t("select",{"onUpdate:modelValue":e[2]||(e[2]=r=>l.value.has_analysis=r),onChange:x,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[12]||(e[12]=[t("option",{value:""},"Tutti",-1),t("option",{value:"true"},"Con Analisi",-1),t("option",{value:"false"},"Senza Analisi",-1)]),544),[[k,l.value.has_analysis]])]),t("div",null,[e[15]||(e[15]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Auto-Risolvibili ",-1)),b(t("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>l.value.auto_healable=r),onChange:x,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[14]||(e[14]=[t("option",{value:""},"Tutti",-1),t("option",{value:"true"},"Sì",-1),t("option",{value:"false"},"No",-1)]),544),[[k,l.value.auto_healable]])])])])]),a(E)?(o(),n("div",ie,[i(u,{name:"arrow-path",size:"lg",class:"animate-spin text-primary-600"})])):a(V)?(o(),n("div",le,[t("div",de,[i(u,{name:"exclamation-triangle",size:"md",class:"text-red-500 mr-3"}),t("div",null,[e[17]||(e[17]=t("h3",{class:"text-lg font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento",-1)),t("p",ue,d(a(V)),1)])])])):(o(),n("div",ge,[t("div",me,[t("h3",ce," Pattern Errori ("+d(a(c).total||0)+") ",1)]),a(S).length===0?(o(),n("div",ye,[i(u,{name:"document-magnifying-glass",size:"xl",class:"text-gray-400 mx-auto mb-4"}),e[18]||(e[18]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun pattern trovato",-1)),e[19]||(e[19]=t("p",{class:"text-gray-600 dark:text-gray-400"}," Non ci sono pattern errori che corrispondono ai filtri selezionati. ",-1))])):(o(),n("div",pe,[(o(!0),n(U,null,H(a(S),r=>{var F;return o(),n("div",{key:r.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[t("div",xe,[t("div",fe,[t("div",ve,[t("span",be,d(C(r.error_type,"Tipo errore sconosciuto")),1),t("span",{class:K(["px-2 py-1 text-xs font-medium rounded",O(r.severity)])},d((F=r.severity)==null?void 0:F.toUpperCase()),3),r.is_auto_healable?(o(),n("span",ke," AUTO-RISOLVIBILE ")):y("",!0)]),t("p",_e,d(C(r.message_pattern,"Messaggio non disponibile")),1),t("div",he,[t("div",we,[i(u,{name:"chart-bar",size:"sm"}),t("span",null,d(r.occurrence_count)+" occorrenze",1)]),t("div",Pe,[i(u,{name:"clock",size:"sm"}),t("span",null,d(a($)(r.last_seen)),1)]),r.file_pattern?(o(),n("div",Ce,[i(u,{name:"document",size:"sm"}),t("span",null,d(C(r.file_pattern,"File sconosciuto")),1)])):y("",!0),r.healing_success_rate?(o(),n("div",ze,[i(u,{name:"check-circle",size:"sm"}),t("span",null,d(Math.round(r.healing_success_rate*100))+"% successo",1)])):y("",!0)])]),t("div",Ae,[r.has_ai_analysis?(o(),A(h,{key:1,variant:"outline",size:"sm",icon:"magnifying-glass",onClick:I=>R(r)},{default:v(()=>e[21]||(e[21]=[_(" Vedi Analisi ")])),_:2,__:[21]},1032,["onClick"])):(o(),A(h,{key:0,variant:"outline",size:"sm",icon:"cpu-chip",loading:a(T)===r.id,onClick:I=>N(r.id)},{default:v(()=>e[20]||(e[20]=[_(" Analizza ")])),_:2,__:[20]},1032,["loading","onClick"])),r.has_ai_analysis?(o(),A(h,{key:2,variant:"primary",size:"sm",icon:"document-text",loading:a(M)===r.id,onClick:I=>B(r.id)},{default:v(()=>e[22]||(e[22]=[_(" Genera Prompt ")])),_:2,__:[22]},1032,["loading","onClick"])):y("",!0)])])])}),128))])),a(c).pages>1?(o(),n("div",Se,[i(Y,{current:a(c).page,total:a(c).pages,"per-page":a(c).per_page,onChange:D},null,8,["current","total","per-page"])])):y("",!0)])),i(Z,{show:p.value,pattern:P.value,analysis:a(G),"analyzing-pattern":a(T),"generating-prompt":a(M),onClose:e[4]||(e[4]=r=>p.value=!1),onAnalyze:N,onGeneratePrompt:B},null,8,["show","pattern","analysis","analyzing-pattern","generating-prompt"]),i(ee,{show:w.value,"prompt-data":a(L),onClose:e[5]||(e[5]=r=>w.value=!1)},null,8,["show","prompt-data"])]))}};export{$e as default};
