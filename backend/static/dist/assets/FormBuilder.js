import{c as q,b as a,o as r,j as d,h as V,l as n,F as p,p as h,s as y,t as s,K as x,e as S,A,q as B}from"./vendor.js";import{_ as F,H as L}from"./app.js";import{_ as E}from"./AlertsSection.js";/* empty css                                                           */const f={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},C={key:0},N=["for"],R={key:0,class:"text-red-500"},j=["id","type","required","placeholder","min","step","value","onInput"],I={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},$={key:1},z=["for"],H={key:0,class:"text-red-500"},O=["id","required","placeholder","rows","value","onInput"],D={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},K={key:2},M=["for"],T={key:0,class:"text-red-500"},G=["id","required","value","onChange"],J={value:""},P=["value"],Q={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},U={key:3},W={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700"},X=["disabled"],Y={key:0,class:"flex items-center"},Z={key:1},ee={__name:"FormBuilder",props:{fields:{type:Array,required:!0,validator:t=>t.every(u=>u.id&&u.label&&u.type&&["text","email","number","textarea","select","custom"].includes(u.type))},modelValue:{type:Object,required:!0},errors:{type:Object,default:()=>({})},globalError:{type:String,default:null},loading:{type:Boolean,default:!1},submitLabel:{type:String,default:"Salva"},loadingLabel:{type:String,default:"Salvataggio..."},cancelLabel:{type:String,default:"Annulla"},cancelRoute:{type:String,default:null},cancelAction:{type:Function,default:null}},emits:["update:modelValue","submit","cancel"],setup(t,{emit:u}){const k=B(),l=t,b=u,v=q(()=>l.globalError?[{id:"form-error",type:"error",title:"Errore nel salvataggio",message:l.globalError}]:[]),i=(c,m)=>{const e={...l.modelValue},o=l.fields.find(_=>_.id===c);o&&o.type==="number"?e[c]=m===""?null:Number(m):e[c]=m,b("update:modelValue",e)},g=()=>{b("submit")},w=()=>{l.cancelAction?l.cancelAction():l.cancelRoute?k.push(l.cancelRoute):b("cancel")};return(c,m)=>(r(),a("div",f,[d("form",{onSubmit:A(g,["prevent"]),class:"p-6 space-y-6"},[(r(!0),a(p,null,h(t.fields,e=>(r(),a("div",{key:e.id,class:"space-y-2"},[e.type==="text"||e.type==="email"||e.type==="number"?(r(),a("div",C,[d("label",{for:e.id,class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[y(s(e.label)+" ",1),e.required?(r(),a("span",R,"*")):n("",!0)],8,N),d("input",{id:e.id,type:e.type,required:e.required,placeholder:e.placeholder,min:e.min,step:e.step,value:t.modelValue[e.id],onInput:o=>i(e.id,o.target.value),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,40,j),t.errors[e.id]?(r(),a("p",I,s(t.errors[e.id]),1)):n("",!0)])):e.type==="textarea"?(r(),a("div",$,[d("label",{for:e.id,class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[y(s(e.label)+" ",1),e.required?(r(),a("span",H,"*")):n("",!0)],8,z),d("textarea",{id:e.id,required:e.required,placeholder:e.placeholder,rows:e.rows||3,value:t.modelValue[e.id],onInput:o=>i(e.id,o.target.value),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,40,O),t.errors[e.id]?(r(),a("p",D,s(t.errors[e.id]),1)):n("",!0)])):e.type==="select"?(r(),a("div",K,[d("label",{for:e.id,class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[y(s(e.label)+" ",1),e.required?(r(),a("span",T,"*")):n("",!0)],8,M),d("select",{id:e.id,required:e.required,value:t.modelValue[e.id],onChange:o=>i(e.id,o.target.value),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},[d("option",J,s(e.placeholder||"Seleziona opzione"),1),(r(!0),a(p,null,h(e.options,o=>(r(),a("option",{key:o.value,value:o.value},s(o.label),9,P))),128))],40,G),t.errors[e.id]?(r(),a("p",Q,s(t.errors[e.id]),1)):n("",!0)])):e.type==="custom"?(r(),a("div",U,[x(c.$slots,`field-${e.id}`,{field:e,value:t.modelValue[e.id],update:o=>i(e.id,o),error:t.errors[e.id]},void 0,!0)])):n("",!0)]))),128)),t.globalError?(r(),V(E,{key:0,alerts:v.value},null,8,["alerts"])):n("",!0),d("div",W,[x(c.$slots,"actions",{loading:t.loading,submit:g},()=>[t.cancelRoute||t.cancelAction?(r(),a("button",{key:0,type:"button",onClick:w,class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},s(t.cancelLabel),1)):n("",!0),d("button",{type:"submit",disabled:t.loading,class:"px-4 py-2 bg-brand-primary-600 hover:bg-brand-primary-700 disabled:bg-brand-primary-400 text-white rounded-md text-sm font-medium transition-colors duration-200"},[t.loading?(r(),a("span",Y,[S(L,{name:"cog-6-tooth",class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white"}),y(" "+s(t.loadingLabel),1)])):(r(),a("span",Z,s(t.submitLabel),1))],8,X)],!0)])],32)]))}},se=F(ee,[["__scopeId","data-v-cbe7292c"]]);export{se as F};
