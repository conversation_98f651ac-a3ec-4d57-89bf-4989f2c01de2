import{r as k,c as w,u as de,x as ce,b as n,e as d,k as j,j as s,l as i,t as r,s as g,n as z,F as y,p as x,q as me,o as a,h as F}from"./vendor.js";import{u as ge}from"./funding.js";import{_ as pe,i as _e,H as c,d as ve}from"./app.js";import{_ as fe}from"./PageHeader.js";import{S as ye}from"./StatusBadge.js";import{S as N}from"./StandardButton.js";const xe={class:"funding-opportunity-view"},be={class:"flex gap-3"},he={key:0,class:"flex justify-center py-12"},ke={key:1,class:"max-w-7xl mx-auto space-y-6"},we={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},ze={class:"flex items-start justify-between"},Se={class:"flex-1"},De={class:"flex items-center gap-3 mb-2"},Ie={class:"text-3xl font-bold text-gray-900 dark:text-white"},Ce={key:0,class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-secondary-100 dark:bg-secondary-900/50 text-secondary-800 dark:text-secondary-200"},Ae={class:"flex items-center gap-6 text-sm text-gray-600 dark:text-gray-400 mb-4"},Te={class:"flex items-center gap-2"},$e={key:0,class:"flex items-center gap-2"},je={key:1,class:"flex items-center gap-2"},Oe={key:0,class:"text-right"},qe={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Be={class:"lg:col-span-2 space-y-6"},Ee={class:"bg-white rounded-lg shadow-sm border p-6"},Fe=["innerHTML"],Ne={key:0,class:"bg-white rounded-lg shadow-sm border p-6"},Me={class:"space-y-2"},Re={class:"text-gray-700"},Le={key:1,class:"bg-white rounded-lg shadow-sm border p-6"},Pe={class:"space-y-2"},Ve={class:"text-gray-700"},Ge={key:2,class:"bg-white rounded-lg shadow-sm border p-6"},He={class:"space-y-3"},Je={class:"flex-1"},Ue={class:"flex items-center justify-between"},We={class:"text-gray-800 font-medium"},Ke={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},Qe={key:0,class:"text-sm text-gray-600 mt-1"},Xe={class:"space-y-6"},Ye={class:"bg-white rounded-lg shadow-sm border p-6"},Ze={class:"space-y-4"},et={key:0},tt={class:"flex items-center text-sm font-medium text-gray-500 mb-1"},st={key:0,class:"text-sm text-gray-600"},at={key:1},ot={class:"flex items-center text-sm font-medium text-gray-500 mb-1"},nt={class:"text-lg font-semibold text-green-600"},it={key:0,class:"text-sm text-gray-600"},lt={key:2},rt={class:"flex items-center text-sm font-medium text-gray-500 mb-1"},ut={class:"text-lg font-semibold text-blue-600"},dt={key:3},ct={class:"flex items-center text-sm font-medium text-gray-500 mb-1"},mt={class:"text-lg font-semibold text-gray-900"},gt={key:4},pt={class:"flex items-center text-sm font-medium text-gray-500 mb-1"},_t={class:"text-lg font-semibold text-gray-900"},vt={key:0,class:"bg-white rounded-lg shadow-sm border p-6"},ft={class:"flex flex-wrap gap-2"},yt={key:1,class:"bg-white rounded-lg shadow-sm border p-6"},xt={class:"space-y-2"},bt={key:0},ht={class:"text-sm text-gray-900"},kt=["href"],wt={key:1},zt={class:"text-sm text-gray-900"},St={key:2},Dt={class:"text-sm text-gray-900"},It=["href"],Ct={key:2,class:"bg-purple-50 rounded-lg border border-purple-200 p-6"},At={class:"text-lg font-semibold text-purple-900 mb-4 flex items-center gap-2"},Tt={class:"space-y-6"},$t={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},jt={key:0,class:"text-center"},Ot={key:1,class:"text-center"},qt={key:2,class:"text-center"},Bt={key:3,class:"text-center"},Et={key:0,class:"bg-white rounded-lg p-4 border border-purple-200"},Ft={class:"text-purple-700 font-semibold"},Nt={key:1,class:"bg-white rounded-lg p-4 border border-purple-200"},Mt={class:"text-purple-700 text-sm leading-relaxed"},Rt={key:2,class:"bg-white rounded-lg p-4 border border-purple-200"},Lt={class:"list-disc list-inside text-purple-700 space-y-1 text-sm"},Pt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Vt={key:0,class:"bg-green-50 rounded-lg p-4 border border-green-200"},Gt={class:"font-medium text-green-800 mb-2 flex items-center gap-2"},Ht={class:"list-disc list-inside text-green-700 space-y-1 text-sm"},Jt={key:1,class:"bg-orange-50 rounded-lg p-4 border border-orange-200"},Ut={class:"font-medium text-orange-800 mb-2 flex items-center gap-2"},Wt={class:"list-disc list-inside text-orange-700 space-y-1 text-sm"},Kt={key:3,class:"bg-blue-50 rounded-lg p-4 border border-blue-200"},Qt={class:"font-medium text-blue-800 mb-2 flex items-center gap-2"},Xt={class:"list-decimal list-inside text-blue-700 space-y-1 text-sm"},Yt={key:4,class:"text-xs text-purple-600 text-center pt-2 border-t border-purple-200"},Zt={key:3,class:"bg-white rounded-lg shadow-sm border p-6"},es={class:"space-y-3"},ts=["href"],ss=["href"],as={key:2,class:"text-center py-12"},os={class:"bg-white rounded-lg shadow-sm border p-8 max-w-md mx-auto"},ns={__name:"FundingOpportunityView",setup(is){const b=de(),U=me(),_=ge(),{showToast:h}=ve(),O=k(!1),C=k(!1),A=k(!1),S=k(null),m=k(null),l=k(null),q=w(()=>b.query.ai==="true"&&m.value),o=w(()=>m.value||S.value),W=w(()=>{var t,e;return q.value?((t=m.value)==null?void 0:t.saved)&&((e=m.value)==null?void 0:e.database_id):S.value&&S.value.status==="open"}),K=w(()=>{var t;return((t=o.value)==null?void 0:t.title)||"Caricamento..."}),Q=w(()=>{if(!o.value)return"";const t=[];return o.value.source_entity&&t.push(o.value.source_entity),o.value.program_name&&t.push(o.value.program_name),t.join(" • ")}),X=w(()=>[{name:"Bandi",href:"/app/funding"},{name:"Dettaglio Bando",href:b.path}]);function Y(t){return t?new Date(t).toLocaleDateString("it-IT",{day:"numeric",month:"long",year:"numeric"}):""}function B(t){return t?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR",minimumFractionDigits:0,maximumFractionDigits:0}).format(t):"€0"}async function Z(){const t=b.params.id;if(b.query.ai==="true")try{const e=b.query.data?JSON.parse(decodeURIComponent(b.query.data)):null;if(e){m.value=e;return}}catch(e){console.error("Errore parsing dati AI:",e)}if(t&&t!=="ai"){O.value=!0;try{S.value=await _.fetchOpportunity(t)}catch(e){console.error("Errore caricamento opportunità:",e),h("error","Errore nel caricamento del bando")}finally{O.value=!1}}}async function ee(){var t,e,p,f,D,I,u,v,L,P,V,G,H,J;if(o.value){C.value=!0;try{const $={name:((e=(t=_.tenantStore)==null?void 0:t.company)==null?void 0:e.name)||"Azienda",sector:((f=(p=_.tenantStore)==null?void 0:p.company)==null?void 0:f.sector)||"ICT",size:((I=(D=_.tenantStore)==null?void 0:D.company)==null?void 0:I.size)||"PMI",location:((v=(u=_.tenantStore)==null?void 0:u.company)==null?void 0:v.location)||((P=(L=_.tenantStore)==null?void 0:L.contact)==null?void 0:P.address)||"Italia",activities:((G=(V=_.tenantStore)==null?void 0:V.company)==null?void 0:G.description)||((J=(H=_.tenantStore)==null?void 0:H.company)==null?void 0:J.tagline)||"Digitalizzazione e innovazione"},ue=await _.calculateAIMatchScore(o.value,$);l.value=ue,h("success","Analisi AI completata")}catch($){console.error("❌ Errore analisi AI:",$),h("error","Errore nell'analisi AI: "+($.message||"Errore sconosciuto"))}finally{C.value=!1}}}async function te(){if(m.value){A.value=!0;try{const t={title:m.value.title,description:m.value.description,source_entity:m.value.source_entity||"Da verificare",max_grant_amount:m.value.max_grant_amount||1e5,contribution_percentage:m.value.contribution_percentage||50,application_deadline:m.value.deadline||new Date(Date.now()+5184e6).toISOString().split("T")[0],status:"open",geographic_scope:"nazionale",ai_generated:!0,ai_search_query:b.query.search||"",ai_match_score:m.value.match_score||0,ai_content:JSON.stringify(m.value)},e=await _.createOpportunity(t);m.value.saved=!0,m.value.database_id=e.id,h("success","Bando salvato nel database")}catch(t){console.error("Errore salvataggio:",t),h("error","Errore nel salvataggio")}finally{A.value=!1}}}function se(){var e,p;const t=((e=m.value)==null?void 0:e.database_id)||((p=S.value)==null?void 0:p.id);t?U.push(`/app/funding/applications/new/${t}`):h("warning","Salva prima il bando nel database")}function ae(t){return t?`<p class="mb-4">${t.replace(/^### (.+)$/gm,'<h3 class="text-lg font-semibold text-gray-900 mt-6 mb-3">$1</h3>').replace(/^## (.+)$/gm,'<h2 class="text-xl font-semibold text-gray-900 mt-6 mb-3">$1</h2>').replace(/^# (.+)$/gm,'<h1 class="text-2xl font-bold text-gray-900 mt-6 mb-3">$1</h1>').replace(/\*\*(.+?)\*\*/g,'<strong class="font-semibold text-gray-900">$1</strong>').replace(/\*(.+?)\*/g,'<em class="italic text-gray-700">$1</em>').replace(/\n\n/g,'</p><p class="mb-4">').replace(/\n/g,"<br>").replace(/^- (.+)$/gm,'<li class="ml-4 mb-1">• $1</li>')}</p>`:"Nessuna descrizione disponibile"}function oe(t){return{locale:"Locale",regionale:"Regionale",nazionale:"Nazionale",europeo:"Europeo",internazionale:"Internazionale"}[t]||t}function ne(t){return t>=80?"text-green-600":t>=60?"text-yellow-600":"text-red-600"}function T(t){return t>=80?"bg-green-100 text-green-800":t>=60?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}function ie(t){return t?new Date(t).toLocaleDateString("it-IT",{day:"numeric",month:"short",year:"numeric",hour:"2-digit",minute:"2-digit"}):""}function le(t){if(!t)return"text-gray-900";const e=Math.ceil((new Date(t)-new Date)/(1e3*60*60*24));return e<7?"text-red-600":e<30?"text-yellow-600":"text-green-600"}function re(t){if(typeof t=="string")return t;if(typeof t=="object"){if(t.criterion)return t.criterion;if(t.name)return t.name;if(t.criterio)return t.criterio}return typeof t=="object"?JSON.stringify(t):String(t)}function M(t){return typeof t=="object"&&(t.weight||t.peso||t.punteggio)||null}function R(t){return typeof t=="object"&&(t.description||t.descrizione||t.desc)||null}function E(t){if(typeof t=="string")return t;if(typeof t=="object"&&t!==null){const e=["name","nome","title","titolo","value","valore","text","testo","description","descrizione"];for(const f of e)if(t[f])return t[f];const p=Object.keys(t)[0];if(p)return t[p]}return t?typeof t=="object"?JSON.stringify(t):String(t):""}return ce(()=>{Z()}),(t,e)=>{var p,f,D,I;return a(),n("div",xe,[d(fe,{title:K.value,subtitle:Q.value,breadcrumbs:X.value},{actions:j(()=>{var u;return[s("div",be,[q.value?(a(),F(N,{key:0,onClick:ee,disabled:C.value,variant:"secondary",icon:"sparkles"},{default:j(()=>[g(r(C.value?"Analisi in corso...":"Analizza con AI"),1)]),_:1},8,["disabled"])):i("",!0),q.value&&!((u=m.value)!=null&&u.saved)?(a(),F(N,{key:1,onClick:te,disabled:A.value,variant:"primary",icon:"arrow-down-tray"},{default:j(()=>[g(r(A.value?"Salvataggio...":"Salva nel DB"),1)]),_:1},8,["disabled"])):i("",!0),W.value?(a(),F(N,{key:2,onClick:se,variant:"primary",icon:"document-plus"},{default:j(()=>e[1]||(e[1]=[g(" Avvia Candidatura ")])),_:1,__:[1]})):i("",!0)])]}),_:1},8,["title","subtitle","breadcrumbs"]),O.value?(a(),n("div",he,[d(_e)])):o.value?(a(),n("div",ke,[s("div",we,[s("div",ze,[s("div",Se,[s("div",De,[s("h1",Ie,r(o.value.title),1),o.value.ai_generated?(a(),n("span",Ce,[d(c,{name:"sparkles",size:"xs",class:"mr-1"}),e[2]||(e[2]=g(" AI Generated "))])):i("",!0)]),s("div",Ae,[s("span",Te,[d(c,{name:"building-office",size:"sm"}),g(" "+r(o.value.source_entity),1)]),o.value.geographic_scope?(a(),n("span",$e,[d(c,{name:"globe-alt",size:"sm"}),g(" "+r(oe(o.value.geographic_scope)),1)])):i("",!0),o.value.status?(a(),n("span",je,[d(ye,{status:o.value.status,type:"funding_opportunity"},null,8,["status"])])):i("",!0)])]),o.value.match_score?(a(),n("div",Oe,[e[3]||(e[3]=s("div",{class:"text-sm text-gray-500 dark:text-gray-400 mb-1"},"Compatibilità",-1)),s("div",{class:z(["text-3xl font-bold",ne(o.value.match_score)])},r(o.value.match_score)+"% ",3)])):i("",!0)])]),s("div",qe,[s("div",Be,[s("div",Ee,[e[4]||(e[4]=s("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Descrizione",-1)),s("div",{class:"prose prose-sm max-w-none text-gray-700",innerHTML:ae(o.value.description)},null,8,Fe)]),(p=o.value.eligibility_criteria)!=null&&p.length?(a(),n("div",Ne,[e[5]||(e[5]=s("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Criteri di Eleggibilità",-1)),s("ul",Me,[(a(!0),n(y,null,x(o.value.eligibility_criteria,(u,v)=>(a(),n("li",{key:v,class:"flex items-start gap-2"},[d(c,{name:"check-circle",size:"sm",class:"text-green-500 mt-0.5 flex-shrink-0"}),s("span",Re,r(E(u)),1)]))),128))])])):i("",!0),(f=o.value.required_documents)!=null&&f.length?(a(),n("div",Le,[e[6]||(e[6]=s("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Documenti Richiesti",-1)),s("ul",Pe,[(a(!0),n(y,null,x(o.value.required_documents,(u,v)=>(a(),n("li",{key:v,class:"flex items-start gap-2"},[d(c,{name:"document-text",size:"sm",class:"text-blue-500 mt-0.5 flex-shrink-0"}),s("span",Ve,r(E(u)),1)]))),128))])])):i("",!0),(D=o.value.evaluation_criteria)!=null&&D.length?(a(),n("div",Ge,[e[7]||(e[7]=s("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Criteri di Valutazione",-1)),s("ul",He,[(a(!0),n(y,null,x(o.value.evaluation_criteria,(u,v)=>(a(),n("li",{key:v,class:"flex items-start gap-2"},[d(c,{name:"star",size:"sm",class:"text-yellow-500 mt-1 flex-shrink-0"}),s("div",Je,[s("div",Ue,[s("span",We,r(re(u)),1),M(u)?(a(),n("span",Ke,r(M(u))+"% ",1)):i("",!0)]),R(u)?(a(),n("p",Qe,r(R(u)),1)):i("",!0)])]))),128))])])):i("",!0)]),s("div",Xe,[s("div",Ye,[e[13]||(e[13]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Informazioni Chiave",-1)),s("dl",Ze,[o.value.application_deadline?(a(),n("div",et,[s("dt",tt,[d(c,{name:"calendar-days",size:"sm",class:"mr-2"}),e[8]||(e[8]=g(" Scadenza Candidatura "))]),s("dd",{class:z(["text-lg font-semibold",le(o.value.application_deadline)])},r(Y(o.value.application_deadline)),3),o.value.days_to_deadline?(a(),n("dd",st,r(o.value.days_to_deadline)+" giorni rimanenti ",1)):i("",!0)])):i("",!0),o.value.max_grant_amount?(a(),n("div",at,[s("dt",ot,[d(c,{name:"currency-euro",size:"sm",class:"mr-2"}),e[9]||(e[9]=g(" Finanziamento Massimo "))]),s("dd",nt,r(B(o.value.max_grant_amount)),1),o.value.min_grant_amount?(a(),n("dd",it," Minimo: "+r(B(o.value.min_grant_amount)),1)):i("",!0)])):i("",!0),o.value.contribution_percentage?(a(),n("div",lt,[s("dt",rt,[d(c,{name:"calculator",size:"sm",class:"mr-2"}),e[10]||(e[10]=g(" Percentuale Finanziamento "))]),s("dd",ut,r(o.value.contribution_percentage)+"% ",1)])):i("",!0),o.value.project_duration_months?(a(),n("div",dt,[s("dt",ct,[d(c,{name:"clock",size:"sm",class:"mr-2"}),e[11]||(e[11]=g(" Durata Progetto "))]),s("dd",mt,r(o.value.project_duration_months)+" mesi ",1)])):i("",!0),o.value.total_budget?(a(),n("div",gt,[s("dt",pt,[d(c,{name:"banknotes",size:"sm",class:"mr-2"}),e[12]||(e[12]=g(" Budget Totale Bando "))]),s("dd",_t,r(B(o.value.total_budget)),1)])):i("",!0)])]),(I=o.value.target_sectors)!=null&&I.length?(a(),n("div",vt,[e[14]||(e[14]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Settori Target",-1)),s("div",ft,[(a(!0),n(y,null,x(o.value.target_sectors,(u,v)=>(a(),n("span",{key:v,class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"},r(E(u)),1))),128))])])):i("",!0),o.value.contact_info&&Object.keys(o.value.contact_info).length?(a(),n("div",yt,[e[19]||(e[19]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Contatti",-1)),s("dl",xt,[o.value.contact_info.email?(a(),n("div",bt,[e[15]||(e[15]=s("dt",{class:"text-sm font-medium text-gray-500"},"Email",-1)),s("dd",ht,[s("a",{href:`mailto:${o.value.contact_info.email}`,class:"text-blue-600 hover:text-blue-800"},r(o.value.contact_info.email),9,kt)])])):i("",!0),o.value.contact_info.phone?(a(),n("div",wt,[e[16]||(e[16]=s("dt",{class:"text-sm font-medium text-gray-500"},"Telefono",-1)),s("dd",zt,r(o.value.contact_info.phone),1)])):i("",!0),o.value.contact_info.website?(a(),n("div",St,[e[18]||(e[18]=s("dt",{class:"text-sm font-medium text-gray-500"},"Sito Web",-1)),s("dd",Dt,[s("a",{href:o.value.contact_info.website,target:"_blank",class:"text-blue-600 hover:text-blue-800"},[e[17]||(e[17]=g(" Visita il sito ")),d(c,{name:"arrow-top-right-on-square",size:"xs",class:"inline ml-1"})],8,It)])])):i("",!0)])])):i("",!0),l.value?(a(),n("div",Ct,[s("h3",At,[d(c,{name:"sparkles",size:"sm"}),e[20]||(e[20]=g(" Analisi AI Dettagliata "))]),s("div",Tt,[s("div",$t,[l.value.match_score?(a(),n("div",jt,[e[21]||(e[21]=s("div",{class:"text-sm font-medium text-purple-800 mb-1"},"Compatibilità",-1)),s("div",{class:z(["px-3 py-2 rounded-lg text-lg font-bold",T(l.value.match_score)])},r(l.value.match_score)+"% ",3)])):i("",!0),l.value.technical_compatibility?(a(),n("div",Ot,[e[22]||(e[22]=s("div",{class:"text-sm font-medium text-purple-800 mb-1"},"Tecnica",-1)),s("div",{class:z(["px-3 py-2 rounded-lg text-lg font-bold",T(l.value.technical_compatibility)])},r(l.value.technical_compatibility)+"% ",3)])):i("",!0),l.value.financial_feasibility?(a(),n("div",qt,[e[23]||(e[23]=s("div",{class:"text-sm font-medium text-purple-800 mb-1"},"Finanziaria",-1)),s("div",{class:z(["px-3 py-2 rounded-lg text-lg font-bold",T(l.value.financial_feasibility)])},r(l.value.financial_feasibility)+"% ",3)])):i("",!0),l.value.success_probability?(a(),n("div",Bt,[e[24]||(e[24]=s("div",{class:"text-sm font-medium text-purple-800 mb-1"},"Successo",-1)),s("div",{class:z(["px-3 py-2 rounded-lg text-lg font-bold",T(l.value.success_probability)])},r(l.value.success_probability)+"% ",3)])):i("",!0)]),l.value.recommendation?(a(),n("div",Et,[e[25]||(e[25]=s("div",{class:"font-medium text-purple-800 mb-2"},"Raccomandazione:",-1)),s("div",Ft,r(l.value.recommendation),1)])):i("",!0),l.value.detailed_analysis?(a(),n("div",Nt,[e[26]||(e[26]=s("div",{class:"font-medium text-purple-800 mb-2"},"Analisi Dettagliata:",-1)),s("div",Mt,r(l.value.detailed_analysis),1)])):i("",!0),l.value.insights&&l.value.insights.length>0?(a(),n("div",Rt,[e[27]||(e[27]=s("div",{class:"font-medium text-purple-800 mb-2"},"Insights Chiave:",-1)),s("ul",Lt,[(a(!0),n(y,null,x(l.value.insights,u=>(a(),n("li",{key:u},r(u),1))),128))])])):i("",!0),s("div",Pt,[l.value.strengths&&l.value.strengths.length>0?(a(),n("div",Vt,[s("div",Gt,[d(c,{name:"check-circle",size:"sm"}),e[28]||(e[28]=g(" Punti di Forza "))]),s("ul",Ht,[(a(!0),n(y,null,x(l.value.strengths,u=>(a(),n("li",{key:u},r(u),1))),128))])])):i("",!0),l.value.weaknesses&&l.value.weaknesses.length>0?(a(),n("div",Jt,[s("div",Ut,[d(c,{name:"exclamation-triangle",size:"sm"}),e[29]||(e[29]=g(" Aree di Miglioramento "))]),s("ul",Wt,[(a(!0),n(y,null,x(l.value.weaknesses,u=>(a(),n("li",{key:u},r(u),1))),128))])])):i("",!0)]),l.value.next_steps&&l.value.next_steps.length>0?(a(),n("div",Kt,[s("div",Qt,[d(c,{name:"arrow-right",size:"sm"}),e[30]||(e[30]=g(" Prossimi Passi "))]),s("ol",Xt,[(a(!0),n(y,null,x(l.value.next_steps,u=>(a(),n("li",{key:u},r(u),1))),128))])])):i("",!0),l.value.analysis_timestamp?(a(),n("div",Yt," Analisi del "+r(ie(l.value.analysis_timestamp)),1)):i("",!0)])])):i("",!0),o.value.official_url||o.value.guidelines_url?(a(),n("div",Zt,[e[33]||(e[33]=s("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Link Utili",-1)),s("div",es,[o.value.official_url?(a(),n("a",{key:0,href:o.value.official_url,target:"_blank",class:"flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"},[d(c,{name:"globe-alt",size:"sm"}),e[31]||(e[31]=s("span",null,"Pagina Ufficiale",-1)),d(c,{name:"arrow-top-right-on-square",size:"xs"})],8,ts)):i("",!0),o.value.guidelines_url?(a(),n("a",{key:1,href:o.value.guidelines_url,target:"_blank",class:"flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"},[d(c,{name:"document-text",size:"sm"}),e[32]||(e[32]=s("span",null,"Linee Guida",-1)),d(c,{name:"arrow-top-right-on-square",size:"xs"})],8,ss)):i("",!0)])])):i("",!0)])])])):(a(),n("div",as,[s("div",os,[d(c,{name:"exclamation-triangle",size:"lg",class:"mx-auto text-gray-400 mb-4"}),e[35]||(e[35]=s("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Bando non trovato",-1)),e[36]||(e[36]=s("p",{class:"text-gray-600 mb-6"},"Non sono stati trovati dati per questo bando",-1)),s("button",{onClick:e[0]||(e[0]=u=>t.$router.go(-1)),class:"btn-secondary"},[d(c,{name:"arrow-left",size:"sm",class:"mr-2"}),e[34]||(e[34]=g(" Torna indietro "))])])]))])}}},gs=pe(ns,[["__scopeId","data-v-74fc138b"]]);export{gs as default};
