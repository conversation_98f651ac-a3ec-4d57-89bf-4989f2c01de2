import{r as u,c as y,x as j,b as L,e as n,j as r,k as o,q as G,f as H,o as w,s as C,B as M,C as R,h as U,l as q,n as O,t as d}from"./vendor.js";import{c as b,d as J,H as g}from"./app.js";import{A as K}from"./ActionButtonGroup.js";import{_ as Q}from"./PageHeader.js";import{_ as W}from"./FilterBar.js";import{_ as X}from"./StatsGrid.js";import{_ as Y}from"./DataTable.js";import{P as Z}from"./Pagination.js";import"./StandardButton.js";import"./formatters.js";const ee={class:"py-6"},te={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},ae={class:"min-w-[300px]"},re={class:"relative"},oe={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},le={class:"flex items-center justify-between"},se={class:"text-lg font-medium text-gray-900 dark:text-white"},ne={class:"text-sm font-medium text-gray-900 dark:text-white"},ie={class:"text-sm text-gray-900 dark:text-white"},ce={class:"text-sm text-gray-900 dark:text-white"},ue={class:"text-center py-12"},de={class:"mt-6"},we={__name:"PreInvoicesList",setup(pe){G();const{showToast:f}=J(),v=u(!1),i=u([]),x=u([]),l=u({page:1,pages:1,per_page:50,total:0,has_next:!1,has_prev:!1}),p=u({total_count:0,draft_count:0,ready_count:0,total_amount:0}),s=u({client_id:"",status:"",start_date:"",end_date:"",search:""}),P=y(()=>[{id:"client_id",label:"Cliente",placeholder:"Tutti i clienti",value:s.value.client_id,options:x.value.map(e=>({value:e.id,label:e.name}))},{id:"status",label:"Stato",placeholder:"Tutti gli stati",value:s.value.status,options:[{value:"draft",label:"Bozza"},{value:"ready",label:"Pronta"},{value:"sent_external",label:"Inviata esternamente"},{value:"invoiced",label:"Fatturata"}]},{id:"start_date",label:"Da data",placeholder:"Data inizio",value:s.value.start_date,type:"date",options:[]},{id:"end_date",label:"A data",placeholder:"Data fine",value:s.value.end_date,type:"date",options:[]}]),S=[{key:"pre_invoice_number",label:"Numero",type:"text"},{key:"client",label:"Cliente",type:"text"},{key:"period",label:"Periodo",type:"text"},{key:"status",label:"Stato",type:"badge"},{key:"total_amount",label:"Importo",type:"text",format:"currency"},{key:"created_at",label:"Data creazione",type:"text",format:"date"},{key:"actions",label:"Azioni",type:"text"}],z=y(()=>[{id:"total",label:"Totale pre-fatture",value:p.value.total_count||0,icon:"document-text",iconBgColor:"bg-blue-100 dark:bg-blue-900",iconColor:"text-blue-600 dark:text-blue-400"},{id:"draft",label:"In bozza",value:p.value.draft_count||0,icon:"exclamation-circle",iconBgColor:"bg-yellow-100 dark:bg-yellow-900",iconColor:"text-yellow-600 dark:text-yellow-400"},{id:"ready",label:"Pronte",value:p.value.ready_count||0,icon:"check-circle",iconBgColor:"bg-green-100 dark:bg-green-900",iconColor:"text-green-600 dark:text-green-400"},{id:"amount",label:"Valore totale",value:p.value.total_amount||0,format:"currency",icon:"currency-euro",iconBgColor:"bg-purple-100 dark:bg-purple-900",iconColor:"text-purple-600 dark:text-purple-400"}]),$=y(()=>l.value.page),h=e=>new Date(e).toLocaleDateString("it-IT"),B=e=>{const t={draft:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",ready:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",sent_external:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",invoiced:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return t[e]||t.draft},D=(e,t)=>{s.value[e]=t,_()},I=()=>{s.value={client_id:"",status:"",start_date:"",end_date:"",search:""},_()},m=async(e=1)=>{v.value=!0;try{const t=new URLSearchParams({page:e.toString(),per_page:l.value.per_page.toString()});Object.keys(s.value).forEach(a=>{s.value[a]&&t.append(a,s.value[a])});const c=await b.get(`/api/pre-invoices/?${t}`);c.data.success&&(i.value=c.data.data.pre_invoices,l.value=c.data.data.pagination,N())}catch(t){console.error("Errore nel caricamento pre-fatture:",t),f({type:"error",title:"Errore",message:"Impossibile caricare le pre-fatture",duration:4e3})}finally{v.value=!1}},T=async()=>{try{const e=await b.get("/api/clients/");e.data.success&&(x.value=e.data.data.clients||[])}catch(e){console.error("Errore nel caricamento clienti:",e)}},N=()=>{p.value={total_count:i.value.length,draft_count:i.value.filter(e=>e.status==="draft").length,ready_count:i.value.filter(e=>e.status==="ready").length,total_amount:i.value.reduce((e,t)=>e+t.total_amount,0)}},_=()=>{l.value.page=1,m(1)};let k;const V=()=>{clearTimeout(k),k=setTimeout(()=>{_()},500)},E=e=>{e>=1&&e<=l.value.pages&&m(e)},F=async e=>{if(confirm(`Sei sicuro di voler eliminare la pre-fattura ${e.pre_invoice_number}?`))try{(await b.delete(`/api/pre-invoices/${e.id}`)).data.success&&(f({type:"success",title:"Successo",message:"Pre-fattura eliminata con successo",duration:3e3}),m($.value))}catch(t){console.error("Errore nell'eliminazione pre-fattura:",t),f({type:"error",title:"Errore",message:"Impossibile eliminare la pre-fattura",duration:4e3})}};return j(()=>{T(),m()}),(e,t)=>{const c=H("router-link");return w(),L("div",ee,[n(Q,{title:"Pre-fatture",subtitle:"Gestione pre-fatturazione italiana con calcoli fiscali automatici",icon:"document-text","icon-color":"text-purple-600"},{actions:o(()=>[n(c,{to:"/app/invoicing/pre-invoices/new",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:o(()=>[n(g,{name:"plus",class:"w-4 h-4 mr-2"}),t[1]||(t[1]=C(" Nuova Pre-fattura "))]),_:1,__:[1]})]),_:1}),r("div",te,[n(W,{"select-filters":P.value,onFilterChange:D,onClearFilters:I},{"additional-filters":o(()=>[r("div",ae,[r("div",re,[M(r("input",{"onUpdate:modelValue":t[0]||(t[0]=a=>s.value.search=a),onInput:V,type:"text",placeholder:"Cerca per numero pre-fattura...",class:"w-full pl-10 pr-4 py-2 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,544),[[R,s.value.search]]),r("div",oe,[n(g,{name:"magnifying-glass",class:"h-5 w-5 text-gray-400"})])])])]),_:1},8,["select-filters"])]),n(X,{stats:z.value,class:"mb-6"},null,8,["stats"]),n(Y,{title:"Pre-fatture",columns:S,data:i.value,loading:v.value,"empty-message":"Non ci sono pre-fatture che corrispondono ai filtri selezionati.","row-key":"id"},{header:o(()=>[r("div",le,[r("h3",se," Pre-fatture ("+d(l.value.total||0)+") ",1)])]),"cell-pre_invoice_number":o(({row:a})=>[r("div",ne,d(a.pre_invoice_number),1)]),"cell-client":o(({row:a})=>[r("div",ie,d(a.client.name),1)]),"cell-period":o(({row:a})=>[r("div",ce,d(h(a.billing_period_start))+" - "+d(h(a.billing_period_end)),1)]),"cell-status":o(({row:a})=>[r("span",{class:O(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",B(a.status)])},d(a.display_status),3)]),"cell-actions":o(({row:a})=>[n(K,{"show-edit":!1,"show-delete":a.status==="draft",onView:A=>e.$router.push(`/app/invoicing/pre-invoices/${a.id}`),onDelete:A=>F(a),"delete-message":`Sei sicuro di voler eliminare la pre-fattura ${a.number}?`},null,8,["show-delete","onView","onDelete","delete-message"])]),empty:o(()=>[r("div",ue,[n(g,{name:"document-text",class:"mx-auto h-12 w-12 text-gray-400"}),t[3]||(t[3]=r("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna pre-fattura",-1)),t[4]||(t[4]=r("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Non ci sono pre-fatture che corrispondono ai filtri selezionati. ",-1)),r("div",de,[n(c,{to:"/app/invoicing/pre-invoices/new",class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:o(()=>[n(g,{name:"plus",class:"w-4 h-4 mr-2"}),t[2]||(t[2]=C(" Crea la prima pre-fattura "))]),_:1,__:[2]})])])]),footer:o(()=>[l.value.pages>1?(w(),U(Z,{key:0,"current-page":l.value.page,"total-pages":l.value.pages,total:l.value.total,"per-page":l.value.per_page,onPageChange:E},null,8,["current-page","total-pages","total","per-page"])):q("",!0)]),_:1},8,["data","loading"])])}}};export{we as default};
