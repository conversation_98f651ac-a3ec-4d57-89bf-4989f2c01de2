import{r as C,c as D,x as K,b as c,o as n,j as e,e as o,k as p,s as d,l as x,t as r,n as I,E as re,F as Q,p as J,h as R,B as G,C as H,u as oe,q as ne}from"./vendor.js";import{u as W}from"./certifications.js";import{d as X,H as m}from"./app.js";import{S as y}from"./StandardButton.js";import{_ as le}from"./PageHeader.js";import{_ as de}from"./Breadcrumb.js";/* empty css                                                             */const ce={class:"certification-projects"},ue={class:"flex justify-between items-center mb-6"},me={class:"flex space-x-3"},ge={key:0,class:"flex justify-center py-8"},pe={key:1,class:"space-y-6"},xe={key:0,class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ye={class:"bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4"},ve={class:"flex items-center justify-between"},fe={class:"text-primary-900 dark:text-primary-100 text-2xl font-bold"},_e={class:"bg-green-50 dark:bg-green-900/20 rounded-lg p-4"},be={class:"flex items-center justify-between"},ke={class:"text-green-900 dark:text-green-100 text-2xl font-bold"},we={class:"bg-secondary-50 dark:bg-secondary-900/20 rounded-lg p-4"},he={class:"flex items-center justify-between"},$e={class:"text-secondary-900 dark:text-secondary-100 text-2xl font-bold"},Ce={class:"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4"},ze={class:"flex items-center justify-between"},Se={class:"text-orange-900 dark:text-orange-100 text-xl font-bold"},je={key:1,class:"bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-700 rounded-lg p-6"},Ae={class:"flex items-center justify-between mb-4"},Pe={class:"text-lg font-semibold text-primary-900 dark:text-primary-100 flex items-center"},De={class:"text-primary-700 dark:text-primary-300"},Ie={class:"flex space-x-3"},Ne={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Te={class:"text-sm font-medium text-primary-900 dark:text-primary-100"},Re={class:"text-sm font-medium text-primary-900 dark:text-primary-100"},Me={class:"text-sm font-medium text-primary-900 dark:text-primary-100"},Be={class:"mt-4"},Ee={class:"flex items-center justify-between text-sm text-primary-700 dark:text-primary-300 mb-1"},Le={class:"w-full bg-primary-200 dark:bg-primary-800 rounded-full h-2"},Ve={key:0,class:"mt-4 p-3 bg-primary-100 dark:bg-primary-900/20 rounded"},qe={class:"text-primary-800 dark:text-primary-200 text-sm"},Fe={key:2,class:"space-y-4"},Oe={class:"space-y-3"},Ue={class:"flex items-center justify-between"},Ge={class:"flex-1"},He={class:"flex items-center space-x-3"},Qe={class:"font-medium text-gray-900 dark:text-white"},Je={class:"text-sm text-gray-600 dark:text-gray-400"},Ke={class:"flex items-center space-x-4"},We={class:"text-center"},Xe={class:"text-center"},Ye={class:"text-sm font-semibold text-gray-900 dark:text-white"},Ze={class:"text-center"},et={class:"text-sm font-semibold text-gray-900 dark:text-white"},tt={key:3,class:"text-center py-12"},st={class:"flex justify-center space-x-3"},at={key:4,class:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4"},it={class:"flex flex-wrap gap-3"},rt={__name:"CertificationProjects",props:{certification:{type:Object,required:!0}},setup(w){const M=w,k=W(),{addToast:P}=X(),j=C(!1),_=C(null),v=D(()=>{var g;return(g=_.value)!=null&&g.projects&&_.value.projects.find(i=>i.is_current)||null}),b=D(()=>{var g;return(g=_.value)!=null&&g.projects?_.value.projects.filter(i=>!i.is_current):[]}),z=async()=>{j.value=!0;try{const g=await k.fetchCertificationProjects(M.certification.id);g.success&&(_.value=g.data)}catch{P("Errore nel caricamento progetti","error")}finally{j.value=!1}},u=g=>g?new Date(g).toLocaleDateString("it-IT"):null,s=g=>g?new Intl.NumberFormat("it-IT").format(g):"0",N=g=>({planning:"In Pianificazione",active:"Attivo",in_progress:"In Corso",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[g]||g,T=g=>({planning:"bg-blue-100 text-blue-800",active:"bg-green-100 text-green-800",in_progress:"bg-yellow-100 text-yellow-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-purple-100 text-purple-800",cancelled:"bg-red-100 text-red-800"})[g]||"bg-gray-100 text-gray-800";return K(()=>{z()}),(g,i)=>{var A;return n(),c("div",ce,[e("div",ue,[i[1]||(i[1]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Progetti Collegati",-1)),e("div",me,[o(y,{to:`/app/certifications/${w.certification.id}/create-project`,variant:"primary",size:"sm",icon:"plus"},{default:p(()=>i[0]||(i[0]=[d(" Nuovo Progetto ")])),_:1,__:[0]},8,["to"])])]),j.value?(n(),c("div",ge,[o(m,{name:"arrow-path",class:"h-8 w-8 text-secondary-600 dark:text-secondary-400 animate-spin"})])):(n(),c("div",pe,[(A=_.value)!=null&&A.stats?(n(),c("div",xe,[e("div",ye,[e("div",ve,[e("div",null,[i[2]||(i[2]=e("p",{class:"text-primary-600 dark:text-primary-400 text-sm font-medium"},"Progetti Totali",-1)),e("p",fe,r(_.value.stats.total_projects),1)]),o(m,{name:"folder-open",class:"h-8 w-8 text-primary-400 dark:text-primary-500"})])]),e("div",_e,[e("div",be,[e("div",null,[i[3]||(i[3]=e("p",{class:"text-green-600 dark:text-green-400 text-sm font-medium"},"Progetti Attivi",-1)),e("p",ke,r(_.value.stats.active_projects),1)]),o(m,{name:"play",class:"h-8 w-8 text-green-400 dark:text-green-500"})])]),e("div",we,[e("div",he,[e("div",null,[i[4]||(i[4]=e("p",{class:"text-secondary-600 dark:text-secondary-400 text-sm font-medium"},"Completati",-1)),e("p",$e,r(_.value.stats.completed_projects),1)]),o(m,{name:"check-circle",class:"h-8 w-8 text-secondary-400 dark:text-secondary-500"})])]),e("div",Ce,[e("div",ze,[e("div",null,[i[5]||(i[5]=e("p",{class:"text-orange-600 dark:text-orange-400 text-sm font-medium"},"Budget Totale",-1)),e("p",Se,"€"+r(s(_.value.stats.total_budget)),1)]),o(m,{name:"currency-euro",class:"h-8 w-8 text-orange-400 dark:text-orange-500"})])])])):x("",!0),v.value?(n(),c("div",je,[e("div",Ae,[e("div",null,[e("h4",Pe,[o(m,{name:"star",class:"h-5 w-5 mr-2 text-primary-600 dark:text-primary-400"}),i[6]||(i[6]=d(" Progetto Attuale "))]),e("p",De,r(v.value.name),1)]),e("div",Ie,[o(y,{to:`/app/projects/${v.value.id}`,variant:"secondary",size:"sm",icon:"arrow-right","icon-position":"right"},{default:p(()=>i[7]||(i[7]=[d(" Gestisci Progetto ")])),_:1,__:[7]},8,["to"])])]),e("div",Ne,[e("div",null,[i[8]||(i[8]=e("dt",{class:"text-sm text-primary-600 dark:text-primary-400"},"Stato",-1)),e("dd",null,[e("span",{class:I(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",T(v.value.status)])},r(N(v.value.status)),3)])]),e("div",null,[i[9]||(i[9]=e("dt",{class:"text-sm text-primary-600 dark:text-primary-400"},"Inizio",-1)),e("dd",Te,r(u(v.value.start_date)||"Non definito"),1)]),e("div",null,[i[10]||(i[10]=e("dt",{class:"text-sm text-primary-600 dark:text-primary-400"},"Fine Prevista",-1)),e("dd",Re,r(u(v.value.end_date)||"Non definita"),1)]),e("div",null,[i[11]||(i[11]=e("dt",{class:"text-sm text-primary-600 dark:text-primary-400"},"Budget",-1)),e("dd",Me,"€"+r(s(v.value.budget)),1)])]),e("div",Be,[e("div",Ee,[i[12]||(i[12]=e("span",null,"Progresso",-1)),e("span",null,r(v.value.completion_percentage||0)+"%",1)]),e("div",Le,[e("div",{class:"h-2 rounded-full bg-primary-600 dark:bg-primary-500 transition-all duration-300",style:re({width:(v.value.completion_percentage||0)+"%"})},null,4)])]),v.value.description?(n(),c("div",Ve,[e("p",qe,r(v.value.description),1)])):x("",!0)])):x("",!0),b.value.length>0?(n(),c("div",Fe,[i[16]||(i[16]=e("h4",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Storico Progetti",-1)),e("div",Oe,[(n(!0),c(Q,null,J(b.value,$=>(n(),c("div",{key:$.id,class:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-gray-300 dark:hover:border-gray-600 transition-colors"},[e("div",Ue,[e("div",Ge,[e("div",He,[o(m,{name:"folder",class:"h-5 w-5 text-gray-400 dark:text-gray-500"}),e("div",null,[e("h5",Qe,r($.name),1),e("p",Je,r($.description||"Nessuna descrizione"),1)])])]),e("div",Ke,[e("div",We,[e("span",{class:I(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",T($.status)])},r(N($.status)),3)]),e("div",Xe,[e("div",Ye,"€"+r(s($.budget)),1),i[13]||(i[13]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"Budget",-1))]),e("div",Ze,[e("div",et,r(u($.end_date)||"N/A"),1),i[14]||(i[14]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"Completato",-1))]),o(y,{to:`/app/projects/${$.id}`,variant:"secondary",size:"sm",icon:"eye"},{default:p(()=>i[15]||(i[15]=[d(" Dettagli ")])),_:2,__:[15]},1032,["to"])])])]))),128))])])):x("",!0),!v.value&&b.value.length===0?(n(),c("div",tt,[o(m,{name:"folder-open",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),i[19]||(i[19]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun Progetto Collegato",-1)),i[20]||(i[20]=e("p",{class:"text-gray-500 dark:text-gray-400 mb-6"}," Questa certificazione non ha ancora progetti collegati. Crea un progetto per gestire le attività di preparazione o rinnovo. ",-1)),e("div",st,[o(y,{to:"/app/projects/create",variant:"secondary",icon:"folder-plus"},{default:p(()=>i[17]||(i[17]=[d(" Nuovo Progetto ")])),_:1,__:[17]}),o(y,{to:`/app/certifications/${w.certification.id}/create-project`,variant:"primary",icon:"plus"},{default:p(()=>i[18]||(i[18]=[d(" Progetto Certificazione ")])),_:1,__:[18]},8,["to"])])])):x("",!0),v.value||b.value.length>0?(n(),c("div",at,[i[24]||(i[24]=e("h4",{class:"font-medium text-gray-900 dark:text-white mb-3"},"Azioni Rapide",-1)),e("div",it,[w.certification.is_expiring_soon?(n(),R(y,{key:0,variant:"warning",size:"sm",icon:"exclamation-triangle"},{default:p(()=>i[21]||(i[21]=[d(" Avvia Rinnovo ")])),_:1,__:[21]})):x("",!0),o(y,{to:"/app/projects",variant:"secondary",size:"sm",icon:"folder-open"},{default:p(()=>i[22]||(i[22]=[d(" Tutti i Progetti ")])),_:1,__:[22]}),o(y,{variant:"secondary",size:"sm",icon:"document-text"},{default:p(()=>i[23]||(i[23]=[d(" Report Progetti ")])),_:1,__:[23]})])])):x("",!0)]))])}}},ot={class:"certification-status-manager"},nt={key:0,class:"bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-700 rounded-lg p-4 mb-6"},lt={class:"flex items-center justify-between"},dt={class:"font-semibold text-primary-900 dark:text-primary-100 flex items-center"},ct={class:"text-primary-700 dark:text-primary-300 text-sm mt-1"},ut={class:"font-medium"},mt={class:"flex space-x-2"},gt={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},pt={class:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"},xt={class:"flex items-center mb-4"},yt={class:"text-gray-600 dark:text-gray-400 mb-6"},vt={class:"space-y-4 mb-6"},ft={class:"text-xs text-gray-500 mt-1"},_t={class:"flex justify-end space-x-3"},bt={key:2,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},kt={class:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"},wt={class:"flex items-center mb-4"},ht={class:"text-gray-600 dark:text-gray-400 mb-6"},$t={key:0,class:"text-orange-600 dark:text-orange-400"},Ct={class:"flex justify-end space-x-3"},zt={key:3,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},St={class:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4"},jt={class:"flex items-center mb-4"},At={class:"text-gray-600 dark:text-gray-400 mb-6"},Pt={class:"flex justify-end space-x-3"},Dt={__name:"CertificationStatusManager",props:{certification:{type:Object,required:!0}},emits:["status-updated"],setup(w,{emit:M}){const k=w,P=M,j=W(),{addToast:_}=X(),v=C(!1),b=C(!1),z=C(!1),u=C(!1),s=C({issue_date:new Date().toISOString().split("T")[0],certificate_number:"",certifying_body:"",expiry_date:""}),N=D(()=>["planning","active","in_renewal"].includes(k.certification.status)),T=D(()=>k.certification.status==="planning"),g=D(()=>k.certification.status==="active"),i=D(()=>["active","in_renewal"].includes(k.certification.status)),A=S=>({planning:"In Pianificazione",active:"Attiva",in_renewal:"In Rinnovo",expired:"Scaduta",suspended:"Sospesa"})[S]||S,$=async()=>{if(!s.value.issue_date){_("Data di rilascio richiesta","error");return}u.value=!0;try{const S={status:"active",issue_date:s.value.issue_date,certificate_number:s.value.certificate_number,certifying_body:s.value.certifying_body,health_score:100};s.value.expiry_date&&(S.expiry_date=s.value.expiry_date),await j.updateCertificationStatus(k.certification.id,S),_("Certificazione marcata come attiva!","success"),V(),P("status-updated")}catch{_("Errore nell'attivazione della certificazione","error")}finally{u.value=!1}},E=async()=>{u.value=!0;try{await j.updateCertificationStatus(k.certification.id,{status:"in_renewal"}),_("Processo di rinnovo avviato","success"),b.value=!1,P("status-updated")}catch{_("Errore nell'avvio del rinnovo","error")}finally{u.value=!1}},L=async()=>{u.value=!0;try{await j.updateCertificationStatus(k.certification.id,{status:"suspended",health_score:0}),_("Certificazione sospesa","success"),z.value=!1,P("status-updated")}catch{_("Errore nella sospensione della certificazione","error")}finally{u.value=!1}},V=()=>{v.value=!1,s.value={issue_date:new Date().toISOString().split("T")[0],certificate_number:"",certifying_body:"",expiry_date:""}};return K(()=>{k.certification.certificate_number&&(s.value.certificate_number=k.certification.certificate_number),k.certification.certifying_body&&(s.value.certifying_body=k.certification.certifying_body)}),(S,a)=>{var q,F,O,U;return n(),c("div",ot,[N.value?(n(),c("div",nt,[e("div",lt,[e("div",null,[e("h4",dt,[o(m,{name:"arrow-path",class:"h-5 w-5 mr-2"}),a[9]||(a[9]=d(" Gestione Status Certificazione "))]),e("p",ct,[a[10]||(a[10]=d(" Status attuale: ")),e("span",ut,r(A(w.certification.status)),1)])]),e("div",mt,[T.value?(n(),R(y,{key:0,onClick:a[0]||(a[0]=h=>v.value=!0),variant:"success",size:"sm",icon:"check-circle"},{default:p(()=>a[11]||(a[11]=[d(" Marca come Attiva ")])),_:1,__:[11]})):x("",!0),g.value?(n(),R(y,{key:1,onClick:a[1]||(a[1]=h=>b.value=!0),variant:"warning",size:"sm",icon:"arrow-path"},{default:p(()=>a[12]||(a[12]=[d(" Avvia Rinnovo ")])),_:1,__:[12]})):x("",!0),i.value?(n(),R(y,{key:2,onClick:a[2]||(a[2]=h=>z.value=!0),variant:"danger",size:"sm",icon:"pause"},{default:p(()=>a[13]||(a[13]=[d(" Sospendi ")])),_:1,__:[13]})):x("",!0)])])])):x("",!0),v.value?(n(),c("div",gt,[e("div",pt,[e("div",xt,[o(m,{name:"check-circle",class:"h-6 w-6 text-green-500 mr-3"}),a[14]||(a[14]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Marca Certificazione come Attiva",-1))]),e("p",yt,[a[15]||(a[15]=d(" Confermi che la certificazione ")),e("strong",null,r((q=w.certification.standard)==null?void 0:q.name),1),a[16]||(a[16]=d(" è stata ottenuta? "))]),e("div",vt,[e("div",null,[a[17]||(a[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Data Rilascio * ",-1)),G(e("input",{"onUpdate:modelValue":a[3]||(a[3]=h=>s.value.issue_date=h),type:"date",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",required:""},null,512),[[H,s.value.issue_date]])]),e("div",null,[a[18]||(a[18]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Numero Certificato ",-1)),G(e("input",{"onUpdate:modelValue":a[4]||(a[4]=h=>s.value.certificate_number=h),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",placeholder:"es. ISO9001-2024-001"},null,512),[[H,s.value.certificate_number]])]),e("div",null,[a[19]||(a[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Ente Certificatore ",-1)),G(e("input",{"onUpdate:modelValue":a[5]||(a[5]=h=>s.value.certifying_body=h),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",placeholder:"es. Bureau Veritas, DNV, TÜV SÜD"},null,512),[[H,s.value.certifying_body]])]),e("div",null,[a[20]||(a[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Data Scadenza ",-1)),G(e("input",{"onUpdate:modelValue":a[6]||(a[6]=h=>s.value.expiry_date=h),type:"date",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"},null,512),[[H,s.value.expiry_date]]),e("p",ft," Se non specificata, sarà calcolata automaticamente ("+r(((F=w.certification.standard)==null?void 0:F.typical_validity_years)||3)+" anni) ",1)])]),e("div",_t,[o(y,{onClick:V,variant:"secondary",disabled:u.value},{default:p(()=>a[21]||(a[21]=[d(" Annulla ")])),_:1,__:[21]},8,["disabled"]),o(y,{onClick:$,variant:"success",disabled:u.value||!s.value.issue_date,icon:u.value?"arrow-path":"check",loading:u.value},{default:p(()=>[d(r(u.value?"Attivazione...":"Attiva Certificazione"),1)]),_:1},8,["disabled","icon","loading"])])])])):x("",!0),b.value?(n(),c("div",bt,[e("div",kt,[e("div",wt,[o(m,{name:"arrow-path",class:"h-6 w-6 text-orange-500 mr-3"}),a[22]||(a[22]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Avvia Processo di Rinnovo",-1))]),e("p",ht,[a[23]||(a[23]=d(" Vuoi avviare il processo di rinnovo per ")),e("strong",null,r((O=w.certification.standard)==null?void 0:O.name),1),a[24]||(a[24]=d("? ")),w.certification.days_to_expiry?(n(),c("span",$t," (Scade tra "+r(w.certification.days_to_expiry)+" giorni) ",1)):x("",!0)]),e("div",Ct,[o(y,{onClick:a[7]||(a[7]=h=>b.value=!1),variant:"secondary",disabled:u.value},{default:p(()=>a[25]||(a[25]=[d(" Annulla ")])),_:1,__:[25]},8,["disabled"]),o(y,{onClick:E,variant:"warning",disabled:u.value,icon:"arrow-path",loading:u.value},{default:p(()=>[d(r(u.value?"Avvio...":"Avvia Rinnovo"),1)]),_:1},8,["disabled","loading"])])])])):x("",!0),z.value?(n(),c("div",zt,[e("div",St,[e("div",jt,[o(m,{name:"pause",class:"h-6 w-6 text-red-500 mr-3"}),a[26]||(a[26]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Sospendi Certificazione",-1))]),e("p",At,[a[27]||(a[27]=d(" Sei sicuro di voler sospendere ")),e("strong",null,r((U=w.certification.standard)==null?void 0:U.name),1),a[28]||(a[28]=d("? La certificazione non sarà più considerata attiva. "))]),e("div",Pt,[o(y,{onClick:a[8]||(a[8]=h=>z.value=!1),variant:"secondary",disabled:u.value},{default:p(()=>a[29]||(a[29]=[d(" Annulla ")])),_:1,__:[29]},8,["disabled"]),o(y,{onClick:L,variant:"danger",disabled:u.value,icon:u.value?"arrow-path":"pause",loading:u.value},{default:p(()=>[d(r(u.value?"Sospensione...":"Sospendi"),1)]),_:1},8,["disabled","icon","loading"])])])])):x("",!0)])}}},It={class:"certification-view"},Nt={key:0,class:"flex justify-center items-center py-12"},Tt={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},Rt={class:"flex items-center"},Mt={class:"text-red-800 dark:text-red-200"},Bt={key:2,class:"space-y-6"},Et={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm"},Lt={class:"flex border-b border-gray-200 dark:border-gray-700"},Vt=["onClick"],qt={class:"p-6"},Ft={key:0,class:"space-y-6"},Ot={class:"flex items-center justify-between"},Ut={class:"flex items-center"},Gt={class:"font-semibold"},Ht={class:"text-sm opacity-90"},Qt={class:"text-right"},Jt={class:"text-lg font-bold"},Kt={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Wt={class:"bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4"},Xt={class:"flex items-center justify-between"},Yt={class:"text-primary-900 dark:text-primary-100 text-lg font-bold"},Zt={class:"bg-green-50 dark:bg-green-900/20 rounded-lg p-4"},es={class:"flex items-center justify-between"},ts={class:"text-green-900 dark:text-green-100 text-lg font-bold"},ss={class:"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4"},as={class:"flex items-center justify-between"},is={class:"text-orange-900 dark:text-orange-100 text-lg font-bold"},rs={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},os={class:"bg-gray-50 rounded-lg p-4"},ns={class:"font-semibold text-gray-900 mb-4 flex items-center"},ls={class:"space-y-3"},ds={class:"text-gray-900"},cs={class:"text-gray-900"},us={key:0},ms={class:"text-gray-900"},gs={class:"text-gray-900"},ps={class:"bg-gray-50 rounded-lg p-4"},xs={class:"font-semibold text-gray-900 mb-4 flex items-center"},ys={class:"space-y-3"},vs={key:0},fs={class:"text-gray-900"},_s={class:"text-gray-900"},bs={key:1},ks={key:2,class:"space-y-4"},ws={class:"flex justify-between items-center"},hs={key:0,class:"space-y-3"},$s={class:"flex items-center justify-between"},Cs={class:"flex-1"},zs={class:"flex items-center space-x-3"},Ss={class:"font-medium text-gray-900"},js={class:"text-sm text-gray-600"},As={key:0,class:"ml-2"},Ps={class:"flex items-center space-x-4"},Ds={class:"text-center"},Is={key:0,class:"text-center"},Ns={class:"text-sm font-semibold text-gray-900"},Ts={key:1,class:"text-center"},Rs={class:"text-sm font-semibold text-gray-900"},Ms={key:1,class:"text-center py-8"},Bs={key:3,class:"space-y-4"},Es={class:"flex justify-between items-center"},Ls={class:"bg-orange-50 border border-orange-200 rounded-lg p-4"},Vs={class:"space-y-2"},qs={class:"flex justify-between"},Fs={class:"font-medium"},Os={class:"flex justify-between"},Us={class:"font-medium"},Gs={class:"flex justify-between"},Hs={class:"bg-gray-50 border border-gray-200 rounded-lg p-4"},Qs={class:"flex items-center"},Js={key:4,class:"space-y-4"},Ks={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ws={class:"bg-green-50 rounded-lg p-4"},Xs={class:"flex items-center justify-between"},Ys={class:"text-green-900 text-2xl font-bold"},Zs={class:"bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4"},ea={class:"flex items-center justify-between"},ta={class:"text-primary-900 dark:text-primary-100 text-2xl font-bold"},sa={class:"bg-secondary-50 dark:bg-secondary-900/20 rounded-lg p-4"},aa={class:"flex items-center justify-between"},ia={class:"text-secondary-900 dark:text-secondary-100 text-lg font-bold"},ra={class:"bg-gray-50 border border-gray-200 rounded-lg p-4"},oa={class:"flex items-center"},na={key:3,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},la={class:"bg-white rounded-lg p-6 max-w-md w-full mx-4"},da={class:"flex items-center mb-4"},ca={class:"text-gray-600 mb-6"},ua={class:"flex justify-end space-x-3"},ba={__name:"CertificationView",setup(w){const M=ne(),k=oe(),P=W(),{addToast:j}=X(),_=C(!1),v=C(null),b=C("overview"),z=C(!1),u=C(!1),s=D(()=>P.currentCertification),N=D(()=>{var l,t,B;return[{name:"Certificazioni",href:"/app/certifications/dashboard"},{name:"Lista",href:"/app/certifications/list"},{name:((t=(l=s.value)==null?void 0:l.standard)==null?void 0:t.name)||((B=s.value)==null?void 0:B.standard_name)||"Dettagli",href:null}]}),T=[{id:"overview",name:"Panoramica",icon:"eye"},{id:"projects",name:"Storico Progetti",icon:"folder-open"},{id:"audits",name:"Audit",icon:"clipboard-document-check"},{id:"renewals",name:"Scadenze & Rinnovi",icon:"calendar"},{id:"analytics",name:"Analytics",icon:"chart-bar"}],g=async()=>{const l=k.params.id;if(!l){v.value="ID certificazione non valido";return}_.value=!0,v.value=null;try{await P.fetchCertification(parseInt(l))}catch(t){v.value=t.message||"Errore nel caricamento della certificazione"}finally{_.value=!1}},i=async()=>{if(s.value){u.value=!0;try{await P.deleteCertification(s.value.id),j("Certificazione eliminata con successo","success"),M.push("/app/certifications/list")}catch{j("Errore nell'eliminazione della certificazione","error")}finally{u.value=!1,z.value=!1}}},A=l=>l?new Date(l).toLocaleDateString("it-IT"):null,$=l=>l?new Intl.NumberFormat("it-IT").format(l):"0",E=l=>({quality:"Qualità",security:"Sicurezza",environmental:"Ambientale",privacy:"Privacy",regulatory:"Normativo"})[l]||l||"Non specificata",L=l=>({draft:"Bozza",active:"Attiva",in_progress:"In Corso",completed:"Completata",expired:"Scaduta",suspended:"Sospesa",deleted:"Eliminata"})[l]||l||"Sconosciuto",V=l=>({draft:"La certificazione è in fase di preparazione",active:"Certificazione attiva e valida",in_progress:"Processo di certificazione in corso",completed:"Certificazione ottenuta con successo",expired:"Certificazione scaduta, necessario rinnovo",suspended:"Certificazione temporaneamente sospesa",deleted:"Certificazione eliminata dal sistema"})[l]||"Stato non definito",S=l=>({draft:"document-text",active:"check-circle",in_progress:"arrow-path",completed:"trophy",expired:"exclamation-triangle",suspended:"pause-circle",deleted:"x-circle"})[l]||"question-mark-circle",a=l=>({draft:"bg-gray-100 text-gray-800",active:"bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200",in_progress:"bg-primary-100 text-primary-800 dark:bg-primary-900/50 dark:text-primary-200",completed:"bg-secondary-100 text-secondary-800 dark:bg-secondary-900/50 dark:text-secondary-200",expired:"bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200",suspended:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200",deleted:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"})[l]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",q=l=>({low:"Bassa",medium:"Media",high:"Alta",critical:"Critica"})[l]||l||"Non definita",F=l=>({initial:"Audit Iniziale",surveillance:"Audit di Sorveglianza",renewal:"Audit di Rinnovo",special:"Audit Speciale"})[l]||l||"Audit",O=l=>({initial:"rocket-launch",surveillance:"eye",renewal:"arrow-path",special:"exclamation-triangle"})[l]||"clipboard-document-check",U=l=>({scheduled:"text-primary-600 dark:text-primary-400",in_progress:"text-yellow-600 dark:text-yellow-400",completed:"text-green-600 dark:text-green-400",cancelled:"text-gray-600 dark:text-gray-400"})[l]||"text-gray-600 dark:text-gray-400",h=l=>({scheduled:"Programmato",in_progress:"In Corso",completed:"Completato",cancelled:"Annullato"})[l]||l||"Sconosciuto",ie=l=>({scheduled:"bg-primary-100 text-primary-800 dark:bg-primary-900/50 dark:text-primary-200",in_progress:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200",completed:"bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200",cancelled:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"})[l]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200";return K(()=>{g()}),(l,t)=>{var B,Y,Z,ee,te,se,ae;return n(),c("div",It,[o(de,{items:N.value},null,8,["items"]),o(le,{title:((Y=(B=s.value)==null?void 0:B.standard)==null?void 0:Y.name)||((Z=s.value)==null?void 0:Z.standard_name)||"Caricamento...",subtitle:s.value?`${E((ee=s.value.standard)==null?void 0:ee.category)} • ${L(s.value.status)}`:"Dettagli certificazione aziendale",icon:"shield-check","icon-color":"text-secondary-600 dark:text-secondary-400"},{actions:p(()=>[s.value?(n(),R(y,{key:0,variant:"secondary",icon:"pencil",to:`/app/certifications/${s.value.id}/edit`},{default:p(()=>t[2]||(t[2]=[d(" Modifica ")])),_:1,__:[2]},8,["to"])):x("",!0),s.value&&s.value.status!=="deleted"?(n(),R(y,{key:1,variant:"danger",icon:"trash",onClick:t[0]||(t[0]=f=>z.value=!0)},{default:p(()=>t[3]||(t[3]=[d(" Elimina ")])),_:1,__:[3]})):x("",!0),o(y,{variant:"secondary",icon:"arrow-left",to:"/app/certifications/dashboard"},{default:p(()=>t[4]||(t[4]=[d(" Dashboard ")])),_:1,__:[4]})]),_:1},8,["title","subtitle"]),_.value?(n(),c("div",Nt,[o(m,{name:"arrow-path",class:"h-8 w-8 text-primary-600 dark:text-primary-400 animate-spin"}),t[5]||(t[5]=e("span",{class:"ml-3 text-gray-600 dark:text-gray-400"},"Caricamento certificazione...",-1))])):v.value?(n(),c("div",Tt,[e("div",Rt,[o(m,{name:"exclamation-triangle",class:"h-5 w-5 text-red-400 mr-2"}),e("span",Mt,r(v.value),1)])])):s.value?(n(),c("div",Bt,[o(Dt,{certification:s.value,onStatusUpdated:g},null,8,["certification"]),e("div",Et,[e("nav",Lt,[(n(),c(Q,null,J(T,f=>e("button",{key:f.id,onClick:ma=>b.value=f.id,class:I(["px-6 py-3 text-sm font-medium border-b-2 transition-colors",b.value===f.id?"border-primary-500 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"])},[o(m,{name:f.icon,size:"sm",class:"inline mr-2"},null,8,["name"]),d(" "+r(f.name),1)],10,Vt)),64))]),e("div",qt,[b.value==="overview"?(n(),c("div",Ft,[e("div",{class:I([a(s.value.status),"rounded-lg p-4"])},[e("div",Ot,[e("div",Ut,[o(m,{name:S(s.value.status),class:"h-6 w-6 mr-3"},null,8,["name"]),e("div",null,[e("h3",Gt,r(L(s.value.status)),1),e("p",Ht,r(V(s.value.status)),1)])]),e("div",Qt,[e("div",Jt,r(s.value.current_score||0)+"%",1),t[6]||(t[6]=e("div",{class:"text-sm opacity-90"},"Score Attuale",-1))])])],2),e("div",Kt,[e("div",Wt,[e("div",Xt,[e("div",null,[t[7]||(t[7]=e("p",{class:"text-primary-600 dark:text-primary-400 text-sm font-medium"},"Data Target",-1)),e("p",Yt,r(A(s.value.target_date)||"Non definita"),1)]),o(m,{name:"calendar",class:"h-8 w-8 text-primary-400 dark:text-primary-500"})])]),e("div",Zt,[e("div",es,[e("div",null,[t[8]||(t[8]=e("p",{class:"text-green-600 dark:text-green-400 text-sm font-medium"},"Budget",-1)),e("p",ts," €"+r($(s.value.estimated_budget)||"0"),1)]),o(m,{name:"currency-euro",class:"h-8 w-8 text-green-400 dark:text-green-500"})])]),e("div",ss,[e("div",as,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-orange-600 dark:text-orange-400 text-sm font-medium"},"Priorità",-1)),e("p",is,r(q(s.value.priority)),1)]),o(m,{name:"flag",class:"h-8 w-8 text-orange-400 dark:text-orange-500"})])])]),e("div",rs,[e("div",os,[e("h3",ns,[o(m,{name:"information-circle",class:"h-5 w-5 mr-2"}),t[10]||(t[10]=d(" Informazioni Base "))]),e("div",ls,[e("div",null,[t[11]||(t[11]=e("label",{class:"text-sm font-medium text-gray-600"},"Standard",-1)),e("p",ds,r(s.value.standard_code),1)]),e("div",null,[t[12]||(t[12]=e("label",{class:"text-sm font-medium text-gray-600"},"Categoria",-1)),e("p",cs,r(E(s.value.category)),1)]),s.value.description?(n(),c("div",us,[t[13]||(t[13]=e("label",{class:"text-sm font-medium text-gray-600"},"Descrizione",-1)),e("p",ms,r(s.value.description),1)])):x("",!0),e("div",null,[t[14]||(t[14]=e("label",{class:"text-sm font-medium text-gray-600"},"Data Creazione",-1)),e("p",gs,r(A(s.value.created_at)),1)])])]),e("div",ps,[e("h3",xs,[o(m,{name:"users",class:"h-5 w-5 mr-2"}),t[15]||(t[15]=d(" Team "))]),e("div",ys,[s.value.team_lead_id?(n(),c("div",vs,[t[16]||(t[16]=e("label",{class:"text-sm font-medium text-gray-600"},"Team Lead",-1)),e("p",fs,"Team Lead ID: "+r(s.value.team_lead_id),1)])):x("",!0),e("div",null,[t[17]||(t[17]=e("label",{class:"text-sm font-medium text-gray-600"},"Progetto Collegato",-1)),e("p",_s,r(s.value.project_id?`Progetto #${s.value.project_id}`:"Nessun progetto collegato"),1)])])])])])):b.value==="projects"?(n(),c("div",bs,[o(rt,{certification:s.value},null,8,["certification"])])):b.value==="audits"?(n(),c("div",ks,[e("div",ws,[t[19]||(t[19]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Audit e Verifiche",-1)),o(y,{variant:"primary",icon:"plus",size:"sm"},{default:p(()=>t[18]||(t[18]=[d(" Nuovo Audit ")])),_:1,__:[18]})]),s.value.audit_events&&s.value.audit_events.length>0?(n(),c("div",hs,[(n(!0),c(Q,null,J(s.value.audit_events,f=>(n(),c("div",{key:f.id,class:"bg-white border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"},[e("div",$s,[e("div",Cs,[e("div",zs,[o(m,{name:O(f.audit_type),class:I([U(f.status),"h-5 w-5"])},null,8,["name","class"]),e("div",null,[e("h4",Ss,r(F(f.audit_type)),1),e("p",js,[d(r(A(f.planned_date))+" ",1),f.lead_auditor?(n(),c("span",As,"• "+r(f.lead_auditor),1)):x("",!0)])])])]),e("div",Ps,[e("div",Ds,[e("div",{class:I([ie(f.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},r(h(f.status)),3)]),f.overall_score?(n(),c("div",Is,[e("div",Ns,r(f.overall_score)+"%",1),t[20]||(t[20]=e("div",{class:"text-xs text-gray-500"},"Score",-1))])):x("",!0),f.major_findings||f.minor_findings?(n(),c("div",Ts,[e("div",Rs,r((f.major_findings||0)+(f.minor_findings||0)),1),t[21]||(t[21]=e("div",{class:"text-xs text-gray-500"},"Findings",-1))])):x("",!0)])])]))),128))])):(n(),c("div",Ms,[o(m,{name:"clipboard-document-check",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),t[23]||(t[23]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Nessun Audit Registrato",-1)),t[24]||(t[24]=e("p",{class:"text-gray-500 mb-4"},"Non sono stati ancora programmati audit per questa certificazione",-1)),o(y,{variant:"primary",icon:"plus",class:"mx-auto"},{default:p(()=>t[22]||(t[22]=[d(" Programma Primo Audit ")])),_:1,__:[22]})]))])):b.value==="renewals"?(n(),c("div",Bs,[e("div",Es,[t[26]||(t[26]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Scadenze & Rinnovi",-1)),o(y,{variant:"primary",icon:"plus",size:"sm"},{default:p(()=>t[25]||(t[25]=[d(" Pianifica Rinnovo ")])),_:1,__:[25]})]),e("div",Ls,[t[30]||(t[30]=e("h4",{class:"font-semibold text-orange-900 mb-3"},"Timeline Certificazione",-1)),e("div",Vs,[e("div",qs,[t[27]||(t[27]=e("span",{class:"text-orange-700"},"Data Rilascio:",-1)),e("span",Fs,r(A(s.value.issue_date)),1)]),e("div",Os,[t[28]||(t[28]=e("span",{class:"text-orange-700"},"Data Scadenza:",-1)),e("span",Us,r(A(s.value.expiry_date)),1)]),e("div",Gs,[t[29]||(t[29]=e("span",{class:"text-orange-700"},"Giorni Rimanenti:",-1)),e("span",{class:I(["font-medium",s.value.days_to_expiry<=90?"text-red-600":"text-green-600"])},r(s.value.days_to_expiry)+" giorni ",3)])])]),e("div",Hs,[e("div",Qs,[o(m,{name:"clock",class:"h-5 w-5 text-gray-600 mr-2"}),t[31]||(t[31]=e("span",{class:"text-gray-700"},"Gestione completa rinnovi in sviluppo.",-1))])])])):b.value==="analytics"?(n(),c("div",Js,[t[36]||(t[36]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Analytics & Metriche",-1)),e("div",Ks,[e("div",Ws,[e("div",Xs,[e("div",null,[t[32]||(t[32]=e("p",{class:"text-green-600 text-sm font-medium"},"Health Score",-1)),e("p",Ys,r(s.value.health_score||0)+"%",1)]),o(m,{name:"heart",class:"h-8 w-8 text-green-400"})])]),e("div",Zs,[e("div",ea,[e("div",null,[t[33]||(t[33]=e("p",{class:"text-primary-600 dark:text-primary-400 text-sm font-medium"},"Readiness Score",-1)),e("p",ta,r(s.value.readiness_score||0)+"%",1)]),o(m,{name:"check-circle",class:"h-8 w-8 text-primary-400 dark:text-primary-500"})])]),e("div",sa,[e("div",aa,[e("div",null,[t[34]||(t[34]=e("p",{class:"text-secondary-600 dark:text-secondary-400 text-sm font-medium"},"Costo Annuale",-1)),e("p",ia,"€"+r($(s.value.annual_maintenance_cost)),1)]),o(m,{name:"currency-euro",class:"h-8 w-8 text-secondary-400 dark:text-secondary-500"})])])]),e("div",ra,[e("div",oa,[o(m,{name:"chart-bar",class:"h-5 w-5 text-gray-600 mr-2"}),t[35]||(t[35]=e("span",{class:"text-gray-700"},"Analytics avanzate e report in sviluppo.",-1))])])])):x("",!0)])])])):x("",!0),z.value?(n(),c("div",na,[e("div",la,[e("div",da,[o(m,{name:"exclamation-triangle",class:"h-6 w-6 text-red-500 mr-3"}),t[37]||(t[37]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Conferma Eliminazione",-1))]),e("p",ca,' Sei sicuro di voler eliminare la certificazione "'+r(((se=(te=s.value)==null?void 0:te.standard)==null?void 0:se.name)||((ae=s.value)==null?void 0:ae.standard_name))+'"? Questa azione non può essere annullata. ',1),e("div",ua,[o(y,{variant:"secondary",onClick:t[1]||(t[1]=f=>z.value=!1),disabled:u.value},{default:p(()=>t[38]||(t[38]=[d(" Annulla ")])),_:1,__:[38]},8,["disabled"]),o(y,{variant:"danger",onClick:i,disabled:u.value,loading:u.value,icon:u.value?"arrow-path":"trash"},{default:p(()=>[d(r(u.value?"Eliminazione...":"Elimina"),1)]),_:1},8,["disabled","loading","icon"])])])])):x("",!0)])}}};export{ba as default};
