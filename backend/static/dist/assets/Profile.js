import{r as g,c as w,x as N,b as n,j as e,A as z,t as l,B as c,C as p,l as P,F as A,p as B,Q as D,q as $,o as d}from"./vendor.js";import{a as L}from"./app.js";const O={class:"max-w-4xl mx-auto"},q={class:"bg-white shadow rounded-lg"},G={key:0,class:"flex items-center justify-center h-64"},I={class:"flex items-center space-x-6"},X={class:"flex-shrink-0"},Y={class:"h-20 w-20 rounded-full bg-primary-100 flex items-center justify-center"},J={class:"text-2xl font-medium text-primary-700"},Q={class:"flex-1"},H={class:"text-lg font-medium text-gray-900"},K={class:"text-sm text-gray-500"},W={class:"text-sm text-gray-500"},Z={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ee={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},te={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},se={class:"border-t border-gray-200 pt-6"},oe={key:0,class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4"},ae={class:"bg-gray-50 rounded-lg p-4 text-center"},re={class:"text-2xl font-semibold text-gray-900"},le={class:"text-sm text-gray-600"},ie={class:"bg-gray-50 rounded-lg p-4 text-center"},ne={class:"text-2xl font-semibold text-green-600"},de={class:"bg-gray-50 rounded-lg p-4 text-center"},ue={key:0,class:"text-2xl font-semibold text-yellow-600"},me={key:1,class:"text-2xl font-semibold text-gray-400"},ce={key:1,class:"space-y-2"},pe={class:"flex-1"},ge={class:"text-sm font-medium text-gray-900"},fe={class:"text-xs text-gray-600"},ve={class:"text-right"},ye={class:"text-sm font-medium text-gray-900"},xe={class:"text-xs text-gray-500"},be={key:2,class:"text-center py-6 bg-gray-50 rounded-lg"},_e={class:"text-sm text-gray-600"},he={key:3,class:"text-center py-6 bg-gray-50 rounded-lg"},ke={class:"border-t border-gray-200 pt-6"},we={class:"flex items-center"},Ce={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200"},Ue=["disabled"],je={__name:"Profile",setup(Pe){const f=L(),S=$(),x=g(!0),b=g(!1),s=g(null),u=g(null),h=g([]),r=g({first_name:"",last_name:"",email:"",phone:"",position:"",department:"",bio:"",dark_mode:!1}),V=w(()=>{var i;if(!s.value)return"U";const o=s.value.first_name||"",t=s.value.last_name||"";return(o.charAt(0)+t.charAt(0)).toUpperCase()||((i=s.value.username)==null?void 0:i.charAt(0).toUpperCase())||"U"}),j=w(()=>{var t,i;return{admin:"Amministratore",manager:"Manager",employee:"Dipendente",human_resources:"Risorse Umane",sales:"Vendite"}[(t=s.value)==null?void 0:t.role]||((i=s.value)==null?void 0:i.role)||"Utente"}),v=w(()=>new Date().getFullYear()),R=async()=>{try{if(f.user)s.value=f.user,k();else{const o=await fetch("/api/auth/profile",{headers:{"Content-Type":"application/json","X-CSRFToken":f.csrfToken}});if(o.ok){const t=await o.json();s.value=t.data.user,k()}}}catch(o){console.error("Error loading profile:",o)}finally{x.value=!1}},k=()=>{s.value&&(r.value={first_name:s.value.first_name||"",last_name:s.value.last_name||"",email:s.value.email||"",phone:s.value.phone||"",position:s.value.position||"",department:s.value.department||"",bio:s.value.bio||"",dark_mode:s.value.dark_mode||!1})},T=()=>{k()},E=async()=>{b.value=!0;try{const o=await fetch("/api/auth/profile",{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":f.csrfToken},body:JSON.stringify(r.value)});if(!o.ok)throw new Error("Errore nel salvataggio del profilo");const t=await o.json();s.value=t.data.user,await f.refreshUser(),alert("Profilo aggiornato con successo!")}catch(o){console.error("Error saving profile:",o),alert("Errore nel salvataggio del profilo")}finally{b.value=!1}},M=async()=>{var o,t,i,y,a;if((o=s.value)!=null&&o.id)try{const _=await fetch(`/api/performance/employees/${s.value.id}/summary/${v.value}`,{credentials:"include"});if(_.ok){const m=await _.json();m.success&&(u.value={goals_completed:((t=m.data.goals_stats)==null?void 0:t.completed)||0,total_goals:((i=m.data.goals_stats)==null?void 0:i.total)||0,avg_progress:((y=m.data.goals_stats)==null?void 0:y.avg_progress)||0,latest_rating:((a=m.data.reviews_stats)==null?void 0:a.avg_overall_rating)||null})}const U=await fetch(`/api/performance/employees/${s.value.id}/goals/${v.value}`,{credentials:"include"});if(U.ok){const m=await U.json();m.success&&(h.value=m.data.goals||[])}}catch(_){console.error("Error loading performance data:",_)}},C=()=>{var o;(o=s.value)!=null&&o.id&&S.push(`/app/personnel/${s.value.id}/performance/${v.value}`)},F=o=>({technical:"Tecniche",soft_skills:"Soft Skills",business:"Business",career_development:"Sviluppo Carriera"})[o]||o;return N(async()=>{var o;await R(),(o=s.value)!=null&&o.id&&await M()}),(o,t)=>{var i,y;return d(),n("div",O,[e("div",q,[t[25]||(t[25]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h1",{class:"text-xl font-semibold text-gray-900"},"Il tuo Profilo"),e("p",{class:"mt-1 text-sm text-gray-600"},"Gestisci le informazioni del tuo account")],-1)),x.value?(d(),n("div",G,t[8]||(t[8]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):(d(),n("form",{key:1,onSubmit:z(E,["prevent"]),class:"p-6 space-y-6"},[e("div",I,[e("div",X,[e("div",Y,[e("span",J,l(V.value),1)])]),e("div",Q,[e("h3",H,l((i=s.value)==null?void 0:i.username),1),e("p",K,l((y=s.value)==null?void 0:y.email),1),e("p",W,"Ruolo: "+l(j.value),1)])]),e("div",Z,[e("div",null,[t[9]||(t[9]=e("label",{for:"first_name",class:"block text-sm font-medium text-gray-700"},"Nome",-1)),c(e("input",{id:"first_name","onUpdate:modelValue":t[0]||(t[0]=a=>r.value.first_name=a),type:"text",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[p,r.value.first_name]])]),e("div",null,[t[10]||(t[10]=e("label",{for:"last_name",class:"block text-sm font-medium text-gray-700"},"Cognome",-1)),c(e("input",{id:"last_name","onUpdate:modelValue":t[1]||(t[1]=a=>r.value.last_name=a),type:"text",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[p,r.value.last_name]])])]),e("div",ee,[e("div",null,[t[11]||(t[11]=e("label",{for:"email",class:"block text-sm font-medium text-gray-700"},"Email",-1)),c(e("input",{id:"email","onUpdate:modelValue":t[2]||(t[2]=a=>r.value.email=a),type:"email",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[p,r.value.email]])]),e("div",null,[t[12]||(t[12]=e("label",{for:"phone",class:"block text-sm font-medium text-gray-700"},"Telefono",-1)),c(e("input",{id:"phone","onUpdate:modelValue":t[3]||(t[3]=a=>r.value.phone=a),type:"tel",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[p,r.value.phone]])])]),e("div",te,[e("div",null,[t[13]||(t[13]=e("label",{for:"position",class:"block text-sm font-medium text-gray-700"},"Posizione",-1)),c(e("input",{id:"position","onUpdate:modelValue":t[4]||(t[4]=a=>r.value.position=a),type:"text",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[p,r.value.position]])]),e("div",null,[t[14]||(t[14]=e("label",{for:"department",class:"block text-sm font-medium text-gray-700"},"Dipartimento",-1)),c(e("input",{id:"department","onUpdate:modelValue":t[5]||(t[5]=a=>r.value.department=a),type:"text",readonly:"",class:"mt-1 block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm"},null,512),[[p,r.value.department]])])]),e("div",null,[t[15]||(t[15]=e("label",{for:"bio",class:"block text-sm font-medium text-gray-700"},"Bio",-1)),c(e("textarea",{id:"bio","onUpdate:modelValue":t[6]||(t[6]=a=>r.value.bio=a),rows:"3",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Racconta qualcosa di te..."},null,512),[[p,r.value.bio]])]),e("div",se,[e("div",{class:"flex items-center justify-between mb-4"},[t[16]||(t[16]=e("h3",{class:"text-lg font-medium text-gray-900"},"Le mie Performance",-1)),e("button",{type:"button",onClick:C,class:"text-sm text-primary-600 hover:text-primary-500 font-medium"}," Visualizza Tutto → ")]),u.value?(d(),n("div",oe,[e("div",ae,[e("div",re,l(u.value.goals_completed),1),e("div",le,"Obiettivi Completati "+l(v.value),1)]),e("div",ie,[e("div",ne,l(u.value.avg_progress)+"%",1),t[17]||(t[17]=e("div",{class:"text-sm text-gray-600"},"Progresso Medio",-1))]),e("div",de,[u.value.latest_rating?(d(),n("div",ue,l(u.value.latest_rating)+"/5 ",1)):(d(),n("div",me,"-")),t[18]||(t[18]=e("div",{class:"text-sm text-gray-600"},"Ultima Valutazione",-1))])])):P("",!0),h.value.length>0?(d(),n("div",ce,[t[19]||(t[19]=e("h4",{class:"text-sm font-medium text-gray-700 mb-2"},"Obiettivi Recenti",-1)),(d(!0),n(A,null,B(h.value.slice(0,3),a=>(d(),n("div",{key:a.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer",onClick:C},[e("div",pe,[e("div",ge,l(a.title),1),e("div",fe,l(F(a.category)),1)]),e("div",ve,[e("div",ye,l(a.progress_percentage||0)+"%",1),e("div",xe,l(a.status),1)])]))),128))])):!x.value&&u.value&&u.value.total_goals===0?(d(),n("div",be,[t[20]||(t[20]=e("div",{class:"text-gray-400 mb-2"},"📊",-1)),e("p",_e,"Nessun obiettivo definito per "+l(v.value),1),t[21]||(t[21]=e("p",{class:"text-xs text-gray-500 mt-1"},"Contatta il tuo manager per impostare gli obiettivi",-1))])):x.value?(d(),n("div",he,t[22]||(t[22]=[e("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto mb-2"},null,-1),e("p",{class:"text-sm text-gray-600"},"Caricamento dati performance...",-1)]))):P("",!0)]),e("div",ke,[t[24]||(t[24]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Preferenze",-1)),e("div",we,[c(e("input",{id:"dark_mode","onUpdate:modelValue":t[7]||(t[7]=a=>r.value.dark_mode=a),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[D,r.value.dark_mode]]),t[23]||(t[23]=e("label",{for:"dark_mode",class:"ml-2 block text-sm text-gray-900"}," Modalità scura ",-1))])]),e("div",Ce,[e("button",{type:"button",onClick:T,class:"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"}," Ripristina "),e("button",{type:"submit",disabled:b.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},l(b.value?"Salvataggio...":"Salva Profilo"),9,Ue)])],32))])])}}};export{je as default};
