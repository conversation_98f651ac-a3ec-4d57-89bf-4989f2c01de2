import{d as c}from"./vendor.js";import{c as s}from"./app.js";const h=c("certifications",{state:()=>({certifications:[],currentCertification:null,standardsCatalog:{},overview:null,loading:!1,error:null,catalogLastFetched:null,catalogCacheDuration:5*60*1e3}),getters:{activeCertifications:e=>e.certifications.filter(r=>r.status==="active"),expiringCertifications:e=>e.certifications.filter(r=>r.is_expiring_soon),certificationsByCategory:e=>{const r={};return e.certifications.forEach(t=>{const o=t.category||"other";r[o]||(r[o]=[]),r[o].push(t)}),r},standardsByCategory:e=>e.standardsCatalog.catalog||{},availableCategories:e=>e.standardsCatalog.categories||[]},actions:{async getOverview(){var e,r;this.loading=!0,this.error=null;try{const t=await s.get("/api/certifications/overview");if(t.data.success)return this.overview=t.data.data,t.data.data;throw console.error("❌ [Certifications] API returned success=false:",t.data.message),new Error(t.data.message||"Errore nel caricamento overview")}catch(t){throw console.error("💥 [Certifications] Overview error:",t),console.error("💥 [Certifications] Error response:",t.response),console.error("💥 [Certifications] Error status:",(e=t.response)==null?void 0:e.status),console.error("💥 [Certifications] Error data:",(r=t.response)==null?void 0:r.data),this.error=t.message,t}finally{this.loading=!1}},async fetchCertifications(e={}){this.loading=!0,this.error=null;try{const r=new URLSearchParams;Object.entries(e).forEach(([o,i])=>{i!=null&&i!==""&&r.append(o,i)});const t=await s.get(`/api/certifications?${r}`);if(t.data.success)return this.certifications=t.data.data,t.data;throw new Error(t.data.message||"Errore nel caricamento certificazioni")}catch(r){throw this.error=r.message,console.error("Errore caricamento certificazioni:",r),r}finally{this.loading=!1}},async fetchCertification(e){this.loading=!0,this.error=null;try{const r=await s.get(`/api/certifications/${e}`);if(r.data.success)return this.currentCertification=r.data.data,r.data.data;throw new Error(r.data.message||"Certificazione non trovata")}catch(r){throw this.error=r.message,console.error("Errore caricamento certificazione:",r),r}finally{this.loading=!1}},async fetchStandardsCatalog(e=!1){var o,i;const r=Date.now(),t=!this.catalogLastFetched||r-this.catalogLastFetched>this.catalogCacheDuration;if(!e&&!t&&Object.keys(this.standardsCatalog).length>0)return this.standardsCatalog;this.loading=!0,this.error=null;try{const a=await s.get("/api/certifications/standards/catalog");if(a.data.success)return this.standardsCatalog=a.data.data,this.catalogLastFetched=r,a.data.data;throw console.error("❌ [Certifications] Catalog API returned success=false:",a.data.message),new Error(a.data.message||"Errore nel caricamento catalogo")}catch(a){throw console.error("💥 [Certifications] Catalog error:",a),console.error("💥 [Certifications] Catalog error response:",a.response),console.error("💥 [Certifications] Catalog error status:",(o=a.response)==null?void 0:o.status),console.error("💥 [Certifications] Catalog error data:",(i=a.response)==null?void 0:i.data),this.error=a.message,a}finally{this.loading=!1}},async createCertification(e){this.loading=!0,this.error=null;try{const r=await s.post("/api/certifications",e);if(r.data.success)return await this.fetchCertifications(),r.data.data;throw new Error(r.data.message||"Errore nella creazione certificazione")}catch(r){throw this.error=r.message,console.error("Errore creazione certificazione:",r),r}finally{this.loading=!1}},async updateCertification(e,r){this.loading=!0,this.error=null;try{const t=await s.put(`/api/certifications/${e}`,r);if(t.data.success)return this.currentCertification&&this.currentCertification.id===e&&await this.fetchCertification(e),await this.fetchCertifications(),t.data.data;throw new Error(t.data.message||"Errore nell'aggiornamento certificazione")}catch(t){throw this.error=t.message,console.error("Errore aggiornamento certificazione:",t),t}finally{this.loading=!1}},async deleteCertification(e){var r,t,o,i;this.loading=!0,this.error=null;try{const a=await s.delete(`/api/certifications/${e}`);if(a.data.success)return this.certifications=this.certifications.filter(n=>n.id!==e),this.currentCertification&&this.currentCertification.id===e&&(this.currentCertification=null),!0;throw console.error("❌ [Certifications] Delete API returned success=false:",(r=a.data)==null?void 0:r.message),new Error(a.data.message||"Errore nell'eliminazione certificazione")}catch(a){throw console.error(`💥 [Certifications] Error deleting certification ${e}:`,a),console.error("💥 [Certifications] Error details:",{message:a.message,status:(t=a.response)==null?void 0:t.status,statusText:(o=a.response)==null?void 0:o.statusText,data:(i=a.response)==null?void 0:i.data,config:a.config}),this.error=a.message,a}finally{this.loading=!1}},async syncCatalog(){this.loading=!0,this.error=null;try{const e=await s.post("/api/certifications/sync-catalog");if(e.data.success)return await this.fetchStandardsCatalog(!0),!0;throw new Error(e.data.message||"Errore nella sincronizzazione catalogo")}catch(e){throw this.error=e.message,console.error("Errore sincronizzazione catalogo:",e),e}finally{this.loading=!1}},async updateReadinessTask(e,r){this.loading=!0,this.error=null;try{const t=await s.put(`/api/certifications/readiness-tasks/${e}`,r);if(t.data.success)return this.currentCertification&&await this.fetchCertification(this.currentCertification.id),t.data.data;throw new Error(t.data.message||"Errore nell'aggiornamento task")}catch(t){throw this.error=t.message,console.error("Errore aggiornamento task:",t),t}finally{this.loading=!1}},async createAudit(e,r){this.loading=!0,this.error=null;try{const t=await s.post(`/api/certifications/${e}/audits`,r);if(t.data.success)return this.currentCertification&&this.currentCertification.id===e&&await this.fetchCertification(e),t.data.data;throw new Error(t.data.message||"Errore nella creazione audit")}catch(t){throw this.error=t.message,console.error("Errore creazione audit:",t),t}finally{this.loading=!1}},resetState(){this.certifications=[],this.currentCertification=null,this.standardsCatalog={},this.overview=null,this.error=null,this.catalogLastFetched=null},clearError(){this.error=null},async fetchPlatformCompliance(e){try{return(await s.get(`/api/certifications/platform-compliance/${e}`)).data}catch(r){throw console.error("Errore nel caricamento platform compliance:",r),r}},async fetchAIInsights(e){try{return(await s.post("/api/certifications/ai-insights",{standard_code:e})).data}catch(r){throw console.error("Errore nel caricamento AI insights:",r),r}},async fetchAvailableProjects(){try{return(await s.get("/api/certifications/available-projects")).data}catch(e){throw console.error("Errore nel caricamento progetti disponibili:",e),e}},async createCertificationWithAI(e){var r,t,o;try{const i=await s.post("/api/certifications/create-certification",e);if(!((r=i.data)!=null&&r.success))throw console.error("❌ [Certifications] API returned success=false:",(t=i.data)==null?void 0:t.message),new Error(((o=i.data)==null?void 0:o.message)||"Creazione fallita");try{await this.fetchCertifications()}catch(a){console.error("Could not refresh certifications list:",a)}return i.data}catch(i){throw console.error("💥 [Certifications] Error in createCertificationWithAI:",i),console.error("💥 [Certifications] Error details:",{message:i.message,stack:i.stack,response:i.response}),i}},async fetchCertificationProjects(e){try{return(await s.get(`/api/certifications/${e}/projects`)).data}catch(r){throw console.error("Errore nel caricamento progetti certificazione:",r),r}},async updateCertificationStatus(e,r){this.loading=!0,this.error=null;try{const t=await s.put(`/api/certifications/${e}/status`,r);if(t.data.success)return this.currentCertification&&this.currentCertification.id===e&&await this.fetchCertification(e),await this.fetchCertifications(),t.data;throw new Error(t.data.message||"Errore nell'aggiornamento dello status")}catch(t){throw this.error=t.message,console.error("Errore aggiornamento status certificazione:",t),t}finally{this.loading=!1}}}});export{h as u};
