import{d as x,r as i,c as v}from"./vendor.js";import{d as S,c as n}from"./app.js";const V=x("governance",()=>{const{showToast:s}=S(),c=i([]),l=i([]),u=i([]),h=i([]),t=i(!1),g=i(null),p=i(null),w=i(null),E=i(null),f=i({metrics:{},breakdowns:{},trends:{},recent_events:[],high_risk_activities:[]}),y=i({total_risks:0,critical_risks:0,overdue_reviews:0,by_category:[],by_level:[],by_status:[]}),m=i({critical:0,open:0,resolved:0,avg_resolution_time:0}),k=v(()=>c.value.length),_=v(()=>l.value.length),R=v(()=>u.value.length),G=v(()=>c.value.filter(r=>r.risk_level==="critical")),b=v(()=>l.value.filter(r=>r.status==="open")),z=v(()=>l.value.filter(r=>r.severity==="critical")),P=v(()=>u.value.filter(r=>r.is_active));return{risks:c,events:l,policies:u,auditLogs:h,loading:t,error:g,currentRisk:p,currentEvent:w,currentPolicy:E,dashboardData:f,riskStats:y,eventStats:m,risksCount:k,eventsCount:_,policiesCount:R,criticalRisks:G,openEvents:b,criticalEvents:z,activePolicies:P,fetchDashboard:async(r=30)=>{t.value=!0;try{const e=await n.get(`/api/governance/dashboard?days=${r}`);if(e.data.success)return f.value=e.data.data,e.data.data;throw console.error("❌ [Governance] Dashboard API returned success=false:",e.data.message),new Error(e.data.message||"Errore nel caricamento dashboard")}catch(e){throw console.error("💥 [Governance] Dashboard error:",e),console.error("💥 [Governance] Error response:",e.response),s("Errore nel caricamento della dashboard compliance","error"),e}finally{t.value=!1}},fetchRisks:async(r={})=>{t.value=!0,g.value=null;try{const e=await n.get("/api/governance/risks",{params:r});if(e.data.success)return c.value=e.data.data.risks||[],e.data.data;throw new Error(e.data.message||"Errore nel caricamento rischi")}catch(e){throw console.error("💥 [Governance] Risks error:",e),g.value=e.message||"Errore nel caricamento dei rischi",s("Errore nel caricamento dei rischi","error"),e}finally{t.value=!1}},fetchRiskStats:async()=>{try{const r=await n.get("/api/governance/risks/stats");if(r.data.success)return y.value=r.data.data,r.data.data;throw new Error(r.data.message||"Errore nel caricamento statistiche rischi")}catch(r){throw console.error("Errore caricamento statistiche rischi:",r),s("Errore nel caricamento delle statistiche","error"),r}},createRisk:async r=>{t.value=!0;try{const e=await n.post("/api/governance/risks",r);if(e.data.success)return c.value.unshift(e.data.data.risk||e.data.data),s("Rischio creato con successo","success"),e.data.data;throw new Error(e.data.message||"Errore nella creazione del rischio")}catch(e){throw console.error("Errore creazione rischio:",e),s("Errore nella creazione del rischio","error"),e}finally{t.value=!1}},updateRisk:async(r,e)=>{t.value=!0;try{const a=await n.put(`/api/governance/risks/${r}`,e);if(a.data.success){const o=c.value.findIndex(d=>d.id===r);return o!==-1&&(c.value[o]={...c.value[o],...e}),s("Rischio aggiornato con successo","success"),a.data.data}else throw new Error(a.data.message||"Errore nell'aggiornamento del rischio")}catch(a){throw console.error("💥 [Governance] Update risk error:",a),s("Errore nell'aggiornamento del rischio","error"),a}finally{t.value=!1}},deleteRisk:async r=>{t.value=!0;try{const e=await n.delete(`/api/governance/risks/${r}`);if(e.data.success)return c.value=c.value.filter(a=>a.id!==r),s("Rischio eliminato con successo","success"),!0;throw new Error(e.data.message||"Errore nell'eliminazione del rischio")}catch(e){throw console.error("💥 [Governance] Delete risk error:",e),s("Errore nell'eliminazione del rischio","error"),e}finally{t.value=!1}},mitigateRisk:async(r,e="")=>{t.value=!0;try{const a=await n.put(`/api/governance/risks/${r}/mitigate`,{mitigation_notes:e});if(a.data.success){const o=c.value.findIndex(d=>d.id===r);return o!==-1&&(c.value[o].status="mitigated",c.value[o].mitigation_notes=e),s("Rischio mitigato con successo","success"),a.data.data}else throw new Error(a.data.message||"Errore nella mitigazione del rischio")}catch(a){throw console.error("💥 [Governance] Mitigate risk error:",a),s("Errore nella mitigazione del rischio","error"),a}finally{t.value=!1}},fetchEvents:async(r={})=>{t.value=!0;try{const e=await n.get("/api/governance/events",{params:r});if(e.data.success)return l.value=e.data.data.events||[],e.data.data;throw new Error(e.data.message||"Errore nel caricamento eventi")}catch(e){throw console.error("💥 [Governance] Events error:",e),s("Errore nel caricamento degli eventi","error"),e}finally{t.value=!1}},createEvent:async r=>{t.value=!0;try{const e=await n.post("/api/governance/events",r);if(e.data.success)return l.value.unshift(e.data.data.event||e.data.data),s("Evento creato con successo","success"),e.data.data;throw new Error(e.data.message||"Errore nella creazione dell'evento")}catch(e){throw console.error("💥 [Governance] Create event error:",e),s("Errore nella creazione dell'evento","error"),e}finally{t.value=!1}},updateEvent:async(r,e)=>{t.value=!0;try{const a=await n.put(`/api/governance/events/${r}`,e);if(a.data.success){const o=l.value.findIndex(d=>d.id===r);return o!==-1&&(l.value[o]={...l.value[o],...e}),s("Evento aggiornato con successo","success"),a.data.data}else throw new Error(a.data.message||"Errore nell'aggiornamento dell'evento")}catch(a){throw console.error("💥 [Governance] Update event error:",a),s("Errore nell'aggiornamento dell'evento","error"),a}finally{t.value=!1}},resolveEvent:async(r,e="")=>{try{const a=await n.put(`/api/governance/events/${r}/resolve`,{resolution_notes:e});if(a.data.success){const o=l.value.findIndex(d=>d.id===r);return o!==-1&&(l.value[o].status="resolved",l.value[o].resolved_at=new Date().toISOString(),l.value[o].resolution_notes=e),s("Evento risolto con successo","success"),a.data.data}else throw new Error(a.data.message||"Errore nella risoluzione dell'evento")}catch(a){throw console.error("💥 [Governance] Resolve event error:",a),s("Errore nella risoluzione dell'evento","error"),a}},fetchPolicies:async(r={})=>{t.value=!0;try{const e=await n.get("/api/governance/policies",{params:r});if(e.data.success)return u.value=e.data.data.policies||[],e.data.data;throw new Error(e.data.message||"Errore nel caricamento delle policy")}catch(e){throw console.error("💥 [Governance] Policies error:",e),s("Errore nel caricamento delle policy","error"),e}finally{t.value=!1}},createPolicy:async r=>{t.value=!0;try{const e=await n.post("/api/governance/policies",r);if(e.data.success)return u.value.unshift(e.data.data.policy||e.data.data),s("Policy creata con successo","success"),e.data.data;throw new Error(e.data.message||"Errore nella creazione della policy")}catch(e){throw console.error("💥 [Governance] Create policy error:",e),s("Errore nella creazione della policy","error"),e}finally{t.value=!1}},updatePolicy:async(r,e)=>{t.value=!0;try{const a=await n.put(`/api/governance/policies/${r}`,e);if(a.data.success){const o=u.value.findIndex(d=>d.id===r);return o!==-1&&(u.value[o]={...u.value[o],...e}),s("Policy aggiornata con successo","success"),a.data.data}else throw new Error(a.data.message||"Errore nell'aggiornamento della policy")}catch(a){throw console.error("💥 [Governance] Update policy error:",a),s("Errore nell'aggiornamento della policy","error"),a}finally{t.value=!1}},fetchAuditTrail:async(r={})=>{t.value=!0;try{const e=await n.get("/api/governance/audit-trail",{params:r});if(e.data.success)return h.value=e.data.data.audit_logs||[],e.data.data;throw new Error(e.data.message||"Errore nel caricamento audit trail")}catch(e){throw console.error("💥 [Governance] Audit trail error:",e),s("Errore nel caricamento audit trail","error"),e}finally{t.value=!1}},reset:()=>{c.value=[],l.value=[],u.value=[],h.value=[],p.value=null,w.value=null,E.value=null,f.value={metrics:{},breakdowns:{},trends:{},recent_events:[],high_risk_activities:[]}}}});export{V as u};
