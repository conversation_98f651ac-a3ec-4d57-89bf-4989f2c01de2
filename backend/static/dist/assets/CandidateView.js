const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/jspdf.es.min.js","assets/app.js","assets/vendor.js","assets/index.css"])))=>i.map(i=>d[i]);
import{r as w,c as O,w as de,x as oe,b as i,o,l as m,j as e,e as c,s as f,t as n,B as L,H as W,I as re,F as P,p as j,C as ne,k as H,h as ae,A as se,n as M,u as ue,q as ce}from"./vendor.js";import{u as pe}from"./recruiting.js";import{_ as ie,d as le,H as b,j as me,c as ge}from"./app.js";import{S as v}from"./StandardButton.js";const xe={class:"resume-enhancement-modal"},ve={class:"flex justify-between items-center mb-6"},ye={class:"flex items-center space-x-3"},fe={class:"flex-shrink-0"},be={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},ke={class:"space-y-4"},_e={class:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700"},we={class:"space-y-2 text-sm"},he={key:0},Ce={key:1},ze=["value"],$e={class:"flex items-center justify-between pt-4"},Ee={class:"text-xs text-gray-500"},Se={key:1},Ie={class:"bg-gray-50 rounded-lg p-4 h-full"},Ae={key:0,class:"text-center text-gray-500 py-16"},Pe={key:1,class:"text-center py-16"},je={key:2,class:"space-y-4"},Te={class:"flex justify-between items-center"},Ve={class:"font-medium text-gray-900 flex items-center"},Me={class:"flex space-x-2"},Le={class:"max-h-96 overflow-y-auto space-y-4"},Ne={key:0},Re={class:"bg-white p-3 rounded border"},Oe={class:"text-sm text-gray-700 whitespace-pre-wrap"},Fe={key:1},De={class:"bg-white p-3 rounded border max-h-32 overflow-y-auto"},Ge={key:2},qe={class:"bg-white p-3 rounded border"},Ue={class:"flex flex-wrap gap-2"},Be={key:3},He={class:"bg-amber-50 p-3 rounded border border-amber-200"},We={class:"text-sm text-amber-800 whitespace-pre-wrap"},Je={key:4},Ke={class:"bg-green-50 p-3 rounded border border-green-200"},Ze={class:"text-sm text-green-800 whitespace-pre-wrap"},Ye={key:5,class:"border-t pt-3"},Qe={class:"flex items-center space-x-4 text-xs text-gray-500"},Xe={key:3,class:"text-center py-16"},et={class:"text-sm text-red-600 mb-4"},tt={class:"flex justify-between items-center mt-6 pt-6 border-t"},at={class:"flex space-x-3"},st={__name:"ResumeEnhancementModal",props:{show:{type:Boolean,default:!1},candidate:{type:Object,required:!0},availablePositions:{type:Array,default:()=>[]}},emits:["enhancement-generated","enhancement-applied","close"],setup(h,{emit:F}){const T=F,_=h,{showToast:k}=le(),r=w(!1),y=w(!1),C=w(null),d=w(null),g=w({focus_area:"",target_position:"",industry_context:"",custom_prompt:""}),N=O(()=>g.value.focus_area),D=O(()=>{var u;try{return(u=d.value)!=null&&u.specific_improvements?d.value.specific_improvements.split(`
`).filter(t=>t.trim()):[]}catch{return[]}}),z=O(()=>{var u;try{return(u=d.value)!=null&&u.ats_keywords?d.value.ats_keywords.split(",").map(t=>t.trim()).filter(t=>t):[]}catch{return[]}}),G=async()=>{var u,t;if(!N.value){k("Seleziona almeno un'area di focus","warning");return}y.value=!0,C.value=null;try{const p=await ge.post(`/api/recruiting/ai/candidate/${_.candidate.id}/enhance-resume`,{enhancement_prompt:J()});if(p.data.success)d.value=p.data.data.enhancement_suggestions,T("enhancement-generated",p.data.data),k("Suggerimenti generati con successo!","success");else throw new Error(p.data.message||"Errore nella generazione")}catch(p){C.value=((t=(u=p.response)==null?void 0:u.data)==null?void 0:t.message)||p.message||"Errore nella generazione AI",console.error("AI Enhancement Error:",p),k("Errore nella generazione suggerimenti","error")}finally{y.value=!1}},J=()=>{let u=`Focus area: ${g.value.focus_area}`;if(g.value.industry_context&&(u+=`
Industry context: ${g.value.industry_context}`),g.value.target_position){const t=_.availablePositions.find(p=>p.id===parseInt(g.value.target_position));t&&(u+=`
Target position: ${t.title}`)}return g.value.custom_prompt&&(u+=`
Specific requests: ${g.value.custom_prompt}`),u},R=()=>{d.value&&(T("enhancement-applied",d.value),k("Suggerimenti applicati al profilo!","success"),E())},q=async()=>{if(!d.value)return;const u=`
SUGGERIMENTI MIGLIORAMENTO CV - ${_.candidate.full_name}

RIEPILOGO ESECUTIVO:
${d.value.executive_summary||""}

MIGLIORAMENTI SPECIFICI:
${d.value.specific_improvements||""}

KEYWORDS ATS CONSIGLIATE:
${d.value.ats_keywords||""}

GAP COMPETENZE:
${d.value.skill_gaps||""}

PIANO D'AZIONE:
${d.value.action_plan||""}
  `.trim();try{await navigator.clipboard.writeText(u),k("Suggerimenti copiati negli appunti","success")}catch(t){console.error("Copy failed:",t),k("Errore nella copia","error")}},K=()=>{try{if(!enhancedContent.value){k("Nessun contenuto da esportare","warning");return}me(async()=>{const{jsPDF:u}=await import("./jspdf.es.min.js").then(t=>t.j);return{jsPDF:u}},__vite__mapDeps([0,1,2,3])).then(({jsPDF:u})=>{const t=new u,p=20,V=7,S=t.internal.pageSize.getWidth(),I=t.internal.pageSize.getHeight(),x=S-p*2;t.setFontSize(16),t.setFont("helvetica","bold"),t.text("CV Migliorato - DatPortal",p,p+10),t.setFontSize(10),t.setFont("helvetica","normal"),t.text(`Generato il: ${new Date().toLocaleDateString("it-IT")}`,p,p+20),t.setFontSize(12);const $=t.splitTextToSize(enhancedContent.value,x);let A=p+35;$.forEach(ee=>{A>I-p&&(t.addPage(),A=p),t.text(ee,p,A),A+=V});const X=`CV_migliorato_${new Date().toISOString().split("T")[0]}.pdf`;t.save(X),k("CV esportato con successo in PDF","success")}).catch(u=>{console.error("Errore import jsPDF:",u),k("Errore durante l'export PDF","error")})}catch(u){console.error("Errore export CV:",u),k("Errore durante l'export del CV","error")}},Z=()=>{C.value=null},E=()=>{r.value=!1,T("close")},Y=()=>{g.value={focus_area:"",target_position:"",industry_context:"",custom_prompt:""},d.value=null,C.value=null},Q=u=>({website:"Sito Web",linkedin:"LinkedIn",referral:"Referral",agency:"Agenzia"})[u]||u,U=u=>u?new Date(u).toLocaleString("it-IT"):"";return de(()=>_.show,u=>{r.value=u,u&&Y()}),oe(()=>{}),(u,t)=>{var p,V,S,I;return o(),i("div",xe,[r.value?(o(),i("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:E},[e("div",{class:"relative top-10 mx-auto p-6 border max-w-4xl shadow-lg rounded-md bg-white",onClick:t[4]||(t[4]=se(()=>{},["stop"]))},[e("div",ve,[e("div",ye,[e("div",fe,[c(b,{name:"document-plus",class:"w-6 h-6 text-purple-600"})]),t[5]||(t[5]=e("div",null,[e("h3",{class:"text-xl font-semibold text-gray-900"}," ✨ Miglioratore CV AI "),e("p",{class:"text-sm text-gray-600"}," Ottimizza il profilo candidato con suggerimenti intelligenti dell'AI ")],-1))]),e("button",{onClick:E,class:"text-gray-400 hover:text-gray-600"},[c(b,{name:"x-mark",class:"w-6 h-6"})])]),e("div",be,[e("div",ke,[e("div",_e,[t[10]||(t[10]=e("h4",{class:"font-medium text-blue-900 dark:text-blue-300 mb-2"},"Informazioni Candidato",-1)),e("div",we,[e("div",null,[t[6]||(t[6]=e("strong",null,"Nome:",-1)),f(" "+n((p=h.candidate)==null?void 0:p.full_name),1)]),e("div",null,[t[7]||(t[7]=e("strong",null,"Email:",-1)),f(" "+n((V=h.candidate)==null?void 0:V.email),1)]),(S=h.candidate)!=null&&S.location?(o(),i("div",he,[t[8]||(t[8]=e("strong",null,"Località:",-1)),f(" "+n(h.candidate.location),1)])):m("",!0),(I=h.candidate)!=null&&I.source?(o(),i("div",Ce,[t[9]||(t[9]=e("strong",null,"Fonte:",-1)),f(" "+n(Q(h.candidate.source)),1)])):m("",!0)])]),e("div",null,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Obiettivo Miglioramento * ",-1)),L(e("select",{"onUpdate:modelValue":t[0]||(t[0]=x=>g.value.focus_area=x),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500",required:""},t[11]||(t[11]=[re('<option value="" data-v-da9a2a72>Seleziona area di focus</option><option value="ats_optimization" data-v-da9a2a72>Ottimizzazione ATS</option><option value="skills_highlight" data-v-da9a2a72>Evidenziare Competenze</option><option value="experience_structure" data-v-da9a2a72>Strutturare Esperienza</option><option value="achievement_focus" data-v-da9a2a72>Focus sui Risultati</option><option value="personal_branding" data-v-da9a2a72>Personal Branding</option><option value="formatting" data-v-da9a2a72>Formattazione Professionale</option><option value="gap_analysis" data-v-da9a2a72>Analisi Gap</option>',8)]),512),[[W,g.value.focus_area]])]),e("div",null,[t[14]||(t[14]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Posizione Target (Opzionale) ",-1)),L(e("select",{"onUpdate:modelValue":t[1]||(t[1]=x=>g.value.target_position=x),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"},[t[13]||(t[13]=e("option",{value:""},"Nessuna posizione specifica",-1)),(o(!0),i(P,null,j(h.availablePositions,x=>(o(),i("option",{key:x.id,value:x.id},n(x.title),9,ze))),128))],512),[[W,g.value.target_position]])]),e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Contesto Settoriale ",-1)),L(e("select",{"onUpdate:modelValue":t[2]||(t[2]=x=>g.value.industry_context=x),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"},t[15]||(t[15]=[re('<option value="" data-v-da9a2a72>Generico</option><option value="technology" data-v-da9a2a72>Tecnologia &amp; IT</option><option value="finance" data-v-da9a2a72>Finanza &amp; Banking</option><option value="healthcare" data-v-da9a2a72>Sanità &amp; Farmaceutico</option><option value="manufacturing" data-v-da9a2a72>Manifatturiero</option><option value="consulting" data-v-da9a2a72>Consulenza</option><option value="retail" data-v-da9a2a72>Retail &amp; E-commerce</option><option value="education" data-v-da9a2a72>Educazione &amp; Formazione</option><option value="startup" data-v-da9a2a72>Startup &amp; Scale-up</option>',9)]),512),[[W,g.value.industry_context]])]),e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Richieste Specifiche ",-1)),L(e("textarea",{"onUpdate:modelValue":t[3]||(t[3]=x=>g.value.custom_prompt=x),rows:"3",placeholder:"Es: Migliorare sezione competenze tecniche, evidenziare leadership, ottimizzare per ruoli remote...",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"},null,512),[[ne,g.value.custom_prompt]])]),e("div",$e,[e("div",Ee,[c(b,{name:"information-circle",class:"w-4 h-4 inline mr-1"}),t[18]||(t[18]=f(" L'analisi richiede 15-25 secondi "))]),c(v,{onClick:G,disabled:!N.value||y.value,variant:"primary",class:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"},{default:H(()=>[y.value?(o(),ae(b,{key:0,name:"arrow-path",class:"animate-spin h-4 w-4 mr-2"})):(o(),i("span",Se,"✨")),f(" "+n(y.value?"Analisi in corso...":"Genera Suggerimenti"),1)]),_:1},8,["disabled"])])]),e("div",Ie,[!d.value&&!y.value?(o(),i("div",Ae,[c(b,{name:"document-plus",class:"mx-auto h-16 w-16 text-gray-400 mb-4"}),t[19]||(t[19]=e("h4",{class:"text-lg font-medium text-gray-900 mb-2"}," Suggerimenti AI ",-1)),t[20]||(t[20]=e("p",{class:"text-sm"}," I miglioramenti AI per il CV appariranno qui ",-1)),t[21]||(t[21]=e("div",{class:"mt-4 text-xs text-gray-400"},[e("div",{class:"flex items-center justify-center space-x-4"},[e("span",null,"📝 Ottimizzazioni"),e("span",null,"🎯 Keyword ATS"),e("span",null,"✨ Suggerimenti Pro")])],-1))])):m("",!0),y.value?(o(),i("div",Pe,t[22]||(t[22]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"},null,-1),e("h4",{class:"text-lg font-medium text-gray-900 mb-2"}," Analisi in corso... ",-1),e("p",{class:"text-sm text-gray-600"}," L'AI sta analizzando il profilo e generando suggerimenti personalizzati ",-1)]))):m("",!0),d.value&&!y.value?(o(),i("div",je,[e("div",Te,[e("h4",Ve,[c(b,{name:"sparkles",class:"w-4 h-4 text-purple-600 mr-2"}),t[23]||(t[23]=f(" Suggerimenti Miglioramento "))]),e("div",Me,[e("button",{onClick:q,class:"text-purple-600 hover:text-purple-800 text-sm flex items-center"},[c(b,{name:"clipboard",class:"w-4 h-4 mr-1"}),t[24]||(t[24]=f(" Copia "))]),e("button",{onClick:K,class:"text-blue-600 hover:text-blue-800 text-sm flex items-center"},[c(b,{name:"document-arrow-down",class:"w-4 h-4 mr-1"}),t[25]||(t[25]=f(" Export "))])])]),e("div",Le,[d.value.executive_summary?(o(),i("div",Ne,[t[26]||(t[26]=e("h5",{class:"text-sm font-medium text-purple-900 mb-2"},"Riepilogo Esecutivo:",-1)),e("div",Re,[e("p",Oe,n(d.value.executive_summary),1)])])):m("",!0),d.value.specific_improvements?(o(),i("div",Fe,[t[28]||(t[28]=e("h5",{class:"text-sm font-medium text-purple-900 mb-2"},"Miglioramenti Specifici:",-1)),e("div",De,[(o(!0),i(P,null,j(D.value,(x,$)=>(o(),i("div",{key:$,class:"text-sm text-gray-700 mb-2 flex items-start space-x-2"},[t[27]||(t[27]=e("span",{class:"text-purple-500 mt-1"},"•",-1)),e("span",null,n(x),1)]))),128))])])):m("",!0),d.value.ats_keywords?(o(),i("div",Ge,[t[29]||(t[29]=e("h5",{class:"text-sm font-medium text-purple-900 mb-2"},"Keywords ATS Consigliate:",-1)),e("div",qe,[e("div",Ue,[(o(!0),i(P,null,j(z.value,(x,$)=>(o(),i("span",{key:$,class:"px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs"},n(x),1))),128))])])])):m("",!0),d.value.skill_gaps?(o(),i("div",Be,[t[30]||(t[30]=e("h5",{class:"text-sm font-medium text-purple-900 mb-2"},"Gap Competenze Identificati:",-1)),e("div",He,[e("p",We,n(d.value.skill_gaps),1)])])):m("",!0),d.value.action_plan?(o(),i("div",Je,[t[31]||(t[31]=e("h5",{class:"text-sm font-medium text-purple-900 mb-2"},"Piano d'Azione:",-1)),e("div",Ke,[e("p",Ze,n(d.value.action_plan),1)])])):m("",!0),d.value.ai_metadata?(o(),i("div",Ye,[e("div",Qe,[e("span",null,"Generato: "+n(U(d.value.ai_metadata.generated_at)),1),e("span",null,"Confidenza: "+n(Math.round((d.value.ai_metadata.confidence_score||.9)*100))+"%",1)])])):m("",!0)])])):m("",!0),C.value?(o(),i("div",Xe,[c(b,{name:"exclamation-triangle",class:"mx-auto h-12 w-12 text-red-500 mb-4"}),t[33]||(t[33]=e("h4",{class:"text-lg font-medium text-gray-900 mb-2"}," Errore nella generazione ",-1)),e("p",et,n(C.value),1),c(v,{onClick:Z,variant:"secondary",size:"sm"},{default:H(()=>t[32]||(t[32]=[f(" Riprova ")])),_:1,__:[32]})])):m("",!0)])]),e("div",tt,[t[36]||(t[36]=e("div",{class:"text-xs text-gray-500"}," Powered by OpenAI GPT-4o-mini ",-1)),e("div",at,[c(v,{onClick:E,variant:"secondary"},{default:H(()=>t[34]||(t[34]=[f(" Chiudi ")])),_:1,__:[34]}),d.value?(o(),ae(v,{key:0,onClick:R,variant:"primary",class:"bg-green-600 hover:bg-green-700"},{default:H(()=>[c(b,{name:"check",class:"w-4 h-4 mr-2"}),t[35]||(t[35]=f(" Applica al Profilo "))]),_:1,__:[35]})):m("",!0)])])])])):m("",!0)])}}},rt=ie(st,[["__scopeId","data-v-da9a2a72"]]),ot={class:"candidate-view"},nt={key:0,class:"flex justify-center py-12"},it={key:1,class:"space-y-6"},lt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},dt={class:"flex items-start justify-between mb-4"},ut={class:"flex items-center space-x-4"},ct={class:"flex items-center space-x-4"},pt={class:"flex-shrink-0 h-16 w-16 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},mt={class:"text-xl font-medium text-gray-700 dark:text-gray-300"},gt={class:"text-2xl font-bold text-gray-900 dark:text-white"},xt={class:"text-gray-600 dark:text-gray-400 flex items-center"},vt={key:0,class:"text-gray-600 dark:text-gray-400 flex items-center"},yt={class:"flex items-center space-x-3"},ft={class:"flex items-center space-x-3"},bt={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},kt={class:"lg:col-span-2 space-y-6"},_t={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},wt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ht={class:"mt-1 text-sm text-gray-900 dark:text-white"},Ct={class:"mt-1 text-sm text-gray-900 dark:text-white"},zt={class:"mt-1 text-sm text-gray-900 dark:text-white"},$t={class:"mt-1 text-sm text-gray-900 dark:text-white"},Et={class:"mt-1 text-sm text-gray-900 dark:text-white"},St={key:0,class:"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700"},It={class:"flex items-center justify-between"},At={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},Pt={class:"flex items-center justify-between mb-4"},jt={key:0,class:"space-y-4"},Tt={class:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},Vt={class:"flex items-center space-x-3"},Mt={class:"text-xs text-gray-500 dark:text-gray-400"},Lt={key:0,class:"mt-4"},Nt={class:"flex flex-wrap gap-2"},Rt={key:1,class:"text-center py-8"},Ot={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},Ft={class:"flex items-center justify-between mb-4"},Dt={class:"text-lg font-medium text-gray-900 dark:text-white"},Gt={key:0,class:"space-y-3"},qt=["onClick"],Ut={class:"flex items-center justify-between"},Bt={class:"text-sm font-medium text-gray-900 dark:text-white"},Ht={class:"text-xs text-gray-500 dark:text-gray-400 flex items-center"},Wt={class:"flex items-center space-x-3"},Jt={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Kt={key:1,class:"text-center py-6"},Zt={class:"space-y-6"},Yt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},Qt={class:"space-y-3"},Xt={class:"flex justify-between"},ea={class:"text-sm text-gray-900 dark:text-white"},ta={class:"flex justify-between"},aa={class:"flex justify-between"},sa={class:"flex justify-between"},ra={class:"text-sm font-medium text-gray-900 dark:text-white"},oa={key:0,class:"flex justify-between"},na={class:"text-sm text-gray-900 dark:text-white"},ia={key:0,class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},la={class:"space-y-3"},da={class:"text-sm text-gray-500 dark:text-gray-400"},ua={class:"text-sm text-gray-900 dark:text-white"},ca={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},pa={class:"space-y-3"},ma={class:"mt-3"},ga={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},xa={class:"space-y-4"},va=["value"],ya={class:"flex justify-end space-x-3 mt-6"},fa={__name:"CandidateView",setup(h){const F=ue(),T=ce(),_=pe(),k=le(),r=w(null),y=w([]),C=w([]),d=w(!1),g=w(!1),N=w([]),D=w(null),z=w({job_posting_id:"",cover_letter:""}),G=O(()=>_.loading),J=O(()=>{const s={application_received:0,screening:0,interview_1:0,interview_2:0,offer:0};return Array.isArray(y.value)&&y.value.forEach(a=>{a&&a.current_step&&s.hasOwnProperty(a.current_step)&&s[a.current_step]++}),s}),R=async()=>{try{const s=parseInt(F.params.id),a=await _.fetchCandidate(s);r.value=a}catch(s){console.error("Error loading candidate:",s)}},q=async()=>{try{const s=parseInt(F.params.id),a=await _.fetchCandidateApplications(s);y.value=a||[]}catch(s){console.error("Error loading applications:",s),y.value=[]}},K=async()=>{try{const s=await _.fetchJobPostings({status:"active"});C.value=(s==null?void 0:s.job_postings)||[]}catch(s){console.error("Error loading job postings:",s)}},Z=async()=>{try{await _.applyCandidateToJobPosting(r.value.id,z.value.job_posting_id,z.value.cover_letter),d.value=!1,z.value={job_posting_id:"",cover_letter:""},await q()}catch(s){console.error("Error creating application:",s),k.error("Errore nella creazione della candidatura")}},E=()=>{D.value.click()},Y=async s=>{const a=s.target.files[0];if(a)try{await _.uploadCandidateCV(r.value.id,a),await R()}catch(l){console.error("Error uploading CV:",l),k.error("Errore nel caricamento del CV")}finally{s.target.value=""}},Q=async()=>{if(confirm(`Eliminare il candidato "${r.value.full_name}"?`))try{await _.deleteCandidate(r.value.id),T.push("/app/recruiting/candidates")}catch(s){console.error("Error deleting candidate:",s),k.error(s.message||"Errore nell'eliminazione del candidato")}},U=s=>{window.open(s,"_blank")},u=()=>{window.location.href=`mailto:${r.value.email}`},t=s=>{console.log("Enhancement generated:",s),showToast("Suggerimenti di miglioramento generati con successo!","success")},p=s=>{console.log("Enhancement applied:",s),showToast("Suggerimenti applicati al profilo candidato!","success"),R()},V=()=>{T.push("/app/recruiting/candidates")},S=s=>({new:"Nuovo",screening:"Screening",interviewing:"In colloquio",offered:"Offerta fatta",hired:"Assunto",rejected:"Scartato"})[s]||s,I=s=>({new:"bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200",screening:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",interviewing:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",offered:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",hired:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",rejected:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"})[s]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",x=s=>({website:"Sito Web",linkedin:"LinkedIn",referral:"Referral",agency:"Agenzia"})[s]||s,$=s=>({website:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",linkedin:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",referral:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",agency:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"})[s]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",A=s=>({application_received:"Ricevuta",screening:"Screening",interview_1:"Primo Colloquio",interview_2:"Secondo Colloquio",offer:"Offerta"})[s]||s,X=s=>({application_received:"bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200",screening:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",interview_1:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",interview_2:"bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",offer:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"})[s]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",ee=s=>{if(!s)return"N/A";const a=s.first_name||"",l=s.last_name||"";return`${a.charAt(0)}${l.charAt(0)}`.toUpperCase()},B=s=>s?new Date(s).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"";return oe(async()=>{await Promise.all([R(),q(),K()]);try{const s=await _.fetchJobPostings();N.value=s||[]}catch(s){console.error("Error loading available positions:",s)}}),(s,a)=>(o(),i("div",ot,[G.value?(o(),i("div",nt,a[14]||(a[14]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary-600"},null,-1)]))):r.value?(o(),i("div",it,[e("div",lt,[e("div",dt,[e("div",ut,[c(v,{variant:"ghost",icon:"arrow-left",onClick:V,size:"sm"}),e("div",ct,[e("div",pt,[e("span",mt,n(ee(r.value)),1)]),e("div",null,[e("h1",gt,n(r.value.full_name),1),e("p",xt,[c(b,{name:"envelope",size:"xs",class:"mr-1"}),f(" "+n(r.value.email),1)]),r.value.phone?(o(),i("p",vt,[c(b,{name:"phone",size:"xs",class:"mr-1"}),f(" "+n(r.value.phone),1)])):m("",!0)])])]),e("div",yt,[e("span",{class:M(["inline-flex items-center px-3 py-1 rounded-full text-sm font-medium",I(r.value.status)])},n(S(r.value.status)),3),e("span",{class:M(["inline-flex items-center px-3 py-1 rounded-full text-sm font-medium",$(r.value.source)])},n(x(r.value.source)),3)])]),e("div",ft,[c(v,{variant:"primary",icon:"pencil",text:"Modifica",onClick:a[0]||(a[0]=l=>s.$router.push(`/app/recruiting/candidates/${r.value.id}/edit`))}),c(v,{variant:"outline-primary",icon:"plus-circle",text:"Candida a Posizione",onClick:a[1]||(a[1]=l=>d.value=!0)}),r.value.linkedin_url?(o(),ae(v,{key:0,variant:"outline-secondary",icon:"link",text:"LinkedIn",onClick:a[2]||(a[2]=l=>U(r.value.linkedin_url))})):m("",!0),c(v,{variant:"secondary",icon:"document-arrow-up",text:"Carica CV",onClick:E}),c(v,{variant:"primary",class:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700",icon:"sparkles",text:"Migliora CV con AI",onClick:a[3]||(a[3]=l=>g.value=!0)}),c(v,{variant:"outline-danger",icon:"trash",text:"Elimina",onClick:Q})])]),e("div",bt,[e("div",kt,[e("div",_t,[a[22]||(a[22]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Informazioni Personali ",-1)),e("div",wt,[e("div",null,[a[15]||(a[15]=e("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Nome",-1)),e("p",ht,n(r.value.first_name),1)]),e("div",null,[a[16]||(a[16]=e("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Cognome",-1)),e("p",Ct,n(r.value.last_name),1)]),e("div",null,[a[17]||(a[17]=e("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Email",-1)),e("p",zt,n(r.value.email),1)]),e("div",null,[a[18]||(a[18]=e("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Telefono",-1)),e("p",$t,n(r.value.phone||"N/A"),1)]),e("div",null,[a[19]||(a[19]=e("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Località",-1)),e("p",Et,n(r.value.location||"N/A"),1)]),e("div",null,[a[20]||(a[20]=e("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Fonte",-1)),e("span",{class:M(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",$(r.value.source)])},n(x(r.value.source)),3)])]),r.value.linkedin_url?(o(),i("div",St,[e("div",It,[a[21]||(a[21]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-white"},"LinkedIn",-1)),c(v,{variant:"ghost",icon:"link",text:"Apri Profilo",size:"sm",onClick:a[4]||(a[4]=l=>U(r.value.linkedin_url))})])])):m("",!0)]),e("div",At,[e("div",Pt,[a[23]||(a[23]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Curriculum Vitae ",-1)),c(v,{variant:"outline-primary",icon:"document-arrow-up",text:"Carica Nuovo CV",size:"sm",onClick:E})]),r.value.cv_path?(o(),i("div",jt,[e("div",Tt,[e("div",Vt,[c(b,{name:"document-text",size:"md",class:"text-primary-600"}),e("div",null,[a[24]||(a[24]=e("p",{class:"text-sm font-medium text-gray-900 dark:text-white"},"CV Caricato",-1)),e("p",Mt," Aggiornato: "+n(B(r.value.updated_at)),1)])]),c(v,{variant:"ghost",icon:"document-arrow-down",size:"sm",title:"Scarica CV"})]),r.value.skills&&r.value.skills.length?(o(),i("div",Lt,[a[25]||(a[25]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-2"},"Competenze Estratte",-1)),e("div",Nt,[(o(!0),i(P,null,j(r.value.skills,l=>(o(),i("span",{key:l.id,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200"},n(l.name),1))),128))])])):m("",!0)])):(o(),i("div",Rt,[c(b,{name:"document-text",size:"lg",class:"mx-auto text-gray-400 mb-4"}),a[26]||(a[26]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-2"}," Nessun CV Caricato ",-1)),a[27]||(a[27]=e("p",{class:"text-sm text-gray-500 dark:text-gray-400 mb-4"}," Carica il CV del candidato per l'analisi automatica delle competenze ",-1)),c(v,{variant:"primary",icon:"document-arrow-up",text:"Carica CV",onClick:E})]))]),e("div",Ot,[e("div",Ft,[e("h3",Dt," Storico Candidature ("+n(y.value.length)+") ",1),c(v,{variant:"outline-primary",icon:"plus",text:"Nuova Candidatura",size:"sm",onClick:a[5]||(a[5]=l=>d.value=!0)})]),y.value.length?(o(),i("div",Gt,[(o(!0),i(P,null,j(y.value,l=>(o(),i("div",{key:l.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",onClick:te=>s.$router.push(`/app/recruiting/applications/${l.id}`)},[e("div",Ut,[e("div",null,[e("div",Bt,n(l.job_posting.title),1),e("div",Ht,[c(b,{name:"map-pin",size:"xs",class:"mr-1"}),f(" "+n(l.job_posting.location)+" ",1),a[28]||(a[28]=e("span",{class:"mx-2"},"•",-1)),f(" "+n(B(l.applied_at)),1)])]),e("div",Wt,[e("span",{class:M(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",X(l.current_step)])},n(A(l.current_step)),3),l.overall_score?(o(),i("span",Jt,n(l.overall_score)+"/10 ",1)):m("",!0)])])],8,qt))),128))])):(o(),i("div",Kt,[c(b,{name:"briefcase",size:"lg",class:"mx-auto text-gray-400 mb-2"}),a[29]||(a[29]=e("p",{class:"text-sm text-gray-500 dark:text-gray-400"}," Nessuna candidatura per questo candidato ",-1))]))])]),e("div",Zt,[e("div",Yt,[a[35]||(a[35]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Riepilogo Candidato ",-1)),e("div",Qt,[e("div",Xt,[a[30]||(a[30]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Data Registrazione",-1)),e("span",ea,n(B(r.value.created_at)),1)]),e("div",ta,[a[31]||(a[31]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Stato",-1)),e("span",{class:M(["text-sm px-2 py-1 rounded-full",I(r.value.status)])},n(S(r.value.status)),3)]),e("div",aa,[a[32]||(a[32]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Fonte Acquisizione",-1)),e("span",{class:M(["text-sm px-2 py-1 rounded-full",$(r.value.source)])},n(x(r.value.source)),3)]),e("div",sa,[a[33]||(a[33]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Candidature Totali",-1)),e("span",ra,n(y.value.length),1)]),r.value.updated_at?(o(),i("div",oa,[a[34]||(a[34]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ultimo Aggiornamento",-1)),e("span",na,n(B(r.value.updated_at)),1)])):m("",!0)])]),y.value.length?(o(),i("div",ia,[a[36]||(a[36]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Statistiche Candidature ",-1)),e("div",la,[(o(!0),i(P,null,j(J.value,(l,te)=>(o(),i("div",{key:te,class:"flex justify-between"},[e("span",da,n(A(te)),1),e("span",ua,n(l),1)]))),128))])])):m("",!0),e("div",ca,[a[37]||(a[37]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Azioni Rapide ",-1)),e("div",pa,[c(v,{variant:"outline-primary",icon:"briefcase",text:"Visualizza Tutte le Posizioni",block:"",onClick:a[6]||(a[6]=l=>s.$router.push("/app/recruiting/job-postings"))}),c(v,{variant:"outline-primary",icon:"document-text",text:"Tutte le Candidature",block:"",onClick:a[7]||(a[7]=l=>s.$router.push(`/app/recruiting/applications?candidate_id=${r.value.id}`))}),c(v,{variant:"outline-secondary",icon:"envelope",text:"Invia Email",block:"",onClick:u})])])])])])):m("",!0),d.value?(o(),i("div",{key:2,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:a[12]||(a[12]=l=>d.value=!1)},[e("div",{class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:a[11]||(a[11]=se(()=>{},["stop"]))},[e("div",ma,[e("h3",ga," Candida "+n(r.value.full_name),1),e("form",{onSubmit:se(Z,["prevent"])},[e("div",xa,[e("div",null,[a[39]||(a[39]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Posizione * ",-1)),L(e("select",{"onUpdate:modelValue":a[8]||(a[8]=l=>z.value.job_posting_id=l),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[a[38]||(a[38]=e("option",{value:""},"Seleziona posizione",-1)),(o(!0),i(P,null,j(C.value,l=>(o(),i("option",{key:l.id,value:l.id},n(l.title)+" - "+n(l.location),9,va))),128))],512),[[W,z.value.job_posting_id]])]),e("div",null,[a[40]||(a[40]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Lettera di Motivazione ",-1)),L(e("textarea",{"onUpdate:modelValue":a[9]||(a[9]=l=>z.value.cover_letter=l),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Motivazione del candidato..."},null,512),[[ne,z.value.cover_letter]])])]),e("div",ya,[c(v,{variant:"secondary",text:"Annulla",onClick:a[10]||(a[10]=l=>d.value=!1)}),c(v,{variant:"primary",text:"Crea Candidatura",loading:G.value,type:"submit"},null,8,["loading"])])],32)])])])):m("",!0),e("input",{ref_key:"cvInput",ref:D,type:"file",accept:".pdf,.doc,.docx,.txt",style:{display:"none"},onChange:Y},null,544),c(rt,{show:g.value,candidate:r.value,"available-positions":N.value,onEnhancementGenerated:t,onEnhancementApplied:p,onClose:a[13]||(a[13]=l=>g.value=!1)},null,8,["show","candidate","available-positions"])]))}},ha=ie(fa,[["__scopeId","data-v-cdcefb51"]]);export{ha as default};
