import{r,c as E,b as g,j as e,e as v,k as u,F as h,p as B,o as f,t as o,B as d,Q as R,C as l,H as q,s as H}from"./vendor.js";import{W as V}from"./WizardContainer.js";import{_ as L,H as _}from"./app.js";const Q={class:"space-y-8"},$={class:"space-y-4"},G={class:"bg-gray-50 dark:bg-gray-800 rounded-lg p-6"},J={class:"space-y-6"},K={class:"space-y-6"},O={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},X={class:"space-y-6"},Y={class:"bg-gray-50 rounded-lg p-4 space-y-3"},Z={class:"flex justify-between"},ee={class:"font-medium"},te={class:"flex justify-between"},ae={class:"font-medium"},se={class:"flex justify-between"},ne={class:"font-medium"},ie={class:"flex items-center"},oe={class:"space-y-4"},re={class:"bg-gray-50 dark:bg-gray-800 rounded-lg p-6"},de={class:"space-y-6"},le={class:"space-y-6"},ue={class:"space-y-4"},pe=["onUpdate:modelValue"],ce=["onUpdate:modelValue"],me=["onClick"],ge={class:"space-y-6"},ve={class:"bg-gray-50 rounded-lg p-4 space-y-3"},fe={class:"flex justify-between"},ye={class:"font-medium"},xe={class:"flex justify-between"},be={class:"font-medium"},we={class:"flex justify-between"},ze={class:"font-medium text-lg"},Se={class:"space-y-4"},ke={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},Ce={class:"overflow-x-auto"},he={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Be={class:"divide-y divide-gray-200 dark:divide-gray-700"},Ve={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white"},_e={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},Ie={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},De={class:"px-6 py-4 text-sm text-gray-500 dark:text-gray-400"},Ae={__name:"WizardContainerExample",setup(Fe){const w=r([{id:"info",title:"Informazioni",description:"Dati base"},{id:"details",title:"Dettagli",description:"Configurazione"},{id:"review",title:"Riepilogo",description:"Conferma"}]),p=r(0),y=r(!1),x=r(!1),n=r({name:"",description:"",budget:0,category:""}),z=r([{id:"proposal",title:"Proposta",description:"Info cliente"},{id:"services",title:"Servizi",description:"Dettagli costi"},{id:"confirmation",title:"Conferma",description:"Verifica finale"}]),c=r(0),b=r(!1),i=r({title:"",client:"",services:[{name:"",cost:0}]}),I=E(()=>i.value.services.reduce((s,t)=>s+(t.cost||0),0)),D=[{name:"steps",type:"Array",default:"[]",description:"Array degli step del wizard con id, title, description"},{name:"currentStepIndex",type:"Number",default:"0",description:"Indice dello step corrente (v-model)"},{name:"isStepValid",type:"Function",default:"() => true",description:"Funzione di validazione per step (stepIndex, stepId)"},{name:"isFormValid",type:"Function",default:"() => true",description:"Funzione di validazione complessiva del form"},{name:"loading",type:"Boolean",default:"false",description:"Stato di caricamento per disabilitare controlli"},{name:"showSaveDraft",type:"Boolean",default:"false",description:"Mostra il bottone salva bozza"},{name:"canSaveDraft",type:"Boolean",default:"true",description:"Abilita il salvataggio bozza"},{name:"allowStepNavigation",type:"Boolean",default:"true",description:"Permette navigazione diretta tra step"},{name:"submitButtonText",type:"String",default:"Invia",description:"Testo del bottone finale"},{name:"saveDraftText",type:"String",default:"Salva Bozza",description:"Testo del bottone salva bozza"}];function S(s,t){switch(t){case"info":return n.value.name.length>0;case"details":return n.value.budget>0&&n.value.category;case"review":return x.value;default:return!0}}function A(){var s;return S(p.value,(s=w.value[p.value])==null?void 0:s.id)}function k(s,t){switch(t){case"proposal":return i.value.title&&i.value.client;case"services":return i.value.services.some(a=>a.name&&a.cost>0);case"confirmation":return!0;default:return!0}}function F(){var s;return k(c.value,(s=z.value[c.value])==null?void 0:s.id)}function T(){y.value=!0,setTimeout(()=>{alert("Wizard base completato!"),y.value=!1},1500)}function U(s){console.log("Basic step changed to:",s)}function P(){b.value=!0,setTimeout(()=>{alert("Proposta creata con successo!"),b.value=!1},2e3)}function W(){alert("Bozza salvata!")}function N(s){console.log("Advanced step changed to:",s)}function j(){i.value.services.push({name:"",cost:0})}function M(s){i.value.services.splice(s,1)}return(s,t)=>(f(),g("div",Q,[t[34]||(t[34]=e("div",null,[e("h2",{class:"text-2xl font-bold text-gray-900 dark:text-white mb-2"},"WizardContainer Component"),e("p",{class:"text-gray-600 dark:text-gray-400"}," Componente wizard riutilizzabile per processi multi-step con navigazione, validazione e salvataggio bozza. ")],-1)),e("section",$,[t[21]||(t[21]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Wizard Base",-1)),e("div",G,[v(V,{steps:w.value,"current-step-index":p.value,"onUpdate:currentStepIndex":t[5]||(t[5]=a=>p.value=a),"is-step-valid":S,"is-form-valid":A,loading:y.value,onSubmit:T,onStepChange:U},{"step-info":u(()=>[e("div",J,[t[11]||(t[11]=e("h3",{class:"text-lg font-medium text-gray-900"},"Informazioni Base",-1)),e("div",null,[t[9]||(t[9]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Nome Progetto *",-1)),d(e("input",{"onUpdate:modelValue":t[0]||(t[0]=a=>n.value.name=a),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,512),[[l,n.value.name]])]),e("div",null,[t[10]||(t[10]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Descrizione",-1)),d(e("textarea",{"onUpdate:modelValue":t[1]||(t[1]=a=>n.value.description=a),rows:"4",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,512),[[l,n.value.description]])])])]),"step-details":u(()=>[e("div",K,[t[15]||(t[15]=e("h3",{class:"text-lg font-medium text-gray-900"},"Dettagli",-1)),e("div",O,[e("div",null,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Budget",-1)),d(e("input",{"onUpdate:modelValue":t[2]||(t[2]=a=>n.value.budget=a),type:"number",min:"0",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,512),[[l,n.value.budget,void 0,{number:!0}]])]),e("div",null,[t[14]||(t[14]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Categoria",-1)),d(e("select",{"onUpdate:modelValue":t[3]||(t[3]=a=>n.value.category=a),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},t[13]||(t[13]=[e("option",{value:""},"Seleziona categoria",-1),e("option",{value:"sviluppo"},"Sviluppo",-1),e("option",{value:"marketing"},"Marketing",-1),e("option",{value:"ricerca"},"Ricerca",-1)]),512),[[q,n.value.category]])])])])]),"step-review":u(()=>[e("div",X,[t[20]||(t[20]=e("h3",{class:"text-lg font-medium text-gray-900"},"Riepilogo",-1)),e("div",Y,[e("div",Z,[t[16]||(t[16]=e("span",{class:"text-gray-600"},"Nome:",-1)),e("span",ee,o(n.value.name),1)]),e("div",te,[t[17]||(t[17]=e("span",{class:"text-gray-600"},"Budget:",-1)),e("span",ae,"€"+o(n.value.budget||0),1)]),e("div",se,[t[18]||(t[18]=e("span",{class:"text-gray-600"},"Categoria:",-1)),e("span",ne,o(n.value.category||"Non specificata"),1)])]),e("div",ie,[d(e("input",{"onUpdate:modelValue":t[4]||(t[4]=a=>x.value=a),type:"checkbox",id:"basic-terms",class:"h-4 w-4 text-brand-primary-600 focus:ring-brand-primary-500 border-gray-300 rounded"},null,512),[[R,x.value]]),t[19]||(t[19]=e("label",{for:"basic-terms",class:"ml-3 text-sm text-gray-700"}," Accetto i termini e condizioni ",-1))])])]),_:1},8,["steps","current-step-index","loading"])])]),e("section",oe,[t[31]||(t[31]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Wizard Avanzato con Salvataggio Bozza",-1)),e("div",re,[v(V,{steps:z.value,"current-step-index":c.value,"onUpdate:currentStepIndex":t[8]||(t[8]=a=>c.value=a),"is-step-valid":k,"is-form-valid":F,loading:b.value,"show-save-draft":!0,"can-save-draft":!0,"submit-button-text":"Crea Proposta","save-draft-text":"Salva Bozza",onSubmit:P,onSaveDraft:W,onStepChange:N},{"step-proposal":u(()=>[e("div",de,[t[24]||(t[24]=e("h3",{class:"text-lg font-medium text-gray-900"},"Proposta Commerciale",-1)),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Titolo Proposta *",-1)),d(e("input",{"onUpdate:modelValue":t[6]||(t[6]=a=>i.value.title=a),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,512),[[l,i.value.title]])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Cliente *",-1)),d(e("input",{"onUpdate:modelValue":t[7]||(t[7]=a=>i.value.client=a),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,512),[[l,i.value.client]])])])]),"step-services":u(()=>[e("div",le,[t[26]||(t[26]=e("h3",{class:"text-lg font-medium text-gray-900"},"Servizi e Costi",-1)),e("div",ue,[(f(!0),g(h,null,B(i.value.services,(a,C)=>(f(),g("div",{key:C,class:"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg"},[d(e("input",{"onUpdate:modelValue":m=>a.name=m,type:"text",placeholder:"Nome servizio",class:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,8,pe),[[l,a.name]]),d(e("input",{"onUpdate:modelValue":m=>a.cost=m,type:"number",placeholder:"Costo",class:"w-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,8,ce),[[l,a.cost,void 0,{number:!0}]]),e("button",{type:"button",onClick:m=>M(C),class:"text-red-500 hover:text-red-700 p-1"},[v(_,{name:"trash",size:"sm"})],8,me)]))),128)),e("button",{type:"button",onClick:j,class:"btn-secondary text-sm"},[v(_,{name:"plus",size:"sm",class:"mr-1"}),t[25]||(t[25]=H(" Aggiungi Servizio "))])])])]),"step-confirmation":u(()=>[e("div",ge,[t[30]||(t[30]=e("h3",{class:"text-lg font-medium text-gray-900"},"Conferma",-1)),e("div",ve,[e("div",fe,[t[27]||(t[27]=e("span",{class:"text-gray-600"},"Cliente:",-1)),e("span",ye,o(i.value.client),1)]),e("div",xe,[t[28]||(t[28]=e("span",{class:"text-gray-600"},"Servizi:",-1)),e("span",be,o(i.value.services.length),1)]),e("div",we,[t[29]||(t[29]=e("span",{class:"text-gray-600"},"Totale:",-1)),e("span",ze,"€"+o(I.value),1)])])])]),_:1},8,["steps","current-step-index","loading"])])]),e("section",Se,[t[33]||(t[33]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Props & API",-1)),e("div",ke,[e("div",Ce,[e("table",he,[t[32]||(t[32]=e("thead",null,[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Prop"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Tipo"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Default"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Descrizione")])],-1)),e("tbody",Be,[(f(),g(h,null,B(D,a=>e("tr",{key:a.name},[e("td",Ve,o(a.name),1),e("td",_e,o(a.type),1),e("td",Ie,o(a.default),1),e("td",De,o(a.description),1)])),64))])])])])]),t[35]||(t[35]=e("section",{class:"space-y-4"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Esempi di Codice"),e("div",{class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},[e("pre",{class:"bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm"},[e("code",null,`// Basic Usage
<WizardContainer
  :steps="wizardSteps"
  v-model:current-step-index="currentStep"
  :is-step-valid="validateStep"
  :is-form-valid="validateForm"
  :loading="loading"
  @submit="handleSubmit"
>
  <template #step-basic>
    <!-- Step content here -->
  </template>
</WizardContainer>

// Step Configuration
const wizardSteps = [
  {
    id: 'basic',
    title: 'Informazioni Base',
    description: 'Dati principali'
  },
  {
    id: 'details',
    title: 'Dettagli',
    description: 'Informazioni aggiuntive'
  },
  {
    id: 'review',
    title: 'Riepilogo',
    description: 'Verifica e conferma'
  }
]

// Validation Functions
function validateStep(stepIndex, stepId) {
  switch (stepId) {
    case 'basic':
      return form.name && form.email
    case 'details':
      return form.description.length > 10
    case 'review':
      return acceptTerms.value
    default:
      return true
  }
}

function validateForm() {
  return validateStep(currentStep.value, steps[currentStep.value].id)
}`)])])],-1))]))}},We=L(Ae,[["__scopeId","data-v-63df1275"]]);export{We as default};
