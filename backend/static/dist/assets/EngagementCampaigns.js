import{b as d,j as e,l as u,B as z,H as B,I as L,e as m,s as f,f as M,F as w,p as C,t as i,r as k,c as D,x as N,q as V,o as c,n as E,A as O}from"./vendor.js";import{u as F}from"./engagement.js";import{_ as H,H as R}from"./app.js";const A={name:"EngagementCampaigns",components:{HeroIcon:R},setup(){const y=V(),t=F(),r=k(null),s=k({status:"all"}),h=D(()=>t.campaigns),_=D(()=>t.loading),l=async(n=1)=>{try{const o={page:n.toString(),per_page:"12"};s.value.status!=="all"&&(o.status=s.value.status);const g=await t.fetchCampaigns(o);r.value=g.pagination}catch(o){console.error("Error fetching campaigns:",o)}},a=n=>{y.push(`/app/engagement/campaigns/${n}`)},p=()=>{var n;(n=r.value)!=null&&n.has_prev&&l(r.value.page-1)},b=()=>{var n;(n=r.value)!=null&&n.has_next&&l(r.value.page+1)},S=()=>{if(!r.value)return"0-0";const n=(r.value.page-1)*r.value.per_page+1,o=Math.min(r.value.page*r.value.per_page,r.value.total);return`${n}-${o}`},j=(n,o)=>o?"bg-green-100 text-green-800":{draft:"bg-gray-100 text-gray-800",active:"bg-blue-100 text-blue-800",completed:"bg-purple-100 text-purple-800",archived:"bg-gray-100 text-gray-800"}[n]||"bg-gray-100 text-gray-800",P=(n,o)=>o?"Attiva":{draft:"Bozza",active:"Programmata",completed:"Completata",archived:"Archiviata"}[n]||n,T=n=>n?new Date(n).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"",I=(n,o)=>{if(!n||!o)return"";const g=new Date(n),x=new Date(o),v={month:"short",day:"numeric"};return g.getFullYear()===x.getFullYear()?g.getMonth()===x.getMonth()?`${g.getDate()} - ${x.toLocaleDateString("it-IT",{...v,year:"numeric"})}`:`${g.toLocaleDateString("it-IT",v)} - ${x.toLocaleDateString("it-IT",{...v,year:"numeric"})}`:`${g.toLocaleDateString("it-IT",{...v,year:"numeric"})} - ${x.toLocaleDateString("it-IT",{...v,year:"numeric"})}`};return N(()=>{l()}),{pagination:r,filters:s,campaigns:h,loading:_,fetchCampaigns:l,goToCampaignDetail:a,previousPage:p,nextPage:b,getItemRange:S,getCampaignStatusClass:j,getCampaignStatusText:P,formatDate:T,formatDateRange:I}}},Y={class:"engagement-campaigns"},q={class:"bg-white shadow rounded-lg mb-6 p-6"},U={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},G={class:"md:col-span-3 flex items-end"},J={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6"},K=["onClick"],Q={class:"p-6"},W={class:"flex items-start justify-between mb-4"},X={class:"text-lg font-semibold text-gray-900"},Z={class:"text-sm text-gray-600 mb-4 line-clamp-2"},$={class:"space-y-2 mb-4"},ee={class:"flex items-center text-sm text-gray-500"},te={key:0,class:"flex items-center text-sm text-amber-600"},se={key:0,class:"mb-4"},ae={class:"flex items-center text-sm font-medium text-green-600"},ne={key:1,class:"mb-4"},oe={class:"space-y-1"},ie={key:0,class:"text-xs text-gray-500"},re={class:"flex justify-between items-center"},le={class:"text-xs text-gray-500"},de=["onClick"],ce={key:1,class:"text-center py-12"},ge={key:2,class:"bg-white px-6 py-3 flex items-center justify-between border-t border-gray-200 rounded-lg shadow"},me={class:"flex-1 flex justify-between sm:hidden"},ue=["disabled"],pe=["disabled"],xe={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},ve={class:"text-sm text-gray-700"},fe={class:"font-medium"},be={class:"font-medium"},ye={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},he=["disabled"],_e={class:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"},we=["disabled"],Ce={key:3,class:"fixed inset-0 bg-gray-50 bg-opacity-75 flex items-center justify-center z-50"};function ke(y,t,r,s,h,_){const l=M("HeroIcon");return c(),d("div",Y,[t[17]||(t[17]=e("div",{class:"mb-6"},[e("h1",{class:"text-2xl font-bold text-gray-900"},"Campagne Engagement"),e("p",{class:"mt-1 text-sm text-gray-600"}," Partecipa alle iniziative aziendali e guadagna punti ")],-1)),e("div",q,[t[9]||(t[9]=e("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Filtri",-1)),e("div",U,[e("div",null,[t[7]||(t[7]=e("label",{class:"block text-sm font-medium text-gray-700"},"Status",-1)),z(e("select",{"onUpdate:modelValue":t[0]||(t[0]=a=>s.filters.status=a),class:"mt-1 block w-full rounded-md border-gray-300"},t[6]||(t[6]=[L('<option value="all" data-v-e9ac0ae5>Tutte</option><option value="active" data-v-e9ac0ae5>Attive</option><option value="upcoming" data-v-e9ac0ae5>In arrivo</option><option value="past" data-v-e9ac0ae5>Passate</option><option value="draft" data-v-e9ac0ae5>Bozze</option>',5)]),512),[[B,s.filters.status]])]),e("div",G,[e("button",{onClick:t[1]||(t[1]=(...a)=>s.fetchCampaigns&&s.fetchCampaigns(...a)),class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"},[m(l,{name:"search",class:"w-4 h-4 inline mr-2"}),t[8]||(t[8]=f(" Cerca "))])])])]),s.campaigns.length>0?(c(),d("div",J,[(c(!0),d(w,null,C(s.campaigns,a=>(c(),d("div",{key:a.id,class:"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer",onClick:p=>s.goToCampaignDetail(a.id)},[e("div",Q,[e("div",W,[e("h3",X,i(a.name),1),e("span",{class:E(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s.getCampaignStatusClass(a.status,a.is_active)])},i(s.getCampaignStatusText(a.status,a.is_active)),3)]),e("p",Z,i(a.description),1),e("div",$,[e("div",ee,[m(l,{name:"calendar",class:"w-4 h-4 mr-2"}),e("span",null,i(s.formatDateRange(a.start_date,a.end_date)),1)]),a.is_active&&a.days_remaining>0?(c(),d("div",te,[m(l,{name:"clock",class:"w-4 h-4 mr-2"}),e("span",null,i(a.days_remaining)+" giorni rimanenti",1)])):u("",!0)]),a.points_multiplier>1?(c(),d("div",se,[e("div",ae,[m(l,{name:"star",class:"w-4 h-4 mr-2"}),e("span",null,i(a.points_multiplier)+"x Punti Bonus!",1)])])):u("",!0),a.objectives_config&&Object.keys(a.objectives_config).length>0?(c(),d("div",ne,[t[10]||(t[10]=e("div",{class:"text-sm font-medium text-gray-700 mb-2"},"Obiettivi:",-1)),e("div",oe,[(c(!0),d(w,null,C(Object.entries(a.objectives_config).slice(0,2),(p,b)=>(c(),d("div",{key:b,class:"text-xs text-gray-600 flex items-center"},[m(l,{name:"check-circle",class:"w-3 h-3 mr-2 text-green-500"}),e("span",null,i(p[1].description)+" (+"+i(p[1].points)+"pts)",1)]))),128)),Object.keys(a.objectives_config).length>2?(c(),d("div",ie," +"+i(Object.keys(a.objectives_config).length-2)+" altri obiettivi... ",1)):u("",!0)])])):u("",!0),e("div",re,[e("span",le," Creata il "+i(s.formatDate(a.created_at)),1),e("button",{onClick:O(p=>s.goToCampaignDetail(a.id),["stop"]),class:"bg-primary-100 hover:bg-primary-200 text-primary-800 px-3 py-1 rounded-md text-sm font-medium"}," Visualizza ",8,de)])])],8,K))),128))])):s.loading?u("",!0):(c(),d("div",ce,[m(l,{name:"megaphone",class:"mx-auto h-12 w-12 text-gray-400"}),t[11]||(t[11]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Nessuna campagna trovata",-1)),t[12]||(t[12]=e("p",{class:"mt-1 text-sm text-gray-500"}," Non ci sono campagne che corrispondono ai filtri selezionati. ",-1))])),s.pagination&&s.pagination.pages>1?(c(),d("div",ge,[e("div",me,[e("button",{onClick:t[2]||(t[2]=(...a)=>s.previousPage&&s.previousPage(...a)),disabled:!s.pagination.has_prev,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Precedente ",8,ue),e("button",{onClick:t[3]||(t[3]=(...a)=>s.nextPage&&s.nextPage(...a)),disabled:!s.pagination.has_next,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Successivo ",8,pe)]),e("div",xe,[e("div",null,[e("p",ve,[t[13]||(t[13]=f(" Mostrando ")),e("span",fe,i(s.getItemRange()),1),t[14]||(t[14]=f(" di ")),e("span",be,i(s.pagination.total),1),t[15]||(t[15]=f(" campagne "))])]),e("div",null,[e("nav",ye,[e("button",{onClick:t[4]||(t[4]=(...a)=>s.previousPage&&s.previousPage(...a)),disabled:!s.pagination.has_prev,class:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"},[m(l,{name:"chevron-left",class:"h-5 w-5"})],8,he),e("span",_e,i(s.pagination.page)+" / "+i(s.pagination.pages),1),e("button",{onClick:t[5]||(t[5]=(...a)=>s.nextPage&&s.nextPage(...a)),disabled:!s.pagination.has_next,class:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"},[m(l,{name:"chevron-right",class:"h-5 w-5"})],8,we)])])])])):u("",!0),s.loading?(c(),d("div",Ce,t[16]||(t[16]=[e("div",{class:"bg-white p-6 rounded-lg shadow-lg flex items-center space-x-3"},[e("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"}),e("span",{class:"text-gray-900"},"Caricamento campagne...")],-1)]))):u("",!0)])}const Pe=H(A,[["render",ke],["__scopeId","data-v-e9ac0ae5"]]);export{Pe as default};
