import{r as d,c as p,x as X,w as Y,b as u,l as z,j as e,e as c,t as r,B as y,H as M,C as D,F as Z,p as ee,A as V,o as n,n as j,v as C,h as te}from"./vendor.js";import{u as ae}from"./timesheet.js";import{a as se,H as g}from"./app.js";import{A as re}from"./ActionButtonGroup.js";import"./StandardButton.js";const q=m=>new Date(m).toLocaleDateString("it-IT"),de=m=>{switch(m){case"approved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"submitted":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"confirmed":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"rejected":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"}},oe=m=>{switch(m){case"approved":return"Approvato";case"submitted":return"In Attesa";case"confirmed":return"Confermato";case"rejected":return"Rifiutato";default:return"Bozza"}},ie={class:"space-y-6"},le={key:0,class:"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4"},ne={class:"flex"},ue={class:"flex-shrink-0"},ce={class:"ml-3"},ge={class:"text-sm text-red-800 dark:text-red-200"},me={class:"ml-auto pl-3"},xe={class:"-mx-1.5 -my-1.5"},pe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ye={class:"flex justify-between items-center"},ve={class:"flex space-x-3"},be=["disabled"],fe=["disabled"],he=["disabled"],ke={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},we={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},_e={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Re={class:"overflow-x-auto"},Te={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},De={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Se={class:"px-6 py-4 whitespace-nowrap"},Ae={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},ze={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Ce={class:"px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate"},qe={class:"px-6 py-4 whitespace-nowrap"},Fe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Me={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Ve={key:1,class:"text-gray-400 dark:text-gray-500"},je={key:0,class:"text-center py-8"},Ie={class:"mx-auto h-12 w-12 text-gray-400"},Ne={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},$e={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Be={class:"flex items-center"},Pe={class:"flex-shrink-0"},Ue={class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},Ee={class:"ml-5 w-0 flex-1"},We={class:"text-lg font-medium text-gray-900 dark:text-white"},Le={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Oe={class:"flex items-center"},He={class:"flex-shrink-0"},Qe={class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},Ge={class:"ml-5 w-0 flex-1"},Je={class:"text-lg font-medium text-gray-900 dark:text-white"},Ke={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Xe={class:"flex items-center"},Ye={class:"flex-shrink-0"},Ze={class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},et={class:"ml-5 w-0 flex-1"},tt={class:"text-lg font-medium text-gray-900 dark:text-white"},at={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},st={class:"flex items-center"},rt={class:"flex-shrink-0"},dt={class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},ot={class:"ml-5 w-0 flex-1"},it={class:"text-lg font-medium text-gray-900 dark:text-white"},lt={class:"mt-3"},nt={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},ut={class:"grid grid-cols-1 gap-4"},ct={class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},gt=["required","placeholder"],mt={class:"flex justify-end space-x-3 mt-6"},xt=["disabled"],ht={__name:"TimesheetRequests",setup(m){const i=ae();se();const v=d([]),b=d(!1),h=d(!1),k=d(!1),l=d(""),x=d({vacation:{remaining:0,total:0},leave:{used:0,total:0},smartworking:{used:0}}),w=d(""),_=d(""),R=d(""),o=d({start_date:"",end_date:"",notes:""}),F=p(()=>i.error),I=p(()=>{var a;return((a=x.value.vacation)==null?void 0:a.remaining)||0}),N=p(()=>{var a;return((a=x.value.leave)==null?void 0:a.used)||0}),$=p(()=>{var a;return((a=x.value.leave)==null?void 0:a.total)||0}),B=p(()=>{var a;return((a=x.value.smartworking)==null?void 0:a.used)||0}),P=p(()=>Array.isArray(v.value)?v.value.filter(a=>a.status==="pending").length:0),S=async()=>{try{const a=await i.loadTimeOffQuotas();x.value=a,console.log("Time-off quotas loaded:",a)}catch(a){console.error("Failed to load time-off quotas:",a),x.value={vacation:{remaining:0,total:0},leave:{used:0,total:0},smartworking:{used:0}},i.setError("Impossibile caricare i dati delle quote di ferie e permessi. Verranno mostrati valori predefiniti.")}},f=async()=>{b.value=!0;try{const a={request_type:w.value,status:_.value,start_date:R.value};v.value=await i.loadTimeOffRequests(a)}finally{b.value=!1}},A=a=>{l.value=a,o.value={start_date:"",end_date:"",notes:""},h.value=!0},T=()=>{h.value=!1,l.value=""},U=()=>{switch(l.value){case"vacation":return"Richiesta Ferie";case"leave":return"Richiesta Permesso";case"smartworking":return"Richiesta Smart Working";default:return"Nuova Richiesta"}},E=async()=>{k.value=!0;try{const a={request_type:l.value,...o.value};await i.createTimeOffRequest(a)?(await f(),await S(),T()):(T(),window.scrollTo({top:0,behavior:"smooth"}))}finally{k.value=!1}},W=a=>{l.value=a.request_type,o.value={start_date:a.start_date,end_date:a.end_date,notes:a.notes||""},h.value=!0},L=async a=>{if(!confirm("Sei sicuro di voler eliminare questa richiesta?"))return;await i.deleteTimeOffRequest(a)&&(await f(),await S())},O=a=>`${q(a.start_date)} - ${q(a.end_date)}`,H=a=>`${a.duration_days||0} giorni`,Q=a=>{switch(a){case"vacation":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"leave":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"smartworking":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"}},G=a=>{switch(a){case"vacation":return"Ferie";case"leave":return"Permesso";case"smartworking":return"Smart Working";default:return"Altro"}},J=()=>{i.clearError()};return X(()=>{f(),S()}),Y([w,_,R],()=>{f()}),(a,t)=>(n(),u("div",ie,[F.value?(n(),u("div",le,[e("div",ne,[e("div",ue,[c(g,{name:"exclamation-triangle",size:"md",color:"text-red-400"})]),e("div",ce,[e("p",ge,r(F.value),1)]),e("div",me,[e("div",xe,[e("button",{onClick:J,class:"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900"},[c(g,{name:"x-mark",size:"sm"})])])])])])):z("",!0),e("div",pe,[e("div",ye,[t[10]||(t[10]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Richieste"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Gestisci le tue richieste di ferie, permessi e smart working ")],-1)),e("div",ve,[e("button",{onClick:t[0]||(t[0]=s=>A("vacation")),disabled:b.value,"data-testid":"vacation-button",class:"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium"}," Richiedi Ferie ",8,be),e("button",{onClick:t[1]||(t[1]=s=>A("leave")),disabled:b.value,"data-testid":"leave-button",class:"bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded-md text-sm font-medium"}," Richiedi Permesso ",8,fe),e("button",{onClick:t[2]||(t[2]=s=>A("smartworking")),disabled:b.value,class:"bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-4 py-2 rounded-md text-sm font-medium"}," Smart Working ",8,he)])])]),e("div",ke,[e("div",we,[e("div",null,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Richiesta ",-1)),y(e("select",{"onUpdate:modelValue":t[3]||(t[3]=s=>w.value=s),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[11]||(t[11]=[e("option",{value:""},"Tutti i tipi",-1),e("option",{value:"vacation"},"Ferie",-1),e("option",{value:"leave"},"Permessi",-1),e("option",{value:"smartworking"},"Smart Working",-1)]),512),[[M,w.value]])]),e("div",null,[t[14]||(t[14]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stato ",-1)),y(e("select",{"onUpdate:modelValue":t[4]||(t[4]=s=>_.value=s),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[13]||(t[13]=[e("option",{value:""},"Tutti gli stati",-1),e("option",{value:"pending"},"In Attesa",-1),e("option",{value:"approved"},"Approvato",-1),e("option",{value:"rejected"},"Rifiutato",-1)]),512),[[M,_.value]])]),e("div",null,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Da Data ",-1)),y(e("input",{"onUpdate:modelValue":t[5]||(t[5]=s=>R.value=s),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[D,R.value]])]),e("div",{class:"flex items-end"},[e("button",{onClick:f,class:"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Filtra ")])])]),e("div",_e,[t[19]||(t[19]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Le Mie Richieste ")],-1)),e("div",Re,[e("table",Te,[t[16]||(t[16]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Tipo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Periodo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Durata "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Motivo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Richiesta il "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",De,[(n(!0),u(Z,null,ee(v.value,s=>(n(),u("tr",{key:s.id},[e("td",Se,[e("span",{class:j(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",Q(s.request_type)])},r(G(s.request_type)),3)]),e("td",Ae,r(O(s)),1),e("td",ze,r(H(s)),1),e("td",Ce,r(s.notes||"N/A"),1),e("td",qe,[e("span",{class:j(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",C(de)(s.status)])},r(C(oe)(s.status)),3)]),e("td",Fe,r(C(q)(s.created_at)),1),e("td",Me,[s.status==="pending"?(n(),te(re,{key:0,"show-view":!1,onEdit:K=>W(s),onDelete:K=>L(s.id),"delete-message":"Sei sicuro di voler eliminare questa richiesta di modifica?"},null,8,["onEdit","onDelete"])):(n(),u("span",Ve,r(s.status==="approved"?"Approvata":"Rifiutata"),1))])]))),128))])]),v.value.length===0?(n(),u("div",je,[e("div",Ie,[c(g,{name:"document",size:"xl"})]),t[17]||(t[17]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna richiesta",-1)),t[18]||(t[18]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Non hai ancora effettuato richieste per il periodo selezionato. ",-1))])):z("",!0)])]),e("div",Ne,[e("div",$e,[e("div",Be,[e("div",Pe,[e("div",Ue,[c(g,{name:"calendar",size:"md",color:"text-white"})])]),e("div",Ee,[e("dl",null,[t[20]||(t[20]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ferie Rimanenti ",-1)),e("dd",We,r(I.value)+" giorni ",1)])])])]),e("div",Le,[e("div",Oe,[e("div",He,[e("div",Qe,[c(g,{name:"clock",size:"md",color:"text-white"})])]),e("div",Ge,[e("dl",null,[t[21]||(t[21]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Permessi Usati ",-1)),e("dd",Je,r(N.value)+" / "+r($.value)+" giorni ",1)])])])]),e("div",Ke,[e("div",Xe,[e("div",Ye,[e("div",Ze,[c(g,{name:"user",size:"md",color:"text-white"})])]),e("div",et,[e("dl",null,[t[22]||(t[22]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Smart Working ",-1)),e("dd",tt,r(B.value)+" giorni ",1)])])])]),e("div",at,[e("div",st,[e("div",rt,[e("div",dt,[c(g,{name:"document-text",size:"md",color:"text-white"})])]),e("div",ot,[e("dl",null,[t[23]||(t[23]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," In Attesa ",-1)),e("dd",it,r(P.value),1)])])])])]),h.value?(n(),u("div",{key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:T},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[9]||(t[9]=V(()=>{},["stop"]))},[e("div",lt,[e("h3",nt,r(U()),1),e("form",{onSubmit:V(E,["prevent"])},[e("div",ut,[e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Inizio ",-1)),y(e("input",{"onUpdate:modelValue":t[6]||(t[6]=s=>o.value.start_date=s),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[D,o.value.start_date]])]),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Fine ",-1)),y(e("input",{"onUpdate:modelValue":t[7]||(t[7]=s=>o.value.end_date=s),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[D,o.value.end_date]])]),e("div",null,[e("label",ct,r(l.value==="smartworking"?"Note (opzionale)":"Motivo"),1),y(e("textarea",{"onUpdate:modelValue":t[8]||(t[8]=s=>o.value.notes=s),rows:"3",required:l.value!=="smartworking",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:l.value==="smartworking"?"Note aggiuntive...":"Descrivi il motivo della richiesta..."},null,8,gt),[[D,o.value.notes]])])]),e("div",mt,[e("button",{type:"button",onClick:T,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:k.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},r(k.value?"Invio...":"Invia Richiesta"),9,xt)])],32)])])])):z("",!0)]))}};export{ht as default};
