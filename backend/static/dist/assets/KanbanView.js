import{H as K}from"./app.js";import{c as _,b as n,o as l,j as r,t as d,n as y,F as g,p as h,l as m,K as p,e as k}from"./vendor.js";const S={class:"bg-white rounded-lg shadow overflow-hidden"},w={class:"p-4 border-b border-gray-200"},I={class:"text-lg font-semibold text-gray-900"},N={class:"p-6"},C={class:"flex items-center justify-between mb-4"},B={class:"flex items-center space-x-2"},M={class:"font-medium text-gray-900"},D={class:"bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full"},V={class:"bg-white rounded-lg border border-gray-200 p-4 cursor-pointer hover:shadow-md transition-shadow"},A={class:"font-medium text-gray-900 text-sm mb-2"},j={key:0,class:"text-sm text-gray-600 mb-2"},q={class:"text-xs text-gray-500"},z={key:0,class:"text-center py-8"},F={class:"text-sm text-gray-500"},$={__name:"KanbanView",props:{title:{type:String,default:"Kanban Board"},stages:{type:Array,required:!0,validator:e=>e.every(c=>c.name&&(c.key||c.status))},items:{type:Array,required:!0},stageKey:{type:String,default:"status"},itemKey:{type:String,default:"id"},titleKey:{type:String,default:"title"},descriptionKey:{type:String,default:"description"},dateKey:{type:String,default:"created_at"},columns:{type:Number,default:null},columnClass:{type:String,default:""},emptyMessage:{type:String,default:"Nessun elemento"}},emits:["item-click","stage-change","item-drop"],setup(e,{emit:c}){const a=e,x=_(()=>{const t=a.columns||Math.min(a.stages.length,5);return{1:"grid-cols-1",2:"grid-cols-1 md:grid-cols-2",3:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3",4:"grid-cols-1 md:grid-cols-2 lg:grid-cols-4",5:"grid-cols-1 md:grid-cols-3 lg:grid-cols-5"}[t]||"grid-cols-1 md:grid-cols-3 lg:grid-cols-5"}),u=t=>a.items.filter(i=>i[a.stageKey]===t),f=t=>u(t).length,b=t=>a.items.findIndex(i=>i[a.itemKey]===t[a.itemKey]),v=t=>t?new Date(t).toLocaleDateString("it-IT"):"N/A";return(t,i)=>(l(),n("div",S,[r("div",w,[r("h2",I,d(e.title),1)]),r("div",N,[r("div",{class:y(["grid gap-6",x.value])},[(l(!0),n(g,null,h(e.stages,s=>(l(),n("div",{key:s.key||s.status,class:"bg-gray-50 rounded-lg p-4"},[r("div",C,[r("div",B,[s.color?(l(),n("div",{key:0,class:y(["w-3 h-3 rounded-full",s.color])},null,2)):m("",!0),r("h3",M,d(s.name),1),r("span",D,d(f(s.key||s.status)),1)])]),r("div",{class:y(["space-y-3",e.columnClass])},[(l(!0),n(g,null,h(u(s.key||s.status),o=>p(t.$slots,"item",{key:o[e.itemKey],item:o,stage:s,index:b(o)},()=>[r("div",V,[r("h4",A,d(o[e.titleKey]||o.title||o.name),1),o[e.descriptionKey]?(l(),n("p",j,d(o[e.descriptionKey]),1)):m("",!0),r("div",q,d(o[e.dateKey]?v(o[e.dateKey]):""),1)])])),128)),u(s.key||s.status).length===0?(l(),n("div",z,[p(t.$slots,"empty-state",{stage:s},()=>[k(K,{name:"document-text",size:"lg",class:"mx-auto mb-2 text-gray-300"}),r("p",F,d(e.emptyMessage||"Nessun elemento"),1)])])):m("",!0)],2)]))),128))],2)])]))}};export{$ as _};
