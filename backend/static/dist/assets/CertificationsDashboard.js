import{u as S}from"./certifications.js";import{D as A}from"./DashboardTemplate.js";import{_ as I,H as N}from"./app.js";import{S as B}from"./StandardButton.js";import{h as V,k as l,r as _,c as j,x as T,f as p,o,j as t,b as n,e as d,s as x,F as w,p as C,t as i,n as D,l as F}from"./vendor.js";const H={name:"CertificationsDashboard",components:{DashboardTemplate:A,HeroIcon:N,StandardButton:B},setup(){const u=S(),e=_(!0),g=_({}),r=_([]),f=_([]),h=j(()=>[{label:"Certificazioni Attive",value:g.value.active_certifications||0,icon:"shield-check",color:"green"},{label:"In Scadenza",value:g.value.expiring_soon||0,icon:"exclamation-triangle",color:"orange"},{label:"Compliance Score",value:`${g.value.compliance_score||0}%`,icon:"chart-bar",color:"blue"},{label:"Costo Annuale",value:`€${y(g.value.total_annual_cost||0)}`,icon:"currency-euro",color:"purple"}]),c=async()=>{try{e.value=!0;const s=await u.getOverview();g.value=s.metrics||{},r.value=s.certifications||[],f.value=s.upcoming_actions||[]}catch(s){console.error("Errore nel caricamento dashboard certificazioni:",s)}finally{e.value=!1}},y=s=>s?new Intl.NumberFormat("it-IT",{minimumFractionDigits:0,maximumFractionDigits:0}).format(s):"0",m=s=>s?new Date(s).toLocaleDateString("it-IT"):"N/A",b=s=>({active:"bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200",expired:"bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200",in_renewal:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200",suspended:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"})[s]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",a=s=>({active:"Attiva",expired:"Scaduta",in_renewal:"In Rinnovo",suspended:"Sospesa"})[s]||s,k=s=>({renewal_due:"exclamation-triangle",audit_scheduled:"calendar"})[s]||"info",z=s=>({high:"h-5 w-5 text-red-500 dark:text-red-400",medium:"h-5 w-5 text-orange-500 dark:text-orange-400",low:"h-5 w-5 text-primary-500 dark:text-primary-400"})[s]||"h-5 w-5 text-gray-500 dark:text-gray-400";return T(()=>{c()}),{loading:e,metrics:g,certifications:r,upcomingActions:f,statsData:h,loadDashboardData:c,formatCurrency:y,formatDate:m,getStatusBadgeClass:b,getStatusLabel:a,getActionIcon:k,getActionIconClass:z}}},E={class:"flex space-x-2"},L={class:"space-y-6"},R={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"},P={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"},G={key:0,class:"flex justify-center py-8"},M={key:1,class:"p-6 text-center text-gray-500 dark:text-gray-400"},O={class:"text-center py-12"},q={class:"flex justify-center space-x-3"},J={key:2,class:"divide-y divide-gray-200 dark:divide-gray-700"},K=["onClick"],Q={class:"flex items-start justify-between"},U={class:"flex-1"},W={class:"flex items-center space-x-3"},X={class:"font-semibold text-gray-900 dark:text-white"},Y={class:"text-sm text-gray-600 dark:text-gray-400 mt-1"},Z={class:"flex items-center space-x-4 mt-2 text-sm text-gray-500 dark:text-gray-400"},$={key:0},tt={class:"flex items-center space-x-4"},et={class:"text-center"},at={class:"text-sm font-semibold text-gray-900 dark:text-white"},st={class:"text-center"},rt={class:"text-sm font-semibold text-gray-900 dark:text-white"},ot={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"},it={key:0,class:"p-6 text-center text-gray-500 dark:text-gray-400"},nt={key:1,class:"divide-y divide-gray-200 dark:divide-gray-700"},dt={class:"flex items-center space-x-4"},ct={class:"flex-shrink-0"},lt={class:"flex-1"},gt={class:"flex items-center justify-between"},xt={class:"font-medium text-gray-900 dark:text-white"},mt={class:"text-sm text-gray-500 dark:text-gray-400"},yt={class:"text-sm text-gray-600 dark:text-gray-400 mt-1"},_t={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"};function pt(u,e,g,r,f,h){const c=p("StandardButton"),y=p("router-link"),m=p("HeroIcon"),b=p("DashboardTemplate");return o(),V(b,{title:"Dashboard Certificazioni",subtitle:"Gestisci e monitora le certificazioni aziendali",stats:r.statsData,onRefresh:r.loadDashboardData},{"header-actions":l(()=>[t("div",E,[d(c,{variant:"secondary",icon:"chart-bar",to:"/app/certifications/readiness"},{default:l(()=>e[0]||(e[0]=[x(" Valuta Preparazione ")])),_:1,__:[0]}),d(c,{variant:"secondary",icon:"book-open",to:"/app/certifications/catalog"},{default:l(()=>e[1]||(e[1]=[x(" Esplora Catalogo ")])),_:1,__:[1]}),d(c,{variant:"primary",icon:"plus",to:"/app/certifications/create"},{default:l(()=>e[2]||(e[2]=[x(" Nuova Certificazione ")])),_:1,__:[2]})])]),widget:l(()=>[t("div",L,[t("div",R,[t("div",P,[e[4]||(e[4]=t("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Certificazioni Attive",-1)),d(y,{to:"/app/certifications/list",class:"text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium"},{default:l(()=>e[3]||(e[3]=[x(" Vedi tutte → ")])),_:1,__:[3]})]),r.loading?(o(),n("div",G,[d(m,{name:"arrow-path",class:"h-8 w-8 text-primary-600 dark:text-primary-400 animate-spin"})])):r.certifications.length===0?(o(),n("div",M,[t("div",O,[d(m,{name:"shield-check",class:"h-12 w-12 text-gray-400 dark:text-gray-600 mx-auto mb-4"}),e[7]||(e[7]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessuna Certificazione",-1)),e[8]||(e[8]=t("p",{class:"text-gray-500 dark:text-gray-400 mb-6"},"Inizia subito creando la tua prima certificazione aziendale",-1)),t("div",q,[d(c,{variant:"secondary",icon:"book-open",to:"/app/certifications/catalog"},{default:l(()=>e[5]||(e[5]=[x(" Esplora Catalogo ")])),_:1,__:[5]}),d(c,{variant:"primary",icon:"chart-bar",to:"/app/certifications/readiness"},{default:l(()=>e[6]||(e[6]=[x(" Valuta Preparazione ")])),_:1,__:[6]})])])])):(o(),n("div",J,[(o(!0),n(w,null,C(r.certifications.slice(0,5),a=>(o(),n("div",{key:a.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors cursor-pointer",onClick:k=>u.$router.push(`/app/certifications/${a.id}`)},[t("div",Q,[t("div",U,[t("div",W,[t("h4",X,i(a.standard_name),1),t("span",{class:D([r.getStatusBadgeClass(a.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},i(r.getStatusLabel(a.status)),3)]),t("p",Y,i(a.certifying_body),1),t("div",Z,[t("span",null,"Scadenza: "+i(r.formatDate(a.expiry_date)),1),a.responsible_person?(o(),n("span",$," Responsabile: "+i(a.responsible_person.name),1)):F("",!0)])]),t("div",tt,[t("div",et,[t("div",at,i(a.health_score||"N/A")+"%",1),e[9]||(e[9]=t("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"Health Score",-1))]),t("div",st,[t("div",rt,i(r.formatDate(a.next_audit_date)),1),e[10]||(e[10]=t("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"Prossimo Audit",-1))])])])],8,K))),128))]))]),t("div",ot,[e[11]||(e[11]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Azioni in Scadenza")],-1)),r.upcomingActions.length===0?(o(),n("div",it," Nessuna azione in scadenza ")):(o(),n("div",nt,[(o(!0),n(w,null,C(r.upcomingActions.slice(0,5),a=>(o(),n("div",{key:a.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"},[t("div",dt,[t("div",ct,[d(m,{name:r.getActionIcon(a.type),class:D(r.getActionIconClass(a.priority))},null,8,["name","class"])]),t("div",lt,[t("div",gt,[t("h4",xt,i(a.title),1),t("span",mt,i(r.formatDate(a.due_date)),1)]),t("p",yt,i(a.description),1),t("p",_t,i(a.certification_name),1)])])]))),128))]))])])]),_:1},8,["stats","onRefresh"])}const kt=I(H,[["render",pt]]);export{kt as default};
