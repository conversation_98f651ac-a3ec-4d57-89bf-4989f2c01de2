import{_ as o}from"./app.js";import{c,b as a,o as l}from"./vendor.js";const s={name:"MarkdownContent",props:{content:{type:String,required:!0}},setup(r){return{renderedContent:c(()=>{let e=r.content;return e=e.replace(/^### (.*$)/gim,"<h3>$1</h3>"),e=e.replace(/^## (.*$)/gim,"<h2>$1</h2>"),e=e.replace(/^# (.*$)/gim,"<h1>$1</h1>"),e=e.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>"),e=e.replace(/\*(.*?)\*/g,"<em>$1</em>"),e=e.replace(/^• (.*$)/gim,"<li>$1</li>"),e=e.replace(/(<li>.*?<\/li>\s*)+/gs,"<ul>$&</ul>"),e=e.replace(/\n/g,"<br>"),e=e.replace(/<br>\s*(<[uh][1-6l]>)/g,"$1"),e=e.replace(/(<\/[uh][1-6l]>)\s*<br>/g,"$1"),e=e.replace(/<br>\s*(<\/?ul>)/g,"$1"),e=e.replace(/(<\/?ul>)\s*<br>/g,"$1"),e})}}},p=["innerHTML"];function i(r,n,e,t,d,$){return l(),a("div",{class:"markdown-content",innerHTML:t.renderedContent},null,8,p)}const u=o(s,[["render",i],["__scopeId","data-v-91f1c0a0"]]);export{u as M};
