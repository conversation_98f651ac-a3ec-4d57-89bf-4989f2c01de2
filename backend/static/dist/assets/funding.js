import{d as de,r as c,c as _}from"./vendor.js";import{c as i}from"./app.js";const he=de("funding",()=>{const u=c([]),g=c([]),p=c([]),f=c(null),z=c(null),x=c({}),S=c({}),v=c([]),b=c(!1),s=c(!1),r=c(null),O=c({}),I=c({ai_service_available:!1,perplexity_configured:!1}),D=_(()=>u.value.filter(a=>a.status==="open"&&!a.is_expired)),M=_(()=>u.value.filter(a=>a.is_deadline_approaching)),C=_(()=>g.value.filter(a=>a.created_by===a.current_user_id||a.project_manager_id===a.current_user_id)),$=_(()=>{const a={};return g.value.forEach(e=>{a[e.status]||(a[e.status]=[]),a[e.status].push(e)}),a});async function k(a={}){s.value=!0,r.value=null;try{const e=await i.get("/api/funding/opportunities",{params:a});if(e.data.success)return u.value=e.data.data.opportunities,e.data.data;throw new Error(e.data.message||"Errore nel caricamento delle opportunità")}catch(e){throw r.value=e.message,console.error("Error fetching funding opportunities:",e),e}finally{s.value=!1}}async function L(a){s.value=!0,r.value=null;try{const e=await i.get(`/api/funding/opportunities/${a}`);if(e.data.success)return f.value=e.data.data.opportunity,e.data.data.opportunity;throw new Error(e.data.message||"Opportunità non trovata")}catch(e){throw r.value=e.message,console.error("Error fetching funding opportunity:",e),e}finally{s.value=!1}}async function N(a){s.value=!0,r.value=null;try{const e=await i.post("/api/funding/opportunities",a);if(e.data.success){const t=e.data.data.opportunity;return u.value.unshift(t),t}else throw new Error(e.data.message||"Errore nella creazione dell'opportunità")}catch(e){throw r.value=e.message,console.error("Error creating funding opportunity:",e),e}finally{s.value=!1}}async function T(a,e){var t;s.value=!0,r.value=null;try{const n=await i.put(`/api/funding/opportunities/${a}`,e);if(n.data.success){const o=n.data.data.opportunity,l=u.value.findIndex(d=>d.id===a);return l!==-1&&(u.value[l]=o),((t=f.value)==null?void 0:t.id)===a&&(f.value=o),o}else throw new Error(n.data.message||"Errore nell'aggiornamento dell'opportunità")}catch(n){throw r.value=n.message,console.error("Error updating funding opportunity:",n),n}finally{s.value=!1}}async function U(a){var e;s.value=!0,r.value=null;try{const t=await i.delete(`/api/funding/opportunities/${a}`);if(t.data.success)return u.value=u.value.filter(n=>n.id!==a),((e=f.value)==null?void 0:e.id)===a&&(f.value=null),!0;throw new Error(t.data.message||"Errore nell'eliminazione dell'opportunità")}catch(t){throw r.value=t.message,console.error("Error deleting funding opportunity:",t),t}finally{s.value=!1}}async function W(a){try{const e=await i.post("/api/funding/opportunities/check-duplicate",{title:a});if(e.data.success)return e.data.data;throw new Error(e.data.message||"Errore nel controllo duplicati")}catch(e){throw console.error("Error checking duplicate opportunity:",e),e}}async function q(a={}){s.value=!0,r.value=null;try{const e=await i.get("/api/funding/applications",{params:a});if(e.data.success)return g.value=e.data.data.applications,e.data.data;throw new Error(e.data.message||"Errore nel caricamento delle candidature")}catch(e){throw r.value=e.message,console.error("Error fetching funding applications:",e),e}finally{s.value=!1}}async function j(a){s.value=!0,r.value=null;try{const e=await i.post("/api/funding/applications",a);if(e.data.success){const t=e.data.data.application;return g.value.unshift(t),t}else throw new Error(e.data.message||"Errore nella creazione della candidatura")}catch(e){throw r.value=e.message,console.error("Error creating funding application:",e),e}finally{s.value=!1}}async function H(a){s.value=!0,r.value=null;try{const e=await i.post(`/api/funding/applications/${a}/submit`);if(e.data.success){const t=e.data.data.application,n=g.value.findIndex(o=>o.id===a);return n!==-1&&(g.value[n]=t),t}else throw new Error(e.data.message||"Errore nella sottomissione della candidatura")}catch(e){throw r.value=e.message,console.error("Error submitting funding application:",e),e}finally{s.value=!1}}async function B(){s.value=!0,r.value=null;try{const a=await i.get("/api/funding/dashboard/stats");if(a.data.success)return x.value=a.data.data.stats,a.data.data.stats;throw new Error(a.data.message||"Errore nel caricamento delle statistiche")}catch(a){throw r.value=a.message,console.error("Error fetching funding dashboard stats:",a),a}finally{s.value=!1}}async function G(a=10){s.value=!0,r.value=null;try{const e=await i.get("/api/funding/dashboard/recent",{params:{limit:a}});if(e.data.success)return S.value=e.data.data,e.data.data;throw new Error(e.data.message||"Errore nel caricamento delle attività recenti")}catch(e){throw r.value=e.message,console.error("Error fetching funding recent activity:",e),e}finally{s.value=!1}}async function J(a,e=null){s.value=!0,r.value=null;try{const t={company_profile:a,search_criteria:e},n=await i.post("/api/funding/ai-search",t);if(n.data.success){const o=n.data.data;return u.value=o.opportunities||[],O.value={content:o.ai_content,citations:o.citations,stats:o.stats,search_performed:o.search_performed,model_used:o.model_used,search_timestamp:new Date().toISOString()},o}else throw new Error(n.data.message||"Errore nella ricerca AI")}catch(t){throw r.value=t.message,console.error("Error in AI search:",t),t}finally{s.value=!1}}async function F(a){s.value=!0,r.value=null;try{const e=await i.post("/api/funding/ai-suggestions",{company_profile:a});if(e.data.success){const t=e.data.data;return v.value=t.suggestions||[],t}else throw new Error(e.data.message||"Errore nel generare suggerimenti AI")}catch(e){throw r.value=e.message,console.error("Error getting AI suggestions:",e),e}finally{s.value=!1}}async function K(a,e){try{const t=await i.post("/api/funding/ai-match-score",{opportunity:a,company_profile:e});if(t.data.success)return t.data.data;throw new Error(t.data.message||"Errore nel calcolo match score")}catch(t){return console.error("Error calculating AI match score:",t),{match_score:50,insights:["Errore nel calcolo"]}}}async function V(){try{const a=await i.get("/api/funding/ai-status");if(a.data.success)return a.data.data;throw new Error(a.data.message||"Servizio AI non disponibile")}catch(a){return console.error("Error checking AI service status:",a),{ai_service_available:!1,perplexity_configured:!1,error:a.message}}}function Y(a,e={}){let t=0,n=0;const o=35;if(n+=o,a.target_sectors&&e.sectors){const w=a.target_sectors.filter(y=>e.sectors.includes(y)).length/Math.max(a.target_sectors.length,1);t+=w*o}const l=20;if(n+=l,a.company_size_requirements&&e.company_size){const m=a.company_size_requirements;(m.includes(e.company_size)||m.includes("any"))&&(t+=l)}else t+=l*.5;const d=25;if(n+=d,a.min_grant_amount&&a.max_grant_amount&&e.budget_range){const m=a.min_grant_amount,w=a.max_grant_amount,[y,A]=e.budget_range;if(A>=m&&y<=w){const ie=Math.max(m,y),le=Math.min(w,A)-ie,ce=Math.max(w-m,A-y),ue=le/ce;t+=d*ue}}else t+=d*.3;const h=20;n+=h,a.status==="open"&&!a.is_expired?a.is_deadline_approaching?t+=h*.6:t+=h:a.status==="announced"&&(t+=h*.8);const E=10;a.target_company_types&&e.company_type&&a.target_company_types.includes(e.company_type)&&(t+=E*.5),a.geographic_scope&&e.location&&(a.geographic_scope==="locale"&&a.location===e.location?t+=E*.5:a.geographic_scope==="regionale"&&a.region===e.region?t+=E*.3:["nazionale","europeo","internazionale"].includes(a.geographic_scope)&&(t+=E*.2));const oe=n>0?t/n*100:0;return Math.min(Math.max(Math.round(oe),0),100)}function Q(){u.value=[],g.value=[],f.value=null,z.value=null,x.value={},S.value={},s.value=!1,r.value=null}function X(a){let e=[...u.value];return a.status&&(e=e.filter(t=>t.status===a.status)),a.source_entity&&(e=e.filter(t=>{var n;return(n=t.source_entity)==null?void 0:n.toLowerCase().includes(a.source_entity.toLowerCase())})),a.geographic_scope&&(e=e.filter(t=>t.geographic_scope===a.geographic_scope)),a.max_amount&&(e=e.filter(t=>t.max_grant_amount&&t.max_grant_amount<=a.max_amount)),a.deadline_approaching&&(e=e.filter(t=>t.is_deadline_approaching)),e}async function Z(a={}){s.value=!0,r.value=null;try{const e=await i.get("/api/funding/expenses",{params:a});if(e.data.success)return p.value=e.data.data.expenses,p.value;throw new Error(e.data.message||"Errore nel caricamento delle spese")}catch(e){throw r.value=e.message,console.error("Error fetching funding expenses:",e),e}finally{s.value=!1}}async function P(a){s.value=!0,r.value=null;try{const e=await i.post("/api/funding/expenses",a);if(e.data.success){const t=e.data.data.expense;return p.value.unshift(t),t}else throw new Error(e.data.message||"Errore nella creazione della spesa")}catch(e){throw r.value=e.message,console.error("Error creating funding expense:",e),e}finally{s.value=!1}}async function ee(a){s.value=!0,r.value=null;try{const e=await i.get(`/api/funding/expenses/${a}`);if(e.data.success)return e.data.data.expense;throw new Error(e.data.message||"Spesa non trovata")}catch(e){throw r.value=e.message,console.error("Error fetching funding expense:",e),e}finally{s.value=!1}}async function ae(a,e){s.value=!0,r.value=null;try{const t=await i.put(`/api/funding/expenses/${a}`,e);if(t.data.success){const n=t.data.data.expense,o=p.value.findIndex(l=>l.id===a);return o!==-1&&(p.value[o]=n),n}else throw new Error(t.data.message||"Errore nell'aggiornamento della spesa")}catch(t){throw r.value=t.message,console.error("Error updating funding expense:",t),t}finally{s.value=!1}}async function te(a){s.value=!0,r.value=null;try{const e=await i.delete(`/api/funding/expenses/${a}`);if(e.data.success)return p.value=p.value.filter(t=>t.id!==a),!0;throw new Error(e.data.message||"Errore nell'eliminazione della spesa")}catch(e){throw r.value=e.message,console.error("Error deleting funding expense:",e),e}finally{s.value=!1}}async function R(a=!1){const e="funding_ai_suggestions";try{if(!a){const o=localStorage.getItem(e);if(o){const l=JSON.parse(o),d=new Date().getTime();if(l.timestamp&&d-l.timestamp<144e5)return v.value=l.data.suggestions||[],l.data}}b.value=!0;const n=await i.get("/api/funding/ai-dashboard-suggestions");if(n.data.success){const o=n.data.data;v.value=o.suggestions||[];const l={timestamp:new Date().getTime(),data:o};return localStorage.setItem(e,JSON.stringify(l)),o}else throw new Error(n.data.message||"Errore nel caricamento suggerimenti AI")}catch(n){console.error("❌ Error fetching AI suggestions:",n),r.value=n.message||"Errore nel caricamento suggerimenti AI";const o={suggestions:[{title:"Monitora bandi PNRR aggiornati",description:"Controlla regolarmente i nuovi bandi del Piano Nazionale di Ripresa e Resilienza.",priority:"high",action:"Visita il portale PA digitale 2026",category:"ricerca"},{title:"Organizza documentazione aziendale",description:"Prepara i documenti standard per accelerare le future candidature.",priority:"medium",action:"Crea cartelle digitali per bilanci e business plan",category:"candidatura"}],generated_at:new Date().toISOString()};return v.value=o.suggestions,o}finally{b.value=!1}}async function re(){return await R(!0)}async function ne(a){s.value=!0,r.value=null;try{const e=await i.get(`/api/funding/reporting/${a}`);if(e.data.success)return e.data.data;throw new Error(e.data.message||"Errore nel caricamento dati rendicontazione")}catch(e){throw console.error("❌ Error fetching reporting data:",e),r.value=e.message||"Errore nel caricamento dati rendicontazione",e}finally{s.value=!1}}async function se(a){try{const e=await i.get(`/api/funding/export/${a}`,{responseType:"blob"}),t=new Blob([e.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),n=window.URL.createObjectURL(t),o=document.createElement("a");o.href=n;const l=e.headers["content-disposition"];let d=`Rendicontazione_${a}_${new Date().toISOString().split("T")[0]}.xlsx`;if(l){const h=l.match(/filename="(.+)"/);h&&(d=h[1])}return o.download=d,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(n),{success:!0,filename:d}}catch(e){throw console.error("❌ Error exporting report:",e),r.value=e.message||"Errore nell'esportazione del report",e}}return{opportunities:u,applications:g,expenses:p,currentOpportunity:f,currentApplication:z,dashboardStats:x,recentActivity:S,aiSuggestions:v,aiSuggestionsLoading:b,loading:s,error:r,aiSearchResults:O,aiServiceStatus:I,openOpportunities:D,deadlineApproachingOpportunities:M,userApplications:C,applicationsByStatus:$,fetchOpportunities:k,fetchOpportunity:L,createOpportunity:N,updateOpportunity:T,deleteOpportunity:U,checkDuplicateOpportunity:W,fetchApplications:q,createApplication:j,submitApplication:H,fetchDashboardStats:B,fetchRecentActivity:G,fetchExpenses:Z,createExpense:P,fetchExpense:ee,updateExpense:ae,deleteExpense:te,searchOpportunitiesWithAI:J,getAISuggestions:F,calculateAIMatchScore:K,checkAIServiceStatus:V,fetchAIDashboardSuggestions:R,refreshAISuggestions:re,fetchReportingData:ne,exportReport:se,calculateMatchScore:Y,filterOpportunities:X,resetStore:Q}});export{he as u};
