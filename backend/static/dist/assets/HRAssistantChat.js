import{r as A,c as y,x as T,b as s,o as a,j as e,e as n,l as c,F as b,p as m,t as u,n as H,s as $,A as V,B as j,C as B,z as I}from"./vendor.js";import{u as E,_ as U}from"./ConfidenceBadge.js";import{H as d}from"./app.js";const D={class:"flex flex-col h-full bg-white dark:bg-gray-900 rounded-lg shadow-lg"},G={class:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700"},P={class:"flex items-center space-x-3"},Q={class:"flex-shrink-0"},q={class:"flex items-center space-x-2"},J={key:0,class:"text-center py-8"},K={class:"grid grid-cols-2 md:grid-cols-3 gap-3 max-w-2xl mx-auto"},O=["onClick"],W={class:"text-sm font-medium text-gray-700 dark:text-gray-300"},X={key:0,class:"max-w-xs lg:max-w-md bg-blue-600 text-white rounded-lg p-3"},Y={key:1,class:"max-w-xs lg:max-w-2xl bg-gray-100 dark:bg-gray-800 rounded-lg p-4"},Z={class:"flex items-center space-x-2 mb-2"},ee={class:"text-xs font-medium text-gray-500 dark:text-gray-400"},te=["innerHTML"],re={key:0,class:"mt-3 pt-3 border-t border-gray-200 dark:border-gray-600"},se={class:"space-y-1"},ae=["onClick"],oe={key:1,class:"mt-3 pt-3 border-t border-gray-200 dark:border-gray-600 flex items-center space-x-2"},ne=["onClick"],le=["onClick"],ie={key:2,class:"mt-2 text-xs text-gray-500 dark:text-gray-400"},de={key:1,class:"flex justify-start"},ce={class:"border-t border-gray-200 dark:border-gray-700 p-4"},ue={class:"flex-1 relative"},xe=["disabled"],ge={class:"absolute right-3 top-2 text-xs text-gray-400"},pe=["disabled"],ye={key:0,class:"mt-2 text-sm text-red-600 dark:text-red-400"},be={__name:"HRAssistantChat",setup(S){const l=E(),o=A(""),g=A(null),v=y(()=>l.currentConversation),h=y(()=>l.categoriesList),p=y(()=>l.loading),f=y(()=>l.error);async function k(){if(!o.value.trim()||p.value)return;const i=o.value.trim();o.value="",(await l.sendMessage(i)).success&&(await I(),C())}async function _(i){o.value=i,await k()}function z(i){const r=h.value.find(t=>t.key===i);return(r==null?void 0:r.label)||"Generale"}function M(i){return i.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/`(.*?)`/g,'<code class="bg-gray-200 dark:bg-gray-700 px-1 rounded">$1</code>').replace(/\n/g,"<br>")}async function w(i,r){await l.provideFeedback(i,r)}function F(){l.startNewSession()}function N(){confirm("Sei sicuro di voler cancellare tutta la cronologia delle conversazioni?")&&l.clearConversations()}function L(){l.clearError()}function C(){g.value&&(g.value.scrollTop=g.value.scrollHeight)}return T(()=>{C()}),(i,r)=>(a(),s("div",D,[e("div",G,[e("div",P,[e("div",Q,[n(d,{name:"chat-bubble-left-right",class:"h-8 w-8 text-primary-600 dark:text-primary-400"})]),r[1]||(r[1]=e("div",null,[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," Assistente HR "),e("p",{class:"text-sm text-gray-500 dark:text-gray-400"}," Supporto per procedure e informazioni aziendali ")],-1))]),e("div",q,[e("button",{onClick:F,class:"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800",title:"Nuova conversazione"},[n(d,{name:"plus",class:"h-5 w-5"})]),e("button",{onClick:N,class:"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800",title:"Cancella cronologia"},[n(d,{name:"trash",class:"h-5 w-5"})])])]),e("div",{ref_key:"messagesContainer",ref:g,class:"flex-1 overflow-y-auto p-4 space-y-4"},[v.value.length===0?(a(),s("div",J,[n(d,{name:"academic-cap",class:"h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto mb-4"}),r[2]||(r[2]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"}," Ciao! Sono il tuo Assistente HR ",-1)),r[3]||(r[3]=e("p",{class:"text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto"}," Posso aiutarti con informazioni su contratti, ferie, permessi, benefit e molto altro. ",-1)),e("div",K,[(a(!0),s(b,null,m(h.value.slice(0,6),t=>(a(),s("button",{key:t.key,onClick:x=>_(`Vorrei informazioni su ${t.label.toLowerCase()}`),class:"flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"},[n(d,{name:t.icon,class:"h-5 w-5 text-primary-600 dark:text-primary-400"},null,8,["name"]),e("span",W,u(t.label),1)],8,O))),128))])])):c("",!0),(a(!0),s(b,null,m(v.value,t=>(a(),s("div",{key:t.id,class:H(t.type==="user"?"flex justify-end":"flex justify-start")},[t.type==="user"?(a(),s("div",X,u(t.content),1)):(a(),s("div",Y,[e("div",Z,[n(d,{name:"cpu-chip",class:"h-5 w-5 text-primary-600 dark:text-primary-400"}),e("span",ee,u(z(t.category)),1),n(U,{confidence:t.confidence,class:"text-xs"},null,8,["confidence"])]),e("div",{class:"prose prose-sm dark:prose-invert max-w-none",innerHTML:M(t.content)},null,8,te),t.suggestedActions&&t.suggestedActions.length>0?(a(),s("div",re,[r[4]||(r[4]=e("p",{class:"text-xs font-medium text-gray-500 dark:text-gray-400 mb-2"}," Azioni suggerite: ",-1)),e("div",se,[(a(!0),s(b,null,m(t.suggestedActions,(x,R)=>(a(),s("button",{key:R,onClick:he=>_(x),class:"block w-full text-left text-xs text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 p-1 rounded hover:bg-primary-50 dark:hover:bg-primary-900/20"}," • "+u(x),9,ae))),128))])])):c("",!0),t.conversationId&&!t.userFeedback?(a(),s("div",oe,[r[5]||(r[5]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"Utile?",-1)),e("button",{onClick:x=>w(t.conversationId,"helpful"),class:"p-1 text-green-600 hover:text-green-800 hover:bg-green-50 dark:hover:bg-green-900/20 rounded",title:"Utile"},[n(d,{name:"hand-thumb-up",class:"h-4 w-4"})],8,ne),e("button",{onClick:x=>w(t.conversationId,"not_helpful"),class:"p-1 text-red-600 hover:text-red-800 hover:bg-red-50 dark:hover:bg-red-900/20 rounded",title:"Non utile"},[n(d,{name:"hand-thumb-down",class:"h-4 w-4"})],8,le)])):c("",!0),t.userFeedback?(a(),s("div",ie,[n(d,{name:t.userFeedback==="helpful"?"check-circle":"x-circle",class:H([t.userFeedback==="helpful"?"text-green-500":"text-red-500","h-4 w-4 inline mr-1"])},null,8,["name","class"]),r[6]||(r[6]=$(" Feedback ricevuto "))])):c("",!0)]))],2))),128)),p.value?(a(),s("div",de,r[7]||(r[7]=[e("div",{class:"max-w-xs bg-gray-100 dark:bg-gray-800 rounded-lg p-4"},[e("div",{class:"flex items-center space-x-2"},[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Sto elaborando...")])],-1)]))):c("",!0)],512),e("div",ce,[e("form",{onSubmit:V(k,["prevent"]),class:"flex space-x-3"},[e("div",ue,[j(e("input",{"onUpdate:modelValue":r[0]||(r[0]=t=>o.value=t),type:"text",placeholder:"Scrivi la tua domanda...",disabled:p.value,class:"w-full px-4 py-2 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"},null,8,xe),[[B,o.value]]),e("div",ge,u(o.value.length)+"/500 ",1)]),e("button",{type:"submit",disabled:!o.value.trim()||p.value||o.value.length>500,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"},[n(d,{name:"paper-airplane",class:"h-5 w-5"})],8,pe)],32),f.value?(a(),s("div",ye,[$(u(f.value)+" ",1),e("button",{onClick:L,class:"ml-2 text-xs underline hover:no-underline"}," Chiudi ")])):c("",!0)])]))}},me={class:"h-full flex flex-col"},ve={class:"flex-1 min-h-0"},we={__name:"HRAssistantChat",setup(S){return(l,o)=>(a(),s("div",me,[o[0]||(o[0]=e("div",{class:"mb-6"},[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"}," Assistente HR "),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Chatta con l'assistente HR per risolvere dubbi su procedure, benefit e normative aziendali ")],-1)),e("div",ve,[n(be)])]))}};export{we as default};
