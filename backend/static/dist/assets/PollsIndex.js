import{b as d,o as n,j as t,t as u,e as g,f as j,A as E,B as v,C as V,l as p,F as O,p as U,s as D,H as F,Q as I,h as M,r as S,c as z,x as it,k as N,v as P,q as nt,E as at}from"./vendor.js";import{_ as B,i as rt,H as f,e as L,a as dt}from"./app.js";import{u as ut}from"./useFormatters.js";import{_ as mt}from"./ListPageTemplate.js";import{S as ct}from"./StatusBadge.js";import{E as gt}from"./EditPollModal.js";import{P as xt}from"./PollVoteModal.js";import{C as ft}from"./ConfirmationModal.js";import"./formatters.js";import"./Pagination.js";const pt={name:"CreatePollModal",components:{HeroIcon:f,LoadingSpinner:rt},props:{poll:{type:Object,default:null}},emits:["close","created","updated"],data(){return{isSubmitting:!1,form:{title:"",description:"",options:[{text:""},{text:""}],endDate:"",multipleChoice:!1,anonymous:!1,showResults:!0,allowComments:!0}}},computed:{isEditing(){return!!this.poll},isFormValid(){const m=this.form.title.trim().length>0,e=this.form.options.length>=2&&this.form.options.every(b=>b.text.trim().length>0),c=!this.form.endDate||new Date(this.form.endDate)>new Date;return m&&e&&c},minDateTime(){const m=new Date;return m.setMinutes(m.getMinutes()+30),m.toISOString().slice(0,16)}},watch:{poll:{handler(){this.initializeForm()},immediate:!0}},methods:{initializeForm(){var m;this.isEditing&&this.poll?this.form={title:this.poll.title||"",description:this.poll.description||"",options:((m=this.poll.options)==null?void 0:m.map(e=>({text:e.text||e})))||[{text:""},{text:""}],endDate:this.poll.endDate?new Date(this.poll.endDate).toISOString().slice(0,16):"",multipleChoice:this.poll.multipleChoice||!1,anonymous:this.poll.anonymous||!1,showResults:this.poll.showResults!==void 0?this.poll.showResults:!0,allowComments:this.poll.allowComments!==void 0?this.poll.allowComments:!0}:this.form={title:"",description:"",options:[{text:""},{text:""}],endDate:"",multipleChoice:!1,anonymous:!1,showResults:!0,allowComments:!0}},addOption(){this.form.options.length<10&&this.form.options.push({text:""})},removeOption(m){this.form.options.length>2&&this.form.options.splice(m,1)},async submitPoll(){if(!(!this.isFormValid||this.isSubmitting)){this.isSubmitting=!0;try{const m={...this.form,options:this.form.options.map(b=>b.text.trim()),endDate:this.form.endDate?new Date(this.form.endDate).toISOString():null},c=await L().createPoll(m);this.$emit("created",c),this.closeModal()}catch(m){console.error("Errore durante il salvataggio del sondaggio:",m)}finally{this.isSubmitting=!1}}},closeModal(){this.$emit("close"),setTimeout(()=>{this.initializeForm(),this.isSubmitting=!1},300)}}},vt={class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},ht={class:"card w-full max-w-3xl mx-4 max-h-[90vh] overflow-y-auto"},yt={class:"flex items-center justify-between p-6 border-b dark:border-gray-600"},bt={class:"text-lg font-semibold text-gray-900 dark:text-white"},_t={class:"text-right text-xs text-gray-500 dark:text-gray-400 mt-1"},kt={class:"text-right text-xs text-gray-500 dark:text-gray-400 mt-1"},wt={class:"space-y-3"},Ct={class:"flex-1"},St=["onUpdate:modelValue","placeholder"],zt=["onClick"],Dt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Pt=["min"],Mt={class:"space-y-3"},Vt={class:"flex items-center"},Et={class:"flex items-center"},Ot={class:"flex items-center"},It={class:"flex justify-end space-x-3 pt-4 border-t dark:border-gray-600"},Nt=["disabled"];function Ut(m,e,c,b,i,r){const h=j("HeroIcon"),C=j("LoadingSpinner");return n(),d("div",vt,[t("div",ht,[t("div",yt,[t("h3",bt,u(r.isEditing?"Modifica Sondaggio":"Nuovo Sondaggio"),1),t("button",{onClick:e[0]||(e[0]=(...l)=>r.closeModal&&r.closeModal(...l)),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"},[g(h,{name:"x-mark",size:"md"})])]),t("form",{onSubmit:e[10]||(e[10]=E((...l)=>r.submitPoll&&r.submitPoll(...l),["prevent"])),class:"p-6 space-y-6"},[t("div",null,[e[11]||(e[11]=t("label",{for:"title",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Titolo * ",-1)),v(t("input",{id:"title","onUpdate:modelValue":e[1]||(e[1]=l=>i.form.title=l),type:"text",required:"",maxlength:"200",class:"input w-full",placeholder:"Inserisci il titolo del sondaggio"},null,512),[[V,i.form.title]]),t("div",_t,u(i.form.title.length)+"/200 ",1)]),t("div",null,[e[12]||(e[12]=t("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Descrizione ",-1)),v(t("textarea",{id:"description","onUpdate:modelValue":e[2]||(e[2]=l=>i.form.description=l),rows:"3",maxlength:"1000",class:"input-field resize-vertical",placeholder:"Descrizione opzionale del sondaggio"},null,512),[[V,i.form.description]]),t("div",kt,u(i.form.description.length)+"/1000 ",1)]),t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"}," Opzioni di risposta * ",-1)),t("div",wt,[(n(!0),d(O,null,U(i.form.options,(l,x)=>(n(),d("div",{key:x,class:"flex items-center space-x-3"},[t("div",Ct,[v(t("input",{"onUpdate:modelValue":y=>l.text=y,type:"text",placeholder:`Opzione ${x+1}`,maxlength:"200",class:"input w-full",required:""},null,8,St),[[V,l.text]])]),i.form.options.length>2?(n(),d("button",{key:0,type:"button",onClick:y=>r.removeOption(x),class:"text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-1"},[g(h,{name:"trash",size:"sm"})],8,zt)):p("",!0)]))),128))]),i.form.options.length<10?(n(),d("button",{key:0,type:"button",onClick:e[3]||(e[3]=(...l)=>r.addOption&&r.addOption(...l)),class:"btn-secondary mt-3"},[g(h,{name:"plus",size:"sm",class:"mr-2"}),e[13]||(e[13]=D(" Aggiungi opzione "))])):p("",!0),e[15]||(e[15]=t("div",{class:"text-xs text-gray-500 dark:text-gray-400 mt-2"}," Minimo 2 opzioni, massimo 10 ",-1))]),t("div",Dt,[t("div",null,[e[16]||(e[16]=t("label",{for:"endDate",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data di scadenza ",-1)),v(t("input",{id:"endDate","onUpdate:modelValue":e[4]||(e[4]=l=>i.form.endDate=l),type:"datetime-local",min:r.minDateTime,class:"input w-full"},null,8,Pt),[[V,i.form.endDate]]),e[17]||(e[17]=t("div",{class:"text-xs text-gray-500 dark:text-gray-400 mt-1"}," Lascia vuoto per sondaggio senza scadenza ",-1))]),t("div",null,[e[19]||(e[19]=t("label",{for:"multipleChoice",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tipo di voto ",-1)),v(t("select",{id:"multipleChoice","onUpdate:modelValue":e[5]||(e[5]=l=>i.form.multipleChoice=l),class:"input w-full"},e[18]||(e[18]=[t("option",{value:!1},"Scelta singola",-1),t("option",{value:!0},"Scelta multipla",-1)]),512),[[F,i.form.multipleChoice]])])]),t("div",Mt,[e[23]||(e[23]=t("h4",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Opzioni avanzate",-1)),t("div",Vt,[v(t("input",{id:"anonymous","onUpdate:modelValue":e[6]||(e[6]=l=>i.form.anonymous=l),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"},null,512),[[I,i.form.anonymous]]),e[20]||(e[20]=t("label",{for:"anonymous",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Voto anonimo ",-1))]),t("div",Et,[v(t("input",{id:"showResults","onUpdate:modelValue":e[7]||(e[7]=l=>i.form.showResults=l),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"},null,512),[[I,i.form.showResults]]),e[21]||(e[21]=t("label",{for:"showResults",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Mostra risultati in tempo reale ",-1))]),t("div",Ot,[v(t("input",{id:"allowComments","onUpdate:modelValue":e[8]||(e[8]=l=>i.form.allowComments=l),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"},null,512),[[I,i.form.allowComments]]),e[22]||(e[22]=t("label",{for:"allowComments",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Permetti commenti ",-1))])]),t("div",It,[t("button",{type:"button",onClick:e[9]||(e[9]=(...l)=>r.closeModal&&r.closeModal(...l)),class:"btn-secondary"}," Annulla "),t("button",{type:"submit",disabled:i.isSubmitting||!r.isFormValid,class:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center"},[i.isSubmitting?(n(),M(C,{key:0,class:"mr-2",size:"sm"})):p("",!0),D(" "+u(i.isSubmitting?"Salvataggio...":r.isEditing?"Aggiorna":"Crea Sondaggio"),1)],8,Nt)])],32)])])}const At=B(pt,[["render",Ut],["__scopeId","data-v-b90bd55f"]]),Rt={class:"grid grid-cols-1 gap-5 sm:grid-cols-4"},Tt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},jt={class:"p-5"},Ft={class:"flex items-center"},Bt={class:"flex-shrink-0"},Lt={class:"ml-5 w-0 flex-1"},$t={class:"text-lg font-medium text-gray-900 dark:text-white"},Ht={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},qt={class:"p-5"},Gt={class:"flex items-center"},Qt={class:"flex-shrink-0"},Jt={class:"ml-5 w-0 flex-1"},Kt={class:"text-lg font-medium text-gray-900 dark:text-white"},Wt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Xt={class:"p-5"},Yt={class:"flex items-center"},Zt={class:"flex-shrink-0"},te={class:"ml-5 w-0 flex-1"},ee={class:"text-lg font-medium text-gray-900 dark:text-white"},se={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},oe={class:"p-5"},le={class:"flex items-center"},ie={class:"flex-shrink-0"},ne={class:"ml-5 w-0 flex-1"},ae={class:"text-lg font-medium text-gray-900 dark:text-white"},re={class:"flex space-x-4"},de={key:0,class:"flex justify-center items-center h-64"},ue={key:1,class:"text-center py-12"},me={key:2,class:"p-6"},ce={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"},ge=["onClick"],xe={class:"p-6"},fe={class:"flex items-start justify-between mb-3"},pe={class:"text-lg font-semibold text-gray-900 dark:text-white line-clamp-2"},ve={class:"text-gray-600 dark:text-gray-400 text-sm line-clamp-2 mb-4"},he={class:"mb-4"},ye={class:"flex justify-between text-sm text-gray-500 dark:text-gray-400 mb-2"},be={key:0},_e={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},ke={class:"space-y-2 mb-4"},we={class:"text-gray-600 dark:text-gray-400 truncate"},Ce={class:"text-gray-500"},Se={key:0,class:"text-xs text-gray-400"},ze={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-4"},De={class:"flex items-center space-x-2"},Pe={class:"flex justify-between items-center"},Me={class:"flex space-x-2"},Ve=["onClick"],Ee={key:1,class:"inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full"},Oe={class:"flex space-x-2"},Ie=["onClick"],Ne=["onClick"],Ue={__name:"PollsIndex",setup(m){const e=nt(),c=L(),b=dt(),{formatDate:i}=ut(),r=S(!1),h=S(!1),C=S(!1),l=S(!1),x=S(null),y=S(""),$=z(()=>b.hasPermission("PERMISSION_CREATE_POLLS")),H=z(()=>b.hasPermission("PERMISSION_MANAGE_COMMUNICATION")),q=z(()=>c.polls.filter(o=>_(o)==="active").length),G=z(()=>c.polls.filter(o=>_(o)==="closed").length),Q=z(()=>c.polls.reduce((o,s)=>o+(s.total_votes||0),0)),A=z(()=>{let o=c.polls;return y.value==="active"?o=o.filter(s=>_(s)==="active"):y.value==="closed"?o=o.filter(s=>_(s)==="closed"):y.value==="draft"&&(o=o.filter(s=>_(s)==="draft")),o}),_=o=>o.is_active?o.expires_at&&new Date(o.expires_at)<new Date?"closed":"active":"draft",J=o=>o.expires_at&&new Date(o.expires_at)<new Date,K=o=>o.max_participants?Math.min(100,(o.total_votes||0)/o.max_participants*100):100,R=o=>c.getUserVoteForPoll(o.id)!==null,W=o=>{e.push(`/app/communications/polls/${o.id}`)},X=o=>{x.value=o,C.value=!0},Y=o=>{x.value=o,h.value=!0},Z=o=>{x.value=o,l.value=!0},tt=o=>{r.value=!1},et=o=>{h.value=!1,x.value=null},st=o=>{C.value=!1,x.value=null,c.fetchPolls()},ot=async()=>{try{await c.deletePoll(x.value.id),l.value=!1,x.value=null}catch(o){console.error("Errore nell'eliminazione del sondaggio:",o)}};return it(async()=>{try{await c.fetchPolls()}catch(o){console.error("Errore nel caricamento dei sondaggi:",o)}}),(o,s)=>{var T;return n(),d(O,null,[g(mt,{title:"Sondaggi Aziendali",subtitle:"Gestione e partecipazione ai sondaggi interni",data:P(c).polls,loading:P(c).loading.polls,"can-create":$.value,"create-label":"Nuovo Sondaggio","search-placeholder":"Cerca sondaggi...","empty-message":"Nessun sondaggio disponibile","results-label":"sondaggi",onCreate:s[1]||(s[1]=k=>r.value=!0)},{stats:N(()=>[t("div",Rt,[t("div",Tt,[t("div",jt,[t("div",Ft,[t("div",Bt,[g(f,{name:"chart-bar",size:"lg",class:"text-green-500"})]),t("div",Lt,[t("dl",null,[s[6]||(s[6]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Totali",-1)),t("dd",$t,u(P(c).polls.length),1)])])])])]),t("div",Ht,[t("div",qt,[t("div",Gt,[t("div",Qt,[g(f,{name:"play",size:"lg",class:"text-blue-500"})]),t("div",Jt,[t("dl",null,[s[7]||(s[7]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Attivi",-1)),t("dd",Kt,u(q.value),1)])])])])]),t("div",Wt,[t("div",Xt,[t("div",Yt,[t("div",Zt,[g(f,{name:"stop",size:"lg",class:"text-red-500"})]),t("div",te,[t("dl",null,[s[8]||(s[8]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Chiusi",-1)),t("dd",ee,u(G.value),1)])])])])]),t("div",se,[t("div",oe,[t("div",le,[t("div",ie,[g(f,{name:"hand-raised",size:"lg",class:"text-purple-500"})]),t("div",ne,[t("dl",null,[s[9]||(s[9]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Voti Totali",-1)),t("dd",ae,u(Q.value),1)])])])])])])]),filters:N(()=>[t("div",re,[v(t("select",{"onUpdate:modelValue":s[0]||(s[0]=k=>y.value=k),class:"block w-32 pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},s[10]||(s[10]=[t("option",{value:""},"Tutti",-1),t("option",{value:"active"},"Attivi",-1),t("option",{value:"closed"},"Chiusi",-1),t("option",{value:"draft"},"Bozze",-1)]),512),[[F,y.value]])])]),content:N(({data:k,loading:lt})=>[lt?(n(),d("div",de,s[11]||(s[11]=[t("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):A.value.length===0?(n(),d("div",ue,[g(f,{name:"chart-bar",size:"2xl",class:"mx-auto text-gray-400 mb-4"}),s[12]||(s[12]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Nessun sondaggio trovato",-1)),s[13]||(s[13]=t("p",{class:"text-gray-500 dark:text-gray-400"},"Non ci sono sondaggi da visualizzare con i filtri selezionati.",-1))])):(n(),d("div",me,[t("div",ce,[(n(!0),d(O,null,U(A.value,a=>(n(),d("div",{key:a.id,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer",onClick:w=>W(a)},[t("div",xe,[t("div",fe,[t("h3",pe,u(a.title),1),g(ct,{status:_(a),size:"sm"},null,8,["status"])]),t("p",ve,u(a.description),1),t("div",he,[t("div",ye,[t("span",null,u(a.total_votes||0)+" voti",1),a.expires_at?(n(),d("span",be,u(J(a)?"Scaduto":`Scade ${P(i)(a.expires_at)}`),1)):p("",!0)]),t("div",_e,[t("div",{class:"bg-green-500 h-2 rounded-full transition-all duration-300",style:at({width:`${K(a)}%`})},null,4)])]),t("div",ke,[(n(!0),d(O,null,U((a.options||[]).slice(0,3),w=>(n(),d("div",{key:w.id,class:"flex justify-between items-center text-xs"},[t("span",we,u(w.text),1),t("span",Ce,u(w.votes||0),1)]))),128)),(a.options||[]).length>3?(n(),d("div",Se," +"+u((a.options||[]).length-3)+" altre opzioni ",1)):p("",!0)]),t("div",ze,[t("div",De,[g(f,{name:"user",size:"xs"}),t("span",null,u(a.author_name||"Anonimo"),1)]),t("span",null,u(P(i)(a.created_at)),1)]),t("div",Pe,[t("div",Me,[!R(a)&&_(a)==="active"?(n(),d("button",{key:0,onClick:E(w=>X(a),["stop"]),class:"inline-flex items-center px-3 py-1 border border-green-300 shadow-sm text-xs font-medium rounded text-green-700 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"},[g(f,{name:"hand-raised",size:"xs",class:"mr-1"}),s[14]||(s[14]=D(" Vota "))],8,Ve)):R(a)?(n(),d("span",Ee,[g(f,{name:"check",size:"xs",class:"mr-1"}),s[15]||(s[15]=D(" Votato "))])):p("",!0)]),t("div",Oe,[t("button",{onClick:E(w=>Y(a),["stop"]),class:"inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[g(f,{name:"pencil",size:"xs",class:"mr-1"}),s[16]||(s[16]=D(" Modifica "))],8,Ie),H.value?(n(),d("button",{key:0,onClick:E(w=>Z(a),["stop"]),class:"inline-flex items-center px-3 py-1 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},[g(f,{name:"trash",size:"xs",class:"mr-1"}),s[17]||(s[17]=D(" Elimina "))],8,Ne)):p("",!0)])])])],8,ge))),128))])]))]),_:1},8,["data","loading","can-create"]),r.value?(n(),M(At,{key:0,onClose:s[2]||(s[2]=k=>r.value=!1),onCreated:tt})):p("",!0),h.value?(n(),M(gt,{key:1,poll:x.value,onClose:s[3]||(s[3]=k=>h.value=!1),onUpdated:et},null,8,["poll"])):p("",!0),C.value?(n(),M(xt,{key:2,poll:x.value,onClose:s[4]||(s[4]=k=>C.value=!1),onVoted:st},null,8,["poll"])):p("",!0),l.value?(n(),M(ft,{key:3,title:"Elimina Sondaggio",message:`Sei sicuro di voler eliminare il sondaggio '${(T=x.value)==null?void 0:T.title}'?`,"confirm-text":"Elimina","confirm-class":"bg-red-600 hover:bg-red-700",onConfirm:ot,onCancel:s[5]||(s[5]=k=>l.value=!1)},null,8,["message"])):p("",!0)],64)}}},Ge=B(Ue,[["__scopeId","data-v-28242b88"]]);export{Ge as default};
