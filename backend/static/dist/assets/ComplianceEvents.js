import{r as h,c as _,w as W,b as v,l as M,j as e,t as d,e as g,A as Y,B as m,C as f,H as k,I as C,s as E,o as p,h as Z,f as A,F as $,p as ee,x as te,n as D}from"./vendor.js";import{H as V,c as T,_ as oe}from"./app.js";import{u as se}from"./governance.js";const le={key:0,class:"fixed inset-0 bg-gray-50 bg-opacity-75 flex items-center justify-center z-50"},ne={class:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-screen overflow-y-auto m-4"},re={class:"px-6 py-4 border-b border-gray-200"},ie={class:"flex items-center justify-between"},ae={class:"text-lg font-medium text-gray-900"},de={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ue={class:"md:col-span-2"},me={class:"md:col-span-2"},pe={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ve={class:"space-y-4"},ce={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ge={class:"flex justify-end space-x-3 pt-4 border-t border-gray-200"},ye=["disabled"],be={key:0,class:"flex items-center"},xe={key:1},fe={__name:"EventFormModal",props:{isOpen:{type:Boolean,default:!1},event:{type:Object,default:null}},emits:["close","saved"],setup(b,{emit:t}){const y=b,s=t,w=h(!1),r=h({title:"",description:"",event_type:"",severity:"",status:"open",framework:"",event_date:"",due_date:"",resolution_date:"",actions_taken:"",impact:"",evidence:"",responsible_person:"",related_policy:""}),u=_(()=>{var a;return!!((a=y.event)!=null&&a.id)});W(()=>y.isOpen,a=>{console.log("🔥 EventFormModal isOpen changed:",a),a&&(z(),y.event&&l(y.event))});const z=()=>{r.value={title:"",description:"",event_type:"",severity:"",status:"open",framework:"",event_date:"",due_date:"",resolution_date:"",actions_taken:"",impact:"",evidence:"",responsible_person:"",related_policy:""}},l=a=>{r.value={title:a.title||"",description:a.description||"",event_type:a.event_type||"",severity:a.severity||"",status:a.status||"open",framework:a.framework||"",event_date:a.event_date?a.event_date.split("T")[0]:"",due_date:a.due_date?a.due_date.split("T")[0]:"",resolution_date:a.resolution_date?a.resolution_date.split("T")[0]:"",actions_taken:a.actions_taken||"",impact:a.impact||"",evidence:a.evidence||"",responsible_person:a.responsible_person||"",related_policy:a.related_policy||""}},S=async()=>{w.value=!0,console.log("🔥 EventFormModal submitForm called, editMode:",u.value);try{const a={...r.value};console.log("🔥 EventFormModal payload:",a),["framework","due_date","resolution_date","actions_taken","impact","evidence","responsible_person","related_policy"].forEach(x=>{a[x]===""&&(a[x]=null)});let i;if(u.value){const x=`/api/governance/events/${y.event.id}`;console.log("🔥 EventFormModal PUT URL:",x),i=await T.put(x,a)}else{const x="/api/governance/events";console.log("🔥 EventFormModal POST URL:",x),i=await T.post(x,a)}console.log("🔥 EventFormModal API response:",i),i.data.success?(s("saved",i.data.data),F()):console.error("🔥 EventFormModal API response not successful:",i.data)}catch(a){console.error("🔥 EventFormModal Error saving event:",a),console.error("🔥 EventFormModal Error response:",a.response)}finally{w.value=!1}},F=()=>{s("close")};return(a,o)=>b.isOpen?(p(),v("div",le,[e("div",ne,[e("div",re,[e("div",ie,[e("h3",ae,d(u.value?"Modifica Evento Compliance":"Nuovo Evento Compliance"),1),e("button",{onClick:F,class:"text-gray-400 hover:text-gray-600"},[g(V,{name:"x-mark",size:"md"})])])]),e("form",{onSubmit:Y(S,["prevent"]),class:"p-6 space-y-6"},[e("div",de,[e("div",ue,[o[14]||(o[14]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Titolo Evento * ",-1)),m(e("input",{"onUpdate:modelValue":o[0]||(o[0]=i=>r.value.title=i),type:"text",required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Es. Violazione Policy di Sicurezza"},null,512),[[f,r.value.title]])]),e("div",me,[o[15]||(o[15]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Descrizione * ",-1)),m(e("textarea",{"onUpdate:modelValue":o[1]||(o[1]=i=>r.value.description=i),rows:"3",required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Descrizione dettagliata dell'evento"},null,512),[[f,r.value.description]])]),e("div",null,[o[17]||(o[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Tipo Evento * ",-1)),m(e("select",{"onUpdate:modelValue":o[2]||(o[2]=i=>r.value.event_type=i),required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},o[16]||(o[16]=[C('<option value="">Seleziona tipo</option><option value="violation">Violazione</option><option value="audit">Audit</option><option value="risk_assessment">Valutazione Rischio</option><option value="training">Formazione</option><option value="incident">Incidente</option><option value="review">Revisione</option>',7)]),512),[[k,r.value.event_type]])]),e("div",null,[o[19]||(o[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Severità * ",-1)),m(e("select",{"onUpdate:modelValue":o[3]||(o[3]=i=>r.value.severity=i),required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},o[18]||(o[18]=[C('<option value="">Seleziona severità</option><option value="low">Bassa</option><option value="medium">Media</option><option value="high">Alta</option><option value="critical">Critica</option>',5)]),512),[[k,r.value.severity]])]),e("div",null,[o[21]||(o[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Status * ",-1)),m(e("select",{"onUpdate:modelValue":o[4]||(o[4]=i=>r.value.status=i),required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},o[20]||(o[20]=[e("option",{value:"open"},"Aperto",-1),e("option",{value:"in_progress"},"In Corso",-1),e("option",{value:"resolved"},"Risolto",-1),e("option",{value:"closed"},"Chiuso",-1)]),512),[[k,r.value.status]])]),e("div",null,[o[23]||(o[23]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Framework di Riferimento ",-1)),m(e("select",{"onUpdate:modelValue":o[5]||(o[5]=i=>r.value.framework=i),class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},o[22]||(o[22]=[C('<option value="">Nessun framework</option><option value="GDPR">GDPR</option><option value="ISO27001">ISO 27001</option><option value="SOX">SOX</option><option value="HIPAA">HIPAA</option><option value="ISO9001">ISO 9001</option><option value="COBIT">COBIT</option>',7)]),512),[[k,r.value.framework]])])]),e("div",pe,[e("div",null,[o[24]||(o[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Data Evento * ",-1)),m(e("input",{"onUpdate:modelValue":o[6]||(o[6]=i=>r.value.event_date=i),type:"date",required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[f,r.value.event_date]])]),e("div",null,[o[25]||(o[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Data Scadenza ",-1)),m(e("input",{"onUpdate:modelValue":o[7]||(o[7]=i=>r.value.due_date=i),type:"date",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[f,r.value.due_date]])]),e("div",null,[o[26]||(o[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Data Risoluzione ",-1)),m(e("input",{"onUpdate:modelValue":o[8]||(o[8]=i=>r.value.resolution_date=i),type:"date",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[f,r.value.resolution_date]])])]),e("div",ve,[e("div",null,[o[27]||(o[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Azioni Intraprese ",-1)),m(e("textarea",{"onUpdate:modelValue":o[9]||(o[9]=i=>r.value.actions_taken=i),rows:"4",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Descrizione delle azioni intraprese per gestire l'evento..."},null,512),[[f,r.value.actions_taken]])]),e("div",null,[o[28]||(o[28]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Impatto ",-1)),m(e("textarea",{"onUpdate:modelValue":o[10]||(o[10]=i=>r.value.impact=i),rows:"3",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Descrizione dell'impatto dell'evento sull'organizzazione..."},null,512),[[f,r.value.impact]])]),e("div",null,[o[29]||(o[29]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Evidenze ",-1)),m(e("textarea",{"onUpdate:modelValue":o[11]||(o[11]=i=>r.value.evidence=i),rows:"3",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Documentazione e evidenze relative all'evento..."},null,512),[[f,r.value.evidence]])])]),e("div",ce,[e("div",null,[o[30]||(o[30]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Responsabile ",-1)),m(e("input",{"onUpdate:modelValue":o[12]||(o[12]=i=>r.value.responsible_person=i),type:"text",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Nome del responsabile"},null,512),[[f,r.value.responsible_person]])]),e("div",null,[o[31]||(o[31]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Policy Correlata ",-1)),m(e("input",{"onUpdate:modelValue":o[13]||(o[13]=i=>r.value.related_policy=i),type:"text",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Nome o ID della policy correlata"},null,512),[[f,r.value.related_policy]])])]),e("div",ge,[e("button",{type:"button",onClick:F,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"}," Annulla "),e("button",{type:"submit",disabled:w.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[w.value?(p(),v("div",be,o[32]||(o[32]=[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),E(" Salvando... ")]))):(p(),v("span",xe,d(u.value?"Aggiorna Evento":"Crea Evento"),1))],8,ye)])],32)])])):M("",!0)}},we={name:"ComplianceEvents",components:{HeroIcon:V,EventFormModal:fe},setup(){const b=se(),t=h(null),y=h(!1),s=h(!1),w=h(null),r=h({event_type:"",severity:"",status:"",framework:""}),u=_(()=>b.events||[]),z=_(()=>b.loading),l=_(()=>b.error),S=_(()=>u.value.filter(n=>n.severity==="critical").length),F=_(()=>u.value.filter(n=>n.status==="open").length),a=_(()=>u.value.filter(n=>n.status==="resolved").length),o=async()=>{try{await b.fetchEvents()}catch(n){console.error("Error loading events:",n)}},i=async()=>{try{const n=Object.fromEntries(Object.entries(r.value).filter(([c,Q])=>Q));await b.fetchEvents(n)}catch(n){console.error("Error applying filters:",n)}},x=()=>{r.value={event_type:"",severity:"",status:"",framework:""},o()},I=()=>{t.value=null,y.value=!0},U=n=>{t.value=n,y.value=!0},O=()=>{y.value=!1,t.value=null},B=()=>{o()},R=n=>{w.value=n,s.value=!0},P=()=>{s.value=!1,w.value=null},N=async n=>{try{await b.resolveEvent(n.id,"Risolto manualmente"),o()}catch(c){console.error("Error resolving event:",c)}},L=n=>({access_violation:"Violazione Accesso",data_breach:"Data Breach",policy_violation:"Violazione Policy",unauthorized_access:"Accesso Non Autorizzato",system_anomaly:"Anomalia Sistema"})[n]||n,j=n=>({access_violation:"bg-red-100 text-red-800",data_breach:"bg-red-100 text-red-800",policy_violation:"bg-orange-100 text-orange-800",unauthorized_access:"bg-yellow-100 text-yellow-800",system_anomaly:"bg-blue-100 text-blue-800"})[n]||"bg-gray-100 text-gray-800",H=n=>({low:"Bassa",medium:"Media",high:"Alta",critical:"Critica"})[n]||n,q=n=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",critical:"bg-red-100 text-red-800"})[n]||"bg-gray-100 text-gray-800",G=n=>({open:"Aperto",investigating:"In Indagine",resolved:"Risolto",closed:"Chiuso"})[n]||n,X=n=>({open:"bg-red-100 text-red-800",investigating:"bg-yellow-100 text-yellow-800",resolved:"bg-green-100 text-green-800",closed:"bg-gray-100 text-gray-800"})[n]||"bg-gray-100 text-gray-800",J=n=>n?n.length>80?n.substring(0,80)+"...":n:"-",K=n=>{if(!n)return"-";try{const c=new Date(n);return isNaN(c.getTime())?"-":c.toLocaleString("it-IT",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}catch{return"-"}};return te(()=>{o()}),{selectedEvent:t,showEventModal:y,showDetailModal:s,viewedEvent:w,filters:r,events:u,loading:z,error:l,criticalEvents:S,openEvents:F,resolvedEvents:a,loadEvents:o,applyFilters:i,resetFilters:x,openCreateEvent:I,editEvent:U,closeEventModal:O,onEventSaved:B,viewEvent:R,closeDetailModal:P,resolveEvent:N,getEventTypeLabel:L,getEventTypeBadgeClass:j,getSeverityLabel:H,getSeverityBadgeClass:q,getStatusLabel:G,getStatusBadgeClass:X,truncateDescription:J,formatDateTime:K}}},Ee={class:"compliance-events"},ke={class:"bg-white rounded-lg shadow-sm p-6 mb-6"},_e={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6"},he={class:"mt-4 lg:mt-0"},Ce={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},Se={class:"bg-red-50 rounded-lg p-4"},ze={class:"flex items-center"},Fe={class:"ml-3"},Me={class:"text-2xl font-bold text-red-900"},De={class:"bg-yellow-50 rounded-lg p-4"},Ae={class:"flex items-center"},Te={class:"ml-3"},Ve={class:"text-2xl font-bold text-yellow-900"},Ie={class:"bg-green-50 rounded-lg p-4"},Ue={class:"flex items-center"},Oe={class:"ml-3"},Be={class:"text-2xl font-bold text-green-900"},Re={class:"bg-blue-50 rounded-lg p-4"},Pe={class:"flex items-center"},Ne={class:"ml-3"},Le={class:"text-2xl font-bold text-blue-900"},je={class:"grid grid-cols-1 md:grid-cols-5 gap-4"},He={class:"flex items-end"},qe={class:"bg-white rounded-lg shadow-sm"},Ge={key:0,class:"flex justify-center py-12"},Xe={key:1,class:"p-6 text-center text-red-600"},Je={key:2,class:"p-12 text-center text-gray-500"},Ke={key:3},Qe={class:"overflow-x-auto"},We={class:"min-w-full divide-y divide-gray-200"},Ye={class:"bg-white divide-y divide-gray-200"},Ze={class:"px-6 py-4"},$e={class:"text-sm font-medium text-gray-900"},et={class:"text-sm text-gray-500"},tt={class:"px-6 py-4 whitespace-nowrap"},ot={class:"px-6 py-4 whitespace-nowrap"},st={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},lt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},nt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},rt={class:"px-6 py-4 whitespace-nowrap"},it={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},at=["onClick"],dt=["onClick"],ut=["onClick"],mt={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},pt={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},vt={class:"mt-3"},ct={class:"flex items-center justify-between mb-4"},gt={key:0,class:"space-y-3"},yt={class:"mt-4"};function bt(b,t,y,s,w,r){const u=A("HeroIcon"),z=A("EventFormModal");return p(),v("div",Ee,[e("div",ke,[e("div",_e,[t[15]||(t[15]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Eventi Compliance"),e("p",{class:"text-gray-600 mt-1"},"Monitoraggio eventi e violazioni compliance")],-1)),e("div",he,[e("button",{onClick:t[0]||(t[0]=(...l)=>s.openCreateEvent&&s.openCreateEvent(...l)),class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"},[g(u,{name:"plus",class:"h-5 w-5 mr-2"}),t[14]||(t[14]=E(" Nuovo Evento "))])])]),e("div",Ce,[e("div",Se,[e("div",ze,[g(u,{name:"exclamation-triangle",class:"h-8 w-8 text-red-600"}),e("div",Fe,[t[16]||(t[16]=e("p",{class:"text-sm text-red-600 font-medium"},"Eventi Critici",-1)),e("p",Me,d(s.criticalEvents),1)])])]),e("div",De,[e("div",Ae,[g(u,{name:"clock",class:"h-8 w-8 text-yellow-600"}),e("div",Te,[t[17]||(t[17]=e("p",{class:"text-sm text-yellow-600 font-medium"},"Eventi Aperti",-1)),e("p",Ve,d(s.openEvents),1)])])]),e("div",Ie,[e("div",Ue,[g(u,{name:"check-circle",class:"h-8 w-8 text-green-600"}),e("div",Oe,[t[18]||(t[18]=e("p",{class:"text-sm text-green-600 font-medium"},"Eventi Risolti",-1)),e("p",Be,d(s.resolvedEvents),1)])])]),e("div",Re,[e("div",Pe,[g(u,{name:"chart-bar",class:"h-8 w-8 text-blue-600"}),e("div",Ne,[t[19]||(t[19]=e("p",{class:"text-sm text-blue-600 font-medium"},"Totali",-1)),e("p",Le,d(s.events.length),1)])])])]),e("div",je,[e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Tipo Evento",-1)),m(e("select",{"onUpdate:modelValue":t[1]||(t[1]=l=>s.filters.event_type=l),onChange:t[2]||(t[2]=(...l)=>s.applyFilters&&s.applyFilters(...l)),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},t[20]||(t[20]=[C('<option value="">Tutti</option><option value="access_violation">Violazione Accesso</option><option value="data_breach">Data Breach</option><option value="policy_violation">Violazione Policy</option><option value="unauthorized_access">Accesso Non Autorizzato</option><option value="system_anomaly">Anomalia Sistema</option>',6)]),544),[[k,s.filters.event_type]])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Severità",-1)),m(e("select",{"onUpdate:modelValue":t[3]||(t[3]=l=>s.filters.severity=l),onChange:t[4]||(t[4]=(...l)=>s.applyFilters&&s.applyFilters(...l)),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},t[22]||(t[22]=[C('<option value="">Tutte</option><option value="low">Bassa</option><option value="medium">Media</option><option value="high">Alta</option><option value="critical">Critica</option>',5)]),544),[[k,s.filters.severity]])]),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Status",-1)),m(e("select",{"onUpdate:modelValue":t[5]||(t[5]=l=>s.filters.status=l),onChange:t[6]||(t[6]=(...l)=>s.applyFilters&&s.applyFilters(...l)),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},t[24]||(t[24]=[C('<option value="">Tutti</option><option value="open">Aperto</option><option value="investigating">In Indagine</option><option value="resolved">Risolto</option><option value="closed">Chiuso</option>',5)]),544),[[k,s.filters.status]])]),e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Framework",-1)),m(e("select",{"onUpdate:modelValue":t[7]||(t[7]=l=>s.filters.framework=l),onChange:t[8]||(t[8]=(...l)=>s.applyFilters&&s.applyFilters(...l)),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},t[26]||(t[26]=[C('<option value="">Tutti</option><option value="GDPR">GDPR</option><option value="ISO27001">ISO 27001</option><option value="SOX">SOX</option><option value="HIPAA">HIPAA</option>',5)]),544),[[k,s.filters.framework]])]),e("div",He,[e("button",{onClick:t[9]||(t[9]=(...l)=>s.resetFilters&&s.resetFilters(...l)),class:"w-full bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"}," Reset Filtri ")])])]),e("div",qe,[s.loading?(p(),v("div",Ge,t[28]||(t[28]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)]))):s.error?(p(),v("div",Xe,[g(u,{name:"exclamation-triangle",class:"h-8 w-8 mx-auto mb-2"}),e("p",null,d(s.error),1),e("button",{onClick:t[10]||(t[10]=(...l)=>s.loadEvents&&s.loadEvents(...l)),class:"mt-3 text-blue-600 hover:text-blue-800 font-medium"}," Riprova ")])):s.events.length===0?(p(),v("div",Je,[g(u,{name:"shield-exclamation",class:"h-12 w-12 mx-auto mb-4 text-gray-300"}),t[29]||(t[29]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Nessun evento trovato",-1)),t[30]||(t[30]=e("p",{class:"text-gray-600 mb-6"},"Non ci sono eventi di compliance registrati",-1)),e("button",{onClick:t[11]||(t[11]=(...l)=>s.openCreateEvent&&s.openCreateEvent(...l)),class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"}," Registra Primo Evento ")])):(p(),v("div",Ke,[e("div",Qe,[e("table",We,[t[31]||(t[31]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Evento "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Tipo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Severità "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Framework "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Utente "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Data Creazione "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",Ye,[(p(!0),v($,null,ee(s.events,l=>(p(),v("tr",{key:l.id,class:"hover:bg-gray-50"},[e("td",Ze,[e("div",$e,d(l.title),1),e("div",et,d(s.truncateDescription(l.description)),1)]),e("td",tt,[e("span",{class:D(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s.getEventTypeBadgeClass(l.event_type)])},d(s.getEventTypeLabel(l.event_type)),3)]),e("td",ot,[e("span",{class:D(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s.getSeverityBadgeClass(l.severity)])},d(s.getSeverityLabel(l.severity)),3)]),e("td",st,d(l.compliance_framework||"-"),1),e("td",lt,d(l.username||"-"),1),e("td",nt,d(s.formatDateTime(l.created_at)),1),e("td",rt,[e("span",{class:D(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s.getStatusBadgeClass(l.status)])},d(s.getStatusLabel(l.status)),3)]),e("td",it,[e("button",{onClick:S=>s.editEvent(l),class:"text-indigo-600 hover:text-indigo-900 mr-3"},[g(u,{name:"pencil-square",class:"h-4 w-4"})],8,at),e("button",{onClick:S=>s.viewEvent(l),class:"text-gray-600 hover:text-gray-900 mr-3"},[g(u,{name:"eye",class:"h-4 w-4"})],8,dt),l.status==="open"?(p(),v("button",{key:0,onClick:S=>s.resolveEvent(l),class:"text-green-600 hover:text-green-900"},[g(u,{name:"check",class:"h-4 w-4"})],8,ut)):M("",!0)])]))),128))])])])]))]),s.showDetailModal?(p(),v("div",mt,[e("div",pt,[e("div",vt,[e("div",ct,[t[32]||(t[32]=e("h3",{class:"text-lg font-medium text-gray-900"},"Dettaglio Evento",-1)),e("button",{onClick:t[12]||(t[12]=(...l)=>s.closeDetailModal&&s.closeDetailModal(...l)),class:"text-gray-400 hover:text-gray-600"},[g(u,{name:"x-mark",class:"w-5 h-5"})])]),s.viewedEvent?(p(),v("div",gt,[e("div",null,[t[33]||(t[33]=e("strong",null,"Titolo:",-1)),E(" "+d(s.viewedEvent.title),1)]),e("div",null,[t[34]||(t[34]=e("strong",null,"Tipo:",-1)),E(" "+d(s.getEventTypeLabel(s.viewedEvent.event_type)),1)]),e("div",null,[t[35]||(t[35]=e("strong",null,"Severità:",-1)),E(" "+d(s.getSeverityLabel(s.viewedEvent.severity)),1)]),e("div",null,[t[36]||(t[36]=e("strong",null,"Descrizione:",-1)),E(" "+d(s.viewedEvent.description),1)]),e("div",null,[t[37]||(t[37]=e("strong",null,"Framework:",-1)),E(" "+d(s.viewedEvent.compliance_framework||"-"),1)]),e("div",null,[t[38]||(t[38]=e("strong",null,"Utente:",-1)),E(" "+d(s.viewedEvent.username||"-"),1)]),e("div",null,[t[39]||(t[39]=e("strong",null,"Data:",-1)),E(" "+d(s.formatDateTime(s.viewedEvent.created_at)),1)])])):M("",!0),e("div",yt,[e("button",{onClick:t[13]||(t[13]=(...l)=>s.closeDetailModal&&s.closeDetailModal(...l)),class:"w-full px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-md text-sm font-medium"}," Chiudi ")])])])])):M("",!0),s.showEventModal?(p(),Z(z,{key:1,"is-open":s.showEventModal,event:s.selectedEvent,onClose:s.closeEventModal,onSaved:s.onEventSaved},null,8,["is-open","event","onClose","onSaved"])):M("",!0)])}const Et=oe(we,[["render",bt]]);export{Et as default};
