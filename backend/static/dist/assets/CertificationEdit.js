import{r as f,c as _,u as U,w as $,x as N,b as g,e as o,l as k,k as r,j as t,t as u,A,s as d,B as E,C as P,E as q,n as R,q as H,o as p,h as F}from"./vendor.js";import{u as G}from"./certifications.js";import{H as v,d as Q}from"./app.js";import{S as b}from"./StandardButton.js";import{S as n}from"./StandardInput.js";import{_ as J}from"./PageHeader.js";import{_ as K}from"./Breadcrumb.js";/* empty css                                                             */const O={class:"certification-edit"},W={key:0,class:"flex justify-center items-center py-12"},X={key:1,class:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6"},Y={class:"flex items-center"},Z={class:"text-red-800"},ee={key:2,class:"max-w-4xl mx-auto"},te={class:"bg-white rounded-lg shadow-sm p-6"},ae={class:"mb-8"},ie={class:"text-lg font-semibold text-gray-900 mb-4 flex items-center"},oe={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},se={class:"md:col-span-2"},le={class:"mb-8"},re={class:"text-lg font-semibold text-gray-900 mb-4 flex items-center"},ne={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},de={class:"mb-8"},ue={class:"text-lg font-semibold text-gray-900 mb-4 flex items-center"},ce={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},me={class:"flex items-center space-x-3"},pe={class:"text-sm font-medium text-gray-700 w-12"},ve={class:"mt-2 w-full bg-gray-200 rounded-full h-2"},fe={class:"mb-8"},ge={class:"flex items-center"},be={class:"font-semibold"},xe={class:"text-sm opacity-90"},ye={class:"flex justify-between items-center pt-6 border-t border-gray-200"},_e={class:"flex items-center space-x-3"},ze={class:"text-sm text-gray-500"},Ve={class:"flex space-x-3"},Ie={__name:"CertificationEdit",setup(Se){const C=H(),z=U(),x=G(),{addToast:V}=Q(),y=f(!1),c=f(null),m=f(!1),i=f({project_name:"",description:"",target_date:"",team_lead_id:"",estimated_budget:null,priority:"medium",status:"draft",current_score:0}),s=_(()=>x.currentCertification),h=_(()=>{var a;return[{name:"Certificazioni",href:"/app/certifications/dashboard"},{name:"Lista",href:"/app/certifications/list"},{name:((a=s.value)==null?void 0:a.project_name)||"Modifica",href:`/app/certifications/${z.params.id}`},{name:"Modifica",href:null}]}),S=_(()=>i.value.project_name&&i.value.project_name.trim().length>3),w=async()=>{const a=z.params.id;if(!a){c.value="ID certificazione non valido";return}y.value=!0,c.value=null;try{await x.fetchCertification(parseInt(a))}catch(e){c.value=e.message||"Errore nel caricamento della certificazione"}finally{y.value=!1}},B=async()=>{if(!(!S.value||!s.value)){m.value=!0;try{await x.updateCertification(s.value.id,i.value),V("Certificazione aggiornata con successo!","success"),C.push(`/app/certifications/${s.value.id}`)}catch{V("Errore nell'aggiornamento della certificazione","error")}finally{m.value=!1}}},D=a=>a?new Date(a).toLocaleDateString("it-IT"):null,j=a=>({quality:"Qualità",security:"Sicurezza",environmental:"Ambientale",privacy:"Privacy",regulatory:"Normativo"})[a]||a||"Non specificata",M=a=>({draft:"Bozza",active:"Attiva",in_progress:"In Corso",completed:"Completata",expired:"Scaduta",suspended:"Sospesa",deleted:"Eliminata"})[a]||a||"Sconosciuto",I=a=>({draft:"La certificazione è in fase di preparazione",active:"Certificazione attiva e valida",in_progress:"Processo di certificazione in corso",completed:"Certificazione ottenuta con successo",expired:"Certificazione scaduta, necessario rinnovo",suspended:"Certificazione temporaneamente sospesa",deleted:"Certificazione eliminata dal sistema"})[a]||"Stato non definito",L=a=>({draft:"document-text",active:"check-circle",in_progress:"arrow-path",completed:"trophy",expired:"exclamation-triangle",suspended:"pause-circle",deleted:"x-circle"})[a]||"question-mark-circle",T=a=>({draft:"bg-gray-100 text-gray-800",active:"bg-green-100 text-green-800",in_progress:"bg-primary-100 text-primary-800",completed:"bg-secondary-100 text-secondary-800 dark:bg-secondary-900/50 dark:text-secondary-200",expired:"bg-red-100 text-red-800",suspended:"bg-yellow-100 text-yellow-800",deleted:"bg-gray-100 text-gray-800"})[a]||"bg-gray-100 text-gray-800";return $(s,a=>{a&&(i.value={project_name:a.project_name||"",description:a.description||"",target_date:a.target_date?a.target_date.split("T")[0]:"",team_lead_id:a.team_lead_id||"",estimated_budget:a.estimated_budget||null,priority:a.priority||"medium",status:a.status||"draft",current_score:a.current_score||0})},{immediate:!0}),N(()=>{w()}),(a,e)=>(p(),g("div",O,[o(K,{items:h.value},null,8,["items"]),o(J,{title:s.value?`Modifica ${s.value.project_name}`:"Modifica Certificazione",subtitle:s.value?`${j(s.value.category)} • ${s.value.status}`:"Modifica certificazione aziendale",icon:"pencil","icon-color":"text-primary-600"},{actions:r(()=>[s.value?(p(),F(b,{key:0,variant:"secondary",icon:"eye",to:`/app/certifications/${s.value.id}`},{default:r(()=>e[8]||(e[8]=[d(" Visualizza ")])),_:1,__:[8]},8,["to"])):k("",!0),o(b,{variant:"secondary",icon:"arrow-left",to:"/app/certifications/dashboard"},{default:r(()=>e[9]||(e[9]=[d(" Dashboard ")])),_:1,__:[9]})]),_:1},8,["title","subtitle"]),y.value?(p(),g("div",W,e[10]||(e[10]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"},null,-1),t("span",{class:"ml-3 text-gray-600"},"Caricamento certificazione...",-1)]))):c.value?(p(),g("div",X,[t("div",Y,[o(v,{name:"exclamation-triangle",class:"h-5 w-5 text-red-400 mr-2"}),t("span",Z,u(c.value),1)])])):s.value?(p(),g("div",ee,[t("div",te,[t("form",{onSubmit:A(B,["prevent"])},[t("div",ae,[t("h2",ie,[o(v,{name:"information-circle",class:"h-6 w-6 mr-2 text-primary-600"}),e[11]||(e[11]=d(" Informazioni Base "))]),t("div",oe,[t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Nome Progetto * ",-1)),o(n,{modelValue:i.value.project_name,"onUpdate:modelValue":e[0]||(e[0]=l=>i.value.project_name=l),type:"text",placeholder:"Nome del progetto di certificazione",required:""},null,8,["modelValue"])]),t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Standard di Certificazione ",-1)),o(n,{value:s.value.standard_code,type:"text",disabled:""},null,8,["value"]),e[14]||(e[14]=t("p",{class:"text-xs text-gray-500 mt-1"},"Lo standard non può essere modificato dopo la creazione",-1))]),t("div",se,[e[15]||(e[15]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Descrizione ",-1)),o(n,{modelValue:i.value.description,"onUpdate:modelValue":e[1]||(e[1]=l=>i.value.description=l),type:"textarea",rows:3,placeholder:"Descrizione dettagliata del progetto di certificazione..."},null,8,["modelValue"])])])]),t("div",le,[t("h2",re,[o(v,{name:"clipboard-document-check",class:"h-6 w-6 mr-2 text-primary-600"}),e[16]||(e[16]=d(" Dettagli Progetto "))]),t("div",ne,[t("div",null,[e[17]||(e[17]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Data Target Completamento ",-1)),o(n,{modelValue:i.value.target_date,"onUpdate:modelValue":e[2]||(e[2]=l=>i.value.target_date=l),type:"date"},null,8,["modelValue"])]),t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Budget Stimato (€) ",-1)),o(n,{modelValue:i.value.estimated_budget,"onUpdate:modelValue":e[3]||(e[3]=l=>i.value.estimated_budget=l),modelModifiers:{number:!0},type:"number",min:"0",step:"100",placeholder:"Budget in euro"},null,8,["modelValue"])]),t("div",null,[e[20]||(e[20]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Priorità ",-1)),o(n,{modelValue:i.value.priority,"onUpdate:modelValue":e[4]||(e[4]=l=>i.value.priority=l),type:"select"},{default:r(()=>e[19]||(e[19]=[t("option",{value:"low"},"Bassa",-1),t("option",{value:"medium"},"Media",-1),t("option",{value:"high"},"Alta",-1),t("option",{value:"critical"},"Critica",-1)])),_:1,__:[19]},8,["modelValue"])]),t("div",null,[e[22]||(e[22]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Stato ",-1)),o(n,{modelValue:i.value.status,"onUpdate:modelValue":e[5]||(e[5]=l=>i.value.status=l),type:"select"},{default:r(()=>e[21]||(e[21]=[t("option",{value:"draft"},"Bozza",-1),t("option",{value:"in_progress"},"In Corso",-1),t("option",{value:"active"},"Attiva",-1),t("option",{value:"completed"},"Completata",-1),t("option",{value:"suspended"},"Sospesa",-1)])),_:1,__:[21]},8,["modelValue"])])])]),t("div",de,[t("h2",ue,[o(v,{name:"chart-bar",class:"h-6 w-6 mr-2 text-primary-600"}),e[23]||(e[23]=d(" Progresso "))]),t("div",ce,[t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Score Attuale (%) ",-1)),t("div",me,[E(t("input",{"onUpdate:modelValue":e[6]||(e[6]=l=>i.value.current_score=l),type:"range",min:"0",max:"100",class:"flex-1"},null,512),[[P,i.value.current_score,void 0,{number:!0}]]),t("span",pe,u(i.value.current_score)+"%",1)]),t("div",ve,[t("div",{class:"h-2 rounded-full bg-primary-600 transition-all duration-300",style:q({width:i.value.current_score+"%"})},null,4)])]),t("div",null,[e[26]||(e[26]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Team Lead ",-1)),o(n,{modelValue:i.value.team_lead_id,"onUpdate:modelValue":e[7]||(e[7]=l=>i.value.team_lead_id=l),type:"select"},{default:r(()=>e[25]||(e[25]=[t("option",{value:""},"Seleziona Team Lead",-1),t("option",{value:"1"},"Mario Rossi",-1),t("option",{value:"2"},"Laura Bianchi",-1),t("option",{value:"3"},"Giuseppe Verdi",-1)])),_:1,__:[25]},8,["modelValue"])])])]),t("div",fe,[t("div",{class:R([T(i.value.status),"rounded-lg p-4"])},[t("div",ge,[o(v,{name:L(i.value.status),class:"h-6 w-6 mr-3"},null,8,["name"]),t("div",null,[t("h3",be,u(M(i.value.status)),1),t("p",xe,u(I(i.value.status)),1)])])],2)]),t("div",ye,[t("div",_e,[t("span",ze," Ultima modifica: "+u(D(s.value.updated_at)),1)]),t("div",Ve,[o(b,{variant:"secondary",to:`/app/certifications/${s.value.id}`},{default:r(()=>e[27]||(e[27]=[d(" Annulla ")])),_:1,__:[27]},8,["to"]),o(b,{variant:"primary",type:"submit",disabled:!S.value||m.value,loading:m.value,icon:"check"},{default:r(()=>[d(u(m.value?"Salvataggio...":"Salva Modifiche"),1)]),_:1},8,["disabled","loading"])])])],32)])])):k("",!0)]))}};export{Ie as default};
