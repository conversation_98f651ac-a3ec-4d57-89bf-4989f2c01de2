import{u as O}from"./timesheet.js";import{T}from"./TimesheetGrid.js";import{r as y,c as u,b as m,j as o,e as x,l as W,t as $,F as B,p as H,o as g}from"./vendor.js";import"./app.js";const R={class:"space-y-8"},V={class:"space-y-4"},q={class:"space-y-4"},J={class:"flex items-center justify-between mb-4"},U={class:"flex items-center space-x-2"},K={class:"text-sm text-gray-500"},Q={class:"space-y-4"},X={class:"space-y-4"},Y={class:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4 max-h-64 overflow-y-auto"},Z={key:0,class:"text-gray-500 dark:text-gray-400 text-sm"},ee={key:1,class:"space-y-2"},te={class:"text-gray-500 dark:text-gray-400"},ae={class:"ml-2"},ne={__name:"TimesheetGridExample",setup(se){const h=y(!1),n=y(new Map),f=y([]),b=O(),d=[{key:"2024-01-01",label:"1",sublabel:"Lun",isWeekend:!1,isToday:!1},{key:"2024-01-02",label:"2",sublabel:"Mar",isWeekend:!1,isToday:!1},{key:"2024-01-03",label:"3",sublabel:"Mer",isWeekend:!1,isToday:!1},{key:"2024-01-04",label:"4",sublabel:"Gio",isWeekend:!1,isToday:!1},{key:"2024-01-05",label:"5",sublabel:"Ven",isWeekend:!1,isToday:!1},{key:"2024-01-06",label:"6",sublabel:"Sab",isWeekend:!0,isToday:!1},{key:"2024-01-07",label:"7",sublabel:"Dom",isWeekend:!0,isToday:!1},{key:"2024-01-08",label:"8",sublabel:"Lun",isWeekend:!1,isToday:!0},{key:"2024-01-09",label:"9",sublabel:"Mar",isWeekend:!1,isToday:!1},{key:"2024-01-10",label:"10",sublabel:"Mer",isWeekend:!1,isToday:!1}],p=[{id:1,name:"CRM Development - Frontend Dashboard",projectName:"CRM Development",taskName:"Frontend Dashboard",assignees:"Mario R., Giulia B.",hours:{"2024-01-01":"8.0","2024-01-02":"7.5","2024-01-03":"8.0"},billing:{"2024-01-01":!0,"2024-01-02":!0,"2024-01-03":!0},total:"23.5"},{id:2,name:"E-commerce Platform - API Integration",projectName:"E-commerce Platform",taskName:"API Integration",assignees:"Luca M.",hours:{"2024-01-01":"4.0","2024-01-02":"6.0","2024-01-04":"8.0"},billing:{"2024-01-01":!0,"2024-01-02":!0,"2024-01-04":!1},total:"18.0"},{id:3,name:"Internal Tools - Documentation",projectName:"Internal Tools",taskName:"Documentation",assignees:"Sara T.",hours:{"2024-01-03":"4.0","2024-01-05":"3.5"},billing:{"2024-01-03":!1,"2024-01-05":!1},total:"7.5"}],c=y(JSON.parse(JSON.stringify(p))),E=[{id:4,name:"Sistema di Gestione Avanzata per il Monitoraggio delle Performance Aziendali con Dashboard Interattive e Report Automatizzati",projectName:"Sistema di Gestione Avanzata per il Monitoraggio delle Performance Aziendali",taskName:"Dashboard Interattive e Report Automatizzati",assignees:"Team Frontend: Alessandro M., Francesca R., Roberto T.",hours:{"2024-01-01":"8.0","2024-01-02":"6.5","2024-01-03":"7.0"},billing:{"2024-01-01":!0,"2024-01-02":!0,"2024-01-03":!0},total:"21.5"},{id:5,name:"Piattaforma E-commerce Multi-Tenant con Integrazione Avanzata di Sistemi di Pagamento e Gestione Inventario Automatizzata",projectName:"Piattaforma E-commerce Multi-Tenant con Integrazione Avanzata",taskName:"Sistemi di Pagamento e Gestione Inventario Automatizzata",assignees:"Team Backend: Marco V., Elena S., Davide P., Chiara L.",hours:{"2024-01-02":"8.0","2024-01-03":"8.0","2024-01-04":"7.5"},billing:{"2024-01-02":!0,"2024-01-03":!0,"2024-01-04":!0},total:"23.5"}],w=u(()=>{const t={};return d.forEach(e=>{t[e.key]=p.reduce((s,a)=>s+parseFloat(a.hours[e.key]||0),0)}),t}),z=u(()=>Object.values(w.value).reduce((t,e)=>t+e,0)),_=u(()=>({status:"pending",totalHours:z.value,billableHours:p.reduce((t,e)=>t+Object.entries(e.billing||{}).filter(([s,a])=>a&&e.hours[s]).reduce((s,[a])=>s+parseFloat(e.hours[a]||0),0),0),pendingChanges:0})),C=u(()=>{const t={};return d.forEach(e=>{t[e.key]=c.value.reduce((s,a)=>s+parseFloat(a.hours[e.key]||0),0)}),t}),N=u(()=>Object.values(C.value).reduce((t,e)=>t+e,0)),I=u(()=>({status:"pending",totalHours:N.value,billableHours:c.value.reduce((t,e)=>t+Object.entries(e.billing||{}).filter(([s,a])=>a&&e.hours[s]).reduce((s,[a])=>s+parseFloat(e.hours[a]||0),0),0),pendingChanges:n.value.size})),F=u(()=>{const t={};return d.slice(0,7).forEach(e=>{t[e.key]=E.reduce((s,a)=>s+parseFloat(a.hours[e.key]||0),0)}),t}),j=u(()=>Object.values(F.value).reduce((t,e)=>t+e,0)),r=t=>{f.value.push({timestamp:new Date().toLocaleTimeString(),message:t})},k=(t,e)=>{r(`Clic su cella: ${t.name} - ${e.label}/${e.sublabel} (${t.hours[e.key]||0}h)`)},A=async(t,e,s)=>{r(`Aggiornamento diretto: Task ${t}, ${e}, ${s}h`),h.value=!0;try{const a=c.value.find(v=>v.id===t),l=`${(a==null?void 0:a.id)||t}-notask`,i=parseInt(e.split("-")[2]);await b.saveEntry(l,i,s)?(a&&(parseFloat(s)>0?a.hours[e]=parseFloat(s).toFixed(1):delete a.hours[e],a.total=Object.values(a.hours).reduce((v,S)=>v+parseFloat(S),0).toFixed(1)),r(`✅ Salvato: Task ${t}, ${e}, ${s}h`)):r(`❌ Errore salvataggio: ${b.error||"Errore sconosciuto"}`)}catch(a){r(`❌ Errore salvataggio: ${a.message}`),console.error("Error saving timesheet entry:",a)}finally{h.value=!1}},D=(t,e,s)=>{const a=`${t}-${e}`;s&&parseFloat(s)>0?(n.value.set(a,{taskId:t,date:e,hours:parseFloat(s)}),r(`📝 Modifica in attesa: Task ${t}, ${e}, ${s}h`)):(n.value.delete(a),r(`🗑️ Rimossa modifica: Task ${t}, ${e}`))},G=async t=>{r(`💾 Inizio salvataggio batch: ${t.size} modifiche`),h.value=!0;try{let e=0,s=0;for(const[a,l]of t.entries())try{const i=c.value.find(L=>L.id===l.taskId),M=`${(i==null?void 0:i.id)||l.taskId}-notask`,v=parseInt(l.date.split("-")[2]);await b.saveEntry(M,v,l.hours)?(i&&(l.hours>0?i.hours[l.date]=l.hours.toFixed(1):delete i.hours[l.date]),e++):(s++,r(`❌ Errore singolo: Task ${l.taskId}, ${l.date} - ${b.error||"Errore sconosciuto"}`))}catch(i){s++,r(`❌ Errore singolo: Task ${l.taskId}, ${l.date} - ${i.message}`),console.error("Error in bulk save:",i)}c.value.forEach(a=>{a.total=Object.values(a.hours).reduce((l,i)=>l+parseFloat(i),0).toFixed(1)}),s===0?(n.value.clear(),r(`✅ Salvataggio batch completato: ${e} modifiche salvate`)):r(`⚠️ Salvataggio batch parziale: ${e} salvate, ${s} fallite`)}catch(e){r(`❌ Errore salvataggio batch: ${e.message}`),console.error("Error in bulk save operation:",e)}finally{h.value=!1}},P=()=>{const t=n.value.size;n.value.clear(),r(`🧹 Annullate ${t} modifiche in attesa`)};return(t,e)=>(g(),m("div",R,[e[6]||(e[6]=o("div",null,[o("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white mb-4"}," TimesheetGrid - Griglia Timesheet Ottimizzata "),o("p",{class:"text-gray-600 dark:text-gray-400 mb-6"}," Griglia per la gestione delle ore con funzionalità avanzate: tooltip per nomi lunghi, celle editabili per inserimento massivo, salvataggio batch delle modifiche. ")],-1)),o("div",V,[e[0]||(e[0]=o("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Esempio Base - Modalità Solo Lettura ",-1)),x(T,{title:"Timesheet Gennaio 2024",tasks:p,days:d,"daily-totals":w.value,"grand-total":z.value,loading:!1,editable:!1,"show-stats":!0,"show-day-totals":!0,"show-indicators":!0,"show-legend":!0,status:_.value,"row-header-label":"Progetto/Task","empty-message":"Nessun timesheet per questo periodo",onCellClick:k},null,8,["daily-totals","grand-total","status"])]),o("div",q,[e[2]||(e[2]=o("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Esempio Editabile - Con Inserimento Massivo ",-1)),o("div",J,[e[1]||(e[1]=o("p",{class:"text-sm text-gray-600 dark:text-gray-400"},` Clicca su "Modifica Massiva" per abilitare l'editing delle celle. Le modifiche vengono salvate tutte insieme. `,-1)),o("div",U,[o("span",K," Modifiche in attesa: "+$(n.value.size),1),n.value.size>0?(g(),m("button",{key:0,onClick:P,class:"text-sm text-red-600 hover:text-red-700"}," Annulla Tutto ")):W("",!0)])]),x(T,{title:"Timesheet Gennaio 2024 - Editabile",tasks:c.value,days:d,"daily-totals":C.value,"grand-total":N.value,loading:h.value,editable:!0,"show-stats":!0,"show-day-totals":!0,"show-indicators":!0,"show-legend":!0,status:I.value,"row-header-label":"Progetto/Task","empty-message":"Nessun timesheet per questo periodo",onCellClick:k,onCellUpdate:A,onBulkSave:G,onHoursChanged:D},null,8,["tasks","daily-totals","grand-total","loading","status"])]),o("div",Q,[e[3]||(e[3]=o("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Esempio con Nomi Lunghi - Tooltip e Troncamento ",-1)),e[4]||(e[4]=o("p",{class:"text-sm text-gray-600 dark:text-gray-400 mb-4"}," I nomi dei task vengono troncati automaticamente e mostrati per intero nel tooltip. ",-1)),x(T,{title:"Timesheet con Task Lunghi",tasks:E,days:d.slice(0,7),"daily-totals":F.value,"grand-total":j.value,loading:!1,editable:!1,"show-stats":!0,"show-day-totals":!0,"show-indicators":!0,"show-legend":!0,status:{status:"pending",totalHours:45.5,billableHours:32},"row-header-label":"Progetto/Task",onCellClick:k},null,8,["days","daily-totals","grand-total"])]),o("div",X,[e[5]||(e[5]=o("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Log Eventi ",-1)),o("div",Y,[f.value.length===0?(g(),m("div",Z," Nessun evento registrato ")):(g(),m("div",ee,[(g(!0),m(B,null,H(f.value.slice().reverse(),(s,a)=>(g(),m("div",{key:a,class:"text-sm font-mono bg-white dark:bg-gray-700 p-2 rounded border"},[o("span",te,$(s.timestamp),1),o("span",ae,$(s.message),1)]))),128))]))])])]))}};export{ne as default};
