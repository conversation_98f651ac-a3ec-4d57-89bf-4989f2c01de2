import{r as f,w as se,b as o,l as m,o as a,j as e,t as r,F as z,p as S,e as l,n as y,c as ae,x as oe,h as re,k as E,s as c,E as V,I as ie,B as ne,Q as le}from"./vendor.js";import{u as de}from"./certifications.js";import{_ as ce,H as g}from"./app.js";import{_ as ue}from"./PageHeader.js";import{_ as me}from"./Breadcrumb.js";import{S as j}from"./StandardButton.js";const ge={class:"p-4"},ve={class:"flex items-start justify-between"},pe={class:"flex-1"},_e={class:"text-sm font-medium text-gray-900 mb-2"},he={class:"max-h-64 overflow-y-auto text-xs text-gray-600 space-y-1 font-mono"},xe={__name:"DisposableMessage",props:{title:{type:String,default:"Dettagli Calcolo"},logs:{type:Array,default:()=>[]},show:{type:Boolean,default:!1},autoHide:{type:Number,default:0}},emits:["close"],setup($,{emit:M}){const k=$,w=M,p=f(!1),d=()=>{p.value=!1,setTimeout(()=>{w("close")},300)};return se(()=>k.show,b=>{b?(p.value=!0,k.autoHide>0&&setTimeout(()=>{d()},k.autoHide)):p.value=!1},{immediate:!0}),(b,h)=>p.value?(a(),o("div",{key:0,class:y(["fixed bottom-4 right-4 max-w-md bg-white border border-gray-200 rounded-lg shadow-lg z-50 transition-all duration-300",{"transform translate-y-0 opacity-100":p.value,"transform translate-y-full opacity-0":!p.value}])},[e("div",ge,[e("div",ve,[e("div",pe,[e("h3",_e,r($.title),1),e("div",he,[(a(!0),o(z,null,S($.logs,(n,x)=>(a(),o("div",{key:x,class:"whitespace-pre-wrap"},r(n),1))),128))])]),e("button",{onClick:d,class:"ml-4 text-gray-400 hover:text-gray-600 transition-colors"},[l(g,{name:"x-mark",class:"h-5 w-5"})])])]),e("div",{class:"px-4 pb-4"},[e("button",{onClick:d,class:"w-full px-3 py-2 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"}," Chiudi ")])],2)):m("",!0)}},fe=ce(xe,[["__scopeId","data-v-a51fbda6"]]),ye={class:"certification-readiness"},be={class:"mb-6"},ke={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6"},we={key:0,class:"text-center py-8"},Ce={class:"flex items-center justify-center"},ze={key:1,class:"text-center py-8"},Se={class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4 mb-4"},Me={class:"flex"},qe={class:"flex-shrink-0"},Ee={class:"ml-3"},$e={class:"text-sm text-red-800 dark:text-red-200"},Re={key:2,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},je=["onClick"],Ae={class:"flex items-start justify-between"},Pe={class:"flex-1"},Ve={class:"font-semibold text-gray-900 dark:text-white text-sm"},Be={class:"text-xs text-gray-600 dark:text-gray-400 mt-1"},We={key:0,class:"text-xs text-gray-500 dark:text-gray-400 mt-2 line-clamp-3"},Ne={key:0,class:"ml-2"},De={key:0,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6"},Ie={class:"flex items-center justify-between mb-6"},Oe={class:"text-lg font-semibold text-gray-900 dark:text-white"},Fe={key:0},He={class:"space-y-2 mb-6"},Te=["id","onUpdate:modelValue"],Le=["for"],Ge={key:0,class:"text-green-600 dark:text-green-400 text-xs"},Ue={class:"space-y-4"},Qe={class:"bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4 border-2 border-primary-200 dark:border-primary-700"},Je={class:"flex items-center justify-between mb-2"},Xe={class:"text-sm font-medium text-primary-700 dark:text-primary-300 flex items-center"},Ye={class:"w-full bg-primary-200 dark:bg-primary-800 rounded-full h-3"},Ze={class:"text-xs text-primary-600 dark:text-primary-400 mt-2"},Ke={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},et={class:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4"},tt={class:"flex items-center justify-between mb-2"},st={class:"text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center"},at={class:"w-full bg-gray-200 rounded-full h-2"},ot={class:"bg-green-50 rounded-lg p-4"},rt={class:"flex items-center justify-between mb-2"},it={class:"text-sm font-medium text-green-700 flex items-center"},nt={key:0,class:"ml-2 px-1.5 py-0.5 bg-emerald-100 text-emerald-700 text-xs font-medium rounded"},lt={class:"flex items-center space-x-2"},dt={class:"w-full bg-green-200 rounded-full h-2"},ct={class:"text-xs text-green-600 mt-2"},ut={key:0,class:"mt-4 bg-amber-50 border border-amber-200 rounded-lg p-3"},mt={class:"flex items-center mb-2"},gt={class:"text-xs text-amber-700 space-y-1"},vt={key:0},pt={key:1},_t={key:1,class:"mt-6 bg-white border border-gray-200 rounded-lg p-4"},ht={class:"font-medium text-gray-900 mb-4 flex items-center"},xt={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},ft={class:"space-y-2"},yt={class:"flex-1"},bt={class:"text-sm font-medium text-gray-900"},kt={class:"text-xs text-gray-500"},wt={class:"text-right"},Ct={class:"text-xs text-gray-500"},zt={class:"space-y-2"},St={class:"text-xs text-blue-700"},Mt={key:0,class:"text-blue-600 font-medium mt-1"},qt={key:2,class:"mt-6 bg-gradient-to-br from-emerald-50 to-teal-50 border border-emerald-200 rounded-lg p-4"},Et={class:"font-medium text-emerald-900 mb-4 flex items-center"},$t={class:"ml-2 px-2 py-1 bg-emerald-100 text-emerald-700 text-xs font-medium rounded-full"},Rt={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-4"},jt={class:"text-sm font-medium text-emerald-800 mb-3 flex items-center"},At={class:"space-y-2"},Pt={class:"flex items-center justify-between mb-2"},Vt={class:"flex items-center"},Bt={class:"text-sm font-medium text-gray-900"},Wt={class:"text-xs bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full"},Nt={key:0,class:"text-xs text-gray-600"},Dt={class:"flex items-center"},It={key:0,class:"ml-1 text-emerald-600"},Ot={class:"text-sm font-medium text-emerald-800 mb-3 flex items-center"},Ft={class:"space-y-2"},Ht={class:"flex-1"},Tt={class:"text-sm font-medium text-gray-900 flex items-center"},Lt={class:"text-xs text-gray-500 mt-1"},Gt={class:"text-right"},Ut={class:"text-sm font-bold text-emerald-700"},Qt={class:"bg-white rounded-lg p-3 border border-emerald-100"},Jt={class:"flex items-center mb-2"},Xt={key:3,class:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-6"},Yt={class:"bg-blue-50 rounded-lg p-4"},Zt={class:"space-y-1 text-sm text-blue-700"},Kt={key:0},es={key:1},ts={class:"bg-green-50 rounded-lg p-4"},ss={class:"text-sm text-green-700"},as={key:1,class:"bg-white rounded-lg shadow-sm p-12 text-center"},cs={__name:"CertificationReadiness",setup($){const M=de(),k=f(!1),w=f(null),p=f(!1),d=f(null),b=f({}),h=f(0),n=f(null),x=f(0),R=f(!1),U=[{name:"Certificazioni",path:"/app/certifications/dashboard",icon:"shield-check"},{name:"Valutazione Readiness",path:"/app/certifications/readiness"}],Q=ae(()=>{const s=M.standardsByCategory,t=[];return Object.values(s).forEach(v=>{Object.values(v).forEach(C=>{t.push(C)})}),t}),B=async()=>{k.value=!0,w.value=null;try{await M.fetchStandardsCatalog()}catch(s){w.value=s.message}finally{k.value=!1}},J=s=>{d.value=s,b.value={},h.value=0,s.requirements&&s.requirements.forEach((t,v)=>{b.value[v]=!1})},X=()=>{var v;if(!((v=d.value)!=null&&v.requirements))return;const s=d.value.requirements.length,t=Object.values(b.value).filter(Boolean).length;h.value=Math.round(t/s*100),W()},W=()=>{if(!n.value){x.value=h.value;return}const s=n.value.platform_compliance_score||0,t=n.value.enhanced_modules_analyzed||0;let v=.5,C=.5;t>0&&(v=Math.min(.6,.5+t*.025),C=1-v),x.value=Math.round(h.value*C+s*v),console.log("🎯 Enhanced Readiness Calculation:"),console.log(`   → Requirements Score: ${h.value}% (weight: ${Math.round(C*100)}%)`),console.log(`   → Platform Score: ${s}% (weight: ${Math.round(v*100)}%)`),console.log(`   → Enhanced Modules: ${t}`),console.log(`   → Final Combined Score: ${x.value}%`)},Y=async()=>{if(d.value){p.value=!0;try{const s=await M.fetchPlatformCompliance(d.value.code);s.success&&(n.value=s.data,W())}catch(s){w.value=s.message}finally{p.value=!1}}},Z=s=>({governance:"shield-exclamation",engagement:"trophy",ceo_strategic:"academic-cap",recruiting:"user-plus"})[s]||"circle-stack",K=s=>({governance:"Governance & Compliance",engagement:"Employee Engagement",ceo_strategic:"Strategic Intelligence",recruiting:"Recruiting & Selection"})[s]||s.replace("_"," "),A=()=>{if(!n.value)return{platformWeight:.5,readinessWeight:.5};const s=n.value.enhanced_modules_analyzed||0;let t=.5;return s>0&&(t=Math.min(.6,.5+s*.025)),{platformWeight:t,readinessWeight:1-t}},q=s=>s>=80?"text-green-600":s>=60?"text-yellow-600":s>=40?"text-orange-600":"text-red-600",P=s=>s>=80?"bg-green-500":s>=60?"bg-yellow-500":s>=40?"bg-orange-500":"bg-red-500",ee=s=>s>=80?"Ottimo! Siete pronti per richiedere la certificazione.":s>=60?"Buon livello. Ancora qualche preparazione necessaria.":s>=40?"Preparazione media. Serve più lavoro sui requisiti.":"Preparazione iniziale. Molto lavoro da fare prima della certificazione.",te=s=>s>=80?"2-4 settimane":s>=60?"1-3 mesi":s>=40?"3-6 mesi":"6-12 mesi",N=s=>new Intl.NumberFormat("it-IT").format(s);return oe(()=>{(!M.standardsCatalog.catalog||Object.keys(M.standardsCatalog.catalog).length===0)&&B()}),(s,t)=>{var v,C,D,I,O,F,H,T,L;return a(),o("div",ye,[l(me,{breadcrumbs:U}),e("div",be,[l(ue,{title:"Valutazione Readiness",subtitle:"Valuta il livello di preparazione per ottenere nuove certificazioni",icon:"clipboard-document-check","icon-color":"text-secondary-600 dark:text-secondary-400"},{actions:E(()=>[l(j,{to:"/app/certifications/dashboard",variant:"secondary",icon:"arrow-left"},{default:E(()=>t[2]||(t[2]=[c(" Dashboard ")])),_:1,__:[2]}),l(j,{to:"/app/certifications/catalog",variant:"secondary",icon:"book-open"},{default:E(()=>t[3]||(t[3]=[c(" Catalogo ")])),_:1,__:[3]})]),_:1})]),e("div",ke,[t[6]||(t[6]=e("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},"Seleziona Standard da Valutare",-1)),k.value?(a(),o("div",we,[e("div",Ce,[l(g,{name:"arrow-path",size:"lg",class:"animate-spin text-primary-600 dark:text-primary-400"})]),t[4]||(t[4]=e("p",{class:"text-gray-500 dark:text-gray-400 mt-3"},"Caricamento standard...",-1))])):w.value?(a(),o("div",ze,[e("div",Se,[e("div",Me,[e("div",qe,[l(g,{name:"exclamation-triangle",size:"md",class:"text-red-400"})]),e("div",Ee,[e("p",$e,r(w.value),1)])])]),l(j,{onClick:B,variant:"primary",icon:"arrow-path"},{default:E(()=>t[5]||(t[5]=[c(" Riprova ")])),_:1,__:[5]})])):(a(),o("div",Re,[(a(!0),o(z,null,S(Q.value,i=>{var u,_;return a(),o("div",{key:i.code,class:y(["border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-primary-300 dark:hover:border-primary-600 cursor-pointer transition-colors bg-white dark:bg-gray-800",{"border-primary-500 bg-primary-50 dark:bg-primary-900/20":((u=d.value)==null?void 0:u.code)===i.code}]),onClick:G=>J(i)},[e("div",Ae,[e("div",Pe,[e("h3",Ve,r(i.name),1),e("p",Be,r(i.category),1),i.description?(a(),o("p",We,r(i.description),1)):m("",!0)]),((_=d.value)==null?void 0:_.code)===i.code?(a(),o("div",Ne,[l(g,{name:"check-circle",class:"h-5 w-5 text-primary-500 dark:text-primary-400"})])):m("",!0)])],10,je)}),128))]))]),d.value?(a(),o("div",De,[e("div",Ie,[e("h2",Oe," Valutazione: "+r(d.value.name),1),l(j,{onClick:Y,variant:"primary",icon:p.value?"arrow-path":"play",loading:p.value,disabled:p.value},{default:E(()=>[c(r(p.value?"Analisi in corso...":"Avvia Valutazione"),1)]),_:1},8,["icon","loading","disabled"])]),d.value.requirements&&d.value.requirements.length>0?(a(),o("div",Fe,[t[7]||(t[7]=e("h3",{class:"font-medium text-gray-900 dark:text-white mb-3"},"Requisiti Principali",-1)),e("div",He,[(a(!0),o(z,null,S(d.value.requirements,(i,u)=>(a(),o("div",{key:u,class:"flex items-center p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"},[ne(e("input",{type:"checkbox",id:`req-${u}`,"onUpdate:modelValue":_=>b.value[u]=_,class:"h-4 w-4 text-primary-600 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 rounded focus:ring-primary-500 dark:focus:ring-primary-400",onChange:X},null,40,Te),[[le,b.value[u]]]),e("label",{for:`req-${u}`,class:"ml-3 text-sm text-gray-700 dark:text-gray-300 flex-1"},r(i),9,Le),b.value[u]?(a(),o("span",Ge," ✓ Completato ")):m("",!0)]))),128))])])):m("",!0),e("div",Ue,[e("div",Qe,[e("div",Je,[e("span",Xe,[l(g,{name:"star",class:"h-4 w-4 mr-1"}),t[8]||(t[8]=c(" Livello di Preparazione Complessivo "))]),e("span",{class:y(["text-xl font-bold",q(x.value)])},r(x.value)+"% ",3)]),e("div",Ye,[e("div",{class:y(["h-3 rounded-full transition-all duration-300",P(x.value)]),style:V({width:x.value+"%"})},null,6)]),e("p",Ze,r(ee(x.value)),1)]),e("div",Ke,[e("div",et,[e("div",tt,[e("span",st,[l(g,{name:"clipboard-document-check",class:"h-4 w-4 mr-1"}),t[9]||(t[9]=c(" Requisiti Standard "))]),e("span",{class:y(["text-lg font-bold",q(h.value)])},r(h.value)+"% ",3)]),e("div",at,[e("div",{class:y(["h-2 rounded-full transition-all duration-300",P(h.value)]),style:V({width:h.value+"%"})},null,6)]),t[10]||(t[10]=e("p",{class:"text-xs text-gray-600 mt-2"}," Completamento requisiti specifici ",-1))]),e("div",ot,[e("div",rt,[e("span",it,[l(g,{name:"cpu-chip",class:"h-4 w-4 mr-1"}),t[11]||(t[11]=c(" Compliance DatPortal ")),((v=n.value)==null?void 0:v.enhanced_modules_analyzed)>0?(a(),o("span",nt," ENHANCED ")):m("",!0)]),e("div",lt,[e("span",{class:y(["text-lg font-bold",q(((C=n.value)==null?void 0:C.platform_compliance_score)||0)])},r(((D=n.value)==null?void 0:D.platform_compliance_score)||0)+"% ",3),(I=n.value)!=null&&I.calculation_logs?(a(),o("button",{key:0,onClick:t[0]||(t[0]=i=>R.value=!0),class:"text-blue-600 hover:text-blue-800 transition-colors",title:"Mostra dettagli calcolo"},[l(g,{name:"information-circle",class:"h-4 w-4"})])):m("",!0)])]),e("div",dt,[e("div",{class:y(["h-2 rounded-full transition-all duration-300",P(((O=n.value)==null?void 0:O.platform_compliance_score)||0)]),style:V({width:(((F=n.value)==null?void 0:F.platform_compliance_score)||0)+"%"})},null,6)]),e("p",ct,r(n.value?`${n.value.total_features_analyzed} funzionalità analizzate`:'Clicca "Avvia Valutazione" per analizzare'),1)])]),((H=n.value)==null?void 0:H.enhanced_modules_analyzed)>0?(a(),o("div",ut,[e("div",mt,[l(g,{name:"calculator",class:"h-4 w-4 mr-2 text-amber-600"}),t[12]||(t[12]=e("span",{class:"text-sm font-medium text-amber-800"},"Calcolo Enhanced Readiness Score",-1))]),e("div",gt,[A().platformWeight!==.5?(a(),o("div",vt,[t[13]||(t[13]=c(" • ")),t[14]||(t[14]=e("strong",null,"Peso dinamico applicato:",-1)),c(" Requisiti "+r(Math.round(A().readinessWeight*100))+"% + Platform Compliance "+r(Math.round(A().platformWeight*100))+"% ",1)])):(a(),o("div",pt,t[15]||(t[15]=[c(" • "),e("strong",null,"Peso standard:",-1),c(" Requisiti 50% + Platform Compliance 50% ")]))),e("div",null,[t[16]||(t[16]=c("• ")),t[17]||(t[17]=e("strong",null,"Moduli Enhanced attivi:",-1)),c(" "+r(n.value.enhanced_modules_analyzed)+"/4 moduli avanzati",1)]),e("div",null,[t[18]||(t[18]=c("• ")),t[19]||(t[19]=e("strong",null,"Bonus Enhanced:",-1)),c(" +"+r(Math.round(n.value.enhanced_score_contribution))+" punti dal weighting avanzato",1)])])])):m("",!0)]),n.value?(a(),o("div",_t,[e("h3",ht,[l(g,{name:"chart-bar",class:"h-5 w-5 mr-2 text-blue-600"}),t[20]||(t[20]=c(" Analisi Compliance Piattaforma DatPortal "))]),e("div",xt,[e("div",null,[t[21]||(t[21]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"Funzionalità Analizzate",-1)),e("div",ft,[(a(!0),o(z,null,S(n.value.compliance_details,(i,u)=>{var _;return a(),o("div",{key:u,class:"flex items-center justify-between p-2 bg-gray-50 rounded"},[e("div",yt,[e("div",bt,r(i.name),1),e("div",kt,r(((_=i.requirements_covered)==null?void 0:_.length)||0)+" requisiti coperti",1)]),e("div",wt,[e("div",{class:y(["text-sm font-bold",q(i.weighted_score)])},r(Math.round(i.weighted_score))+"% ",3),e("div",Ct,"peso "+r(i.compliance_score)+"%",1)])])}),128))])]),e("div",null,[t[22]||(t[22]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"Raccomandazioni",-1)),e("div",zt,[(a(!0),o(z,null,S((T=n.value.recommendations)==null?void 0:T.slice(0,5),(i,u)=>(a(),o("div",{key:u,class:"flex items-start p-2 bg-blue-50 rounded"},[l(g,{name:i.type==="feature_adoption"?"arrow-trending-up":"wrench-screwdriver",class:"h-4 w-4 mr-2 text-blue-600 flex-shrink-0 mt-0.5"},null,8,["name"]),e("div",St,[c(r(i.suggestion)+" ",1),i.potential_impact?(a(),o("div",Mt," Impatto potenziale: +"+r(i.potential_impact)+"% ",1)):m("",!0)])]))),128))])])])])):m("",!0),n.value&&n.value.enhanced_modules_analyzed>0?(a(),o("div",qt,[e("h3",Et,[l(g,{name:"sparkles",class:"h-5 w-5 mr-2 text-emerald-600"}),t[23]||(t[23]=c(" 🆕 Analisi Moduli Avanzati Enhanced ")),e("span",$t," +"+r(Math.round(n.value.enhanced_score_contribution))+" punti ",1)]),e("div",Rt,[e("div",null,[e("h4",jt,[l(g,{name:"chart-pie",class:"h-4 w-4 mr-2"}),t[24]||(t[24]=c(" Moduli Enhanced Analizzati "))]),e("div",At,[(a(!0),o(z,null,S(n.value.module_breakdown,(i,u)=>(a(),o("div",{key:u,class:"p-3 bg-white rounded-lg border border-emerald-100"},[e("div",Pt,[e("div",Vt,[l(g,{name:Z(u),class:"h-4 w-4 mr-2 text-emerald-600"},null,8,["name"]),e("span",Bt,r(K(u)),1)]),e("span",Wt,r(Object.keys(i).length)+" features ",1)]),Object.keys(i).length>0?(a(),o("div",Nt,[(a(!0),o(z,null,S(i,(_,G)=>(a(),o("div",{key:G,class:"flex justify-between items-center py-1"},[e("span",null,r(_.name),1),e("div",Dt,[e("span",{class:y(["font-medium",q(_.weighted_score)])},r(Math.round(_.weighted_score))+"% ",3),_.is_enhanced_module?(a(),o("span",It,"⚡")):m("",!0)])]))),128))])):m("",!0)]))),128))])]),e("div",null,[e("h4",Ot,[l(g,{name:"lightning-bolt",class:"h-4 w-4 mr-2"}),t[25]||(t[25]=c(" Enhanced Features Weights "))]),e("div",Ft,[s.details.is_enhanced_module?(a(!0),o(z,{key:0},S(n.value.compliance_details,(i,u)=>(a(),o("div",{key:u,class:"flex items-center justify-between p-3 bg-white rounded-lg border border-emerald-100"},[e("div",Ht,[e("div",Tt,[c(r(i.name)+" ",1),t[26]||(t[26]=e("span",{class:"ml-2 text-emerald-600"},"⚡",-1))]),e("div",Lt," Base: "+r(i.base_compliance_score)+"% × "+r(i.enhanced_weight)+" weight = "+r(i.compliance_score)+"% ",1)]),e("div",Gt,[e("div",Ut,r(Math.round(i.weighted_score))+"% ",1),t[27]||(t[27]=e("div",{class:"text-xs text-emerald-600"},"enhanced",-1))])]))),128)):m("",!0)])])]),e("div",Qt,[e("div",Jt,[l(g,{name:"information-circle",class:"h-4 w-4 mr-2 text-emerald-600"}),t[28]||(t[28]=e("span",{class:"text-sm font-medium text-emerald-800"},"Calcolo Enhanced",-1))]),t[29]||(t[29]=ie('<div class="text-xs text-gray-600 space-y-1"><div>• <strong>Governance Module:</strong> +50% peso per audit, compliance e risk management</div><div>• <strong>CEO Strategic:</strong> +20% peso per strategic oversight e management reviews</div><div>• <strong>Engagement:</strong> +10% peso per training compliance e employee involvement</div><div>• <strong>Recruiting:</strong> +10% peso per competency management e skills assessment</div></div>',1))])])):m("",!0),d.value.estimated_cost?(a(),o("div",Xt,[e("div",Yt,[t[30]||(t[30]=e("h4",{class:"font-medium text-blue-900 mb-2"},"Costi Stimati",-1)),e("div",Zt,[d.value.estimated_cost.initial?(a(),o("div",Kt," Costo iniziale: €"+r(N(d.value.estimated_cost.initial)),1)):m("",!0),d.value.estimated_cost.annual?(a(),o("div",es," Costo annuale: €"+r(N(d.value.estimated_cost.annual)),1)):m("",!0)])]),e("div",ts,[t[33]||(t[33]=e("h4",{class:"font-medium text-green-900 mb-2"},"Timeline Stimata",-1)),e("div",ss,[e("div",null,"Preparazione: "+r(te(h.value)),1),t[31]||(t[31]=e("div",null,"Audit: 2-4 settimane",-1)),t[32]||(t[32]=e("div",null,"Certificazione: 1-2 settimane",-1))])])])):m("",!0)])):!k.value&&!w.value?(a(),o("div",as,[l(g,{name:"clipboard-document-check",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),t[34]||(t[34]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Seleziona uno Standard",-1)),t[35]||(t[35]=e("p",{class:"text-gray-500"},"Scegli uno standard di certificazione per iniziare la valutazione di readiness",-1))])):m("",!0),R.value?(a(),re(fe,{key:2,show:R.value,title:"Dettagli Calcolo Compliance",logs:((L=n.value)==null?void 0:L.calculation_logs)||[],onClose:t[1]||(t[1]=i=>R.value=!1)},null,8,["show","logs"])):m("",!0)])}}};export{cs as default};
