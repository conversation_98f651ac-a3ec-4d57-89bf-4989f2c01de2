import{r as j,c as x,x as O,b as d,j as t,e as i,B as _,C as K,H as $,I as Q,F as S,p as A,t as r,l as b,s as v,h as W,k as X,f as Y,q as Z,o as n,n as tt}from"./vendor.js";import{u as et}from"./recruiting.js";import{_ as at,H as c}from"./app.js";import{S as T}from"./StandardButton.js";import{A as st}from"./ActionButtonGroup.js";const rt={class:"job-postings-list"},ot={class:"mb-6 flex items-center justify-between"},it={class:"bg-white dark:bg-gray-800 rounded-lg shadow mb-6 p-6"},nt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},lt={class:"relative"},dt=["value"],ct={class:"flex items-end space-x-2"},ut={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},gt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},pt={class:"flex items-center"},xt={class:"ml-4"},mt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},yt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},bt={class:"flex items-center"},vt={class:"ml-4"},ht={class:"text-2xl font-semibold text-gray-900 dark:text-white"},kt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},_t={class:"flex items-center"},ft={class:"ml-4"},wt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},zt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},Pt={class:"flex items-center"},Ct={class:"ml-4"},jt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},$t={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},St={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},At={class:"text-lg font-medium text-gray-900 dark:text-white"},Tt={key:0,class:"text-sm font-normal text-gray-500 dark:text-gray-400"},Dt={key:0,class:"flex justify-center py-12"},Jt={key:1,class:"text-center py-12"},Nt={class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},Vt={class:"text-gray-500 dark:text-gray-400 mb-4"},Bt={key:2,class:"overflow-x-auto"},Et={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},It={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Lt=["onClick"],Ft={class:"px-6 py-4 whitespace-nowrap"},Mt={class:"text-sm font-medium text-gray-900 dark:text-white"},Rt={class:"text-sm text-gray-500 dark:text-gray-400 flex items-center"},Ht={key:0,class:"ml-2 flex items-center"},Ut={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},Gt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},qt={class:"px-6 py-4 whitespace-nowrap"},Ot={class:"px-6 py-4 whitespace-nowrap text-center"},Kt={class:"flex items-center justify-center space-x-1"},Qt={class:"text-sm text-gray-900 dark:text-white"},Wt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},Xt={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Yt={class:"flex items-center justify-end"},Zt={key:3,class:"px-6 py-4 border-t border-gray-200 dark:border-gray-700"},te={class:"flex items-center justify-between"},ee={class:"text-sm text-gray-700 dark:text-gray-300"},ae={class:"flex items-center space-x-2"},se=["disabled"],re={class:"px-3 py-1 text-sm text-gray-700 dark:text-gray-300"},oe=["disabled"],ie={__name:"JobPostingsList",setup(ne){const f=Z(),u=et(),w=j([]),l=j({search:"",status:"",department_id:""}),D=x(()=>u.loading),g=x(()=>u.jobPostings),o=x(()=>u.pagination),m=x(()=>({total:g.value.length,active:g.value.filter(a=>a.status==="active").length,applications:g.value.reduce((a,e)=>a+e.applications_count,0),public:g.value.filter(a=>a.is_public).length})),h=x(()=>l.value.search||l.value.status||l.value.department_id);let z=null;const J=()=>{clearTimeout(z),z=setTimeout(()=>{y()},300)},k=async()=>{const a={...l.value};Object.keys(a).forEach(e=>{(a[e]===""||a[e]===null||a[e]===void 0)&&delete a[e]}),await u.fetchJobPostings(a)},y=()=>{u.pagination.page=1,k()},N=()=>{l.value={search:"",status:"",department_id:""},y()},P=a=>{a>=1&&a<=o.value.pages&&(u.pagination.page=a,k())},V=a=>{f.push(`/app/recruiting/job-postings/${a.id}`)},B=a=>{f.push(`/app/recruiting/applications?job_posting_id=${a.id}`)},E=async a=>{try{await u.updateJobPosting(a.id,{is_public:!a.is_public})}catch(e){console.error("Error toggling public status:",e)}},I=async a=>{if(a.applications_count>0){alert(`Impossibile eliminare posizione con ${a.applications_count} candidature`);return}try{await u.deleteJobPosting(a.id)}catch(e){console.error("Error deleting job posting:",e)}},L=a=>[{key:"view-applications",label:`Candidature (${a.applications_count})`,icon:"document-text",danger:!1},{key:"toggle-public",label:a.is_public?"Rendi privata":"Rendi pubblica",icon:a.is_public?"eye-slash":"eye",danger:!1}],F=(a,e)=>{switch(a){case"view-applications":B(e);break;case"toggle-public":E(e);break}},M=a=>({draft:"Bozza",active:"Attiva",paused:"In pausa",closed:"Chiusa"})[a]||a,R=a=>({draft:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",active:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",paused:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",closed:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"})[a]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",H=a=>({full_time:"Tempo pieno",part_time:"Part-time",contract:"Contratto",intern:"Stage"})[a]||a,U=a=>a?new Date(a).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"",G=async()=>{try{w.value=await u.fetchDepartments()}catch(a){console.error("Error loading departments:",a)}};return O(async()=>{await Promise.all([k(),G()])}),(a,e)=>{const q=Y("router-link");return n(),d("div",rt,[t("div",ot,[e[6]||(e[6]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Posizioni Aperte"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Gestione posizioni lavorative e candidature ")],-1)),i(T,{variant:"primary",icon:"plus",text:"Nuova Posizione",onClick:e[0]||(e[0]=s=>a.$router.push("/app/recruiting/job-postings/new"))})]),t("div",it,[t("div",nt,[t("div",null,[e[7]||(e[7]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Cerca posizione ",-1)),t("div",lt,[i(c,{name:"magnifying-glass",size:"sm",class:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),_(t("input",{"onUpdate:modelValue":e[1]||(e[1]=s=>l.value.search=s),type:"text",placeholder:"Titolo, location...",class:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",onInput:J},null,544),[[K,l.value.search]])])]),t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stato ",-1)),_(t("select",{"onUpdate:modelValue":e[2]||(e[2]=s=>l.value.status=s),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",onChange:y},e[8]||(e[8]=[Q('<option value="" data-v-0c94564c>Tutti gli stati</option><option value="draft" data-v-0c94564c>Bozza</option><option value="active" data-v-0c94564c>Attiva</option><option value="paused" data-v-0c94564c>In pausa</option><option value="closed" data-v-0c94564c>Chiusa</option>',5)]),544),[[$,l.value.status]])]),t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Dipartimento ",-1)),_(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>l.value.department_id=s),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",onChange:y},[e[10]||(e[10]=t("option",{value:""},"Tutti i dipartimenti",-1)),(n(!0),d(S,null,A(w.value,s=>(n(),d("option",{key:s.id,value:s.id},r(s.name),9,dt))),128))],544),[[$,l.value.department_id]])]),t("div",ct,[i(T,{variant:"outline-secondary",icon:"funnel",text:"Pulisci Filtri",size:"sm",onClick:N})])])]),t("div",ut,[t("div",gt,[t("div",pt,[i(c,{name:"briefcase",size:"lg",class:"text-gray-600 dark:text-gray-400"}),t("div",xt,[e[12]||(e[12]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Totale Posizioni",-1)),t("p",mt,r(m.value.total),1)])])]),t("div",yt,[t("div",bt,[i(c,{name:"check-circle",size:"lg",class:"text-green-600 dark:text-green-400"}),t("div",vt,[e[13]||(e[13]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Posizioni Attive",-1)),t("p",ht,r(m.value.active),1)])])]),t("div",kt,[t("div",_t,[i(c,{name:"document-text",size:"lg",class:"text-blue-600 dark:text-blue-400"}),t("div",ft,[e[14]||(e[14]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Candidature Totali",-1)),t("p",wt,r(m.value.applications),1)])])]),t("div",zt,[t("div",Pt,[i(c,{name:"globe-alt",size:"lg",class:"text-purple-600 dark:text-purple-400"}),t("div",Ct,[e[15]||(e[15]=t("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Posizioni Pubbliche",-1)),t("p",jt,r(m.value.public),1)])])])]),t("div",$t,[t("div",St,[t("h3",At,[e[16]||(e[16]=v(" Posizioni Lavorative ")),o.value.total?(n(),d("span",Tt," ("+r(o.value.total)+" totali) ",1)):b("",!0)])]),D.value?(n(),d("div",Dt,e[17]||(e[17]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)]))):g.value.length?(n(),d("div",Bt,[t("table",Et,[e[20]||(e[20]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Posizione "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipartimento "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Tipo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato "),t("th",{class:"px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Candidature "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Creata il "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),t("tbody",It,[(n(!0),d(S,null,A(g.value,s=>{var C;return n(),d("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",onClick:p=>V(s)},[t("td",Ft,[t("div",null,[t("div",Mt,r(s.title),1),t("div",Rt,[i(c,{name:"map-pin",size:"xs",class:"mr-1"}),v(" "+r(s.location)+" ",1),s.remote_allowed?(n(),d("span",Ht,[i(c,{name:"globe-alt",size:"xs",class:"mr-1"}),e[19]||(e[19]=v(" Remoto "))])):b("",!0)])])]),t("td",Ut,r(((C=s.department)==null?void 0:C.name)||"N/A"),1),t("td",Gt,r(H(s.employment_type)),1),t("td",qt,[t("span",{class:tt(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",R(s.status)])},r(M(s.status)),3)]),t("td",Ot,[t("div",Kt,[i(c,{name:"users",size:"sm",class:"text-gray-400"}),t("span",Qt,r(s.applications_count),1)])]),t("td",Wt,r(U(s.created_at)),1),t("td",Xt,[t("div",Yt,[i(st,{onView:p=>a.$router.push(`/app/recruiting/job-postings/${s.id}`),onEdit:p=>a.$router.push(`/app/recruiting/job-postings/${s.id}/edit`),onDelete:p=>I(s),"more-actions":L(s),onAction:p=>F(p,s),"delete-message":`Sei sicuro di voler eliminare la posizione '${s.title}'?`},null,8,["onView","onEdit","onDelete","more-actions","onAction","delete-message"])])])],8,Lt)}),128))])])])):(n(),d("div",Jt,[i(c,{name:"briefcase",size:"lg",class:"mx-auto text-gray-400 mb-4"}),t("h3",Nt,r(h.value?"Nessuna posizione trovata":"Nessuna posizione disponibile"),1),t("p",Vt,r(h.value?"Prova a modificare i filtri di ricerca.":"Crea la tua prima posizione lavorativa."),1),h.value?b("",!0):(n(),W(q,{key:0,to:"/app/recruiting/job-postings/new",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"},{default:X(()=>[i(c,{name:"plus",size:"sm",class:"mr-2"}),e[18]||(e[18]=v(" Nuova Posizione "))]),_:1,__:[18]}))])),o.value.pages>1?(n(),d("div",Zt,[t("div",te,[t("div",ee," Mostrando "+r((o.value.page-1)*o.value.per_page+1)+" - "+r(Math.min(o.value.page*o.value.per_page,o.value.total))+" di "+r(o.value.total)+" risultati ",1),t("div",ae,[t("button",{onClick:e[4]||(e[4]=s=>P(o.value.page-1)),disabled:o.value.page<=1,class:"px-3 py-1 rounded border border-gray-300 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"}," Precedente ",8,se),t("span",re," Pagina "+r(o.value.page)+" di "+r(o.value.pages),1),t("button",{onClick:e[5]||(e[5]=s=>P(o.value.page+1)),disabled:o.value.page>=o.value.pages,class:"px-3 py-1 rounded border border-gray-300 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"}," Successiva ",8,oe)])])])):b("",!0)])])}}},pe=at(ie,[["__scopeId","data-v-0c94564c"]]);export{pe as default};
