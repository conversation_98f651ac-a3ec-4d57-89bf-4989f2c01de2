import{d as B,r as d,c as f,b as D,o as q,e as F,s as M,t as R,n as K}from"./vendor.js";import{c as i,H as L}from"./app.js";const N=B("hrAssistant",()=>{const l=d([]),o=d(null),u=d([]),m=d([]),p=d({}),c=d(!1),a=d(null),g=d({}),y=f(()=>l.value.filter(s=>s.sessionId===o.value)),b=f(()=>[{key:"contracts",label:"Informazioni Contrattuali",icon:"document-text"},{key:"onboarding",label:"Procedure di Onboarding",icon:"user-plus"},{key:"offboarding",label:"Procedure di Offboarding",icon:"user-minus"},{key:"leave",label:"Gestione Ferie",icon:"calendar-days"},{key:"permits",label:"<PERSON><PERSON>si e Congedi",icon:"clock"},{key:"travel",label:"Trasferte e Rimborsi",icon:"map-pin"},{key:"benefits",label:"Benefit e Welfare",icon:"gift"},{key:"tools",label:"Strumenti Aziendali",icon:"computer-desktop"},{key:"purchases",label:"Richiesta Acquisti",icon:"shopping-cart"},{key:"training",label:"Formazione e Certificazioni",icon:"academic-cap"}]);async function h(s){var r,t;c.value=!0,a.value=null;try{o.value||(o.value=`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`);const e={id:`msg_${Date.now()}`,type:"user",content:s,timestamp:new Date().toISOString(),sessionId:o.value};l.value.push(e);const n=await i.post("/api/communication/hr-assistant/chat",{message:s,session_id:o.value});if(n.data.success){const A={id:`msg_${Date.now()+1}`,type:"bot",content:n.data.data.response,category:n.data.data.category,confidence:n.data.data.confidence,suggestedActions:n.data.data.suggested_actions||[],timestamp:new Date().toISOString(),sessionId:o.value,conversationId:n.data.data.conversation_id};return l.value.push(A),{success:!0,data:n.data.data}}else return a.value=n.data.message,{success:!1,error:a.value}}catch(e){return a.value=((t=(r=e.response)==null?void 0:r.data)==null?void 0:t.message)||"Errore di connessione",{success:!1,error:a.value}}finally{c.value=!1}}async function w(s,r){try{if((await i.post("/api/communication/hr-assistant/feedback",{conversation_id:s,feedback:r})).data.success){const e=l.value.find(n=>n.conversationId===s);return e&&(e.userFeedback=r),{success:!0}}}catch(t){return console.error("Feedback submission failed:",t),{success:!1}}}async function v(s={}){var r,t;c.value=!0,a.value=null,g.value={...s};try{const e=new URLSearchParams;s.category&&e.append("category",s.category),s.search&&e.append("search",s.search),s.page&&e.append("page",s.page);const n=await i.get(`/api/communication/hr-assistant/knowledge-base?${e}`);return n.data.success?(u.value=n.data.data.entries||[],{success:!0,data:n.data.data}):(u.value=[],{success:!1,error:n.data.message})}catch(e){return a.value=((t=(r=e.response)==null?void 0:r.data)==null?void 0:t.message)||"Errore caricamento knowledge base",u.value=[],{success:!1,error:a.value}}finally{c.value=!1}}async function k(s){var r,t;c.value=!0;try{const e=await i.post("/api/communication/hr-assistant/knowledge-base",s);return e.data.success?(await v(g.value),{success:!0,data:e.data.data}):(a.value=e.data.message,{success:!1,error:a.value})}catch(e){return a.value=((t=(r=e.response)==null?void 0:r.data)==null?void 0:t.message)||"Errore creazione entry",{success:!1,error:a.value}}finally{c.value=!1}}async function _(s,r){var t,e;c.value=!0;try{const n=await i.put(`/api/communication/hr-assistant/knowledge-base/${s}`,r);return n.data.success?(await v(g.value),{success:!0,data:n.data.data}):(a.value=n.data.message,{success:!1,error:a.value})}catch(n){return a.value=((e=(t=n.response)==null?void 0:t.data)==null?void 0:e.message)||"Errore aggiornamento entry",{success:!1,error:a.value}}finally{c.value=!1}}async function x(s){var r,t;c.value=!0;try{const e=await i.delete(`/api/communication/hr-assistant/knowledge-base/${s}`);return e.data.success?(await v(g.value),{success:!0}):(a.value=e.data.message,{success:!1,error:a.value})}catch(e){return a.value=((t=(r=e.response)==null?void 0:r.data)==null?void 0:t.message)||"Errore eliminazione entry",{success:!1,error:a.value}}finally{c.value=!1}}async function S(s){var r,t;c.value=!0;try{const e=await i.post("/api/communication/hr-assistant/knowledge-base",{category:s.category,title:s.title,content:s.content||"",requirements:s.requirements||"",template_id:s.templateId||null,use_ai_assistance:!0});return e.data.success?{success:!0,data:e.data.data}:(a.value=e.data.message,{success:!1,error:a.value})}catch(e){return a.value=((t=(r=e.response)==null?void 0:r.data)==null?void 0:t.message)||"Errore generazione contenuto",{success:!1,error:a.value}}finally{c.value=!1}}async function E(s=null){var r,t;c.value=!0;try{const e=new URLSearchParams;s&&e.append("category",s);const n=await i.get(`/api/communication/hr-assistant/templates?${e}`);if(n.data.success)return m.value=n.data.data,{success:!0,data:n.data.data}}catch(e){return a.value=((t=(r=e.response)==null?void 0:r.data)==null?void 0:t.message)||"Errore caricamento template",{success:!1,error:a.value}}finally{c.value=!1}}async function I(s=30){var r,t;c.value=!0;try{const e=await i.get(`/api/communication/hr-assistant/analytics?days=${s}`);if(e.data.success)return p.value=e.data.data,{success:!0,data:e.data.data}}catch(e){return a.value=((t=(r=e.response)==null?void 0:r.data)==null?void 0:t.message)||"Errore caricamento analytics",{success:!1,error:a.value}}finally{c.value=!1}}function $(){o.value=`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}function C(){l.value=[],o.value=null}function z(){a.value=null}return{conversations:l,currentSessionId:o,knowledgeBase:u,templates:m,analytics:p,loading:c,error:a,lastFilters:g,currentConversation:y,categoriesList:b,sendMessage:h,provideFeedback:w,loadKnowledgeBase:v,createKnowledgeEntry:k,updateKnowledgeEntry:_,deleteKnowledgeEntry:x,generateAIContent:S,loadTemplates:E,loadAnalytics:I,startNewSession:$,clearConversations:C,clearError:z}}),T={__name:"ConfidenceBadge",props:{confidence:{type:String,required:!0,validator:l=>["high","medium","low"].includes(l)}},setup(l){const o=l,u={high:{label:"Alta",icon:"check-circle",classes:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"},medium:{label:"Media",icon:"exclamation-triangle",classes:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"},low:{label:"Bassa",icon:"question-mark-circle",classes:"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"}},m=f(()=>{var a;return((a=u[o.confidence])==null?void 0:a.label)||"Sconosciuta"}),p=f(()=>{var a;return((a=u[o.confidence])==null?void 0:a.icon)||"question-mark-circle"}),c=f(()=>{var a;return((a=u[o.confidence])==null?void 0:a.classes)||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"});return(a,g)=>(q(),D("span",{class:K(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",c.value])},[F(L,{name:p.value,class:"h-3 w-3 mr-1"},null,8,["name"]),M(" "+R(m.value),1)],2))}};export{T as _,N as u};
