import{b as n,e as d,j as t,l as u,f,k as g,t as i,F as v,p as h,c as _,r as D,x as V,q as R,o as s,s as m,h as C,n as I}from"./vendor.js";import{u as P}from"./certifications.js";import{_ as M,H as O}from"./app.js";import{_ as Q}from"./PageHeader.js";import{_ as G}from"./Breadcrumb.js";import{S as J}from"./StandardButton.js";const K={name:"CertificationsCatalog",components:{HeroIcon:O,PageHeader:Q,Breadcrumb:G,StandardButton:J},setup(){const w=R(),e=P(),S=_(()=>e.loading),a=_(()=>e.error),p=_(()=>e.standardsCatalog.catalog||{}),z=_(()=>e.standardsCatalog.categories||[]),b=D(null),l=D(!1),k=[{name:"Certificazioni",path:"/app/certifications/dashboard",icon:"shield-check"},{name:"Catalogo Standard",path:"/app/certifications/catalog"}],y=_(()=>{let o=[];return Object.entries(p.value).forEach(([c,B])=>{Array.isArray(B)&&o.push(...B.map(T=>({...T,category:c})))}),b.value&&(o=o.filter(c=>c.category===b.value)),o}),r=async()=>{try{await e.fetchStandardsCatalog()}catch(o){console.error("Errore nel caricamento catalogo:",o)}},x=async()=>{try{l.value=!0,await e.syncCatalog(),await r()}catch(o){console.error("Errore nella sincronizzazione:",o)}finally{l.value=!1}},q=o=>{w.push({name:"certifications-create",query:{standard_code:o.code}})},L=o=>o?new Intl.NumberFormat("it-IT",{minimumFractionDigits:0,maximumFractionDigits:0}).format(o):"0",N=o=>o?new Date(o).toLocaleDateString("it-IT"):"N/A",H=o=>({quality:"Qualità",security:"Sicurezza",environmental:"Ambientale",privacy:"Privacy",regulatory:"Normativo"})[o]||o,E=o=>({quality:"bg-primary-100 text-primary-800 dark:bg-primary-900/50 dark:text-primary-200",security:"bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200",environmental:"bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200",privacy:"bg-secondary-100 text-secondary-800 dark:bg-secondary-900/50 dark:text-secondary-200",regulatory:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200"})[o]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",j=o=>({active:"bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200",available:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",renewal_due:"bg-orange-100 text-orange-800 dark:bg-orange-900/50 dark:text-orange-200",renewal_in_progress:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200",expired:"bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200"})[o]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",A=o=>({active:"Attiva",available:"Disponibile",renewal_due:"Da Rinnovare",renewal_in_progress:"In Rinnovo",expired:"Scaduta"})[o]||o,F=o=>({manufacturing:"Manifatturiero",services:"Servizi",technology:"Tecnologia",finance:"Finanza",healthcare:"Sanità",consulting:"Consulenza",construction:"Costruzioni",energy:"Energia",chemical:"Chimico",all:"Tutti i settori"})[o]||o;return V(()=>{r()}),{loading:S,error:a,catalog:p,categories:z,selectedCategory:b,syncing:l,filteredStandards:y,breadcrumbs:k,loadCatalog:r,syncCatalog:x,startCertification:q,formatCurrency:L,formatDate:N,getCategoryLabel:H,getCategoryBadgeClass:E,getStatusBadgeClass:j,getStatusLabel:A,getSectorLabel:F}}},U={class:"certifications-catalog"},W={class:"mb-6"},X={key:0,class:"flex justify-center py-12"},Y={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center"},Z={class:"text-red-700 dark:text-red-300"},$={key:2},tt={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6"},et={class:"flex flex-wrap gap-2"},rt={class:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"},at={class:"p-6 border-b border-gray-100 dark:border-gray-700"},ot={class:"flex items-start justify-between"},it={class:"flex-1"},st={class:"text-lg font-semibold text-gray-900 dark:text-white mb-2"},nt={class:"flex items-center space-x-2 mb-3"},lt={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},dt={class:"text-sm text-gray-600 dark:text-gray-400 line-clamp-2"},ct={class:"ml-4"},gt={class:"p-6"},mt={class:"grid grid-cols-2 gap-4 mb-4"},yt={class:"text-sm text-gray-900 dark:text-white"},xt={class:"text-sm text-gray-900 dark:text-white"},bt={class:"text-sm text-gray-900 dark:text-white"},ut={class:"text-sm text-gray-900 dark:text-white"},_t={class:"mb-4"},ft={class:"flex flex-wrap gap-1"},pt={key:0,class:"mb-4 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-200 dark:border-primary-700"},kt={class:"flex items-center justify-between"},vt={class:"text-xs text-primary-700 dark:text-primary-300"},ht={class:"text-right"},Ct={class:"text-sm font-medium text-primary-900 dark:text-primary-100"},wt={class:"px-6 py-3 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-100 dark:border-gray-600 flex justify-between items-center"},St=["href"],zt={class:"flex space-x-2"},Bt={key:0,class:"text-center py-12"},Dt={class:"text-gray-600 dark:text-gray-400"};function It(w,e,S,a,p,z){const b=f("Breadcrumb"),l=f("StandardButton"),k=f("PageHeader"),y=f("HeroIcon");return s(),n("div",U,[d(b,{breadcrumbs:a.breadcrumbs},null,8,["breadcrumbs"]),t("div",W,[d(k,{title:"Catalogo Standard",subtitle:"Esplora gli standard di certificazione disponibili",icon:"book-open","icon-color":"text-purple-600"},{actions:g(()=>[d(l,{variant:"secondary",icon:"arrow-left",to:"/app/certifications/dashboard"},{default:g(()=>e[1]||(e[1]=[m(" Dashboard ")])),_:1,__:[1]}),d(l,{onClick:a.syncCatalog,variant:"primary",icon:"arrow-path",disabled:a.syncing,loading:a.syncing},{default:g(()=>[m(i(a.syncing?"Sincronizzazione...":"Sincronizza Catalogo"),1)]),_:1},8,["onClick","disabled","loading"])]),_:1})]),a.loading?(s(),n("div",X,[d(y,{name:"arrow-path",class:"h-8 w-8 text-primary-600 dark:text-primary-400 animate-spin"})])):a.error?(s(),n("div",Y,[d(y,{name:"exclamation-triangle",class:"h-8 w-8 mx-auto mb-2 text-red-500 dark:text-red-400"}),t("p",Z,i(a.error),1),d(l,{onClick:a.loadCatalog,variant:"primary",class:"mt-3"},{default:g(()=>e[2]||(e[2]=[m(" Riprova ")])),_:1,__:[2]},8,["onClick"])])):a.catalog?(s(),n("div",$,[t("div",tt,[e[4]||(e[4]=t("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},"Categorie",-1)),t("div",et,[(s(!0),n(v,null,h(a.categories,r=>(s(),C(l,{key:r,onClick:x=>a.selectedCategory=r,variant:a.selectedCategory===r?"primary":"ghost",size:"sm"},{default:g(()=>[m(i(a.getCategoryLabel(r)),1)]),_:2},1032,["onClick","variant"]))),128)),d(l,{onClick:e[0]||(e[0]=r=>a.selectedCategory=null),variant:a.selectedCategory===null?"primary":"ghost",size:"sm"},{default:g(()=>e[3]||(e[3]=[m(" Tutte ")])),_:1,__:[3]},8,["variant"])])]),t("div",rt,[(s(!0),n(v,null,h(a.filteredStandards,r=>(s(),n("div",{key:r.code,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"},[t("div",at,[t("div",ot,[t("div",it,[t("h3",st,i(r.name),1),t("div",nt,[t("span",{class:I([a.getCategoryBadgeClass(r.category),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},i(a.getCategoryLabel(r.category)),3),r.version?(s(),n("span",lt," v"+i(r.version),1)):u("",!0)]),t("p",dt,i(r.description),1)]),t("div",ct,[t("span",{class:I([a.getStatusBadgeClass(r.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},i(a.getStatusLabel(r.status)),3)])])]),t("div",gt,[t("div",mt,[t("div",null,[e[5]||(e[5]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Validità",-1)),t("dd",yt,i(r.typical_validity_years)+" anni",1)]),t("div",null,[e[6]||(e[6]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Costo Stimato",-1)),t("dd",xt," €"+i(a.formatCurrency(r.estimated_cost.min))+" - €"+i(a.formatCurrency(r.estimated_cost.max)),1)]),t("div",null,[e[7]||(e[7]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Ente",-1)),t("dd",bt,i(r.issuing_body),1)]),t("div",null,[e[8]||(e[8]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Requisiti",-1)),t("dd",ut,i(r.requirements_count)+" requisiti",1)])]),t("div",_t,[e[9]||(e[9]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"},"Settori Industriali",-1)),t("dd",ft,[(s(!0),n(v,null,h(r.industry_sectors,x=>(s(),n("span",{key:x,class:"inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"},i(a.getSectorLabel(x)),1))),128))])]),r.company_certification?(s(),n("div",pt,[t("div",kt,[t("div",null,[e[10]||(e[10]=t("p",{class:"text-sm font-medium text-primary-900 dark:text-primary-100"},"Certificazione Aziendale",-1)),t("p",vt," Scadenza: "+i(a.formatDate(r.company_certification.expiry_date)),1)]),t("div",ht,[t("div",Ct,i(r.company_certification.health_score)+"% ",1),e[11]||(e[11]=t("div",{class:"text-xs text-primary-700 dark:text-primary-300"},"Health Score",-1))])])])):u("",!0)]),t("div",wt,[t("div",null,[r.website_url?(s(),n("a",{key:0,href:r.website_url,target:"_blank",class:"text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 text-sm font-medium"},[e[12]||(e[12]=m(" Info Dettagliate ")),d(y,{name:"arrow-top-right-on-square",class:"h-4 w-4 inline ml-1"})],8,St)):u("",!0)]),t("div",zt,[r.company_certification?(s(),C(l,{key:0,variant:"primary",size:"sm",to:`/app/certifications/${r.company_certification.id}`},{default:g(()=>e[13]||(e[13]=[m(" Visualizza ")])),_:2,__:[13]},1032,["to"])):(s(),C(l,{key:1,onClick:x=>a.startCertification(r),variant:"primary",size:"sm",class:"bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600"},{default:g(()=>e[14]||(e[14]=[m(" Inizia Certificazione ")])),_:2,__:[14]},1032,["onClick"]))])])]))),128))]),a.filteredStandards.length===0?(s(),n("div",Bt,[d(y,{name:"academic-cap",class:"h-12 w-12 mx-auto mb-4 text-gray-300 dark:text-gray-600"}),e[15]||(e[15]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessuno standard trovato",-1)),t("p",Dt,i(a.selectedCategory?"Nessuno standard disponibile per questa categoria":"Il catalogo è vuoto"),1)])):u("",!0)])):u("",!0)])}const At=M(K,[["render",It]]);export{At as default};
