import{_ as l,H as m}from"./app.js";import{b as n,j as s,F as u,p as y,n as o,K as g,o as t,h as i,l as d,s as p,i as b,t as c}from"./vendor.js";const x={class:"w-full"},f={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},k={class:"border-b border-gray-200 dark:border-gray-700"},v={class:"-mb-px flex space-x-8 px-6","aria-label":"Tabs"},h=["onClick"],C={__name:"TabContainer",props:{tabs:{type:Array,required:!0,validator:r=>r.every(a=>a.id&&a.name&&(a.icon||a.iconName||!0))},activeTab:{type:String,required:!0},iconSize:{type:String,default:"md"},contentClass:{type:String,default:"p-6"}},emits:["tab-change"],setup(r){return(a,_)=>(t(),n("div",x,[s("div",f,[s("div",k,[s("nav",v,[(t(!0),n(u,null,y(r.tabs,e=>(t(),n("button",{key:e.id,onClick:z=>a.$emit("tab-change",e.id),class:o([r.activeTab===e.id?"border-brand-primary text-brand-primary":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300","whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200"])},[e.icon?(t(),i(b(e.icon),{key:0,size:r.iconSize,class:"mr-2"},null,8,["size"])):e.iconName?(t(),i(m,{key:1,name:e.iconName,size:r.iconSize,class:"mr-2"},null,8,["name","size"])):d("",!0),p(" "+c(e.name)+" ",1),e.count!==void 0?(t(),n("span",{key:2,class:o(["ml-2 py-0.5 px-2 rounded-full text-xs font-medium",r.activeTab===e.id?"bg-brand-primary-100 text-brand-primary-700 dark:bg-brand-primary-900 dark:text-brand-primary-200":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300"])},c(e.count),3)):d("",!0)],10,h))),128))])]),s("div",{class:o(["tab-content",r.contentClass])},[g(a.$slots,"default",{},void 0,!0)],2)])]))}},w=l(C,[["__scopeId","data-v-44a95f47"]]);export{w as T};
