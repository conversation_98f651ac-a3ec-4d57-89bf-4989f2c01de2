import{r,c as g,b as m,e as f,j as e,F as k,p as C,B as n,H as S,I as _,Q as b,t as v,o as d,h as z}from"./vendor.js";import{_ as P}from"./PageHeader.js";import{P as T}from"./ProposalCard.js";import"./app.js";const w={class:"p-6"},I={class:"space-y-8"},A={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},M={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Z={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},V={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},B={class:"space-y-4"},D={class:"flex items-center"},E={class:"flex items-center"},R={class:"space-y-3"},F={class:"grid grid-cols-2 gap-3"},j={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3"},L={class:"text-lg font-bold text-primary-600"},N={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3"},$={class:"text-lg font-bold text-green-600"},q={__name:"ProposalCardExample",setup(U){const s=r(""),p=r(!1),c=r(!0),i=r([{id:1,title:"Sviluppo App Mobile",client:{name:"TechCorp S.r.l.",logo:"/api/placeholder/40/40"},status:"sent",value:45e3,created_at:"2024-01-15T10:00:00Z",due_date:"2024-02-15T23:59:59Z",description:"Sviluppo completo di applicazione mobile per iOS e Android con backend API integrato.",tags:["Mobile","React Native","API"],probability:75},{id:2,title:"Portale E-commerce",client:{name:"Fashion Store",logo:"/api/placeholder/40/40"},status:"draft",value:28e3,created_at:"2024-01-20T14:30:00Z",due_date:"2024-03-01T23:59:59Z",description:"Creazione portale e-commerce con gestione prodotti, ordini e pagamenti online.",tags:["E-commerce","Vue.js","Payment"],probability:60},{id:3,title:"Sistema CRM",client:{name:"SalesForce Ltd",logo:"/api/placeholder/40/40"},status:"accepted",value:62e3,created_at:"2024-01-10T09:15:00Z",due_date:"2024-01-30T23:59:59Z",description:"Implementazione sistema CRM personalizzato con dashboard analytics e automazioni.",tags:["CRM","Analytics","Automation"],probability:100},{id:4,title:"Migrazione Cloud",client:{name:"Enterprise Corp",logo:"/api/placeholder/40/40"},status:"rejected",value:85e3,created_at:"2024-01-05T16:45:00Z",due_date:"2024-01-25T23:59:59Z",description:"Migrazione infrastruttura on-premise verso cloud AWS con ottimizzazione performance.",tags:["Cloud","AWS","Migration"],probability:0},{id:5,title:"Dashboard BI",client:{name:"DataInsight Inc",logo:"/api/placeholder/40/40"},status:"sent",value:35e3,created_at:"2024-01-25T11:20:00Z",due_date:"2024-02-20T23:59:59Z",description:"Sviluppo dashboard Business Intelligence con visualizzazioni interattive e report automatici.",tags:["BI","Dashboard","Analytics"],probability:80},{id:6,title:"Integrazione API",client:{name:"ConnectSys",logo:"/api/placeholder/40/40"},status:"draft",value:18e3,created_at:"2024-01-28T13:10:00Z",due_date:"2024-02-28T23:59:59Z",description:"Integrazione sistemi legacy tramite API REST con documentazione completa.",tags:["API","Integration","Legacy"],probability:50}]),u=g(()=>s.value?i.value.filter(a=>a.status===s.value):i.value),y=g(()=>u.value.reduce((a,t)=>a+t.value,0)),x=a=>{console.log("Proposta cliccata:",a.title),alert(`Dettagli Proposta:

Titolo: ${a.title}
Cliente: ${a.client.name}
Valore: €${a.value.toLocaleString()}
Stato: ${a.status}`)},h=(a,t)=>{console.log("Cambio stato proposta:",a.id,"da",a.status,"a",t);const o=i.value.findIndex(l=>l.id===a.id);if(o!==-1){i.value[o].status=t;const l={draft:50,sent:75,accepted:100,rejected:0};i.value[o].probability=l[t]||50}};return(a,t)=>(d(),m("div",w,[f(P,{title:"ProposalCard - Design System",subtitle:"Componente card per visualizzazione proposte commerciali",icon:"identification","icon-color":"text-purple-600"}),e("div",I,[e("div",A,[t[3]||(t[3]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-6"}," Esempi ProposalCard - Diversi Stati ",-1)),e("div",M,[(d(!0),m(k,null,C(i.value,o=>(d(),z(T,{key:o.id,proposal:o,onClick:x,onStatusChange:h},null,8,["proposal"]))),128))])]),e("div",Z,[t[11]||(t[11]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"}," Configurazione Demo ",-1)),e("div",V,[e("div",B,[e("div",null,[t[5]||(t[5]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Filtro per Stato ",-1)),n(e("select",{"onUpdate:modelValue":t[0]||(t[0]=o=>s.value=o),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},t[4]||(t[4]=[_('<option value="">Tutti gli stati</option><option value="draft">Bozza</option><option value="sent">Inviata</option><option value="accepted">Accettata</option><option value="rejected">Rifiutata</option>',5)]),512),[[S,s.value]])]),e("div",null,[e("label",D,[n(e("input",{"onUpdate:modelValue":t[1]||(t[1]=o=>p.value=o),type:"checkbox",class:"rounded border-gray-300 text-primary-600 focus:ring-primary-500"},null,512),[[b,p.value]]),t[6]||(t[6]=e("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"}," Vista compatta ",-1))])]),e("div",null,[e("label",E,[n(e("input",{"onUpdate:modelValue":t[2]||(t[2]=o=>c.value=o),type:"checkbox",class:"rounded border-gray-300 text-primary-600 focus:ring-primary-500"},null,512),[[b,c.value]]),t[7]||(t[7]=e("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"}," Mostra azioni ",-1))])])]),e("div",R,[t[10]||(t[10]=e("h4",{class:"font-medium text-gray-900 dark:text-white"},"Statistiche",-1)),e("div",F,[e("div",j,[t[8]||(t[8]=e("div",{class:"text-sm font-medium text-gray-900 dark:text-white"}," Totale Proposte ",-1)),e("div",L,v(u.value.length),1)]),e("div",N,[t[9]||(t[9]=e("div",{class:"text-sm font-medium text-gray-900 dark:text-white"}," Valore Totale ",-1)),e("div",$," €"+v(y.value.toLocaleString()),1)])])])])]),t[12]||(t[12]=e("div",{class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"}," Utilizzo del Componente "),e("pre",{class:"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 overflow-x-auto text-sm"},[e("code",null,`<template>
  <ProposalCard
    :proposal="proposal"
    @click="handleProposalClick"
    @status-change="handleStatusChange"
  />
</template>

<script setup>
import ProposalCard from '@/components/design-system/ProposalCard.vue'

const proposal = {
  id: 1,
  title: 'Sviluppo App Mobile',
  client: {
    name: 'TechCorp S.r.l.',
    logo: '/api/placeholder/40/40'
  },
  status: 'sent',
  value: 45000,
  created_at: '2024-01-15T10:00:00Z',
  due_date: '2024-02-15T23:59:59Z',
  description: 'Sviluppo completo applicazione mobile...',
  tags: ['Mobile', 'React Native', 'API']
}

const handleProposalClick = (proposal) => {
  console.log('Proposta cliccata:', proposal)
}

const handleStatusChange = (proposal, newStatus) => {
  console.log('Stato cambiato:', proposal.id, newStatus)
}
<\/script>`)])],-1))])]))}};export{q as default};
