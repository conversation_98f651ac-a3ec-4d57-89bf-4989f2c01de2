import{r as C,c as i,x as Z,y as ee,b as u,o as l,j as e,e as f,I as J,l as I,F,p as U,E as ue,t as d,n as N,s as $,h as k,k as K,A as Q}from"./vendor.js";import{_ as ge,H as x,l as me,d as xe}from"./app.js";import{S as X}from"./StandardButton.js";import{u as he}from"./useFormatters.js";import{P as ve,C as ye}from"./ClaudePromptModal.js";import"./formatters.js";import"./BaseModal.js";import"./MarkdownContent.js";const fe={class:"health-trend-chart"},pe={class:"chart-header mb-4"},be={class:"text-lg font-semibold text-gray-900 flex items-center space-x-2"},_e={class:"chart-container bg-white border border-gray-200 rounded-lg p-6"},ke={key:0,class:"empty-state"},we={key:1,class:"chart-content"},Se={class:"relative h-64 w-full"},$e=["points"],Ce=["points"],Ae=["points"],ze={class:"data-points"},Pe=["cx","cy","onMouseenter"],He=["cx","cy","onMouseenter"],Ne={class:"font-medium"},Te={class:"flex items-center space-x-2 mt-1"},Me={class:"flex items-center space-x-2"},Ie={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mt-6 pt-4 border-t border-gray-200"},Ee={class:"text-center"},je={class:"text-center"},De={class:"text-2xl font-bold text-gray-900"},Be={class:"text-center"},Ye={__name:"HealthTrendChart",props:{data:{type:Array,default:()=>[]}},setup(L){const n=L,h=C(null),v=C({show:!1,x:0,y:0,date:"",health:0,errors:0}),{formatDate:A}=he(),g=i(()=>n.data||[]),y=i(()=>{if(!g.value.length)return[];const s=800,r=200,c=50,p=s-c*2,b=r-40,_=Math.max(...g.value.map(m=>m.error_count||0),1);return g.value.map((m,H)=>{const R=c+H/(g.value.length-1)*p,Y=20+(100-m.health_score)/100*b,V=20+(_-(m.error_count||0))/_*b;return{x:R,healthY:Y,errorY:V,health:m.health_score,errors:m.error_count||0,date:A(m.timestamp)}})}),T=i(()=>y.value.map(s=>`${s.x},${s.healthY}`).join(" ")),q=i(()=>{if(!y.value.length)return"";const s=y.value.map(p=>`${p.x},${p.healthY}`).join(" "),r=y.value[0],c=y.value[y.value.length-1];return`${r.x},180 ${s} ${c.x},180`}),E=i(()=>y.value.map(s=>`${s.x},${s.errorY}`).join(" ")),M=i(()=>{if(!g.value.length)return 0;const s=g.value.reduce((r,c)=>r+c.health_score,0);return Math.round(s/g.value.length)}),z=i(()=>g.value.reduce((s,r)=>s+(r.error_count||0),0)),w=i(()=>{if(g.value.length<2)return"stable";const s=g.value.slice(-3),r=g.value.slice(-6,-3);if(s.length===0||r.length===0)return"stable";const c=s.reduce((_,m)=>_+m.health_score,0)/s.length,p=r.reduce((_,m)=>_+m.health_score,0)/r.length,b=c-p;return b>5?"improving":b<-5?"declining":"stable"});function j(s,r){const c=h.value.getBoundingClientRect();v.value={show:!0,x:s.x*(c.width/800)-60,y:s.healthY*(c.height/200)-70,date:s.date,health:s.health,errors:s.errors}}function S(){v.value.show=!1}function P(s){return s>=80?"text-green-600":s>=60?"text-yellow-600":"text-red-600"}function G(s){return s==="improving"?"text-green-600":s==="declining"?"text-red-600":"text-gray-600"}function D(s){return s==="improving"?"arrow-trending-up":s==="declining"?"arrow-trending-down":"minus"}function B(s){return s==="improving"?"Miglioramento":s==="declining"?"Peggioramento":"Stabile"}return Z(()=>{}),ee(()=>{}),(s,r)=>(l(),u("div",fe,[e("div",pe,[e("h3",be,[f(x,{name:"chart-bar",class:"text-blue-600"}),r[0]||(r[0]=e("span",null,"Trend Salute Sistema (7 giorni)",-1))]),r[1]||(r[1]=e("p",{class:"text-sm text-gray-600 mt-1"}," Andamento del punteggio di salute del sistema negli ultimi giorni ",-1))]),e("div",_e,[!g.value||g.value.length===0?(l(),u("div",ke,[f(x,{name:"chart-bar-square",size:"xl",class:"text-gray-400 mx-auto mb-4"}),r[2]||(r[2]=e("p",{class:"text-gray-500 text-center"}," Nessun dato disponibile per il trend di salute ",-1))])):(l(),u("div",we,[e("div",Se,[(l(),u("svg",{ref_key:"chartSvg",ref:h,viewBox:"0 0 800 200",class:"w-full h-full",xmlns:"http://www.w3.org/2000/svg"},[r[3]||(r[3]=J('<defs data-v-da1be677><pattern id="grid" width="80" height="40" patternUnits="userSpaceOnUse" data-v-da1be677><path d="M 80 0 L 0 0 0 40" fill="none" stroke="#f3f4f6" stroke-width="1" data-v-da1be677></path></pattern></defs><rect width="800" height="200" fill="url(#grid)" data-v-da1be677></rect><g class="y-axis" data-v-da1be677><text x="10" y="20" class="text-xs fill-gray-500" text-anchor="start" data-v-da1be677>100</text><text x="10" y="60" class="text-xs fill-gray-500" text-anchor="start" data-v-da1be677>75</text><text x="10" y="100" class="text-xs fill-gray-500" text-anchor="start" data-v-da1be677>50</text><text x="10" y="140" class="text-xs fill-gray-500" text-anchor="start" data-v-da1be677>25</text><text x="10" y="180" class="text-xs fill-gray-500" text-anchor="start" data-v-da1be677>0</text></g>',3)),e("polyline",{points:T.value,fill:"none",stroke:"#3b82f6","stroke-width":"3","stroke-linejoin":"round","stroke-linecap":"round"},null,8,$e),e("polygon",{points:q.value,fill:"url(#healthGradient)",opacity:"0.3"},null,8,Ce),e("polyline",{points:E.value,fill:"none",stroke:"#ef4444","stroke-width":"2","stroke-dasharray":"5,5"},null,8,Ae),e("g",ze,[(l(!0),u(F,null,U(y.value,(c,p)=>(l(),u("circle",{key:`health-${p}`,cx:c.x,cy:c.healthY,r:"4",fill:"#3b82f6",stroke:"white","stroke-width":"2",class:"cursor-pointer hover:r-6 transition-all",onMouseenter:b=>j(c),onMouseleave:S},null,40,Pe))),128)),(l(!0),u(F,null,U(y.value,(c,p)=>(l(),u("circle",{key:`error-${p}`,cx:c.x,cy:c.errorY,r:"3",fill:"#ef4444",stroke:"white","stroke-width":"1",class:"cursor-pointer hover:r-5 transition-all",onMouseenter:b=>j(c),onMouseleave:S},null,40,He))),128))]),r[4]||(r[4]=e("defs",null,[e("linearGradient",{id:"healthGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%"},[e("stop",{offset:"0%",style:{"stop-color":"#3b82f6","stop-opacity":"0.6"}}),e("stop",{offset:"100%",style:{"stop-color":"#3b82f6","stop-opacity":"0.1"}})])],-1))],512)),v.value.show?(l(),u("div",{key:0,class:"absolute pointer-events-none bg-gray-900 text-white text-xs rounded-lg px-3 py-2 shadow-lg z-10",style:ue({left:v.value.x+"px",top:v.value.y+"px"})},[e("div",Ne,d(v.value.date),1),e("div",Te,[r[5]||(r[5]=e("div",{class:"w-2 h-2 bg-blue-400 rounded-full"},null,-1)),e("span",null,"Salute: "+d(v.value.health)+"%",1)]),e("div",Me,[r[6]||(r[6]=e("div",{class:"w-2 h-2 bg-red-400 rounded-full"},null,-1)),e("span",null,"Errori: "+d(v.value.errors),1)])],4)):I("",!0)]),r[10]||(r[10]=J('<div class="chart-legend mt-4 flex items-center justify-center space-x-6" data-v-da1be677><div class="flex items-center space-x-2" data-v-da1be677><div class="w-3 h-3 bg-blue-500 rounded-full" data-v-da1be677></div><span class="text-sm text-gray-600" data-v-da1be677>Punteggio Salute</span></div><div class="flex items-center space-x-2" data-v-da1be677><div class="w-3 h-1 bg-red-500 rounded-full" data-v-da1be677></div><span class="text-sm text-gray-600" data-v-da1be677>Errori 24h</span></div></div>',1)),e("div",Ie,[e("div",Ee,[e("div",{class:N(["text-2xl font-bold",P(M.value)])},d(M.value)+"% ",3),r[7]||(r[7]=e("div",{class:"text-sm text-gray-600"},"Salute Media",-1))]),e("div",je,[e("div",De,d(z.value),1),r[8]||(r[8]=e("div",{class:"text-sm text-gray-600"},"Errori Totali",-1))]),e("div",Be,[e("div",{class:N(["text-2xl font-bold",G(w.value)])},[f(x,{name:D(w.value),class:"inline-block mr-1",size:"sm"},null,8,["name"]),$(" "+d(B(w.value)),1)],2),r[9]||(r[9]=e("div",{class:"text-sm text-gray-600"},"Tendenza",-1))])])]))])]))}},Ve=ge(Ye,[["__scopeId","data-v-da1be677"]]),Fe={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},Ue={class:"mt-4 md:mt-0 flex space-x-3"},qe=["disabled"],Ge=["disabled"],Re={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},Le={class:"p-5"},Oe={class:"flex items-center"},We={class:"flex-shrink-0"},Je={class:"ml-5 w-0 flex-1"},Ke={class:"text-lg font-medium text-gray-900 dark:text-white"},Qe={class:"text-xs text-gray-500 dark:text-gray-400"},Xe={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Ze={class:"p-5"},et={class:"flex items-center"},tt={class:"flex-shrink-0"},rt={class:"ml-5 w-0 flex-1"},st={class:"text-lg font-medium text-gray-900 dark:text-white"},at={class:"text-xs text-gray-500 dark:text-gray-400"},ot={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},nt={class:"p-5"},lt={class:"flex items-center"},it={class:"flex-shrink-0"},dt={class:"ml-5 w-0 flex-1"},ct={class:"text-lg font-medium text-gray-900 dark:text-white"},ut={class:"text-xs text-gray-500 dark:text-gray-400"},gt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},mt={class:"p-5"},xt={class:"flex items-center"},ht={class:"flex-shrink-0"},vt={class:"ml-5 w-0 flex-1"},yt={class:"text-lg font-medium text-gray-900 dark:text-white"},ft={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-8"},pt={class:"mb-8"},bt={class:"flex items-center justify-between mb-4"},_t={key:0,class:"text-center py-12"},kt={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},wt=["onClick"],St={class:"flex items-center justify-between mb-2"},$t={class:"text-sm font-medium text-gray-600 dark:text-gray-400"},Ct={class:"text-sm text-gray-900 dark:text-gray-100 mb-3 line-clamp-2"},At={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-3"},zt={class:"font-medium"},Pt={class:"flex justify-end"},Ht={class:"mb-8"},Nt={class:"flex items-center justify-between mb-4"},Tt={key:0,class:"text-center py-12"},Mt={key:1,class:"space-y-3"},It={class:"flex-shrink-0"},Et={class:"flex-1"},jt={class:"text-sm font-medium text-gray-900 dark:text-gray-100"},Dt={class:"text-xs text-gray-500 dark:text-gray-400 space-x-1"},Bt={key:0},Yt={key:1},Vt={class:"flex-shrink-0"},Jt={__name:"SelfHealingDashboard",setup(L){const n=me(),{showToast:h}=xe(),v=C(!1),A=C(null),g=C(!1),y=C(!1),T=C(null);i(()=>n.systemHealth);const q=i(()=>n.healthTrend),E=i(()=>n.criticalPatterns),M=i(()=>n.recentSessions),z=i(()=>n.quickStats),w=i(()=>n.loading),j=i(()=>n.healthScore),S=i(()=>n.isHealthy),P=i(()=>n.isUnhealthy),G=i(()=>n.criticalIssuesCount),D=i(()=>n.analyzingPattern),B=i(()=>n.generatingPrompt),s=i(()=>n.currentAnalysis),r=i(()=>n.currentPrompt),c=i(()=>P.value?"border-red-200 dark:border-red-700":S.value?"border-green-200 dark:border-green-700":"border-yellow-200 dark:border-yellow-700"),p=i(()=>P.value?"x-circle":S.value?"check-circle":"exclamation-triangle"),b=i(()=>P.value?"text-red-500":S.value?"text-green-500":"text-yellow-500"),_=i(()=>P.value?"Sistema in stato critico":S.value?"Sistema in buona salute":"Attenzione richiesta"),m=i(()=>({timeAgo:a=>{var t;try{return((t=n.formatters)==null?void 0:t.timeAgo(a))||"N/A"}catch{return"N/A"}},formatDate:a=>{var t;try{return((t=n.formatters)==null?void 0:t.formatDate(a))||"N/A"}catch{return"N/A"}},duration:a=>{var t;try{return((t=n.formatters)==null?void 0:t.duration(a))||"N/A"}catch{return"N/A"}},healthScore:a=>{var t;try{return((t=n.formatters)==null?void 0:t.healthScore(a))||"N/A"}catch{return"N/A"}}}));async function H(){try{await n.fetchDashboard(),h("Dashboard aggiornata","success")}catch(a){h(`Errore aggiornamento: ${a.message}`,"error")}}async function R(){try{v.value=!0,await n.updateSystemHealth(),h("System health ricalcolato","success")}catch(a){h(`Errore ricalcolo: ${a.message}`,"error")}finally{v.value=!1}}async function Y(a){try{await n.analyzePattern(a),h("Analisi AI completata","success")}catch(t){h(`Errore analisi: ${t.message}`,"error")}}async function V(a){try{await n.generateClaudePrompt(a),y.value=!0,h("Prompt Claude Code generato","success")}catch(t){h(`Errore generazione: ${t.message}`,"error")}}async function te(){try{await n.copyPromptToClipboard(r.value.prompt),h("Prompt copiato negli appunti","success")}catch(a){h(`Errore copia: ${a.message}`,"error")}}function re(a){A.value=a,g.value=!0}function se(){g.value=!1,A.value=null,n.resetCurrentState()}function O(){y.value=!1,n.resetCurrentState()}async function ae(a){try{await n.completeHealingSession(a,{success:!0,effectiveness_score:.9,admin_feedback:"Risolto tramite prompt Claude Code generato automaticamente"}),h("Sessione healing marcata come completata","success"),O(),await H()}catch(t){h(`Errore nel completamento sessione: ${t.message}`,"error")}}function oe(a){return{critical:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",high:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"}[a]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}function ne(a){return a.status==="in_progress"?"arrow-path":a.success?"check-circle":"x-circle"}function le(a){return a.status==="in_progress"?"text-blue-500 animate-spin":a.success?"text-green-500":"text-red-500"}function ie(a){return{manual:"Healing Manuale",automatic:"Auto-Healing",ai_assisted:"Healing Assistito AI"}[a.healing_type]||"Healing Session"}function de(a,t){return a==="in_progress"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":t?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"}function ce(a,t){return a==="in_progress"?"In corso":t?"Completato":"Fallito"}return Z(async()=>{await H(),T.value=setInterval(()=>{w.value||H()},3e4)}),ee(()=>{T.value&&clearInterval(T.value)}),(a,t)=>(l(),u("div",null,[e("div",Fe,[t[3]||(t[3]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"🔧 Self-Healing System"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Monitoraggio automatico errori e sistema di auto-diagnosi AI ")],-1)),e("div",Ue,[e("button",{onClick:H,disabled:w.value,class:"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[w.value?(l(),k(x,{key:0,name:"arrow-path",size:"sm",className:"animate-spin -ml-1 mr-2"})):(l(),k(x,{key:1,name:"arrow-path",size:"sm",className:"-ml-1 mr-2"})),t[2]||(t[2]=$(" Aggiorna "))],8,qe),e("button",{onClick:R,disabled:v.value,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[v.value?(l(),k(x,{key:0,name:"arrow-path",size:"sm",className:"animate-spin -ml-1 mr-2"})):(l(),k(x,{key:1,name:"cpu-chip",size:"sm",className:"-ml-1 mr-2"})),$(" "+d(v.value?"Ricalcolando...":"Ricalcola Health"),1)],8,Ge)])]),e("div",Re,[e("div",{class:N(["bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border",c.value])},[e("div",Le,[e("div",Oe,[e("div",We,[f(x,{name:p.value,size:"md",className:b.value},null,8,["name","className"])]),e("div",Je,[e("dl",null,[t[4]||(t[4]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," System Health Score ",-1)),e("dd",Ke,d(m.value.healthScore(j.value)),1),e("dd",Qe,d(_.value),1)])])])])],2),e("div",Xe,[e("div",Ze,[e("div",et,[e("div",tt,[f(x,{name:"exclamation-triangle",size:"md",className:"text-red-500"})]),e("div",rt,[e("dl",null,[t[5]||(t[5]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Errori Oggi ",-1)),e("dd",st,d(z.value.errors_today||0),1),e("dd",at,d(z.value.critical_pending||0)+" critici ",1)])])])])]),e("div",ot,[e("div",nt,[e("div",lt,[e("div",it,[f(x,{name:"wrench-screwdriver",size:"md",className:"text-blue-500"})]),e("div",dt,[e("dl",null,[t[6]||(t[6]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Sessioni Healing ",-1)),e("dd",ct,d(z.value.healing_sessions_today||0),1),e("dd",ut,d(z.value.auto_healed_today||0)+" automatiche ",1)])])])])]),e("div",gt,[e("div",mt,[e("div",xt,[e("div",ht,[f(x,{name:"clock",size:"md",className:"text-orange-500"})]),e("div",vt,[e("dl",null,[t[7]||(t[7]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Issues Critici ",-1)),e("dd",yt,d(G.value),1),t[8]||(t[8]=e("dd",{class:"text-xs text-gray-500 dark:text-gray-400"}," Da risolvere ",-1))])])])])])]),e("div",ft,[t[9]||(t[9]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"📈 Trend Health Score",-1)),f(Ve,{data:q.value},null,8,["data"])]),e("div",pt,[e("div",bt,[t[11]||(t[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"🚨 Pattern Errori Critici",-1)),e("button",{onClick:t[0]||(t[0]=o=>a.$router.push("/app/admin/self-healing/patterns")),class:"inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},[f(x,{name:"arrow-top-right-on-square",size:"sm",className:"-ml-1 mr-2"}),t[10]||(t[10]=$(" Vedi Tutti "))])]),E.value.length===0?(l(),u("div",_t,[f(x,{name:"check-circle",size:"xl",class:"text-green-500 mx-auto mb-4"}),t[12]||(t[12]=e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun pattern critico rilevato! 🎉",-1))])):(l(),u("div",kt,[(l(!0),u(F,null,U(E.value.slice(0,6),o=>(l(),u("div",{key:o.id,class:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 cursor-pointer hover:shadow-md transition-shadow",onClick:W=>re(o)},[e("div",St,[e("span",$t,d(o.error_type),1),e("span",{class:N(["px-2 py-1 text-xs font-medium rounded",oe(o.severity)])},d(o.severity.toUpperCase()),3)]),e("div",Ct,d(o.message_preview),1),e("div",At,[e("span",zt,d(o.occurrence_count)+" occorrenze ",1),e("span",null,d(m.value.timeAgo(o.last_seen)),1)]),e("div",Pt,[o.has_ai_analysis?(l(),k(X,{key:1,size:"xs",variant:"success",icon:"document-text",onClick:Q(W=>V(o.id),["stop"]),loading:B.value===o.id},{default:K(()=>t[14]||(t[14]=[$(" Genera Fix ")])),_:2,__:[14]},1032,["onClick","loading"])):(l(),k(X,{key:0,size:"xs",variant:"primary",icon:"cpu-chip",onClick:Q(W=>Y(o.id),["stop"]),loading:D.value===o.id},{default:K(()=>t[13]||(t[13]=[$(" Analizza AI ")])),_:2,__:[13]},1032,["onClick","loading"]))])],8,wt))),128))]))]),e("div",Ht,[e("div",Nt,[t[16]||(t[16]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"⚡ Sessioni Healing Recenti",-1)),e("button",{onClick:t[1]||(t[1]=o=>a.$router.push("/app/admin/self-healing/sessions")),class:"inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},[f(x,{name:"arrow-top-right-on-square",size:"sm",className:"-ml-1 mr-2"}),t[15]||(t[15]=$(" Vedi Tutte "))])]),M.value.length===0?(l(),u("div",Tt,[f(x,{name:"moon",size:"xl",class:"text-gray-400 mx-auto mb-4"}),t[17]||(t[17]=e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessuna attività di healing recente",-1))])):(l(),u("div",Mt,[(l(!0),u(F,null,U(M.value.slice(0,8),o=>(l(),u("div",{key:o.id,class:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 flex items-center space-x-4"},[e("div",It,[f(x,{name:ne(o),class:N(le(o)),size:"md"},null,8,["name","class"])]),e("div",Et,[e("div",jt,d(ie(o)),1),e("div",Dt,[e("span",null,d(o.healing_type),1),t[18]||(t[18]=e("span",null,"•",-1)),e("span",null,d(m.value.timeAgo(o.started_at)),1),o.duration_seconds?(l(),u("span",Bt,"•")):I("",!0),o.duration_seconds?(l(),u("span",Yt,d(m.value.duration(o.duration_seconds)),1)):I("",!0)])]),e("div",Vt,[e("span",{class:N(["px-2 py-1 text-xs font-medium rounded",de(o.status,o.success)])},d(ce(o.status,o.success)),3)])]))),128))]))]),A.value?(l(),k(ve,{key:0,pattern:A.value,analysis:s.value,"analyzing-pattern":D.value,"generating-prompt":B.value,show:g.value,onClose:se,onAnalyze:Y,onGeneratePrompt:V},null,8,["pattern","analysis","analyzing-pattern","generating-prompt","show"])):I("",!0),r.value?(l(),k(ye,{key:1,"prompt-data":r.value,show:y.value,onClose:O,onCopy:te,onMarkCompleted:ae},null,8,["prompt-data","show"])):I("",!0)]))}};export{Jt as default};
