import{b as n,j as t,l as p,e as d,k as R,f as T,t as i,n as b,F as w,p as k,r as _,c as L,x as N,u as z,o,s as j,E as F}from"./vendor.js";import{_ as H,H as M,a as O}from"./app.js";import{u as V}from"./engagement.js";const B={name:"EngagementCampaignDetail",components:{HeroIcon:M},setup(){const m=z(),a=O(),x=V(),e=_(null),P=_([]),D=_(0),C=_(null),c=_([]),h=_(0),u=_(!1);_("overview"),L(()=>{var l;return(l=a.user)==null?void 0:l.id});const s=async()=>{u.value=!0;try{const l=m.params.id,r=await x.getCampaign(l);r&&(e.value=r,await Promise.all([g(l),S(l),I(l)]))}catch(l){console.error("Error fetching campaign detail:",l)}finally{u.value=!1}},g=async l=>{try{const r=await x.fetchCampaignLeaderboard(l);r&&(P.value=r.leaderboard||[],h.value=r.total_participants||0)}catch(r){console.error("Error fetching campaign leaderboard:",r)}},S=async l=>{try{const r=await x.fetchCampaignRewards(l);r&&(c.value=r.rewards||[])}catch(r){console.error("Error fetching campaign rewards:",r)}},I=async l=>{try{const r=await x.fetchUserCampaignData(l);r&&(D.value=r.user_points||0,C.value=r.user_position||null)}catch(r){console.error("Error fetching user campaign data:",r)}},E=(l,r)=>{if(!l||!r)return"";const f=new Date(l),y=new Date(r),v={month:"short",day:"numeric"};return f.getFullYear()===y.getFullYear()?f.getMonth()===y.getMonth()?`${f.getDate()} - ${y.toLocaleDateString("it-IT",{...v,year:"numeric"})}`:`${f.toLocaleDateString("it-IT",v)} - ${y.toLocaleDateString("it-IT",{...v,year:"numeric"})}`:`${f.toLocaleDateString("it-IT",{...v,year:"numeric"})} - ${y.toLocaleDateString("it-IT",{...v,year:"numeric"})}`};return N(()=>{s()}),{campaign:e,leaderboard:P,userPoints:D,userPosition:C,campaignRewards:c,totalParticipants:h,loading:u,isActive,statusText,statusColor,progressPercentage,daysRemaining,fetchCampaignDetail:s,getPositionIcon,getPositionColor,formatDateRange:E}}},U={class:"campaign-detail"},q={class:"mb-6"},A={class:"flex items-center space-x-2 text-sm"},Y={class:"text-gray-900"},Q={key:0,class:"space-y-6"},G={class:"bg-white shadow rounded-lg p-6"},J={class:"flex items-start justify-between"},K={class:"flex-1"},W={class:"flex items-center space-x-3 mb-2"},X={class:"text-2xl font-bold text-gray-900"},Z={class:"text-gray-600 mb-4"},$={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},tt={class:"flex items-center text-sm text-gray-500"},et={key:0,class:"flex items-center text-sm text-amber-600"},st={key:1,class:"flex items-center text-sm font-medium text-green-600"},at={class:"bg-white shadow rounded-lg"},it={class:"border-b border-gray-200"},nt={class:"-mb-px flex space-x-8 px-6"},ot=["onClick"],rt={class:"p-6"},lt={key:0,class:"space-y-6"},ct={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},dt={class:"bg-blue-50 rounded-lg p-4"},mt={class:"flex items-center"},gt={class:"ml-3"},pt={class:"text-2xl font-bold text-blue-600"},_t={class:"bg-green-50 rounded-lg p-4"},ut={class:"flex items-center"},xt={class:"ml-3"},ht={class:"text-2xl font-bold text-green-600"},ft={class:"bg-purple-50 rounded-lg p-4"},yt={class:"flex items-center"},vt={class:"ml-3"},bt={class:"text-2xl font-bold text-purple-600"},wt={key:0},kt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ct={class:"flex items-start justify-between"},Pt={class:"flex-1"},Dt={class:"font-medium text-gray-900 mb-2"},Tt={class:"flex items-center text-sm text-green-600"},jt={key:1},St={class:"bg-gray-50 rounded-lg p-4"},It={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},Et={class:"text-2xl font-bold text-primary-600"},Rt={class:"text-sm text-gray-600 capitalize"},Lt={class:"text-xs text-gray-500 mt-2"},Nt={key:1},zt={class:"flex justify-between items-center mb-4"},Ft={class:"text-sm text-gray-500"},Ht={key:0,class:"space-y-2"},Mt={class:"flex items-center space-x-4"},Ot={class:"font-medium text-gray-900"},Vt={class:"text-sm text-gray-500"},Bt={class:"text-right"},Ut={class:"font-bold text-gray-900"},qt={key:1,class:"text-center py-8"},At={key:2},Yt={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},Qt={class:"flex items-start justify-between mb-3"},Gt={class:"font-medium text-gray-900"},Jt={class:"text-sm font-bold text-primary-600"},Kt={class:"text-sm text-gray-600 mb-3"},Wt={class:"flex items-center justify-between text-sm"},Xt={class:"text-gray-500"},Zt={key:0,class:"mt-2"},$t={class:"text-xs text-gray-500"},te={class:"w-full bg-gray-200 rounded-full h-1 mt-1"},ee={key:1,class:"text-center py-8"},se={key:1,class:"fixed inset-0 bg-gray-50 bg-opacity-75 flex items-center justify-center z-50"};function ae(m,a,x,e,P,D){var h,u;const C=T("router-link"),c=T("HeroIcon");return o(),n("div",U,[t("div",q,[t("nav",A,[d(C,{to:"/engagement/campaigns",class:"text-gray-500 hover:text-gray-700"},{default:R(()=>a[0]||(a[0]=[j(" Campagne ")])),_:1,__:[0]}),d(c,{name:"chevron-right",class:"w-4 h-4 text-gray-400"}),t("span",Y,i(((h=e.campaign)==null?void 0:h.name)||"Dettaglio Campagna"),1)])]),e.campaign?(o(),n("div",Q,[t("div",G,[t("div",J,[t("div",K,[t("div",W,[t("h1",X,i(e.campaign.name),1),t("span",{class:b(["inline-flex items-center px-3 py-1 rounded-full text-sm font-medium",m.getCampaignStatusClass(e.campaign.status,e.campaign.is_active)])},i(m.getCampaignStatusText(e.campaign.status,e.campaign.is_active)),3)]),t("p",Z,i(e.campaign.description),1),t("div",$,[t("div",tt,[d(c,{name:"calendar",class:"w-4 h-4 mr-2"}),t("span",null,i(e.formatDateRange(e.campaign.start_date,e.campaign.end_date)),1)]),e.campaign.is_active&&e.campaign.days_remaining>0?(o(),n("div",et,[d(c,{name:"clock",class:"w-4 h-4 mr-2"}),t("span",null,i(e.campaign.days_remaining)+" giorni rimanenti",1)])):p("",!0),e.campaign.points_multiplier>1?(o(),n("div",st,[d(c,{name:"star",class:"w-4 h-4 mr-2"}),t("span",null,i(e.campaign.points_multiplier)+"x Moltiplicatore Punti",1)])):p("",!0)])])])]),t("div",at,[t("div",it,[t("nav",nt,[(o(!0),n(w,null,k(m.tabs,s=>(o(),n("button",{key:s.id,onClick:g=>m.activeTab=s.id,class:b(["py-4 px-1 border-b-2 font-medium text-sm",m.activeTab===s.id?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])},[d(c,{name:s.icon,class:"w-4 h-4 inline mr-2"},null,8,["name"]),j(" "+i(s.label),1)],10,ot))),128))])]),t("div",rt,[m.activeTab==="overview"?(o(),n("div",lt,[t("div",ct,[t("div",dt,[t("div",mt,[d(c,{name:"star",class:"w-8 h-8 text-blue-600"}),t("div",gt,[a[1]||(a[1]=t("p",{class:"text-sm font-medium text-blue-900"},"I Tuoi Punti",-1)),t("p",pt,i(e.userPoints),1)])])]),t("div",_t,[t("div",ut,[d(c,{name:"trophy",class:"w-8 h-8 text-green-600"}),t("div",xt,[a[2]||(a[2]=t("p",{class:"text-sm font-medium text-green-900"},"Tua Posizione",-1)),t("p",ht,i(((u=e.userPosition)==null?void 0:u.ranking_position)||"Non classificato"),1)])])]),t("div",ft,[t("div",yt,[d(c,{name:"users",class:"w-8 h-8 text-purple-600"}),t("div",vt,[a[3]||(a[3]=t("p",{class:"text-sm font-medium text-purple-900"},"Partecipanti",-1)),t("p",bt,i(e.totalParticipants),1)])])])]),e.campaign.objectives_config&&Object.keys(e.campaign.objectives_config).length>0?(o(),n("div",wt,[a[4]||(a[4]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Obiettivi della Campagna",-1)),t("div",kt,[(o(!0),n(w,null,k(e.campaign.objectives_config,(s,g)=>(o(),n("div",{key:g,class:"border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"},[t("div",Ct,[t("div",Pt,[t("h4",Dt,i(s.description),1),t("div",Tt,[d(c,{name:"gift",class:"w-4 h-4 mr-2"}),t("span",null,"+"+i(s.points)+" punti",1)])]),d(c,{name:"check-circle",class:"w-6 h-6 text-green-500"})])]))),128))])])):p("",!0),e.campaign.points_rules&&Object.keys(e.campaign.points_rules).length>0?(o(),n("div",jt,[a[5]||(a[5]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Regole Punti Speciali",-1)),t("div",St,[t("div",It,[(o(!0),n(w,null,k(e.campaign.points_rules,(s,g)=>(o(),n("div",{key:g,class:"text-center"},[t("div",Et,i(s),1),t("div",Rt,i(g),1)]))),128))]),t("p",Lt," * Punti base con moltiplicatore "+i(e.campaign.points_multiplier)+"x applicato ",1)])])):p("",!0)])):p("",!0),m.activeTab==="leaderboard"?(o(),n("div",Nt,[t("div",zt,[a[6]||(a[6]=t("h3",{class:"text-lg font-semibold text-gray-900"},"Classifica Campagna",-1)),t("span",Ft,i(e.totalParticipants)+" partecipanti",1)]),e.leaderboard.length>0?(o(),n("div",Ht,[(o(!0),n(w,null,k(e.leaderboard,(s,g)=>(o(),n("div",{key:s.user_id,class:b(["flex items-center justify-between p-4 rounded-lg border",s.user_id===m.currentUserId?"bg-blue-50 border-blue-200":"bg-white border-gray-200"])},[t("div",Mt,[t("div",{class:b(["flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold",g<3?m.getPositionClass(g+1):"bg-gray-100 text-gray-600"])},i(s.ranking_position),3),t("div",null,[t("div",Ot,i(s.user_name),1),t("div",Vt,i(s.user_email),1)])]),t("div",Bt,[t("div",Ut,i(s.total_points),1),a[7]||(a[7]=t("div",{class:"text-sm text-gray-500"},"punti",-1))])],2))),128))])):(o(),n("div",qt,[d(c,{name:"trophy",class:"mx-auto h-12 w-12 text-gray-400"}),a[8]||(a[8]=t("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Nessun partecipante",-1)),a[9]||(a[9]=t("p",{class:"mt-1 text-sm text-gray-500"}," La classifica apparirà quando ci saranno dei partecipanti. ",-1))]))])):p("",!0),m.activeTab==="rewards"?(o(),n("div",At,[a[12]||(a[12]=t("div",{class:"flex justify-between items-center mb-4"},[t("h3",{class:"text-lg font-semibold text-gray-900"},"Premi Esclusivi")],-1)),e.campaignRewards.length>0?(o(),n("div",Yt,[(o(!0),n(w,null,k(e.campaignRewards,s=>(o(),n("div",{key:s.id,class:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"},[t("div",Qt,[t("h4",Gt,i(s.name),1),t("span",Jt,i(s.points_cost)+"pts",1)]),t("p",Kt,i(s.description),1),t("div",Wt,[t("span",{class:b(["px-2 py-1 rounded-full text-xs font-medium",s.is_available?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},i(s.is_available?"Disponibile":"Non disponibile"),3),t("div",Xt,i(s.reward_type),1)]),s.max_redemptions?(o(),n("div",Zt,[t("div",$t,i(s.current_redemptions)+"/"+i(s.max_redemptions)+" riscattati ",1),t("div",te,[t("div",{class:"bg-primary-600 h-1 rounded-full",style:F({width:`${s.current_redemptions/s.max_redemptions*100}%`})},null,4)])])):p("",!0)]))),128))])):(o(),n("div",ee,[d(c,{name:"gift",class:"mx-auto h-12 w-12 text-gray-400"}),a[10]||(a[10]=t("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Nessun premio esclusivo",-1)),a[11]||(a[11]=t("p",{class:"mt-1 text-sm text-gray-500"}," Questa campagna non ha premi esclusivi, ma puoi comunque guadagnare punti! ",-1))]))])):p("",!0)])])])):p("",!0),e.loading?(o(),n("div",se,a[13]||(a[13]=[t("div",{class:"bg-white p-6 rounded-lg shadow-lg flex items-center space-x-3"},[t("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"}),t("span",{class:"text-gray-900"},"Caricamento dettagli campagna...")],-1)]))):p("",!0)])}const re=H(B,[["render",ae]]);export{re as default};
