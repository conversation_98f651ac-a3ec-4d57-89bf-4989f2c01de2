import{r as v,c as x,u as T,x as E,b as n,j as t,e as o,l as y,t as r,k as h,I as F,n as k,s as c,B as A,C as M,q as j,o as l}from"./vendor.js";import{u as q}from"./help.js";import{_ as L,H as i,d as P}from"./app.js";import{M as R}from"./MarkdownContent.js";import{S as G}from"./StatusBadge.js";import{S as C}from"./StandardButton.js";import{_ as U}from"./Breadcrumb.js";import{H as W}from"./HelpChatWidget.js";import"./BaseModal.js";const J={class:"min-h-screen bg-gray-50"},K={class:"bg-white border-b border-gray-200"},O={class:"max-w-7xl mx-auto px-6 py-6"},Q={key:0,class:"mt-4"},X={class:"flex items-start justify-between"},Y={class:"flex-1"},Z={class:"text-3xl font-bold text-gray-900 mb-2"},ee={key:0,class:"text-lg text-gray-600 mb-4"},te={class:"flex items-center space-x-4 text-sm text-gray-500"},ae={class:"flex items-center space-x-1"},se={class:"flex items-center space-x-1"},oe={class:"flex items-center space-x-1"},re={class:"flex items-center space-x-3"},ne={class:"bg-white"},le={class:"max-w-7xl mx-auto px-6 pb-12"},ie={class:"max-w-4xl mx-auto"},de={key:0,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-8"},ue={key:1,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-8"},ce={class:"bg-gray-50 rounded-lg p-6 mt-12 pt-8 border-t border-gray-200"},me={class:"flex items-center space-x-4"},pe={key:0,class:"mt-6"},ve={class:"flex justify-end space-x-3 mt-3"},be={key:2,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center py-12"},ge={__name:"HelpContent",setup(fe){const _=T(),H=j(),m=q(),b=P(),g=v(""),p=v(!1),f=v(!1),d=v(null),S=x(()=>m.loading),a=x(()=>m.currentContent),I=x(()=>{var e;const s=[{label:"Help Center",to:"/app/help"}];return(e=a.value)!=null&&e.category&&s.push({label:a.value.category.name,to:`/app/help/content?category=${a.value.category.slug}`}),a.value&&s.push({label:a.value.title,to:_.path}),s}),z=async()=>{const s=_.params.id;if(!s){H.push("/app/help");return}try{await m.fetchContentItem(s),a.value&&(document.title=`${a.value.title} - Help Center - DatPortal`)}catch{b.showError("Errore nel caricamento del contenuto")}},w=async s=>{d.value===null&&(p.value=!0,d.value=s)},D=async()=>{f.value=!0;try{await m.voteContentHelpful(a.value.id,d.value,g.value),b.showSuccess("Grazie per il tuo feedback!"),p.value=!1}catch{b.showError("Errore nell'invio del feedback"),d.value=null}finally{f.value=!1}},V=()=>{window.print()},$=s=>new Date(s).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}),B=s=>({beginner:"Principiante",intermediate:"Intermedio",advanced:"Avanzato"})[s]||s,N=s=>({beginner:"success",intermediate:"warning",advanced:"error"})[s]||"info";return E(()=>{z()}),(s,e)=>(l(),n("div",J,[t("div",K,[t("div",O,[o(U,{items:I.value},null,8,["items"]),a.value?(l(),n("div",Q,[t("div",X,[t("div",Y,[t("h1",Z,r(a.value.title),1),a.value.excerpt?(l(),n("p",ee,r(a.value.excerpt),1)):y("",!0),t("div",te,[t("span",ae,[o(i,{name:"clock",class:"w-4 h-4"}),t("span",null,r(a.value.estimated_read_time)+"min di lettura",1)]),t("span",se,[o(i,{name:"eye",class:"w-4 h-4"}),t("span",null,r(a.value.view_count)+" visualizzazioni",1)]),t("span",oe,[o(i,{name:"calendar",class:"w-4 h-4"}),t("span",null,"Aggiornato "+r($(a.value.updated_at)),1)])])]),t("div",re,[o(G,{status:a.value.difficulty_level,variant:N(a.value.difficulty_level)},{default:h(()=>[c(r(B(a.value.difficulty_level)),1)]),_:1},8,["status","variant"]),t("button",{onClick:V,class:"p-2 text-gray-500 hover:text-gray-700"},[o(i,{name:"printer",class:"w-5 h-5"})])])])])):y("",!0)])]),t("div",ne,[t("div",le,[t("div",ie,[S.value?(l(),n("div",de,e[5]||(e[5]=[F('<div class="animate-pulse space-y-4" data-v-743292af><div class="h-8 bg-gray-200 rounded w-3/4" data-v-743292af></div><div class="h-4 bg-gray-200 rounded" data-v-743292af></div><div class="h-4 bg-gray-200 rounded w-5/6" data-v-743292af></div><div class="h-4 bg-gray-200 rounded w-4/6" data-v-743292af></div></div>',1)]))):a.value?(l(),n("div",ue,[o(R,{content:a.value.content,class:"prose max-w-none"},null,8,["content"]),t("div",ce,[e[7]||(e[7]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"È stato utile questo contenuto?",-1)),t("div",me,[t("button",{onClick:e[0]||(e[0]=u=>w(!0)),class:k(["flex items-center space-x-2 px-4 py-2 text-sm bg-white hover:bg-gray-50 border border-gray-300 rounded-lg transition-colors duration-150",{"bg-brand-primary-50 border-brand-primary-300 text-brand-primary-700":d.value===!0}])},[o(i,{name:"hand-thumb-up",class:"w-5 h-5"}),c(" Sì ("+r(a.value.helpful_votes||0)+") ",1)],2),t("button",{onClick:e[1]||(e[1]=u=>w(!1)),class:k(["flex items-center space-x-2 px-4 py-2 text-sm bg-white hover:bg-gray-50 border border-gray-300 rounded-lg transition-colors duration-150",{"bg-brand-primary-50 border-brand-primary-300 text-brand-primary-700":d.value===!1}])},[o(i,{name:"hand-thumb-down",class:"w-5 h-5"}),c(" No ("+r(a.value.not_helpful_votes||0)+") ",1)],2)]),p.value?(l(),n("div",pe,[A(t("textarea",{"onUpdate:modelValue":e[2]||(e[2]=u=>g.value=u),rows:"3",class:"w-full px-4 py-3 border border-gray-300 rounded-lg resize-none",placeholder:"Aiutaci a migliorare questo contenuto..."},null,512),[[M,g.value]]),t("div",ve,[t("button",{onClick:e[3]||(e[3]=u=>p.value=!1),class:"px-4 py-2 text-gray-600 hover:text-gray-800"}," Annulla "),o(C,{onClick:D,loading:f.value,size:"sm"},{default:h(()=>e[6]||(e[6]=[c(" Invia Feedback ")])),_:1,__:[6]},8,["loading"])])])):y("",!0)])])):(l(),n("div",be,[o(i,{name:"document-magnifying-glass",class:"w-16 h-16 text-gray-300 mx-auto mb-4"}),e[9]||(e[9]=t("h2",{class:"text-xl font-semibold text-gray-900 mb-2"},"Contenuto non trovato",-1)),e[10]||(e[10]=t("p",{class:"text-gray-600 mb-6"},"Il contenuto richiesto non esiste o è stato rimosso.",-1)),o(C,{onClick:e[4]||(e[4]=u=>s.$router.push("/app/help")),variant:"primary"},{default:h(()=>e[8]||(e[8]=[c(" Torna al Help Center ")])),_:1,__:[8]})]))])])]),o(W)]))}},Ie=L(ge,[["__scopeId","data-v-743292af"]]);export{Ie as default};
