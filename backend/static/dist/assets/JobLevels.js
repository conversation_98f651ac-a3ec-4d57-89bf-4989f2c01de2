import{r as w,c as M,w as R,b as m,o as u,j as e,t as g,e as v,A as B,B as k,C as $,H as A,I as F,s as V,l as c,n as J,F as U,p as O,x as H,h as N,k as q}from"./vendor.js";import{_ as P}from"./ListPageTemplate.js";import{H as h,_ as Q}from"./app.js";import{A as G}from"./ActionButtonGroup.js";import"./Pagination.js";import"./StandardButton.js";const K={class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},W={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},X={class:"mt-3"},Y={class:"flex items-center justify-between pb-4"},Z={class:"text-lg font-medium text-gray-900 dark:text-white"},ee={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},te={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},ae=["disabled"],re={key:0,class:"flex items-center"},oe={key:1},se={__name:"JobLevelModal",props:{jobLevel:{type:Object,default:null}},emits:["close","saved"],setup(l,{emit:_}){const y=l,p=_,b=w(!1),a=w({name:"",description:"",category:"",level:"",min_salary:null,max_salary:null,benefits:"",requirements:""}),r=M(()=>{var n;return!!((n=y.jobLevel)!=null&&n.id)}),x=()=>{a.value={name:"",description:"",category:"",level:"",min_salary:null,max_salary:null,benefits:"",requirements:""}},L=n=>{n&&(a.value={name:n.name||"",description:n.description||"",category:n.category||"",level:n.level||"",min_salary:n.min_salary||null,max_salary:n.max_salary||null,benefits:n.benefits||"",requirements:n.requirements||""})},j=async()=>{b.value=!0;try{const n=r.value?`/api/personnel/job-levels/${y.jobLevel.id}`:"/api/personnel/job-levels",t=r.value?"PUT":"POST",i=await fetch(n,{method:t,headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(a.value)});if(!i.ok)throw new Error(`HTTP ${i.status}: ${i.statusText}`);const f=await i.json();if(f.success)p("saved",f.data.job_level);else throw new Error(f.message||"Errore nel salvataggio")}catch(n){console.error("Error saving job level:",n),alert("Errore nel salvataggio: "+n.message)}finally{b.value=!1}};return R(()=>y.jobLevel,n=>{n?L(n):x()},{immediate:!0}),(n,t)=>(u(),m("div",K,[e("div",W,[e("div",X,[e("div",Y,[e("h3",Z,g(r.value?"Modifica Inquadramento":"Nuovo Inquadramento"),1),e("button",{onClick:t[0]||(t[0]=i=>n.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[v(h,{name:"x-mark",size:"md"})])]),e("form",{onSubmit:B(j,["prevent"]),class:"space-y-6"},[e("div",ee,[e("div",null,[t[10]||(t[10]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Nome Inquadramento * ",-1)),k(e("input",{"onUpdate:modelValue":t[1]||(t[1]=i=>a.value.name=i),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"es. Senior Developer"},null,512),[[$,a.value.name]])]),e("div",null,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Categoria * ",-1)),k(e("select",{"onUpdate:modelValue":t[2]||(t[2]=i=>a.value.category=i),required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},t[11]||(t[11]=[F('<option value="">Seleziona categoria</option><option value="dirigente">Dirigente</option><option value="quadro">Quadro</option><option value="impiegato">Impiegato</option><option value="operaio">Operaio</option>',5)]),512),[[A,a.value.category]])]),e("div",null,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Livello ",-1)),k(e("input",{"onUpdate:modelValue":t[3]||(t[3]=i=>a.value.level=i),type:"text",class:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"es. Junior, Senior, Lead"},null,512),[[$,a.value.level]])]),e("div",null,[t[14]||(t[14]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," RAL Minima (€) ",-1)),k(e("input",{"onUpdate:modelValue":t[4]||(t[4]=i=>a.value.min_salary=i),type:"number",min:"0",step:"1000",class:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"25000"},null,512),[[$,a.value.min_salary,void 0,{number:!0}]])]),e("div",null,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," RAL Massima (€) ",-1)),k(e("input",{"onUpdate:modelValue":t[5]||(t[5]=i=>a.value.max_salary=i),type:"number",min:"0",step:"1000",class:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"45000"},null,512),[[$,a.value.max_salary,void 0,{number:!0}]])])]),e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Descrizione ",-1)),k(e("textarea",{"onUpdate:modelValue":t[6]||(t[6]=i=>a.value.description=i),rows:"3",class:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Descrizione del ruolo e responsabilità"},null,512),[[$,a.value.description]])]),e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Benefits ",-1)),k(e("textarea",{"onUpdate:modelValue":t[7]||(t[7]=i=>a.value.benefits=i),rows:"3",class:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"es. Buoni pasto, auto aziendale, assicurazione sanitaria"},null,512),[[$,a.value.benefits]])]),e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Requisiti ",-1)),k(e("textarea",{"onUpdate:modelValue":t[8]||(t[8]=i=>a.value.requirements=i),rows:"3",class:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"es. Laurea in informatica, 3+ anni di esperienza"},null,512),[[$,a.value.requirements]])]),e("div",te,[e("button",{type:"button",onClick:t[9]||(t[9]=i=>n.$emit("close")),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"}," Annulla "),e("button",{type:"submit",disabled:b.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[b.value?(u(),m("span",re,[v(h,{name:"spinner",size:"sm",class:"animate-spin -ml-1 mr-2"}),t[19]||(t[19]=V(" Salvando... "))])):(u(),m("span",oe,g(r.value?"Aggiorna":"Crea"),1))],8,ae)])],32)])])]))}},le={class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},ne={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white dark:bg-gray-800"},ie={class:"mt-3"},de={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},ue={class:"flex items-center"},me={class:"h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-4"},ge={class:"text-lg font-medium text-gray-900 dark:text-white"},ce={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},ye={class:"py-6 space-y-6"},be={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},xe={class:"flex items-center"},ve={class:"text-sm text-gray-900 dark:text-white"},pe={class:"md:col-span-2"},fe={key:0,class:"space-y-1"},ke={key:0,class:"flex items-center"},we={class:"text-sm font-medium text-green-600"},he={key:1,class:"flex items-center"},_e={class:"text-sm font-medium text-blue-600"},je={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},$e={key:0},Le={class:"text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded-md"},qe={key:1},Ce={class:"text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded-md"},Ee={key:2},ze={class:"text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded-md"},Te={key:3},De={class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"},Ie={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3"},Me={class:"flex items-center"},Ve={class:"h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3"},Se={class:"flex-1 min-w-0"},Ne={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},Ae={key:0,class:"text-xs text-gray-500 dark:text-gray-400 truncate"},Je={class:"pt-4 border-t border-gray-200 dark:border-gray-700"},Ue={class:"grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-500 dark:text-gray-400"},Re={key:0},Be={key:1},Fe={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},Oe={__name:"JobLevelViewModal",props:{jobLevel:{type:Object,required:!0}},emits:["close"],setup(l){const _=a=>({dirigente:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",quadro:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",impiegato:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",operaio:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"})[a]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",y=a=>({dirigente:"Dirigente",quadro:"Quadro",impiegato:"Impiegato",operaio:"Operaio"})[a]||a,p=a=>a?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(a):"",b=a=>a?new Date(a).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"";return(a,r)=>{var x,L,j,n,t,i,f,E,z,T,D,I,C,s,d;return u(),m("div",le,[e("div",ne,[e("div",ie,[e("div",de,[e("div",ue,[e("div",me,[v(h,{name:"academic-cap",size:"md",class:"text-blue-600 dark:text-blue-400"})]),e("div",null,[e("h3",ge,g((x=l.jobLevel)==null?void 0:x.name),1),(L=l.jobLevel)!=null&&L.level?(u(),m("p",ce,g(l.jobLevel.level),1)):c("",!0)])]),e("button",{onClick:r[0]||(r[0]=o=>a.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[v(h,{name:"x-mark",size:"md"})])]),e("div",ye,[e("div",be,[e("div",null,[r[2]||(r[2]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Categoria ",-1)),e("span",{class:J(["inline-flex items-center px-3 py-1 rounded-full text-sm font-medium",_((j=l.jobLevel)==null?void 0:j.category)])},g(y((n=l.jobLevel)==null?void 0:n.category)),3)]),e("div",null,[r[3]||(r[3]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Dipendenti Assegnati ",-1)),e("div",xe,[v(h,{name:"users",size:"sm",class:"text-gray-400 mr-2"}),e("span",ve,g(((t=l.jobLevel)==null?void 0:t.employees_count)||0)+" dipendenti ",1)])]),e("div",pe,[r[6]||(r[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Range Retributivo ",-1)),(i=l.jobLevel)!=null&&i.min_salary||(f=l.jobLevel)!=null&&f.max_salary?(u(),m("div",fe,[(E=l.jobLevel)!=null&&E.min_salary?(u(),m("div",ke,[r[4]||(r[4]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400 w-16"},"Min:",-1)),e("span",we,g(p(l.jobLevel.min_salary)),1)])):c("",!0),(z=l.jobLevel)!=null&&z.max_salary?(u(),m("div",he,[r[5]||(r[5]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400 w-16"},"Max:",-1)),e("span",_e,g(p(l.jobLevel.max_salary)),1)])):c("",!0)])):(u(),m("span",je," Non specificato "))])]),(T=l.jobLevel)!=null&&T.description?(u(),m("div",$e,[r[7]||(r[7]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Descrizione ",-1)),e("p",Le,g(l.jobLevel.description),1)])):c("",!0),(D=l.jobLevel)!=null&&D.requirements?(u(),m("div",qe,[r[8]||(r[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Requisiti ",-1)),e("p",Ce,g(l.jobLevel.requirements),1)])):c("",!0),(I=l.jobLevel)!=null&&I.benefits?(u(),m("div",Ee,[r[9]||(r[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Benefits ",-1)),e("p",ze,g(l.jobLevel.benefits),1)])):c("",!0),(C=l.jobLevel)!=null&&C.employees&&l.jobLevel.employees.length>0?(u(),m("div",Te,[e("label",De," Dipendenti Attuali ("+g(l.jobLevel.employees.length)+") ",1),e("div",Ie,[(u(!0),m(U,null,O(l.jobLevel.employees,o=>(u(),m("div",{key:o.id,class:"bg-gray-50 dark:bg-gray-700 p-3 rounded-md"},[e("div",Me,[e("div",Ve,[v(h,{name:"user",size:"sm",class:"text-blue-600 dark:text-blue-400"})]),e("div",Se,[e("p",Ne,g(o.full_name),1),o.position?(u(),m("p",Ae,g(o.position),1)):c("",!0)])])]))),128))])])):c("",!0),e("div",Je,[e("div",Ue,[(s=l.jobLevel)!=null&&s.created_at?(u(),m("div",Re,[r[10]||(r[10]=e("span",{class:"font-medium"},"Creato:",-1)),V(" "+g(b(l.jobLevel.created_at)),1)])):c("",!0),(d=l.jobLevel)!=null&&d.updated_at?(u(),m("div",Be,[r[11]||(r[11]=e("span",{class:"font-medium"},"Aggiornato:",-1)),V(" "+g(b(l.jobLevel.updated_at)),1)])):c("",!0)])])]),e("div",Fe,[e("button",{onClick:r[1]||(r[1]=o=>a.$emit("close")),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"}," Chiudi ")])])])])}}},He={class:"flex items-center"},Pe={class:"h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-900/20 flex items-center justify-center mr-3"},Qe={class:"font-medium"},Ge={key:0,class:"text-sm text-gray-500"},Ke={key:0,class:"text-sm"},We={key:0,class:"text-green-600"},Xe={key:1,class:"text-primary-600 dark:text-primary-400"},Ye={key:1,class:"text-gray-400"},Ze={class:"flex items-center"},et={__name:"JobLevels",setup(l){const _=w(!1),y=w([]),p=w(""),b=w(!1),a=w(!1),r=w(!1),x=w(null),L=[{key:"name",label:"Nome"},{key:"category",label:"Categoria"},{key:"salary_range",label:"Range Retributivo"},{key:"employees_count",label:"Dipendenti"},{key:"actions",label:"Azioni"}],j=M(()=>p.value?y.value.filter(s=>s.category===p.value):y.value),n=M(()=>[{label:"Totale Inquadramenti",value:y.value.length,icon:"academic-cap",iconClass:"text-primary-500"},{label:"Inquadramenti Mostrati",value:j.value.length,icon:"eye",iconClass:"text-purple-500"},{label:"Dipendenti Totali",value:y.value.reduce((s,d)=>s+(d.employees_count||0),0),icon:"users",iconClass:"text-green-500"},{label:"Categorie Attive",value:new Set(y.value.map(s=>s.category)).size,icon:"building-office",iconClass:"text-orange-500"}]),t=async()=>{_.value=!0;try{const s=await fetch("/api/personnel/job-levels",{credentials:"include"});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const d=await s.json();if(d.success)y.value=d.data.job_levels;else throw new Error(d.message||"Errore nel caricamento")}catch(s){console.error("Error loading job levels:",s),alert("Errore nel caricamento degli inquadramenti: "+s.message)}finally{_.value=!1}},i=async s=>{if(s.employees_count>0){alert("Impossibile eliminare un inquadramento associato a dipendenti");return}if(confirm(`Sei sicuro di voler eliminare l'inquadramento "${s.name}"?`))try{const d=await fetch(`/api/personnel/job-levels/${s.id}`,{method:"DELETE",credentials:"include"});if(!d.ok)throw new Error(`HTTP ${d.status}: ${d.statusText}`);const o=await d.json();if(o.success)await t();else throw new Error(o.message||"Errore nell'eliminazione")}catch(d){console.error("Error deleting job level:",d),alert("Errore nell'eliminazione: "+d.message)}},f=()=>{b.value=!1,a.value=!1,x.value=null},E=async()=>{f(),await t()},z=s=>{x.value=s,r.value=!0},T=s=>{x.value=s,a.value=!0},D=s=>({dirigente:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",quadro:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",impiegato:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",operaio:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"})[s]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",I=s=>({dirigente:"Dirigente",quadro:"Quadro",impiegato:"Impiegato",operaio:"Operaio"})[s]||s,C=s=>s?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(s):"";return H(()=>{t()}),(s,d)=>(u(),m(U,null,[v(P,{title:"Gestione Inquadramenti",subtitle:"Gestione dei livelli di inquadramento aziendale",data:j.value,columns:L,stats:n.value,loading:_.value,"can-create":!0,"create-label":"Nuovo Inquadramento","search-placeholder":"Cerca inquadramenti...","results-label":"inquadramenti",onCreate:d[1]||(d[1]=o=>b.value=!0)},{filters:q(()=>[k(e("select",{"onUpdate:modelValue":d[0]||(d[0]=o=>p.value=o),class:"ml-3 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},d[3]||(d[3]=[e("option",{value:""},"Tutte le categorie",-1),e("option",{value:"dirigente"},"Dirigente",-1),e("option",{value:"quadro"},"Quadro",-1),e("option",{value:"impiegato"},"Impiegato",-1),e("option",{value:"operaio"},"Operaio",-1)]),512),[[A,p.value]])]),"column-name":q(({item:o})=>[e("div",He,[e("div",Pe,[v(h,{name:"academic-cap",size:"sm",class:"text-primary-600 dark:text-primary-400"})]),e("div",null,[e("div",Qe,g(o.name),1),o.level?(u(),m("div",Ge,g(o.level),1)):c("",!0)])])]),"column-category":q(({item:o})=>[e("span",{class:J(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",D(o.category)])},g(I(o.category)),3)]),"column-salary_range":q(({item:o})=>[o.min_salary||o.max_salary?(u(),m("div",Ke,[o.min_salary?(u(),m("div",We," Min: "+g(C(o.min_salary)),1)):c("",!0),o.max_salary?(u(),m("div",Xe," Max: "+g(C(o.max_salary)),1)):c("",!0)])):(u(),m("span",Ye,"Non specificato"))]),"column-employees_count":q(({item:o})=>[e("div",Ze,[v(h,{name:"users",size:"sm",class:"text-gray-400 mr-1"}),e("span",null,g(o.employees_count||0),1)])]),"column-actions":q(({item:o})=>[v(G,{onView:S=>z(o),onEdit:S=>T(o),onDelete:S=>i(o),"delete-message":`Sei sicuro di voler eliminare l'inquadramento '${o.name}'?`},null,8,["onView","onEdit","onDelete","delete-message"])]),_:1},8,["data","stats","loading"]),b.value||a.value?(u(),N(se,{key:0,"job-level":x.value,onClose:f,onSaved:E},null,8,["job-level"])):c("",!0),r.value?(u(),N(Oe,{key:1,"job-level":x.value,onClose:d[2]||(d[2]=o=>r.value=!1)},null,8,["job-level"])):c("",!0)],64))}},nt=Q(et,[["__scopeId","data-v-3d30dc8e"]]);export{nt as default};
