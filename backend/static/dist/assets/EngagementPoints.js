import{b as l,j as t,l as C,t as i,B as D,H as I,I as F,F as h,p as w,e as x,s as m,f as z,E as H,r as g,x as R,o as d,n as M}from"./vendor.js";import{u as G}from"./engagement.js";import{_ as O,H as q,c as T}from"./app.js";const J={name:"EngagementPoints",components:{HeroIcon:q},setup(){G();const P=g([]),e=g({}),k=g([]),s=g([]),S=g(null),_=g(!1),n=g(null),p=g({period:"",source_type:"",campaign_id:""}),c=async(a=1)=>{_.value=!0;try{const r=new URLSearchParams({page:a.toString(),per_page:"25"});Object.entries(p.value).forEach(([u,E])=>{E&&r.append(u,E)});const f=await T.get(`/api/engagement/points?${r}`);f.data.success&&(P.value=f.data.data.points,n.value=f.data.data.pagination,e.value=f.data.data.statistics)}catch(r){console.error("Error fetching points:",r)}finally{_.value=!1}},y=async()=>{var a;try{const r=await T.get("/api/engagement/dashboard");r.data.success&&(S.value=r.data.data.user_profile,k.value=((a=r.data.data.recent_points)==null?void 0:a.slice(0,5))||[])}catch(r){console.error("Error fetching user profile:",r)}},v=async()=>{try{const a=await T.get("/api/engagement/campaigns");a.data.success&&(s.value=a.data.data.campaigns)}catch(a){console.error("Error fetching campaigns:",a)}},b=()=>{p.value={period:"",source_type:"",campaign_id:""},c()},o=()=>{var a;(a=n.value)!=null&&a.has_prev&&c(n.value.page-1)},j=()=>{var a;(a=n.value)!=null&&a.has_next&&c(n.value.page+1)},A=()=>{if(!n.value)return"0-0";const a=(n.value.page-1)*n.value.per_page+1,r=Math.min(n.value.page*n.value.per_page,n.value.total);return`${a}-${r}`},B=a=>({audit_log:"Sistema",manual:"Manuale",campaign:"Campagna",bonus:"Bonus",achievement:"Achievement"})[a]||a,L=a=>({audit_log:"bg-blue-400",manual:"bg-green-400",campaign:"bg-purple-400",bonus:"bg-yellow-400",achievement:"bg-red-400"})[a]||"bg-gray-400",N=a=>({audit_log:"bg-blue-100 text-blue-800",manual:"bg-green-100 text-green-800",campaign:"bg-purple-100 text-purple-800",bonus:"bg-yellow-100 text-yellow-800",achievement:"bg-red-100 text-red-800"})[a]||"bg-gray-100 text-gray-800",U=a=>new Date(a).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),V=a=>{const r=new Date(a),u=Math.floor((new Date-r)/(1e3*60*60));return u<1?"Ora":u<24?`${u}h fa`:u<48?"Ieri":r.toLocaleDateString("it-IT",{month:"short",day:"numeric"})};return R(async()=>{await Promise.all([y(),v(),c()])}),{points:P,pointsStats:e,recentActivity:k,campaigns:s,userProfile:S,loading:_,pagination:n,filters:p,fetchPoints:c,fetchCampaigns:v,fetchUserProfile:y,resetFilters:b,nextPage:j,previousPage:o,getItemRange:A,getSourceLabel:B,getSourceColor:L,getSourceBadgeClass:N,formatDate:U,formatDateTime:V}}},K={class:"engagement-points"},Q={class:"bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg shadow-lg p-6 text-white mb-6"},W={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},X={class:"text-center"},Y={class:"text-3xl font-bold"},Z={class:"text-center"},$={class:"text-3xl font-bold"},tt={class:"text-center"},et={class:"text-3xl font-bold"},st={class:"bg-white shadow rounded-lg mb-6 p-6"},ot={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},at=["value"],it={class:"flex items-end"},nt={class:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6"},rt={class:"bg-white shadow rounded-lg p-6"},lt={class:"text-lg font-medium text-gray-900 mb-4"},dt={key:0,class:"space-y-3"},ct={class:"flex items-center"},gt={class:"text-sm text-gray-600 capitalize"},mt={class:"text-sm font-medium text-gray-900"},pt={key:1,class:"text-sm text-gray-500 text-center py-4"},ut={class:"bg-white shadow rounded-lg p-6"},xt={class:"text-lg font-medium text-gray-900 mb-4"},yt={key:0,class:"space-y-3"},vt={class:"font-medium text-gray-900"},ft={class:"text-gray-500"},_t={class:"font-bold text-green-600"},bt={key:1,class:"text-sm text-gray-500 text-center py-4"},ht={class:"bg-white shadow rounded-lg p-6"},wt={class:"text-lg font-medium text-gray-900 mb-4"},Pt={class:"space-y-4"},kt={class:"text-center"},St={class:"text-2xl font-bold text-orange-600"},Ct={class:"text-center"},Dt={class:"text-lg font-medium text-gray-900"},It={key:0,class:"text-center"},Tt={class:"text-sm text-gray-600 mb-1"},Et={class:"w-full bg-gray-200 rounded-full h-2"},Mt={class:"text-xs text-gray-500 mt-1"},jt={class:"bg-white shadow rounded-lg"},At={class:"overflow-x-auto"},Bt={class:"min-w-full divide-y divide-gray-200"},Lt={class:"bg-white divide-y divide-gray-200"},Nt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Ut={class:"px-6 py-4 whitespace-nowrap"},Vt={class:"text-sm text-gray-900"},Ft={class:"text-sm text-gray-500"},zt={class:"px-6 py-4 whitespace-nowrap"},Ht={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Rt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Gt={class:"px-6 py-4 whitespace-nowrap"},Ot={class:"text-sm font-bold text-green-600"},qt={key:0,class:"bg-gray-50 px-6 py-3 flex items-center justify-between border-t border-gray-200"},Jt={class:"flex-1 flex justify-between sm:hidden"},Kt=["disabled"],Qt=["disabled"],Wt={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Xt={class:"text-sm text-gray-700"},Yt={class:"font-medium"},Zt={class:"font-medium"},$t={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},te=["disabled"],ee={class:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"},se=["disabled"],oe={key:0,class:"fixed inset-0 bg-gray-50 bg-opacity-75 flex items-center justify-center z-50"};function ae(P,e,k,s,S,_){var p,c,y,v,b;const n=z("HeroIcon");return d(),l("div",K,[e[31]||(e[31]=t("div",{class:"mb-6"},[t("h1",{class:"text-2xl font-bold text-gray-900"},"I Miei Punti"),t("p",{class:"mt-1 text-sm text-gray-600"}," Storico completo dei punti guadagnati e attività engagement ")],-1)),t("div",Q,[t("div",W,[t("div",X,[t("div",Y,i(s.pointsStats.total_points||0),1),e[9]||(e[9]=t("div",{class:"text-primary-100"},"Punti Totali Guadagnati",-1))]),t("div",Z,[t("div",$,i(s.pointsStats.available_points||0),1),e[10]||(e[10]=t("div",{class:"text-primary-100"},"Punti Disponibili",-1))]),t("div",tt,[t("div",et,i(s.pointsStats.points_spent||0),1),e[11]||(e[11]=t("div",{class:"text-primary-100"},"Punti Spesi",-1))])])]),t("div",st,[e[19]||(e[19]=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Filtri",-1)),t("div",ot,[t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-gray-700"},"Periodo",-1)),D(t("select",{"onUpdate:modelValue":e[0]||(e[0]=o=>s.filters.period=o),class:"mt-1 block w-full rounded-md border-gray-300"},e[12]||(e[12]=[t("option",{value:""},"Tutto",-1),t("option",{value:"7"},"Ultimi 7 giorni",-1),t("option",{value:"30"},"Ultimi 30 giorni",-1),t("option",{value:"90"},"Ultimi 3 mesi",-1)]),512),[[I,s.filters.period]])]),t("div",null,[e[15]||(e[15]=t("label",{class:"block text-sm font-medium text-gray-700"},"Tipo Sorgente",-1)),D(t("select",{"onUpdate:modelValue":e[1]||(e[1]=o=>s.filters.source_type=o),class:"mt-1 block w-full rounded-md border-gray-300"},e[14]||(e[14]=[F('<option value="">Tutte</option><option value="audit_log">Attività Sistema</option><option value="manual">Manuale</option><option value="campaign">Campagna</option><option value="bonus">Bonus</option>',5)]),512),[[I,s.filters.source_type]])]),t("div",null,[e[17]||(e[17]=t("label",{class:"block text-sm font-medium text-gray-700"},"Campagna",-1)),D(t("select",{"onUpdate:modelValue":e[2]||(e[2]=o=>s.filters.campaign_id=o),class:"mt-1 block w-full rounded-md border-gray-300"},[e[16]||(e[16]=t("option",{value:""},"Tutte",-1)),(d(!0),l(h,null,w(s.campaigns,o=>(d(),l("option",{key:o.id,value:o.id},i(o.name),9,at))),128))],512),[[I,s.filters.campaign_id]])]),t("div",it,[t("button",{onClick:e[3]||(e[3]=(...o)=>s.fetchPoints&&s.fetchPoints(...o)),class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium mr-2"},[x(n,{name:"search",class:"w-4 h-4 inline mr-2"}),e[18]||(e[18]=m(" Cerca "))]),t("button",{onClick:e[4]||(e[4]=(...o)=>s.resetFilters&&s.resetFilters(...o)),class:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"}," Reset ")])])]),t("div",nt,[t("div",rt,[t("h3",lt,[x(n,{name:"chart-pie",class:"h-5 w-5 inline mr-2"}),e[20]||(e[20]=m(" Punti per Fonte "))]),(p=s.pointsStats.by_source)!=null&&p.length?(d(),l("div",dt,[(d(!0),l(h,null,w(s.pointsStats.by_source,o=>(d(),l("div",{key:o.source_type,class:"flex items-center justify-between"},[t("div",ct,[t("div",{class:M([s.getSourceColor(o.source_type),"w-3 h-3 rounded-full mr-2"])},null,2),t("span",gt,i(s.getSourceLabel(o.source_type)),1)]),t("span",mt,i(o.total_points),1)]))),128))])):(d(),l("div",pt,"Nessun dato disponibile"))]),t("div",ut,[t("h3",xt,[x(n,{name:"clock",class:"h-5 w-5 inline mr-2"}),e[21]||(e[21]=m(" Attività Recente "))]),s.recentActivity.length?(d(),l("div",yt,[(d(!0),l(h,null,w(s.recentActivity,o=>(d(),l("div",{key:o.id,class:"flex items-center justify-between text-sm"},[t("div",null,[t("div",vt,i(o.description),1),t("div",ft,i(s.formatDateTime(o.earned_at)),1)]),t("span",_t,"+"+i(o.points_earned),1)]))),128))])):(d(),l("div",bt,"Nessuna attività recente"))]),t("div",ht,[t("h3",wt,[x(n,{name:"fire",class:"h-5 w-5 inline mr-2"}),e[22]||(e[22]=m(" Streak & Progressi "))]),t("div",Pt,[t("div",kt,[t("div",St,i(((c=s.userProfile)==null?void 0:c.streak_days)||0),1),e[23]||(e[23]=t("div",{class:"text-sm text-gray-600"},"Giorni consecutivi",-1))]),t("div",Ct,[t("div",Dt,i(((v=(y=s.userProfile)==null?void 0:y.current_level)==null?void 0:v.name)||"Novizio"),1),e[24]||(e[24]=t("div",{class:"text-sm text-gray-600"},"Livello Attuale",-1))]),(b=s.userProfile)!=null&&b.next_level?(d(),l("div",It,[t("div",Tt,"Prossimo livello: "+i(s.userProfile.next_level.name),1),t("div",Et,[t("div",{class:"bg-primary-600 h-2 rounded-full transition-all duration-300",style:H({width:`${s.userProfile.level_progress_percentage||0}%`})},null,4)]),t("div",Mt,i(s.userProfile.points_to_next_level)+" punti mancanti",1)])):C("",!0)])])]),t("div",jt,[e[29]||(e[29]=t("div",{class:"px-6 py-4 border-b border-gray-200"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Storico Punti")],-1)),t("div",At,[t("table",Bt,[e[25]||(e[25]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Data "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Attività "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Tipo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Campagna "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Moltiplicatore "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Punti ")])],-1)),t("tbody",Lt,[(d(!0),l(h,null,w(s.points,o=>(d(),l("tr",{key:o.id,class:"hover:bg-gray-50"},[t("td",Nt,i(s.formatDate(o.earned_at)),1),t("td",Ut,[t("div",Vt,i(o.description),1),t("div",Ft,i(o.action_type)+" - "+i(o.resource_type),1)]),t("td",zt,[t("span",{class:M(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s.getSourceBadgeClass(o.source_type)])},i(s.getSourceLabel(o.source_type)),3)]),t("td",Ht,i(o.campaign_name||"-"),1),t("td",Rt,i(o.multiplier_applied||1)+"x ",1),t("td",Gt,[t("span",Ot,"+"+i(o.points_earned),1)])]))),128))])])]),s.pagination?(d(),l("div",qt,[t("div",Jt,[t("button",{onClick:e[5]||(e[5]=(...o)=>s.previousPage&&s.previousPage(...o)),disabled:!s.pagination.has_prev,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Precedente ",8,Kt),t("button",{onClick:e[6]||(e[6]=(...o)=>s.nextPage&&s.nextPage(...o)),disabled:!s.pagination.has_next,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Successivo ",8,Qt)]),t("div",Wt,[t("div",null,[t("p",Xt,[e[26]||(e[26]=m(" Mostrando ")),t("span",Yt,i(s.getItemRange()),1),e[27]||(e[27]=m(" di ")),t("span",Zt,i(s.pagination.total),1),e[28]||(e[28]=m(" risultati "))])]),t("div",null,[t("nav",$t,[t("button",{onClick:e[7]||(e[7]=(...o)=>s.previousPage&&s.previousPage(...o)),disabled:!s.pagination.has_prev,class:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"},[x(n,{name:"chevron-left",class:"h-5 w-5"})],8,te),t("span",ee,i(s.pagination.page)+" / "+i(s.pagination.pages),1),t("button",{onClick:e[8]||(e[8]=(...o)=>s.nextPage&&s.nextPage(...o)),disabled:!s.pagination.has_next,class:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"},[x(n,{name:"chevron-right",class:"h-5 w-5"})],8,se)])])])])):C("",!0)]),s.loading?(d(),l("div",oe,e[30]||(e[30]=[t("div",{class:"bg-white p-6 rounded-lg shadow-lg flex items-center space-x-3"},[t("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"}),t("span",{class:"text-gray-900"},"Caricamento storico punti...")],-1)]))):C("",!0)])}const le=O(J,[["render",ae]]);export{le as default};
