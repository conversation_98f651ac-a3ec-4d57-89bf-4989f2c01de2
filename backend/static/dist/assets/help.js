import{d as U,r as i,c as h}from"./vendor.js";import{c as u}from"./app.js";const Z=U("help",()=>{const y=i([]),g=i([]),v=i(null),m=i([]),E=i(null),w=i([]),c=i(!1),n=i(null),p=i(!1),f=i({search:"",category_id:null,content_type:"",difficulty:"",featured:null,module:"",sort_by:"featured",sort_order:"desc"}),d=i({page:1,per_page:20,total:0,pages:0}),o=i({isOpen:!1,isMinimized:!1,sessionId:null,messages:[],contextModule:null}),b=h(()=>{const a={root:[],children:{}};return y.value.forEach(t=>{t.parent_id?(a.children[t.parent_id]||(a.children[t.parent_id]=[]),a.children[t.parent_id].push(t)):a.root.push(t)}),a}),S=h(()=>g.value.filter(a=>a.featured===!0)),x=h(()=>{const a={};return g.value.forEach(t=>{const r=t.category_id||"uncategorized";a[r]||(a[r]=[]),a[r].push(t)}),a}),I=h(()=>{const a={};return g.value.forEach(t=>{const r=t.content_type||"other";a[r]||(a[r]=[]),a[r].push(t)}),a}),F=h(()=>m.value.filter(a=>a.status==="active"||a.status==="resolved").sort((a,t)=>new Date(t.last_activity)-new Date(a.last_activity)).slice(0,5)),O=h(()=>m.value.some(a=>a.unread_count>0)),C=async(a={})=>{var t,r;c.value=!0,n.value=null;try{const e=await u.get("/api/help/categories",{params:a});if(!((t=e.data)!=null&&t.success))throw new Error(((r=e.data)==null?void 0:r.message)||"Errore nel caricamento categorie");return y.value=e.data.data.categories||[],e.data.data.categories}catch(e){throw n.value=e.message,console.error("Error fetching help categories:",e),e}finally{c.value=!1}},H=async a=>{var t,r;c.value=!0,n.value=null;try{const e=await u.get(`/api/help/categories/${a}`);if(!((t=e.data)!=null&&t.success))throw new Error(((r=e.data)==null?void 0:r.message)||"Errore nel caricamento categoria");return e.data.data}catch(e){throw n.value=e.message,console.error("Error fetching help category:",e),e}finally{c.value=!1}},_=async(a={})=>{var t,r;c.value=!0,n.value=null;try{const e={page:d.value.page,per_page:d.value.per_page,...f.value,...a},s=await u.get("/api/help/content",{params:e});if(!((t=s.data)!=null&&t.success))throw new Error(((r=s.data)==null?void 0:r.message)||"Errore nel caricamento contenuti");const l=s.data;return l.success&&(g.value=l.data.content||[],l.data.pagination&&(d.value={...d.value,...l.data.pagination})),l.data.content}catch(e){throw n.value=e.message,console.error("Error fetching help content:",e),e}finally{c.value=!1}},R=async a=>{var t,r;c.value=!0,n.value=null;try{const e=await u.get(`/api/help/content/${a}`);if(!((t=e.data)!=null&&t.success))throw new Error(((r=e.data)==null?void 0:r.message)||"Errore nel caricamento contenuto");return v.value=e.data.data,e.data.data}catch(e){throw n.value=e.message,console.error("Error fetching help content item:",e),e}finally{c.value=!1}},$=async(a,t,r=null)=>{var e,s;try{const l=await u.post(`/api/help/content/${a}/vote`,{helpful:t,feedback:r,current_page:window.location.pathname});if(!((e=l.data)!=null&&e.success))throw new Error(((s=l.data)==null?void 0:s.message)||"Errore nel registrare il voto");return v.value&&v.value.id===a&&(v.value.helpful_votes=l.data.data.helpful_votes,v.value.not_helpful_votes=l.data.data.not_helpful_votes),l.data.data}catch(l){throw n.value=l.message,console.error("Error voting on content:",l),l}},k=async a=>{var t,r;if(!a||a.trim().length<2)return w.value=[],[];c.value=!0,n.value=null;try{const e=await u.get("/api/help/search",{params:{q:a.trim()}});if(!((t=e.data)!=null&&t.success))throw new Error(((r=e.data)==null?void 0:r.message)||"Errore nella ricerca");return w.value=e.data.data||{},e.data.data}catch(e){throw n.value=e.message,console.error("Error searching help:",e),e}finally{c.value=!1}},z=async(a=null,t=null)=>{var r,e;p.value=!0,n.value=null;try{const s=await u.post("/api/help/chat/start",{current_module:a,title:t||"Nuova conversazione help"});if(!((r=s.data)!=null&&r.success))throw new Error(((e=s.data)==null?void 0:e.message)||"Errore nell'avvio chat");const l=s.data.data;return E.value=l,o.value.sessionId=l.session_id,o.value.messages=l.messages||[],o.value.contextModule=a,o.value.isOpen=!0,l}catch(s){throw n.value=s.message,console.error("Error starting chat session:",s),s}finally{p.value=!1}},D=async a=>{var t,r;if(!o.value.sessionId)throw new Error("Nessuna sessione chat attiva");p.value=!0,n.value=null;try{const e=await u.post(`/api/help/chat/${o.value.sessionId}/message`,{message:a,current_module:o.value.contextModule});if(!((t=e.data)!=null&&t.success))throw new Error(((r=e.data)==null?void 0:r.message)||"Errore nell'invio messaggio");const s=e.data.data.message;return o.value.messages.push({id:Date.now().toString(),type:"user",content:a,timestamp:new Date().toISOString()}),o.value.messages.push(s),s}catch(e){throw n.value=e.message,console.error("Error sending chat message:",e),e}finally{p.value=!1}},B=async a=>{var t,r;c.value=!0,n.value=null;try{const e=await u.get(`/api/help/chat/${a}`);if(!((t=e.data)!=null&&t.success))throw new Error(((r=e.data)==null?void 0:r.message)||"Errore nel caricamento conversazione");const s=e.data.data;return E.value=s,o.value.sessionId===a&&(o.value.messages=s.messages||[]),s}catch(e){throw n.value=e.message,console.error("Error fetching conversation:",e),e}finally{c.value=!1}},P=async(a={})=>{var t,r;c.value=!0,n.value=null;try{const e=await u.get("/api/help/chat/conversations",{params:a});if(!((t=e.data)!=null&&t.success))throw new Error(((r=e.data)==null?void 0:r.message)||"Errore nel caricamento conversazioni");const s=e.data.data;return m.value=s.conversations||[],s.pagination&&(d.value={...d.value,...s.pagination}),s.conversations}catch(e){throw n.value=e.message,console.error("Error fetching user conversations:",e),e}finally{c.value=!1}},N=async a=>{var t,r;try{const e=await u.post("/api/help/feedback",{...a,current_page:window.location.pathname});if(!((t=e.data)!=null&&t.success))throw new Error(((r=e.data)==null?void 0:r.message)||"Errore nell'invio feedback");return e.data.data}catch(e){throw n.value=e.message,console.error("Error submitting feedback:",e),e}},M=(a=null)=>{o.value.isOpen=!0,o.value.isMinimized=!1,o.value.contextModule=a,o.value.sessionId||z(a)},W=()=>{o.value.isOpen=!1,o.value.isMinimized=!1};return{categories:y,content:g,currentContent:v,conversations:m,currentConversation:E,searchResults:w,loading:c,error:n,chatLoading:p,contentFilters:f,pagination:d,chatWidget:o,categoriesByParent:b,featuredContent:S,contentByCategory:x,contentByType:I,recentConversations:F,hasUnreadMessages:O,fetchCategories:C,fetchCategory:H,fetchContent:_,fetchContentItem:R,voteContentHelpful:$,searchHelp:k,startChatSession:z,sendChatMessage:D,fetchConversation:B,fetchUserConversations:P,submitFeedback:N,openChatWidget:M,closeChatWidget:W,toggleChatWidget:()=>{o.value.isOpen?W():M()},minimizeChatWidget:()=>{o.value.isMinimized=!0},maximizeChatWidget:()=>{o.value.isMinimized=!1},updateContentFilters:a=>{f.value={...f.value,...a},d.value.page=1},clearContentFilters:()=>{f.value={search:"",category_id:null,content_type:"",difficulty:"",featured:null,module:"",sort_by:"featured",sort_order:"desc"},d.value.page=1},clearError:()=>{n.value=null},clearCurrentContent:()=>{v.value=null},clearSearchResults:()=>{w.value=[]},loadHelpCenter:async()=>{c.value=!0,n.value=null;try{const[a,t]=await Promise.all([C(),_({featured:!0,per_page:6})]);return{categories:a,featuredContent:t}}catch(a){throw n.value=a.message,console.error("Error loading help center:",a),a}finally{c.value=!1}}}});export{Z as u};
