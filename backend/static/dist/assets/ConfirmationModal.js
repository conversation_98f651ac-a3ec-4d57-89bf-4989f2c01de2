import{_ as m,H as c}from"./app.js";import{b as d,o as g,j as e,n as o,e as u,f,t as n}from"./vendor.js";const b={name:"ConfirmationModal",components:{HeroIcon:c},props:{title:{type:String,required:!0},message:{type:String,required:!0},confirmText:{type:String,default:"Conferma"},cancelText:{type:String,default:"Annulla"},confirmVariant:{type:String,default:"primary",validator:r=>["primary","danger","warning","success"].includes(r)}},emits:["confirm","cancel"],computed:{iconName(){switch(this.confirmVariant){case"danger":return"exclamation-triangle";case"warning":return"exclamation-triangle";case"success":return"check-circle";default:return"question-mark-circle"}},iconClasses(){switch(this.confirmVariant){case"danger":return"bg-red-100 dark:bg-red-900";case"warning":return"bg-yellow-100 dark:bg-yellow-900";case"success":return"bg-green-100 dark:bg-green-900";default:return"bg-primary-100 dark:bg-primary-900"}},confirmButtonClasses(){switch(this.confirmVariant){case"danger":return"bg-red-600 hover:bg-red-700 focus:ring-red-500";case"warning":return"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500";case"success":return"bg-green-600 hover:bg-green-700 focus:ring-green-500";default:return"bg-primary-600 hover:bg-primary-700 focus:ring-primary-500"}}}},y={class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},x={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},p={class:"relative inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},h={class:"sm:flex sm:items-start"},w={class:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left"},v={class:"text-lg leading-6 font-medium text-gray-900 dark:text-white",id:"modal-title"},k={class:"mt-2"},_={class:"text-sm text-gray-500 dark:text-gray-400"},C={class:"mt-5 sm:mt-4 sm:flex sm:flex-row-reverse"};function S(r,t,s,V,j,a){const l=f("HeroIcon");return g(),d("div",y,[e("div",x,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:t[0]||(t[0]=i=>r.$emit("cancel"))}),t[3]||(t[3]=e("span",{class:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true"},"​",-1)),e("div",p,[e("div",h,[e("div",{class:o(["mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10",a.iconClasses])},[u(l,{name:a.iconName,size:"md",class:"text-white"},null,8,["name"])],2),e("div",w,[e("h3",v,n(s.title),1),e("div",k,[e("p",_,n(s.message),1)])])]),e("div",C,[e("button",{type:"button",class:o(["w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm",a.confirmButtonClasses]),onClick:t[1]||(t[1]=i=>r.$emit("confirm"))},n(s.confirmText),3),e("button",{type:"button",class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm",onClick:t[2]||(t[2]=i=>r.$emit("cancel"))},n(s.cancelText),1)])])])])}const N=m(b,[["render",S]]);export{N as C};
