import{r as m,c as $,x as ne,b as o,j as r,l as F,e as u,t as i,B as W,H as E,F as y,p as b,v as j,A as N,q as le,o as n,n as T,E as ie}from"./vendor.js";import{u as de}from"./recruiting.js";import{S as g}from"./StandardButton.js";import{_ as ue,H as ce}from"./app.js";const ge={class:"interviews-calendar"},ve={class:"mb-6"},me={class:"flex items-center justify-between"},ye={class:"flex items-center space-x-3"},be={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-6"},pe={class:"flex flex-wrap items-center justify-between gap-4"},xe={class:"flex items-center space-x-4"},he={class:"flex items-center space-x-2"},ke={class:"text-lg font-medium text-gray-900 dark:text-white"},fe={class:"flex items-center space-x-3"},we={class:"flex rounded-md shadow-sm"},_e=["value"],De={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},Ce={class:"flex items-center"},Se={class:"flex-shrink-0"},Te={class:"ml-3"},Ie={class:"text-sm font-medium text-gray-500 dark:text-gray-400"},Me={class:"text-lg font-semibold text-gray-900 dark:text-white"},Oe={key:0,class:"flex justify-center py-12"},$e={key:1,class:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden"},ze={key:0,class:"week-view"},Fe={class:"grid grid-cols-8 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600"},Ne={class:"text-sm font-medium text-gray-900 dark:text-white"},Pe={class:"grid grid-cols-8"},Le={class:"border-r border-gray-200 dark:border-gray-600"},Ve=["onClick"],qe=["onClick"],Ae={class:"text-xs font-semibold text-blue-800 dark:text-blue-200 truncate"},We={class:"text-xs text-blue-600 dark:text-blue-300 truncate"},Ee={key:1,class:"month-view"},je={class:"grid grid-cols-7 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600"},Be={class:"grid grid-cols-7"},He=["onClick"],Re={class:"flex justify-between items-start mb-1"},Ye={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Ue={class:"space-y-1"},Ge=["onClick"],Qe={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Je={class:"mt-3"},Ke={class:"text-sm text-gray-600 dark:text-gray-400 mb-4"},Xe={class:"space-y-3"},Ze={class:"flex justify-end mt-4"},et={__name:"InterviewsCalendar",setup(tt){const P=le(),I=de(),M=m(!1),c=m("week"),l=m(new Date),S=m([]),z=m([]),k=m(""),f=m(""),w=m(!1),p=m(null),_=m(null),L=Array.from({length:10},(a,t)=>t+9),B=["Dom","Lun","Mar","Mer","Gio","Ven","Sab"],H=$(()=>{if(c.value==="week"){const a=D(l.value),t=new Date(a);return t.setDate(t.getDate()+6),`${A(a)} - ${A(t)}`}else return l.value.toLocaleDateString("it-IT",{year:"numeric",month:"long"})}),V=$(()=>{const a=D(l.value),t=[];for(let e=0;e<7;e++){const s=new Date(a);s.setDate(s.getDate()+e),t.push({date:s.toISOString().split("T")[0],dayName:s.toLocaleDateString("it-IT",{weekday:"short"}),dayNumber:s.getDate()})}return t}),R=$(()=>{const a=l.value.getFullYear(),t=l.value.getMonth(),e=new Date(a,t,1),s=D(e),d=[];for(let v=0;v<42;v++){const C=new Date(s);C.setDate(C.getDate()+v),d.push({date:C.toISOString().split("T")[0],dayNumber:C.getDate(),isCurrentMonth:C.getMonth()===t})}return d}),Y=$(()=>[{label:"Colloqui Oggi",value:h(new Date().toISOString().split("T")[0]).length,icon:"calendar",iconClass:"text-blue-500"},{label:"Questa Settimana",value:K().length,icon:"clock",iconClass:"text-green-500"},{label:"Programmati",value:S.value.filter(a=>a.status==="scheduled").length,icon:"check-circle",iconClass:"text-purple-500"},{label:"Completati",value:S.value.filter(a=>a.status==="completed").length,icon:"academic-cap",iconClass:"text-orange-500"}]),x=async()=>{M.value=!0;try{const a=c.value==="week"?D(l.value):new Date(l.value.getFullYear(),l.value.getMonth(),1),t=c.value==="week"?new Date(a.getTime()+6*24*60*60*1e3):new Date(l.value.getFullYear(),l.value.getMonth()+1,0),e={date_from:a.toISOString().split("T")[0],date_to:t.toISOString().split("T")[0]};k.value&&(e.status=k.value),f.value&&(e.interviewer_id=f.value);const s=await I.fetchInterviews(e);if(S.value=(s==null?void 0:s.interviews)||[],z.value.length===0){const d=await I.fetchInterviewers();z.value=d||[]}}catch(a){console.error("Error loading calendar data:",a)}finally{M.value=!1}},D=a=>{const t=new Date(a),e=t.getDay(),s=t.getDate()-e+(e===0?-6:1);return new Date(t.setDate(s))},U=()=>{c.value==="week"?l.value.setDate(l.value.getDate()-7):l.value.setMonth(l.value.getMonth()-1),l.value=new Date(l.value),x()},G=()=>{c.value==="week"?l.value.setDate(l.value.getDate()+7):l.value.setMonth(l.value.getMonth()+1),l.value=new Date(l.value),x()},Q=()=>{l.value=new Date,x()},h=a=>S.value.filter(t=>new Date(t.scheduled_date).toISOString().split("T")[0]===a),J=(a,t)=>h(a).filter(e=>new Date(e.scheduled_date).getHours()===t),K=a=>{const e=D(new Date),s=new Date(e.getTime()+6*24*60*60*1e3);return S.value.filter(d=>{const v=new Date(d.scheduled_date);return v>=e&&v<=s})},O=a=>{const t=new Date().toISOString().split("T")[0];return a===t},X=(a,t)=>{p.value=a,_.value=t,w.value=!0},Z=a=>{p.value=a,_.value=null,w.value=!0},ee=()=>{const a=new URLSearchParams;if(p.value){const t=_.value?`${p.value}T${_.value.toString().padStart(2,"0")}:00`:`${p.value}T09:00`;a.set("scheduled_date",t)}w.value=!1,P.push(`/app/recruiting/interviews/new?${a.toString()}`)},q=a=>{P.push(`/app/recruiting/interviews/${a.id}`)},te=a=>({phone:"Tel",phone_screening:"Tel Screen",video:"Video",video_technical:"Video Tech",in_person:"Persona",onsite_cultural:"In Sede",technical:"Tecnico",behavioral:"Comp.",final:"Finale",final_executive:"Finale Exec"})[a]||a,ae=a=>({scheduled:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",completed:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",cancelled:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"})[a]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",re=a=>a?new Date(a).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",A=a=>a.toLocaleDateString("it-IT",{month:"short",day:"numeric"}),se=a=>a?new Date(a).toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit"}):"",oe=async()=>{try{const a={};if(k.value&&(a.status=k.value),f.value&&(a.interviewer_id=f.value),c.value==="week"){const t=D(l.value),e=new Date(t.getTime()+6*24*60*60*1e3);a.date_from=t.toISOString().split("T")[0],a.date_to=e.toISOString().split("T")[0],await I.downloadInterviewsBatchCalendar(a)}else{const t=l.value.getFullYear(),e=l.value.getMonth()+1;await I.downloadMonthlyCalendar(t,e)}}catch(a){console.error("Error exporting calendar:",a),alert("Errore nell'esportazione calendario: "+a.message)}};return ne(()=>{x()}),(a,t)=>(n(),o("div",ge,[r("div",ve,[r("div",me,[t[9]||(t[9]=r("div",null,[r("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"}," Calendario Colloqui "),r("p",{class:"text-sm text-gray-600 dark:text-gray-400"}," Gestione e pianificazione dei colloqui di selezione ")],-1)),r("div",ye,[u(g,{variant:"outline-primary",icon:"arrow-path",text:"Aggiorna",onClick:x,loading:M.value,size:"sm"},null,8,["loading"]),u(g,{variant:"outline-primary",icon:"calendar",text:"Esporta Periodo",onClick:oe,size:"sm"}),u(g,{variant:"primary",icon:"plus",text:"Nuovo Colloquio",onClick:t[0]||(t[0]=e=>a.$router.push("/app/recruiting/interviews/new"))})])])]),r("div",be,[r("div",pe,[r("div",xe,[r("div",he,[u(g,{variant:"outline-secondary",icon:"chevron-left",onClick:U,size:"sm"}),u(g,{variant:"outline-secondary",icon:"chevron-right",onClick:G,size:"sm"})]),r("h2",ke,i(H.value),1),u(g,{variant:"outline-secondary",text:"Oggi",onClick:Q,size:"sm"})]),r("div",fe,[r("div",we,[u(g,{variant:c.value==="week"?"primary":"secondary",text:"Settimana",onClick:t[1]||(t[1]=e=>c.value="week"),size:"sm"},null,8,["variant"]),u(g,{variant:c.value==="month"?"primary":"secondary",text:"Mese",onClick:t[2]||(t[2]=e=>c.value="month"),size:"sm"},null,8,["variant"])]),W(r("select",{"onUpdate:modelValue":t[3]||(t[3]=e=>k.value=e),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",onChange:x},t[10]||(t[10]=[r("option",{value:""},"Tutti gli stati",-1),r("option",{value:"scheduled"},"Programmati",-1),r("option",{value:"completed"},"Completati",-1),r("option",{value:"cancelled"},"Annullati",-1)]),544),[[E,k.value]]),W(r("select",{"onUpdate:modelValue":t[4]||(t[4]=e=>f.value=e),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",onChange:x},[t[11]||(t[11]=r("option",{value:""},"Tutti gli intervistatori",-1)),(n(!0),o(y,null,b(z.value,e=>(n(),o("option",{key:e.id,value:e.id},i(e.full_name),9,_e))),128))],544),[[E,f.value]])])])]),r("div",De,[(n(!0),o(y,null,b(Y.value,e=>(n(),o("div",{key:e.label,class:"bg-white dark:bg-gray-800 rounded-lg shadow p-4"},[r("div",Ce,[r("div",Se,[u(ce,{name:e.icon,size:"md",class:T(e.iconClass)},null,8,["name","class"])]),r("div",Te,[r("p",Ie,i(e.label),1),r("p",Me,i(e.value),1)])])]))),128))]),M.value?(n(),o("div",Oe,t[12]||(t[12]=[r("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary-600"},null,-1)]))):(n(),o("div",$e,[c.value==="week"?(n(),o("div",ze,[r("div",Fe,[t[13]||(t[13]=r("div",{class:"p-3 text-sm font-medium text-gray-500 dark:text-gray-400"},"Orario",-1)),(n(!0),o(y,null,b(V.value,e=>(n(),o("div",{key:e.date,class:"p-3 text-center"},[r("div",Ne,i(e.dayName),1),r("div",{class:T(["text-lg font-bold",O(e.date)?"text-primary-600":"text-gray-900 dark:text-white"])},i(e.dayNumber),3)]))),128))]),r("div",Pe,[r("div",Le,[(n(!0),o(y,null,b(j(L),e=>(n(),o("div",{key:e,class:"h-16 p-2 border-b border-gray-100 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400"},i(e)+":00 ",1))),128))]),(n(!0),o(y,null,b(V.value,e=>(n(),o("div",{key:e.date,class:"border-r border-gray-200 dark:border-gray-600 last:border-r-0 relative"},[(n(!0),o(y,null,b(j(L),s=>(n(),o("div",{key:s,class:"h-16 border-b border-gray-100 dark:border-gray-700 relative p-1",onClick:d=>X(e.date,s)},[(n(!0),o(y,null,b(J(e.date,s),d=>{var v;return n(),o("div",{key:d.id,class:"absolute inset-x-1 top-1 bg-blue-100 dark:bg-blue-900 border-l-4 border-blue-500 rounded-md p-2 cursor-pointer hover:bg-blue-200 dark:hover:bg-blue-800 transition-all duration-200 shadow-sm",style:ie({height:`${Math.max(Math.min(d.duration_minutes/15*4,60),48)}px`}),onClick:N(C=>q(d),["stop"])},[r("div",Ae,i(((v=d.candidate)==null?void 0:v.full_name)||"Candidato N/A"),1),r("div",We,i(te(d.interview_type)),1)],12,qe)}),128))],8,Ve))),128))]))),128))])])):(n(),o("div",Ee,[r("div",je,[(n(),o(y,null,b(B,e=>r("div",{key:e,class:"p-3 text-center text-sm font-medium text-gray-500 dark:text-gray-400"},i(e),1)),64))]),r("div",Be,[(n(!0),o(y,null,b(R.value,e=>(n(),o("div",{key:e.date,class:T(["min-h-24 border-r border-b border-gray-200 dark:border-gray-600 last:border-r-0 p-2",{"bg-gray-50 dark:bg-gray-700":!e.isCurrentMonth,"bg-primary-50 dark:bg-primary-900/20":O(e.date)}]),onClick:s=>Z(e.date)},[r("div",Re,[r("span",{class:T(["text-sm font-medium",{"text-gray-400 dark:text-gray-500":!e.isCurrentMonth,"text-primary-600 dark:text-primary-400":O(e.date),"text-gray-900 dark:text-white":e.isCurrentMonth&&!O(e.date)}])},i(e.dayNumber),3),h(e.date).length>0?(n(),o("div",Ye,i(h(e.date).length),1)):F("",!0)]),r("div",Ue,[(n(!0),o(y,null,b(h(e.date).slice(0,3),s=>{var d;return n(),o("div",{key:s.id,class:T(["text-xs p-1 rounded cursor-pointer truncate",ae(s.status)]),onClick:N(v=>q(s),["stop"])},i(se(s.scheduled_date))+" "+i(((d=s.candidate)==null?void 0:d.full_name)||"Candidato N/A"),11,Ge)}),128)),h(e.date).length>3?(n(),o("div",Qe," +"+i(h(e.date).length-3)+" altri ",1)):F("",!0)])],10,He))),128))])]))])),w.value?(n(),o("div",{key:2,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:t[8]||(t[8]=e=>w.value=!1)},[r("div",{class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[7]||(t[7]=N(()=>{},["stop"]))},[r("div",Je,[t[14]||(t[14]=r("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Programma Colloquio Rapido ",-1)),r("div",Ke,i(p.value?re(p.value):"")+" "+i(_.value?`alle ${_.value}:00`:""),1),r("div",Xe,[u(g,{variant:"outline-primary",icon:"plus",text:"Colloquio Completo",block:"",onClick:ee}),u(g,{variant:"outline-secondary",icon:"calendar",text:"Visualizza Tutti i Colloqui",block:"",onClick:t[5]||(t[5]=e=>a.$router.push("/app/recruiting/interviews"))})]),r("div",Ze,[u(g,{variant:"secondary",text:"Chiudi",onClick:t[6]||(t[6]=e=>w.value=!1)})])])])])):F("",!0)]))}},nt=ue(et,[["__scopeId","data-v-25b871bf"]]);export{nt as default};
