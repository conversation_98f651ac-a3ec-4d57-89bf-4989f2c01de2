import{r as D,c as $,w as ae,x as se,b as r,o as s,j as e,h as K,l as y,A as Q,t as d,e as v,s as N,B as A,C as U,H as W,I as J,F,Q as ie,p as L,E as ee,n as Y,z as ke,u as st,q as rt,k as q,v as E}from"./vendor.js";import{u as re,R as ot,S as lt}from"./ReviewEditModal.js";import{u as _e}from"./personnel.js";import{_ as Z,H as b,a as it}from"./app.js";import{D as nt}from"./DashboardTemplate.js";import{T as dt}from"./TabNavigation.js";import{_ as ut}from"./DataTable.js";import{S as we}from"./StatusBadge.js";import{R as ct}from"./ReviewDetailModal.js";import{C as $e}from"./ConfirmationModal.js";import{_ as mt}from"./StatsGrid.js";import"./StandardButton.js";/* empty css                                                             */import"./formatters.js";import"./MarkdownContent.js";import"./jspdf.es.min.js";const gt={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},pt={class:"text-lg font-medium text-gray-900 dark:text-white"},vt={key:0,class:"text-sm text-gray-500 dark:text-gray-400 mt-1"},yt={class:"flex items-center space-x-3"},bt={key:0,class:"flex items-center text-amber-600 dark:text-amber-400 text-sm"},xt={class:"mt-6"},ft={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6"},ht={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},kt={class:"md:col-span-2"},_t={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},wt={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},$t={class:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"},Ct={class:"space-y-4"},zt={class:"flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700 mt-6"},Tt={class:"flex space-x-3"},St=["disabled"],Rt={__name:"GoalEditModal",props:{goal:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["close","save","delete"],setup(I,{emit:n}){const x=I,z=n,k=re();_e();const o=D({title:"",description:"",category:"",priority:"medium",status:"active",target_date:"",progress:0,year:new Date().getFullYear(),success_criteria:"",measurable_outcomes:"",notes:""}),R=D({}),V=D(!1),C=D(!1),j=D(!0),f=$(()=>{var S;return!!((S=x.goal)!=null&&S.id)}),g=$(()=>o.value.title.trim()&&o.value.category);ae(o,()=>{j.value||(C.value=!0)},{deep:!0});const m=()=>{j.value=!0,x.goal&&(o.value={title:x.goal.title||"",description:x.goal.description||"",category:x.goal.category||"",priority:x.goal.priority||"medium",status:x.goal.status||"active",target_date:x.goal.target_date||"",progress:x.goal.progress||0,year:x.goal.year||new Date().getFullYear(),success_criteria:x.goal.success_criteria||"",measurable_outcomes:x.goal.measurable_outcomes||"",notes:x.goal.notes||""}),C.value=!1,setTimeout(()=>{j.value=!1},100)},u=()=>(R.value={},o.value.title.trim()||(R.value.title="Titolo obiettivo richiesto"),o.value.category||(R.value.category="Categoria richiesta"),Object.keys(R.value).length===0),p=async()=>{if(u())try{const S={...o.value};f.value?await k.updateGoal(x.goal.id,S):await k.createGoal(S),C.value=!1,z("save")}catch(S){console.error("Error saving goal:",S)}},T=async()=>{try{await k.deleteGoal(x.goal.id),V.value=!1,z("delete")}catch(S){console.error("Error deleting goal:",S)}},P=()=>{C.value?confirm("Ci sono modifiche non salvate. Vuoi davvero chiudere?")&&z("close"):z("close")};return se(()=>{m()}),(S,l)=>(s(),r(F,null,[e("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:P},[e("div",{class:"relative top-5 mx-auto p-5 border w-11/12 max-w-5xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:l[12]||(l[12]=Q(()=>{},["stop"]))},[e("div",gt,[e("div",null,[e("h3",pt,d(f.value?"Modifica Obiettivo":"Nuovo Obiettivo"),1),o.value.title?(s(),r("p",vt,d(o.value.category)+" - "+d(o.value.title),1)):y("",!0)]),e("div",yt,[C.value?(s(),r("div",bt,[v(b,{name:"exclamation-triangle",size:"sm",class:"mr-1"}),l[14]||(l[14]=N(" Modifiche non salvate "))])):y("",!0),e("button",{onClick:P,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[v(b,{name:"x-mark",size:"lg"})])])]),e("div",xt,[e("form",{onSubmit:Q(p,["prevent"]),class:"space-y-6"},[e("div",ft,[l[25]||(l[25]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Base",-1)),e("div",ht,[e("div",kt,[l[15]||(l[15]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Titolo Obiettivo * ",-1)),A(e("input",{"onUpdate:modelValue":l[0]||(l[0]=_=>o.value.title=_),type:"text",required:"",placeholder:"Es. Aumentare la produttività del team del 20%",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[U,o.value.title]]),R.value.title?(s(),r("p",_t,d(R.value.title),1)):y("",!0)]),e("div",null,[l[17]||(l[17]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Categoria * ",-1)),A(e("select",{"onUpdate:modelValue":l[1]||(l[1]=_=>o.value.category=_),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},l[16]||(l[16]=[J('<option value="" data-v-9fa0af9d>Seleziona categoria</option><option value="technical" data-v-9fa0af9d>Competenze Tecniche</option><option value="soft_skills" data-v-9fa0af9d>Soft Skills</option><option value="business" data-v-9fa0af9d>Business</option><option value="career_development" data-v-9fa0af9d>Sviluppo Carriera</option><option value="leadership" data-v-9fa0af9d>Leadership</option><option value="teamwork" data-v-9fa0af9d>Lavoro di Squadra</option>',7)]),512),[[W,o.value.category]]),R.value.category?(s(),r("p",wt,d(R.value.category),1)):y("",!0)]),e("div",null,[l[19]||(l[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Priorità ",-1)),A(e("select",{"onUpdate:modelValue":l[2]||(l[2]=_=>o.value.priority=_),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},l[18]||(l[18]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"critical"},"Critica",-1)]),512),[[W,o.value.priority]])]),e("div",null,[l[21]||(l[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Status ",-1)),A(e("select",{"onUpdate:modelValue":l[3]||(l[3]=_=>o.value.status=_),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},l[20]||(l[20]=[J('<option value="active" data-v-9fa0af9d>Attivo</option><option value="in_progress" data-v-9fa0af9d>In Corso</option><option value="completed" data-v-9fa0af9d>Completato</option><option value="cancelled" data-v-9fa0af9d>Annullato</option><option value="deferred" data-v-9fa0af9d>Rinviato</option>',5)]),512),[[W,o.value.status]])]),e("div",null,[l[22]||(l[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data Obiettivo ",-1)),A(e("input",{"onUpdate:modelValue":l[4]||(l[4]=_=>o.value.target_date=_),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[U,o.value.target_date]])]),e("div",null,[l[23]||(l[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Progresso (%) ",-1)),A(e("input",{"onUpdate:modelValue":l[5]||(l[5]=_=>o.value.progress=_),type:"number",min:"0",max:"100",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[U,o.value.progress,void 0,{number:!0}]])]),e("div",null,[l[24]||(l[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Anno ",-1)),A(e("input",{"onUpdate:modelValue":l[6]||(l[6]=_=>o.value.year=_),type:"number",min:2020,max:2030,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[U,o.value.year,void 0,{number:!0}]])])])]),e("div",$t,[l[30]||(l[30]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Dettagli Obiettivo",-1)),e("div",Ct,[e("div",null,[l[26]||(l[26]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Descrizione ",-1)),A(e("textarea",{"onUpdate:modelValue":l[7]||(l[7]=_=>o.value.description=_),rows:"3",placeholder:"Descrizione dettagliata dell'obiettivo...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[U,o.value.description]])]),e("div",null,[l[27]||(l[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Criteri di Successo ",-1)),A(e("textarea",{"onUpdate:modelValue":l[8]||(l[8]=_=>o.value.success_criteria=_),rows:"3",placeholder:"Come sarà misurato il successo di questo obiettivo...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[U,o.value.success_criteria]])]),e("div",null,[l[28]||(l[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Risultati Misurabili ",-1)),A(e("textarea",{"onUpdate:modelValue":l[9]||(l[9]=_=>o.value.measurable_outcomes=_),rows:"3",placeholder:"Risultati quantificabili attesi...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[U,o.value.measurable_outcomes]])]),e("div",null,[l[29]||(l[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Note ",-1)),A(e("textarea",{"onUpdate:modelValue":l[10]||(l[10]=_=>o.value.notes=_),rows:"2",placeholder:"Note aggiuntive...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[U,o.value.notes]])])])])],32)]),e("div",zt,[e("div",null,[f.value?(s(),r("button",{key:0,type:"button",onClick:l[11]||(l[11]=_=>V.value=!0),class:"inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 text-sm font-medium rounded-md text-red-700 dark:text-red-400 bg-white dark:bg-gray-700 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"},[v(b,{name:"trash",size:"sm",class:"mr-2"}),l[31]||(l[31]=N(" Elimina "))])):y("",!0)]),e("div",Tt,[e("button",{type:"button",onClick:P,class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"}," Annulla "),e("button",{type:"submit",onClick:p,disabled:I.loading||!g.value,class:"btn-primary disabled:opacity-50"},[I.loading?(s(),K(b,{key:0,name:"arrow-path",size:"sm",class:"animate-spin mr-2"})):y("",!0),N(" "+d(f.value?"Aggiorna Obiettivo":"Crea Obiettivo"),1)],8,St)])])])]),V.value?(s(),K($e,{key:0,title:"Elimina Obiettivo",message:"Sei sicuro di voler eliminare questo obiettivo? Questa azione non può essere annullata.","confirm-text":"Elimina","confirm-variant":"danger",onConfirm:T,onCancel:l[13]||(l[13]=_=>V.value=!1)})):y("",!0)],64))}},Dt=Z(Rt,[["__scopeId","data-v-9fa0af9d"]]),Mt={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},Vt={class:"text-lg font-medium text-gray-900 dark:text-white"},At={key:0,class:"text-sm text-gray-500 dark:text-gray-400 mt-1"},Pt={class:"flex items-center space-x-3"},Nt={key:0,class:"flex items-center text-amber-600 dark:text-amber-400 text-sm"},jt={class:"mt-6"},It={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6"},Et={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ot={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},Ft={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},Ut={class:"md:col-span-2"},Lt={class:"space-y-2"},Gt={class:"flex items-center"},Bt={class:"flex items-center"},qt={class:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"},Qt={class:"text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center"},Yt={class:"space-y-4"},Ht={class:"flex justify-between items-center mb-4"},Kt={key:0,class:"space-y-4"},Wt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Zt=["onUpdate:modelValue"],Xt=["onUpdate:modelValue"],Jt={class:"flex items-center space-x-2"},ea={class:"flex items-center text-xs"},ta=["onUpdate:modelValue"],aa={class:"flex items-end justify-end space-x-2"},sa=["onClick","disabled"],ra=["onClick","disabled"],oa=["onClick"],la={class:"mt-3"},ia=["onUpdate:modelValue"],na={key:0,class:"mt-3"},da=["onUpdate:modelValue"],ua={key:1,class:"text-center py-8 text-gray-500 dark:text-gray-400"},ca={key:0,class:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"},ma={class:"text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center"},ga={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-4"},pa={class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},va={key:0,class:"text-red-500"},ya={key:0,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-500"},ba={key:1,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-500 h-20"},xa={key:2,class:"flex space-x-1"},fa={key:3,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-500"},ha={key:4,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-500"},ka={key:5,class:"text-xs text-gray-500 dark:text-gray-400"},_a={class:"flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700 mt-6"},wa={class:"flex space-x-3"},$a=["disabled"],Ca={__name:"TemplateModal",props:{template:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["close","save","delete"],setup(I,{emit:n}){const x=I,z=n,k=re(),o=D({name:"",template_type:"",description:"",is_default:!1,is_active:!0,version:"1.0",template_fields:[]}),R=D({}),V=D(!1),C=D(!1),j=D(!0),f=$(()=>{var w;return!!((w=x.template)!=null&&w.id)}),g=$(()=>o.value.name.trim()&&o.value.template_type);ae(o,()=>{j.value||(C.value=!0)},{deep:!0});const m=()=>{j.value=!0,x.template&&(o.value={name:x.template.name||"",template_type:x.template.template_type||"",description:x.template.description||"",is_default:x.template.is_default||!1,is_active:x.template.is_active!==!1,version:x.template.version||"1.0",template_fields:x.template.template_fields||[]}),C.value=!1,setTimeout(()=>{j.value=!1},100)},u=()=>(R.value={},o.value.name.trim()||(R.value.name="Nome template richiesto"),o.value.template_type||(R.value.template_type="Tipo template richiesto"),Object.keys(R.value).length===0),p=()=>{o.value.template_fields.push({name:"",field_type:"text",description:"",is_required:!1,select_options:""})},T=w=>{o.value.template_fields.splice(w,1)},P=w=>{if(w>0){const c=o.value.template_fields.splice(w,1)[0];o.value.template_fields.splice(w-1,0,c)}},S=w=>{if(w<o.value.template_fields.length-1){const c=o.value.template_fields.splice(w,1)[0];o.value.template_fields.splice(w+1,0,c)}},l=async()=>{if(u())try{const w={...o.value};f.value?await k.updateTemplate(x.template.id,w):await k.createTemplate(w),C.value=!1,z("save")}catch(w){console.error("Error saving template:",w)}},_=async()=>{console.log("Delete template:",x.template.id),V.value=!1,z("delete")},G=()=>{C.value?confirm("Ci sono modifiche non salvate. Vuoi davvero chiudere?")&&z("close"):z("close")};return se(()=>{m()}),(w,c)=>(s(),r(F,null,[e("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:G},[e("div",{class:"relative top-5 mx-auto p-5 border w-11/12 max-w-5xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:c[7]||(c[7]=Q(()=>{},["stop"]))},[e("div",Mt,[e("div",null,[e("h3",Vt,d(f.value?"Modifica Template Performance":"Nuovo Template Performance"),1),o.value.name?(s(),r("p",At,d(o.value.template_type)+" - "+d(o.value.name),1)):y("",!0)]),e("div",Pt,[C.value?(s(),r("div",Nt,[v(b,{name:"exclamation-triangle",size:"sm",class:"mr-1"}),c[9]||(c[9]=N(" Modifiche non salvate "))])):y("",!0),e("button",{onClick:G,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[v(b,{name:"x-mark",size:"lg"})])])]),e("div",jt,[e("form",{onSubmit:Q(l,["prevent"]),class:"space-y-6"},[e("div",It,[c[18]||(c[18]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Base",-1)),e("div",Et,[e("div",null,[c[10]||(c[10]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Nome Template * ",-1)),A(e("input",{"onUpdate:modelValue":c[0]||(c[0]=h=>o.value.name=h),type:"text",required:"",placeholder:"Es. Template Valutazione Annuale 2025",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[U,o.value.name]]),R.value.name?(s(),r("p",Ot,d(R.value.name),1)):y("",!0)]),e("div",null,[c[12]||(c[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tipo Template * ",-1)),A(e("select",{"onUpdate:modelValue":c[1]||(c[1]=h=>o.value.template_type=h),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},c[11]||(c[11]=[J('<option value="" data-v-fce19b67>Seleziona tipo</option><option value="annual" data-v-fce19b67>Valutazione Annuale</option><option value="quarterly" data-v-fce19b67>Valutazione Trimestrale</option><option value="probation" data-v-fce19b67>Periodo di Prova</option><option value="project" data-v-fce19b67>Fine Progetto</option><option value="360" data-v-fce19b67>Feedback 360°</option><option value="goals" data-v-fce19b67>Template Obiettivi</option>',7)]),512),[[W,o.value.template_type]]),R.value.template_type?(s(),r("p",Ft,d(R.value.template_type),1)):y("",!0)]),e("div",Ut,[c[13]||(c[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Descrizione ",-1)),A(e("textarea",{"onUpdate:modelValue":c[2]||(c[2]=h=>o.value.description=h),rows:"3",placeholder:"Descrizione del template e del suo utilizzo...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[U,o.value.description]])]),e("div",null,[c[16]||(c[16]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"}," Impostazioni ",-1)),e("div",Lt,[e("label",Gt,[A(e("input",{"onUpdate:modelValue":c[3]||(c[3]=h=>o.value.is_default=h),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[ie,o.value.is_default]]),c[14]||(c[14]=e("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Template Predefinito",-1))]),e("label",Bt,[A(e("input",{"onUpdate:modelValue":c[4]||(c[4]=h=>o.value.is_active=h),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[ie,o.value.is_active]]),c[15]||(c[15]=e("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Template Attivo",-1))])])]),e("div",null,[c[17]||(c[17]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Versione ",-1)),A(e("input",{"onUpdate:modelValue":c[5]||(c[5]=h=>o.value.version=h),type:"text",placeholder:"1.0",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[U,o.value.version]])])])]),e("div",qt,[e("h4",Qt,[v(b,{name:"document-text",size:"sm",class:"mr-2 text-indigo-500"}),c[19]||(c[19]=N(" Struttura Template "))]),e("div",Yt,[e("div",Ht,[c[21]||(c[21]=e("h5",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Campi Template",-1)),e("button",{type:"button",onClick:p,class:"inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-200 dark:hover:bg-blue-800 transition-colors"},[v(b,{name:"plus",size:"sm",class:"mr-1"}),c[20]||(c[20]=N(" Aggiungi Campo "))])]),o.value.template_fields&&o.value.template_fields.length>0?(s(),r("div",Kt,[(s(!0),r(F,null,L(o.value.template_fields,(h,B)=>(s(),r("div",{key:B,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600"},[e("div",Wt,[e("div",null,[c[22]||(c[22]=e("label",{class:"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1"}," Nome Campo ",-1)),A(e("input",{"onUpdate:modelValue":O=>h.name=O,type:"text",placeholder:"Nome del campo",class:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"},null,8,Zt),[[U,h.name]])]),e("div",null,[c[24]||(c[24]=e("label",{class:"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1"}," Tipo ",-1)),A(e("select",{"onUpdate:modelValue":O=>h.field_type=O,class:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"},c[23]||(c[23]=[J('<option value="text" data-v-fce19b67>Testo</option><option value="textarea" data-v-fce19b67>Area Testo</option><option value="number" data-v-fce19b67>Numero</option><option value="rating" data-v-fce19b67>Rating (1-5)</option><option value="select" data-v-fce19b67>Lista Selezione</option><option value="checkbox" data-v-fce19b67>Checkbox</option><option value="date" data-v-fce19b67>Data</option>',7)]),8,Xt),[[W,h.field_type]])]),e("div",null,[c[26]||(c[26]=e("label",{class:"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1"}," Opzioni ",-1)),e("div",Jt,[e("label",ea,[A(e("input",{"onUpdate:modelValue":O=>h.is_required=O,type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,8,ta),[[ie,h.is_required]]),c[25]||(c[25]=e("span",{class:"ml-1 text-gray-600 dark:text-gray-400"},"Richiesto",-1))])])]),e("div",aa,[e("button",{type:"button",onClick:O=>P(B),disabled:B===0,class:"p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"},[v(b,{name:"arrow-up",size:"sm"})],8,sa),e("button",{type:"button",onClick:O=>S(B),disabled:B===o.value.template_fields.length-1,class:"p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"},[v(b,{name:"arrow-down",size:"sm"})],8,ra),e("button",{type:"button",onClick:O=>T(B),class:"p-1 text-red-400 hover:text-red-600"},[v(b,{name:"trash",size:"sm"})],8,oa)])]),e("div",la,[c[27]||(c[27]=e("label",{class:"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1"}," Descrizione/Aiuto ",-1)),A(e("input",{"onUpdate:modelValue":O=>h.description=O,type:"text",placeholder:"Testo di aiuto per l'utente",class:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"},null,8,ia),[[U,h.description]])]),h.field_type==="select"?(s(),r("div",na,[c[28]||(c[28]=e("label",{class:"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1"}," Opzioni Selezione (una per riga) ",-1)),A(e("textarea",{"onUpdate:modelValue":O=>h.select_options=O,rows:"3",placeholder:`Opzione 1
Opzione 2
Opzione 3`,class:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"},null,8,da),[[U,h.select_options]])])):y("",!0)]))),128))])):(s(),r("div",ua,[v(b,{name:"document-text",class:"w-12 h-12 mx-auto mb-2 text-gray-400"}),c[29]||(c[29]=e("p",{class:"text-sm"},"Nessun campo definito",-1)),c[30]||(c[30]=e("p",{class:"text-xs"},'Clicca "Aggiungi Campo" per iniziare',-1))]))])]),o.value.template_fields&&o.value.template_fields.length>0?(s(),r("div",ca,[e("h4",ma,[v(b,{name:"eye",size:"sm",class:"mr-2 text-purple-500"}),c[31]||(c[31]=N(" Anteprima Template "))]),e("div",ga,[(s(!0),r(F,null,L(o.value.template_fields,(h,B)=>(s(),r("div",{key:B,class:"space-y-2"},[e("label",pa,[N(d(h.name)+" ",1),h.is_required?(s(),r("span",va,"*")):y("",!0)]),h.field_type==="text"?(s(),r("div",ya," Campo testo... ")):h.field_type==="textarea"?(s(),r("div",ba," Area di testo... ")):h.field_type==="rating"?(s(),r("div",xa,[(s(),r(F,null,L(5,O=>v(b,{key:O,name:"star",size:"sm",class:"text-gray-300"})),64))])):h.field_type==="select"?(s(),r("div",fa,d(h.select_options?h.select_options.split(`
`)[0]+"...":"Seleziona opzione..."),1)):(s(),r("div",ha," Campo "+d(h.field_type)+"... ",1)),h.description?(s(),r("p",ka,d(h.description),1)):y("",!0)]))),128))])])):y("",!0)],32)]),e("div",_a,[e("div",null,[f.value?(s(),r("button",{key:0,type:"button",onClick:c[6]||(c[6]=h=>V.value=!0),class:"inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 text-sm font-medium rounded-md text-red-700 dark:text-red-400 bg-white dark:bg-gray-700 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"},[v(b,{name:"trash",size:"sm",class:"mr-2"}),c[32]||(c[32]=N(" Elimina "))])):y("",!0)]),e("div",wa,[e("button",{type:"button",onClick:G,class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"}," Annulla "),e("button",{type:"submit",onClick:l,disabled:I.loading||!g.value,class:"btn-primary disabled:opacity-50"},[I.loading?(s(),K(b,{key:0,name:"arrow-path",size:"sm",class:"animate-spin mr-2"})):y("",!0),N(" "+d(f.value?"Aggiorna Template":"Crea Template"),1)],8,$a)])])])]),V.value?(s(),K($e,{key:0,title:"Elimina Template",message:"Sei sicuro di voler eliminare questo template? Questa azione non può essere annullata.","confirm-text":"Elimina","confirm-variant":"danger",onConfirm:_,onCancel:c[8]||(c[8]=h=>V.value=!1)})):y("",!0)],64))}},za=Z(Ca,[["__scopeId","data-v-fce19b67"]]),Ta={class:"performance-trend-chart"},Sa={key:0,class:"h-64 flex items-center justify-center text-gray-500 dark:text-gray-400"},Ra={class:"text-center"},Da={key:1,class:"h-64 relative"},Ma=["d"],Va=["d"],Aa={key:2},Pa=["cx","cy","onMouseover"],Na={class:"text-xs",fill:"#6b7280"},ja=["x"],Ia={class:"font-medium"},Ea={class:"mt-4 grid grid-cols-3 gap-4 text-center text-sm"},Oa={class:"font-semibold text-gray-900 dark:text-white"},Fa={class:"font-semibold text-gray-900 dark:text-white"},Ua={class:"font-semibold text-gray-900 dark:text-white"},La={__name:"PerformanceTrendChart",props:{data:{type:Array,default:()=>[]}},setup(I){const n=I,x=D(null),z=D({show:!1,x:0,y:0,month:"",rating:0,count:0}),k=$(()=>{if(!n.data||n.data.length===0)return[];const m=400,u=200,p=30,T=m-p*2,P=u-p*2,S=n.data.filter(l=>l.average_rating>0);return S.length===0?[]:(Math.max(...S.map(l=>l.average_rating)),Math.min(...S.map(l=>l.average_rating)),S.map((l,_)=>{const G=S.length===1?m/2:p+_/(S.length-1)*T,w=parseFloat(l.average_rating)||0,c=p+(5-w)/4*P;return{x:Math.max(0,G),y:Math.max(0,c),label:l.month_name||"N/A",rating:w.toFixed(1),count:l.reviews_count||0,month:l.month_name||"N/A"}}))}),o=$(()=>{if(k.value.length===0)return"";let m=`M ${k.value[0].x} ${k.value[0].y}`;for(let u=1;u<k.value.length;u++)m+=` L ${k.value[u].x} ${k.value[u].y}`;return m}),R=$(()=>{if(k.value.length===0)return"";let m=`M ${k.value[0].x} ${k.value[0].y}`;for(let u=1;u<k.value.length;u++)m+=` L ${k.value[u].x} ${k.value[u].y}`;return m+=` L ${k.value[k.value.length-1].x} 200`,m+=` L ${k.value[0].x} 200 Z`,m}),V=$(()=>{if(!n.data||n.data.length===0)return"0.0";const m=n.data.filter(p=>p.average_rating>0);if(m.length===0)return"0.0";const u=Math.max(...m.map(p=>parseFloat(p.average_rating)||0));return isNaN(u)?"0.0":u.toFixed(1)}),C=$(()=>{if(!n.data||n.data.length===0)return"0.0";const m=n.data.filter(T=>T.average_rating>0);if(m.length===0)return"0.0";const p=m.reduce((T,P)=>T+(parseFloat(P.average_rating)||0),0)/m.length;return isNaN(p)?"0.0":p.toFixed(1)}),j=$(()=>{if(!n.data||n.data.length<2)return"→ Stabile";const m=n.data.filter(P=>P.average_rating>0);if(m.length<2)return"→ Stabile";const u=parseFloat(m[0].average_rating)||0,T=(parseFloat(m[m.length-1].average_rating)||0)-u;return isNaN(T)?"→ Stabile":T>.2?"↗ Crescita":T<-.2?"↘ Calo":"→ Stabile"}),f=(m,u)=>{z.value={show:!0,x:u.offsetX+10,y:u.offsetY-10,month:m.month,rating:m.rating,count:m.count}},g=()=>{z.value.show=!1};return(m,u)=>(s(),r("div",Ta,[!I.data||I.data.length===0?(s(),r("div",Sa,[e("div",Ra,[v(b,{name:"chart-bar",class:"w-12 h-12 mx-auto mb-2 text-gray-400"}),u[0]||(u[0]=e("p",{class:"text-sm"},"Nessun dato trend disponibile",-1))])])):(s(),r("div",Da,[(s(),r("svg",{class:"w-full h-full",viewBox:"0 0 400 200",ref_key:"chartSvg",ref:x},[u[1]||(u[1]=J('<defs data-v-47159f1a><linearGradient id="performanceGradient" x1="0%" y1="0%" x2="0%" y2="100%" data-v-47159f1a><stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.3;" data-v-47159f1a></stop><stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0;" data-v-47159f1a></stop></linearGradient></defs><g stroke="#e5e7eb" stroke-width="1" opacity="0.5" data-v-47159f1a><line x1="0" y1="40" x2="400" y2="40" data-v-47159f1a></line><line x1="0" y1="80" x2="400" y2="80" data-v-47159f1a></line><line x1="0" y1="120" x2="400" y2="120" data-v-47159f1a></line><line x1="0" y1="160" x2="400" y2="160" data-v-47159f1a></line></g><g class="text-xs" fill="#6b7280" data-v-47159f1a><text x="5" y="45" text-anchor="start" data-v-47159f1a>5.0</text><text x="5" y="85" text-anchor="start" data-v-47159f1a>3.8</text><text x="5" y="125" text-anchor="start" data-v-47159f1a>2.5</text><text x="5" y="165" text-anchor="start" data-v-47159f1a>1.3</text></g>',3)),o.value?(s(),r("path",{key:0,d:R.value,fill:"url(#performanceGradient)"},null,8,Ma)):y("",!0),o.value?(s(),r("path",{key:1,d:o.value,fill:"none",stroke:"#3b82f6","stroke-width":"2"},null,8,Va)):y("",!0),k.value.length>0?(s(),r("g",Aa,[(s(!0),r(F,null,L(k.value,(p,T)=>(s(),r("circle",{key:T,cx:p.x,cy:p.y,r:"4",fill:"#3b82f6",class:"cursor-pointer",onMouseover:P=>f(p,P),onMouseout:g},null,40,Pa))),128))])):y("",!0),e("g",Na,[(s(!0),r(F,null,L(k.value,(p,T)=>(s(),r("text",{key:T,x:p.x,y:"190","text-anchor":"middle"},d(p.label),9,ja))),128))])],512)),z.value.show?(s(),r("div",{key:0,class:"absolute pointer-events-none z-10 bg-gray-900 text-white text-xs rounded px-2 py-1",style:ee({left:z.value.x+"px",top:z.value.y+"px"})},[e("div",Ia,d(z.value.month),1),e("div",null,"Rating: "+d(z.value.rating),1),e("div",null,"Reviews: "+d(z.value.count),1)],4)):y("",!0),u[2]||(u[2]=e("div",{class:"absolute bottom-2 left-2 flex space-x-4 text-xs text-gray-600 dark:text-gray-400"},[e("div",{class:"flex items-center"},[e("div",{class:"w-3 h-3 bg-blue-500 rounded-full mr-1"}),N(" Rating Medio Mensile ")])],-1))])),e("div",Ea,[e("div",null,[e("div",Oa,d(V.value),1),u[3]||(u[3]=e("div",{class:"text-gray-500 dark:text-gray-400"},"Massimo",-1))]),e("div",null,[e("div",Fa,d(C.value),1),u[4]||(u[4]=e("div",{class:"text-gray-500 dark:text-gray-400"},"Media",-1))]),e("div",null,[e("div",Ua,d(j.value),1),u[5]||(u[5]=e("div",{class:"text-gray-500 dark:text-gray-400"},"Trend",-1))])])]))}},Ga=Z(La,[["__scopeId","data-v-47159f1a"]]),Ba={class:"goal-completion-chart"},qa={key:0,class:"h-48 flex items-center justify-center text-gray-500 dark:text-gray-400"},Qa={class:"text-center"},Ya={key:1},Ha={class:"flex items-center justify-center h-48 mb-6"},Ka={class:"relative w-32 h-32"},Wa={class:"w-32 h-32 transform -rotate-90"},Za=["stroke-dasharray","stroke-dashoffset"],Xa={class:"absolute inset-0 flex items-center justify-center"},Ja={class:"text-center"},es={class:"text-2xl font-bold text-gray-900 dark:text-white"},ts={class:"grid grid-cols-3 gap-4 text-center mb-6"},as={class:"text-lg font-semibold text-green-600"},ss={class:"text-lg font-semibold text-yellow-600"},rs={class:"text-lg font-semibold text-gray-600 dark:text-gray-400"},os={class:"space-y-3"},ls={class:"flex items-center justify-between mb-2"},is={class:"text-sm font-medium text-gray-900 dark:text-white capitalize"},ns={class:"flex items-center space-x-2"},ds={class:"text-sm font-semibold text-brand-primary-600"},us={class:"text-xs text-gray-500 dark:text-gray-400"},cs={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mb-2"},ms={class:"flex justify-between text-xs text-gray-600 dark:text-gray-400"},gs={key:0,class:"mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg"},ps={class:"flex items-start"},vs={class:"text-sm text-blue-700 dark:text-blue-300 space-y-1"},ys={key:0},bs={key:1},xs={key:2},fs={key:3},hs={key:4},ks={__name:"GoalCompletionChart",props:{data:{type:Array,default:()=>[]},overallRate:{type:Number,default:0}},setup(I){const n=I,x=$(()=>2*Math.PI*56),z=$(()=>!n.data||n.data.length===0?0:n.data.reduce((m,u)=>m+(parseInt(u.total)||0),0)),k=$(()=>!n.data||n.data.length===0?0:n.data.reduce((m,u)=>m+(parseInt(u.completed)||0),0)),o=$(()=>!n.data||n.data.length===0?0:n.data.reduce((m,u)=>m+(parseInt(u.in_progress)||0),0)),R=$(()=>{const m=parseFloat(n.overallRate)||0;if(m>0)return Math.round(m);const u=z.value>0?Math.round(k.value/z.value*100):0;return isNaN(u)?0:u}),V=$(()=>!n.data||n.data.length===0?null:n.data.reduce((m,u)=>{const p=parseFloat(m.completion_rate)||0;return(parseFloat(u.completion_rate)||0)>p?u:m})),C=$(()=>!n.data||n.data.length===0?null:n.data.reduce((m,u)=>{const p=parseFloat(m.completion_rate)||0;return(parseFloat(u.completion_rate)||0)<p?u:m})),j=$(()=>n.data&&n.data.length>0&&z.value>0),f=m=>({technical:"Competenze Tecniche",soft_skills:"Soft Skills",business:"Business",career_development:"Sviluppo Carriera",leadership:"Leadership",teamwork:"Lavoro di Squadra",other:"Altro"})[m]||m,g=m=>m>=80?"bg-green-500":m>=60?"bg-yellow-500":m>=40?"bg-orange-500":"bg-red-500";return(m,u)=>(s(),r("div",Ba,[!I.data||I.data.length===0?(s(),r("div",qa,[e("div",Qa,[v(b,{name:"target",class:"w-12 h-12 mx-auto mb-2 text-gray-400"}),u[0]||(u[0]=e("p",{class:"text-sm"},"Nessun dato obiettivi disponibile",-1))])])):(s(),r("div",Ya,[e("div",Ha,[e("div",Ka,[(s(),r("svg",Wa,[u[1]||(u[1]=e("circle",{cx:"64",cy:"64",r:"56",stroke:"#e5e7eb","stroke-width":"8",fill:"transparent",class:"dark:stroke-gray-600"},null,-1)),e("circle",{cx:"64",cy:"64",r:"56",stroke:"#10b981","stroke-width":"8",fill:"transparent","stroke-dasharray":x.value,"stroke-dashoffset":x.value-R.value/100*x.value,class:"transition-all duration-1000 ease-in-out"},null,8,Za)])),e("div",Xa,[e("div",Ja,[e("div",es,d(R.value)+"% ",1),u[2]||(u[2]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"Completato",-1))])])])]),e("div",ts,[e("div",null,[e("div",as,d(k.value),1),u[3]||(u[3]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"Completati",-1))]),e("div",null,[e("div",ss,d(o.value),1),u[4]||(u[4]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"In Corso",-1))]),e("div",null,[e("div",rs,d(z.value),1),u[5]||(u[5]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"Totali",-1))])]),e("div",os,[u[6]||(u[6]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"}," Completamento per Categoria ",-1)),(s(!0),r(F,null,L(I.data,p=>(s(),r("div",{key:p.category,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3"},[e("div",ls,[e("span",is,d(f(p.category)),1),e("div",ns,[e("span",ds,d(p.completion_rate)+"% ",1),e("span",us," ("+d(p.completed)+"/"+d(p.total)+") ",1)])]),e("div",cs,[e("div",{class:Y(["h-2 rounded-full transition-all duration-500",g(p.completion_rate)]),style:ee({width:p.completion_rate+"%"})},null,6)]),e("div",ms,[e("span",null,d(p.active)+" attivi",1),e("span",null,d(p.in_progress)+" in corso",1),e("span",null,d(p.completed)+" completati",1)])]))),128))]),j.value?(s(),r("div",gs,[e("div",ps,[v(b,{name:"light-bulb",class:"h-5 w-5 text-blue-400 mt-0.5 mr-3"}),e("div",null,[u[7]||(u[7]=e("h4",{class:"text-sm font-medium text-blue-800 dark:text-blue-200 mb-1"}," Insights Performance ",-1)),e("ul",vs,[V.value?(s(),r("li",ys,[e("strong",null,d(f(V.value.category)),1),N(" ha il miglior tasso di completamento ("+d(V.value.completion_rate)+"%) ",1)])):y("",!0),C.value&&C.value.completion_rate<50?(s(),r("li",bs,[e("strong",null,d(f(C.value.category)),1),N(" necessita attenzione ("+d(C.value.completion_rate)+"% completamento) ",1)])):y("",!0),R.value>=80?(s(),r("li",xs," Eccellente performance generale negli obiettivi! ")):R.value>=60?(s(),r("li",fs," Buona performance, ci sono opportunità di miglioramento. ")):(s(),r("li",hs," Focus su completamento obiettivi per migliorare le performance. "))])])])])):y("",!0)]))]))}},_s=Z(ks,[["__scopeId","data-v-e9f1f370"]]),ws={class:"analytics-dashboard"},$s={key:0,class:"flex justify-center items-center py-12"},Cs={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},zs={class:"flex items-center"},Ts={class:"text-red-800 dark:text-red-200"},Ss={key:2},Rs={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6"},Ds={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ms={class:"flex items-center justify-between mb-4"},Vs={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},As={class:"flex items-center justify-between mb-4"},Ps={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},Ns={class:"flex items-center justify-between mb-4"},js={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Is={class:"text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},Es={class:"text-2xl font-bold text-purple-600 mb-2"},Os={class:"text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},Fs={class:"text-2xl font-bold text-blue-600 mb-2"},Us={class:"text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},Ls={class:"text-2xl font-bold text-green-600 mb-2"},Gs={class:"mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg"},Bs={class:"flex items-start"},qs={class:"text-sm text-blue-700 dark:text-blue-300"},Qs={key:0},Ys={key:1},Hs={key:2},Ks={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},Ws={class:"flex items-center justify-between mb-4"},Zs={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},Xs={class:"flex items-center justify-between mb-3"},Js={class:"font-medium text-gray-900 dark:text-white capitalize"},er={class:"text-lg font-bold text-brand-primary-600"},tr={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mb-3"},ar={class:"grid grid-cols-3 gap-2 text-xs text-gray-600 dark:text-gray-400"},sr={class:"text-center"},rr={class:"font-semibold text-gray-900 dark:text-white"},or={class:"text-center"},lr={class:"font-semibold text-green-600"},ir={class:"text-center"},nr={class:"font-semibold text-yellow-600"},dr={key:1,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ur={class:"flex items-center justify-between mb-4"},cr={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},mr={class:"text-2xl font-bold text-gray-900 dark:text-white mb-2"},gr={class:"text-sm text-gray-600 dark:text-gray-400"},pr={key:2,class:"text-center py-12"},vr={__name:"PerformanceAnalytics",props:{employeeId:{type:[Number,String],default:null},year:{type:Number,default:()=>new Date().getFullYear()}},setup(I){const n=I,x=re(),z=D(!1),k=D(null),o=D(null),R=$(()=>{var g;if(!((g=o.value)!=null&&g.summary_stats))return[];const f=o.value.summary_stats;return[{id:"total_reviews",label:"Valutazioni Totali",value:f.total_reviews||0,icon:"document-text",color:"blue",trend:null},{id:"total_goals",label:"Obiettivi Totali",value:f.total_goals||0,icon:"target",color:"green",trend:null},{id:"goal_completion",label:"Completamento Obiettivi",value:`${f.overall_goal_completion_rate||0}%`,icon:"check-circle",color:"emerald",trend:null},{id:"avg_goals",label:"Obiettivi per Review",value:f.avg_goals_per_review||0,icon:"calculator",color:"purple",trend:null}]}),V=$(()=>{var f,g;return o.value&&(((f=o.value.summary_stats)==null?void 0:f.total_reviews)>0||((g=o.value.summary_stats)==null?void 0:g.total_goals)>0)}),C=async()=>{z.value=!0,k.value=null;try{const f={employee_id:n.employeeId,year:n.year};await x.fetchAnalytics(f),o.value=x.analyticsData}catch(f){k.value=f.message||"Errore nel caricamento analytics",console.error("Error loading analytics:",f)}finally{z.value=!1}},j=f=>({technical:"Competenze Tecniche",soft_skills:"Soft Skills",business:"Business",career_development:"Sviluppo Carriera",leadership:"Leadership",teamwork:"Lavoro di Squadra",other:"Altro"})[f]||f;return ae([()=>n.employeeId,()=>n.year],()=>{C()}),se(()=>{C()}),(f,g)=>{var m,u,p,T,P,S,l,_,G;return s(),r("div",ws,[z.value?(s(),r("div",$s,g[0]||(g[0]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary-600"},null,-1),e("span",{class:"ml-3 text-gray-600 dark:text-gray-400"},"Caricamento analytics...",-1)]))):k.value?(s(),r("div",Cs,[e("div",zs,[v(b,{name:"exclamation-triangle",class:"h-5 w-5 text-red-400 mr-2"}),e("span",Ts,d(k.value),1)])])):o.value?(s(),r("div",Ss,[v(mt,{stats:R.value,class:"mb-6"},null,8,["stats"]),e("div",Rs,[e("div",Ds,[e("div",Ms,[g[1]||(g[1]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Trend Performance ",-1)),v(b,{name:"chart-bar",class:"h-5 w-5 text-brand-primary-600"})]),v(Ga,{data:o.value.performance_trends},null,8,["data"])]),e("div",Vs,[e("div",As,[g[2]||(g[2]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Completamento Obiettivi ",-1)),v(b,{name:"target",class:"h-5 w-5 text-green-600"})]),v(_s,{data:o.value.goal_completion_by_category,"overall-rate":((m=o.value.summary_stats)==null?void 0:m.overall_goal_completion_rate)||0},null,8,["data","overall-rate"])])]),e("div",Ps,[e("div",Ns,[g[3]||(g[3]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Correlazione Obiettivi-Valutazioni ",-1)),v(b,{name:"arrow-trending-up",class:"h-5 w-5 text-purple-600"})]),e("div",js,[e("div",Is,[e("div",Es,d(((u=o.value.review_goal_correlation)==null?void 0:u.coefficient)||0),1),g[4]||(g[4]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400 mb-1"}," Coefficiente di Correlazione ",-1)),v(we,{status:((p=o.value.review_goal_correlation)==null?void 0:p.strength)||"weak",type:"generic"},null,8,["status"])]),e("div",Os,[e("div",Fs,d(((T=o.value.review_goal_correlation)==null?void 0:T.data_points)||0),1),g[5]||(g[5]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"}," Punti Dati Analizzati ",-1))]),e("div",Us,[e("div",Ls,d(((P=o.value.summary_stats)==null?void 0:P.avg_goals_per_review)||0),1),g[6]||(g[6]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"}," Obiettivi Medi per Review ",-1))])]),e("div",Gs,[e("div",Bs,[v(b,{name:"information-circle",class:"h-5 w-5 text-blue-400 mt-0.5 mr-3"}),e("div",null,[g[7]||(g[7]=e("h4",{class:"text-sm font-medium text-blue-800 dark:text-blue-200 mb-1"}," Interpretazione Correlazione ",-1)),e("p",qs,[((S=o.value.review_goal_correlation)==null?void 0:S.strength)==="strong"?(s(),r("span",Qs," Forte correlazione: Il completamento degli obiettivi è strettamente legato alle valutazioni performance. ")):((l=o.value.review_goal_correlation)==null?void 0:l.strength)==="moderate"?(s(),r("span",Ys," Correlazione moderata: Esiste una relazione tra obiettivi e valutazioni, ma altri fattori influenzano le performance. ")):(s(),r("span",Hs," Correlazione debole: Il completamento degli obiettivi ha una relazione limitata con le valutazioni performance. "))])])])])]),((_=o.value.goal_completion_by_category)==null?void 0:_.length)>0?(s(),r("div",Ks,[e("div",Ws,[g[8]||(g[8]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Obiettivi per Categoria ",-1)),v(b,{name:"squares-2x2",class:"h-5 w-5 text-indigo-600"})]),e("div",Zs,[(s(!0),r(F,null,L(o.value.goal_completion_by_category,w=>(s(),r("div",{key:w.category,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},[e("div",Xs,[e("h4",Js,d(j(w.category)),1),e("span",er,d(w.completion_rate)+"% ",1)]),e("div",tr,[e("div",{class:"bg-brand-primary-600 h-2 rounded-full transition-all duration-500",style:ee({width:w.completion_rate+"%"})},null,4)]),e("div",ar,[e("div",sr,[e("div",rr,d(w.total),1),g[9]||(g[9]=e("div",null,"Totali",-1))]),e("div",or,[e("div",lr,d(w.completed),1),g[10]||(g[10]=e("div",null,"Completati",-1))]),e("div",ir,[e("div",nr,d(w.in_progress),1),g[11]||(g[11]=e("div",null,"In Corso",-1))])])]))),128))])])):y("",!0),((G=o.value.team_performance_distribution)==null?void 0:G.length)>0?(s(),r("div",dr,[e("div",ur,[g[12]||(g[12]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Distribuzione Performance Team ",-1)),v(b,{name:"users",class:"h-5 w-5 text-orange-600"})]),e("div",cr,[(s(!0),r(F,null,L(o.value.team_performance_distribution,w=>(s(),r("div",{key:w.range,class:"text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},[e("div",mr,d(w.count),1),e("div",gr," Rating "+d(w.range),1)]))),128))])])):y("",!0),V.value?y("",!0):(s(),r("div",pr,[v(b,{name:"chart-bar",class:"w-16 h-16 mx-auto mb-4 text-gray-400"}),g[13]||(g[13]=e("h3",{class:"text-lg font-medium mb-2 text-gray-900 dark:text-white"}," Dati Analytics Non Disponibili ",-1)),g[14]||(g[14]=e("p",{class:"text-gray-600 dark:text-gray-400 mb-4"}," Non ci sono ancora sufficienti dati per generare analytics meaningful. ",-1)),g[15]||(g[15]=e("p",{class:"text-sm text-gray-500"}," Crea alcune valutazioni e obiettivi per vedere le analytics. ",-1))]))])):y("",!0)])}}},yr=Z(vr,[["__scopeId","data-v-ee2c8204"]]),br={key:0,class:"flex items-center gap-2"},xr=["value"],fr={class:"flex items-center gap-2"},hr={class:"h-64 flex items-center justify-center"},kr={class:"text-center"},_r={class:"w-32 h-32 mx-auto mb-4 relative"},wr={class:"w-32 h-32 transform -rotate-90",viewBox:"0 0 36 36"},$r=["stroke-dasharray"],Cr={class:"absolute inset-0 flex items-center justify-center"},zr={class:"text-center"},Tr={class:"text-xl font-bold text-gray-900 dark:text-white"},Sr={class:"space-y-2"},Rr={class:"flex items-center justify-between text-sm"},Dr={class:"font-medium"},Mr={class:"flex items-center justify-between text-sm"},Vr={class:"font-medium"},Ar={class:"flex items-center justify-between text-sm"},Pr={class:"font-medium"},Nr=["onClick"],jr={class:"flex-1"},Ir={class:"text-sm font-medium text-gray-900 dark:text-white"},Er={class:"text-xs text-gray-500 dark:text-gray-400"},Or={class:"flex items-center mt-1 space-x-2"},Fr={class:"text-right"},Ur={key:0,class:"text-sm font-medium text-gray-900 dark:text-white"},Lr={key:1,class:"text-xs text-gray-500 dark:text-gray-400"},Gr={class:"flex-1 min-w-0"},Br={class:"text-sm font-medium text-gray-900 dark:text-white"},qr={class:"text-xs text-gray-500 dark:text-gray-400"},Qr={class:"flex items-center justify-between mt-1"},Yr={class:"text-xs text-gray-400 dark:text-gray-500"},Hr={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Kr={class:"flex justify-between items-start"},Wr={class:"flex-1"},Zr={class:"text-sm font-medium text-gray-900 dark:text-white flex items-center"},Xr={class:"text-xs text-gray-500 dark:text-gray-400"},Jr={class:"text-right"},eo={class:"text-sm font-bold text-gray-900 dark:text-white"},to={class:"text-xs text-gray-500"},ao={class:"mt-2"},so={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},ro={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},oo={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},lo={class:"p-6"},io={key:0,class:"reviews-tab"},no={key:0,class:"text-center py-8"},uo={key:1,class:"text-center py-8"},co={class:"text-sm text-gray-600 dark:text-gray-400"},mo={key:2},go={key:0,class:"flex items-center"},po={class:"text-sm font-medium"},vo={class:"ml-2 flex"},yo={key:1,class:"text-gray-400"},bo={class:"flex items-center justify-end space-x-2"},xo=["onClick"],fo=["onClick"],ho={key:1,class:"goals-tab"},ko={class:"flex justify-between items-center mb-6"},_o={key:0,class:"text-center py-8"},wo={key:1},$o={key:0,class:"text-center py-8 text-gray-500"},Co={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},zo={class:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"},To={class:"flex space-x-1"},So=["onClick"],Ro=["onClick"],Do={class:"flex justify-between items-start mb-3"},Mo=["onClick"],Vo={class:"flex items-center space-x-2"},Ao={class:"text-xs text-gray-600 dark:text-gray-400 mb-3"},Po={class:"mb-2"},No={class:"flex justify-between text-xs mb-1"},jo={class:"font-medium text-gray-900 dark:text-white"},Io={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},Eo={class:"flex justify-between text-xs"},Oo={class:"font-medium text-gray-700 dark:text-gray-300"},Fo={key:0,class:"mt-2 pt-2 border-t border-gray-200 dark:border-gray-600"},Uo={class:"flex items-center justify-between text-xs"},Lo={class:"flex items-center"},Go={key:2,class:"analytics-tab"},Bo={key:3,class:"templates-tab"},qo={class:"flex justify-between items-center mb-6"},Qo={key:0,class:"text-center py-8"},Yo={key:1},Ho={key:0,class:"text-center py-8 text-gray-500"},Ko={key:1,class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Wo={class:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"},Zo={class:"flex space-x-1"},Xo=["onClick"],Jo=["onClick"],el={class:"flex justify-between items-start mb-3"},tl=["onClick"],al={key:0,class:"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},sl={class:"text-xs text-gray-600 dark:text-gray-400 mb-3"},rl={class:"flex justify-between text-xs"},ol={class:"font-medium text-gray-700 dark:text-gray-300"},ll={__name:"PersonnelPerformance",setup(I){const n=re(),x=_e(),z=it(),k=st(),o=rt(),R=()=>{const a=k.query.tab;return a&&["reviews","goals","analytics","templates"].includes(a)?a:"reviews"},V=D(!1),C=D(R()),j=D(""),f=D(2025),g=D("30"),m=D(!1),u=D(!1),p=D(!1),T=D(!1),P=D(null),S=D(null),l=$(()=>n.dashboardData),_=$(()=>{var t;const a=(t=z.user)==null?void 0:t.role;return a==="admin"||a==="hr"}),G=$(()=>_.value),w=$(()=>[{value:"7",label:"Ultimi 7 giorni"},{value:"30",label:"Ultimo mese"},{value:"90",label:"Ultimi 3 mesi"},{value:"365",label:"Ultimo anno"}]),c=$(()=>{var a,t,i,M,X;return[{id:"reviews",title:"Valutazioni",value:((a=l.value.reviews)==null?void 0:a.total)||0,subtitle:`${((t=l.value.reviews)==null?void 0:t.pending)||0} in attesa`,icon:"clipboard-document-check",color:"blue",clickable:!0,onClick:()=>ue("reviews")},{id:"goals",title:"Obiettivi",value:((i=l.value.goals)==null?void 0:i.total)||0,subtitle:`${((M=l.value.goals)==null?void 0:M.completed)||0} completati`,icon:"presentation-chart-line",color:"green",clickable:!0,onClick:()=>ue("goals")},{id:"average_rating",title:"Rating Medio",value:l.value.average_rating?`${l.value.average_rating.toFixed(1)}★`:"N/A",subtitle:"Performance team",icon:"star",color:"yellow",clickable:!1},{id:"completion_rate",title:"Completamento",value:(X=l.value.reviews)!=null&&X.completion_rate?`${l.value.reviews.completion_rate}%`:"N/A",subtitle:"Valutazioni 2024",icon:"chart-bar",color:"purple",clickable:!1}]}),h=$(()=>[{id:"performance-trends",title:"Trend Performance",type:"line"},{id:"goals-progress",title:"Stato Obiettivi",type:"donut"}]),B=$(()=>n.reviews.filter(a=>a&&(a.status==="pending"||a.due_date)).slice(0,5).map(a=>{var t,i,M;return{id:a.id,title:`Valutazione ${(t=a.employee)==null?void 0:t.first_name} ${(i=a.employee)==null?void 0:i.last_name}`,description:`Anno ${a.review_year} - ${((M=a.template)==null?void 0:M.name)||"Template Standard"}`,status:a.status,priority:a.status==="pending"&&a.due_date?"high":"medium",rating:a.overall_rating,due_date:a.due_date,date:a.due_date||a.created_at}})),O=$(()=>[{id:1,title:"Valutazione completata",description:"Mario Rossi ha completato la valutazione 2024",timestamp:new Date(Date.now()-2*60*60*1e3).toISOString(),type:"review",icon:"clipboard-document-check",user_name:"Mario Rossi"},{id:2,title:"Nuovo obiettivo assegnato",description:"Certificazione AWS per Q2 2025",timestamp:new Date(Date.now()-6*60*60*1e3).toISOString(),type:"goal",icon:"presentation-chart-line",user_name:"Anna Verdi"},{id:3,title:"Feedback 360° ricevuto",description:"Feedback da 3 colleghi per Luca Bianchi",timestamp:new Date(Date.now()-24*60*60*1e3).toISOString(),type:"feedback",icon:"chat-bubble-left-right",user_name:"Luca Bianchi"}]),Ce=$(()=>[{id:"avg_completion_time",name:"Tempo Medio Completamento",description:"Giorni per completare valutazione",current_value:12,target_value:10,unit:" giorni",performance_percentage:83,trend:"down"},{id:"feedback_response_rate",name:"Tasso Risposta Feedback",description:"Percentuale feedback 360° ricevuti",current_value:87,target_value:95,unit:"%",performance_percentage:92,trend:"up"},{id:"goals_on_track",name:"Obiettivi in Linea",description:"Obiettivi con progresso >70%",current_value:15,target_value:20,unit:"",performance_percentage:75,trend:"stable"}]),ne=$(()=>{const a=l.value.goals;return!a||!a.total?0:Math.round(a.completed/a.total*100)}),ze=$(()=>[{id:"reviews",name:"Valutazioni",icon:"clipboard-document-check",badge:n.reviews.length||"",disabled:!1},{id:"goals",name:"Obiettivi",icon:"presentation-chart-line",badge:n.goals.length||"",disabled:!1},{id:"analytics",name:"Analytics",icon:"chart-bar",disabled:!1},{id:"templates",name:"Template",icon:"document-text",badge:n.templates.length||"",disabled:!_.value}]),Te=$(()=>[{key:"employee_name",label:"Dipendente",type:"text"},{key:"review_year",label:"Anno",type:"text"},{key:"status",label:"Stato",type:"custom"},{key:"overall_rating",label:"Rating",type:"custom"},{key:"due_date",label:"Scadenza",type:"date"},{key:"actions",label:"Azioni",type:"custom"}]),Se=$(()=>n.reviews.map(a=>{var t,i;return{...a,employee_name:`${(t=a.employee)==null?void 0:t.first_name} ${(i=a.employee)==null?void 0:i.last_name}`||"N/A"}})),de=async()=>{await H()},Re=a=>{g.value=a,de()},De=()=>{n.clearError()},Me=(a,t)=>{console.log("Chart action:",a,t)},ue=a=>{const t=window.pageYOffset||document.documentElement.scrollTop;a==="reviews"?(C.value="reviews",oe("reviews")):a==="goals"&&(C.value="goals",oe("goals")),ke(()=>{window.scrollTo(0,t)})},Ve=()=>{C.value="reviews",oe("reviews")},oe=a=>{const t={...k.query};a!=="reviews"?t.tab=a:delete t.tab,o.replace({query:t})},Ae=a=>{const t=window.pageYOffset||document.documentElement.scrollTop;C.value=a,ke(()=>{window.scrollTo(0,t)})},Pe=a=>{const t=n.reviews.find(i=>i.id===a.id);t&&ce(t)},Ne=()=>{console.log("Create new review"),n.clearCurrentReview(),u.value=!0},je=async()=>{await H()},Ie=async()=>{await H()},te=async()=>{try{const a={year:f.value,employee_id:j.value||void 0};await n.fetchReviews(a)}catch(a){console.error("Error loading reviews:",a)}},H=async()=>{V.value=!0;try{const a={year:f.value,employee_id:j.value||void 0,period:g.value};await Promise.all([n.fetchDashboardData(a),n.fetchReviews(a),n.fetchGoals(a),n.fetchTemplates()])}catch(a){console.error("Error loading performance data:",a)}finally{V.value=!1}},ce=a=>{console.log("Review clicked:",a),n.setCurrentReview(a),m.value=!0},Ee=a=>{console.log("Edit review:",a),n.setCurrentReview(a),u.value=!0},Oe=a=>_.value&&a&&a.status==="draft",Fe=async a=>{if(confirm(`Sei sicuro di voler eliminare la valutazione di ${a.employee_name} per l'anno ${a.review_year}? Questa azione non può essere annullata.`))try{await n.deleteReview(a.id),await te(),await H()}catch(t){console.error("Error deleting review:",t)}},me=a=>{const t={draft:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",in_progress:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",approved:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return t[a]||t.draft},ge=a=>({draft:"Bozza",in_progress:"In corso",pending:"In attesa",completed:"Completata",approved:"Approvata"})[a]||a,pe=a=>{const t={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return t[a]||t.medium},Ue=a=>a.achievement_rating>0?"evaluated":a.manager_assessment&&a.manager_assessment.length>0?"in_evaluation":"not_evaluated",Le=a=>{const t={up:"bg-green-500",down:"bg-red-500",stable:"bg-yellow-500"};return t[a]||t.stable},Ge=a=>{const t={up:"text-green-600 dark:text-green-400",down:"text-red-600 dark:text-red-400",stable:"text-yellow-600 dark:text-yellow-400"};return t[a]||t.stable},Be=a=>{const t={up:"↗ In crescita",down:"↘ In calo",stable:"→ Stabile"};return t[a]||t.stable},qe=a=>a>=90?"bg-green-500":a>=70?"bg-yellow-500":"bg-red-500",le=a=>a?new Date(a).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"",Qe=a=>{if(!a)return"";const t=new Date(a),M=Math.floor((new Date-t)/(1e3*60*60));return M<1?"Ora":M<24?`${M}h fa`:M<48?"Ieri":le(a)},ve=()=>{P.value=null,p.value=!0},ye=a=>{P.value=a,p.value=!0},Ye=async a=>{if(confirm(`Sei sicuro di voler eliminare l'obiettivo "${a.title}"? Questa azione non può essere annullata.`))try{await n.deleteGoal(a.id),await n.fetchGoals()}catch(t){console.error("Error deleting goal:",t)}},be=()=>{S.value=null,T.value=!0},xe=a=>{S.value=a,T.value=!0},He=async a=>{if(confirm(`Sei sicuro di voler eliminare il template "${a.name}"? Questa azione non può essere annullata.`))try{await n.deleteTemplate(a.id),await n.fetchTemplates()}catch(t){console.error("Error deleting template:",t)}},Ke=()=>{m.value=!1,u.value=!0},We=()=>{u.value=!1,n.clearCurrentReview()},Ze=async()=>{u.value=!1,n.clearCurrentReview(),await te(),await H()},Xe=async()=>{u.value=!1,n.clearCurrentReview(),await te(),await H()},Je=async()=>{p.value=!1,P.value=null,await n.fetchGoals()},et=async()=>{p.value=!1,P.value=null,await n.fetchGoals()},tt=async()=>{T.value=!1,S.value=null,await n.fetchTemplates()},at=async()=>{T.value=!1,S.value=null,await n.fetchTemplates()};return ae([j,f,g],async()=>{await H()}),se(async()=>{_.value&&await x.fetchUsers(),await H()}),(a,t)=>(s(),r(F,null,[v(nt,{title:"Performance Management",subtitle:"Gestione valutazioni e obiettivi del personale",loading:V.value,error:E(n).error,stats:c.value,charts:h.value,"recent-items":B.value,activities:O.value,kpis:Ce.value,"selected-period":g.value,"period-options":w.value,"recent-items-title":"Valutazioni Scadenza","recent-items-empty-message":"Nessuna valutazione in scadenza","recent-items-link":null,"recent-items-link-text":"Vedi tutte le valutazioni",onRecentItemsLinkClick:Ve,"activities-title":"Attività Recenti","activities-empty-message":"Nessuna attività recente","kpis-title":"KPIs Performance",onRefresh:de,onPeriodChange:Re,onClearError:De,onChartAction:Me},{"header-actions":q(()=>[_.value?(s(),r("div",br,[t[6]||(t[6]=e("label",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Dipendente:",-1)),A(e("select",{"onUpdate:modelValue":t[0]||(t[0]=i=>j.value=i),onChange:je,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-brand-primary-500 focus:border-brand-primary-500"},[t[5]||(t[5]=e("option",{value:""},"Tutti i dipendenti",-1)),(s(!0),r(F,null,L(E(x).users,i=>(s(),r("option",{key:i.id,value:i.id},d(i.first_name)+" "+d(i.last_name),9,xr))),128))],544),[[W,j.value]])])):y("",!0),e("div",fr,[t[8]||(t[8]=e("label",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Anno:",-1)),A(e("select",{"onUpdate:modelValue":t[1]||(t[1]=i=>f.value=i),onChange:Ie,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-brand-primary-500 focus:border-brand-primary-500"},t[7]||(t[7]=[e("option",{value:"2024"},"2024",-1),e("option",{value:"2025"},"2025",-1),e("option",{value:"2026"},"2026",-1)]),544),[[W,f.value]])]),G.value?(s(),r("button",{key:1,onClick:Ne,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-brand-primary-600 hover:bg-brand-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary-500"},[v(b,{name:"plus",size:"sm",class:"mr-2"}),t[9]||(t[9]=N(" Nuova Valutazione "))])):y("",!0)]),"chart-performance-trends":q(()=>t[10]||(t[10]=[e("div",{class:"h-64 flex items-center justify-center"},[e("div",{class:"w-full"},[e("svg",{class:"w-full h-48",viewBox:"0 0 400 200"},[e("defs",null,[e("linearGradient",{id:"performanceGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%"},[e("stop",{offset:"0%",style:{"stop-color":"#3b82f6","stop-opacity":"0.3"}}),e("stop",{offset:"100%",style:{"stop-color":"#3b82f6","stop-opacity":"0"}})])]),e("g",{stroke:"#e5e7eb","stroke-width":"1",opacity:"0.5"},[e("line",{x1:"0",y1:"40",x2:"400",y2:"40"}),e("line",{x1:"0",y1:"80",x2:"400",y2:"80"}),e("line",{x1:"0",y1:"120",x2:"400",y2:"120"}),e("line",{x1:"0",y1:"160",x2:"400",y2:"160"})]),e("path",{d:"M 0 160 L 80 140 L 160 130 L 240 115 L 320 110 L 400 105 L 400 200 L 0 200 Z",fill:"url(#performanceGradient)"}),e("path",{d:"M 0 160 L 80 140 L 160 130 L 240 115 L 320 110 L 400 105",fill:"none",stroke:"#3b82f6","stroke-width":"3","stroke-linecap":"round","stroke-linejoin":"round"}),e("g",{fill:"#3b82f6"},[e("circle",{cx:"0",cy:"160",r:"4"}),e("circle",{cx:"80",cy:"140",r:"4"}),e("circle",{cx:"160",cy:"130",r:"4"}),e("circle",{cx:"240",cy:"115",r:"4"}),e("circle",{cx:"320",cy:"110",r:"4"}),e("circle",{cx:"400",cy:"105",r:"4"})])]),e("div",{class:"mt-4 flex justify-center space-x-4 text-sm"},[e("div",{class:"flex items-center"},[e("div",{class:"w-3 h-3 bg-blue-500 rounded-full mr-2"}),e("span",{class:"text-gray-700 dark:text-gray-300"},"Performance Media")])])])],-1)])),"chart-goals-progress":q(()=>{var i,M,X,fe,he;return[e("div",hr,[e("div",kr,[e("div",_r,[(s(),r("svg",wr,[t[11]||(t[11]=e("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"#e5e7eb","stroke-width":"3"},null,-1)),e("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"#10b981","stroke-width":"3","stroke-dasharray":`${ne.value}, 100`,"stroke-linecap":"round"},null,8,$r)])),e("div",Cr,[e("div",zr,[e("div",Tr,d(ne.value)+"%",1),t[12]||(t[12]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"Completati",-1))])])]),e("div",Sr,[e("div",Rr,[t[13]||(t[13]=e("div",{class:"flex items-center"},[e("div",{class:"w-3 h-3 bg-green-500 rounded-full mr-2"}),e("span",null,"Completati")],-1)),e("span",Dr,d(((i=l.value.goals)==null?void 0:i.completed)||0),1)]),e("div",Mr,[t[14]||(t[14]=e("div",{class:"flex items-center"},[e("div",{class:"w-3 h-3 bg-yellow-500 rounded-full mr-2"}),e("span",null,"In Corso")],-1)),e("span",Vr,d(((M=l.value.goals)==null?void 0:M.in_progress)||0),1)]),e("div",Ar,[t[15]||(t[15]=e("div",{class:"flex items-center"},[e("div",{class:"w-3 h-3 bg-gray-300 rounded-full mr-2"}),e("span",null,"Pianificati")],-1)),e("span",Pr,d(((X=l.value.goals)==null?void 0:X.total)-(((fe=l.value.goals)==null?void 0:fe.completed)||0)-(((he=l.value.goals)==null?void 0:he.in_progress)||0)||0),1)])])])])]}),"recent-item":q(({item:i})=>[e("div",{class:"flex justify-between items-start cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 p-2 rounded-md transition-colors",onClick:M=>Pe(i)},[e("div",jr,[e("h3",Ir,d(i.title),1),e("p",Er,d(i.description),1),e("div",Or,[e("span",{class:Y(["inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium",me(i.status)])},d(ge(i.status)),3),i.priority?(s(),r("span",{key:0,class:Y(["inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium",pe(i.priority)])},d(i.priority),3)):y("",!0)])]),e("div",Fr,[i.rating?(s(),r("div",Ur,d(i.rating)+"/5 ",1)):y("",!0),i.due_date?(s(),r("div",Lr," Scadenza: "+d(le(i.due_date)),1)):y("",!0),v(b,{name:"chevron-right",size:"sm",class:"ml-2 text-gray-400"})])],8,Nr)]),"activity-item":q(({activity:i})=>[e("div",Gr,[e("p",Br,d(i.title),1),e("p",qr,d(i.description),1),e("div",Qr,[e("span",Yr,d(Qe(i.timestamp)),1),i.user_name?(s(),r("span",Hr,d(i.user_name),1)):y("",!0)])])]),"kpi-item":q(({kpi:i})=>[e("div",Kr,[e("div",Wr,[e("h3",Zr,[e("div",{class:Y(["w-2 h-2 rounded-full mr-2",Le(i.trend)])},null,2),N(" "+d(i.name),1)]),e("p",Xr,d(i.description),1)]),e("div",Jr,[e("p",eo,d(i.current_value)+d(i.unit),1),e("p",to," Target: "+d(i.target_value)+d(i.unit),1),e("p",{class:Y(["text-xs",Ge(i.trend)])},d(Be(i.trend)),3)])]),e("div",ao,[e("div",so,[e("div",{class:Y(["h-2 rounded-full transition-all duration-500",qe(i.performance_percentage)]),style:ee({width:Math.min(i.performance_percentage,100)+"%"})},null,6)]),e("p",ro,d(Math.round(i.performance_percentage))+"% del target ",1)])]),footer:q(()=>[e("div",oo,[v(dt,{tabs:ze.value,activeTab:C.value,onTabChange:Ae,class:"border-b border-gray-200 dark:border-gray-700"},null,8,["tabs","activeTab"]),e("div",lo,[C.value==="reviews"?(s(),r("div",io,[V.value&&E(n).reviews.length===0?(s(),r("div",no,t[16]||(t[16]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento valutazioni...",-1)]))):E(n).error?(s(),r("div",uo,[t[17]||(t[17]=e("div",{class:"text-red-600 mb-2"},"Errore nel caricamento:",-1)),e("div",co,d(E(n).error),1),e("button",{onClick:te,class:"mt-4 btn btn-sm btn-primary"}," Riprova ")])):(s(),r("div",mo,[v(ut,{title:"Valutazioni Performance",columns:Te.value,data:Se.value,"row-key":i=>i.id,"empty-message":"Nessuna valutazione trovata",onRowClick:ce},{"cell-status":q(({value:i})=>[e("span",{class:Y(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",me(i)])},d(ge(i)),3)]),"cell-overall_rating":q(({value:i})=>[i?(s(),r("div",go,[e("span",po,d(i.toFixed(1)),1),e("div",vo,[(s(),r(F,null,L(5,M=>e("svg",{key:M,class:Y(["w-4 h-4",M<=Math.round(i)?"text-yellow-400 fill-current":"text-gray-300"]),viewBox:"0 0 20 20",fill:"currentColor"},t[18]||(t[18]=[e("path",{"fill-rule":"evenodd",d:"M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401z","clip-rule":"evenodd"},null,-1)]),2)),64))])])):(s(),r("span",yo,"N/A"))]),"cell-actions":q(({row:i})=>[e("div",bo,[e("button",{onClick:Q(M=>Ee(i),["stop"]),class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 dark:text-blue-400 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 transition-colors"},[v(b,{name:"pencil",class:"w-3 h-3 mr-1"}),t[19]||(t[19]=N(" Modifica "))],8,xo),Oe(i)?(s(),r("button",{key:0,onClick:Q(M=>Fe(i),["stop"]),class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 dark:text-red-400 dark:bg-red-900/20 dark:hover:bg-red-900/30 transition-colors"},[v(b,{name:"trash",class:"w-3 h-3 mr-1"}),t[20]||(t[20]=N(" Elimina "))],8,fo)):y("",!0)])]),_:1},8,["columns","data","row-key"])]))])):y("",!0),C.value==="goals"?(s(),r("div",ho,[e("div",ko,[t[22]||(t[22]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Obiettivi Performance"),e("p",{class:"text-sm text-gray-600 dark:text-gray-400"},"Gestisci gli obiettivi individuali e di squadra")],-1)),e("button",{onClick:ve,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"},[v(b,{name:"plus",size:"sm",class:"mr-2"}),t[21]||(t[21]=N(" Nuovo Obiettivo "))])]),V.value&&E(n).goals.length===0?(s(),r("div",_o,t[23]||(t[23]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento obiettivi...",-1)]))):(s(),r("div",wo,[E(n).goals.length===0?(s(),r("div",$o,[v(b,{name:"presentation-chart-line",class:"w-16 h-16 mx-auto mb-2"}),t[25]||(t[25]=e("p",null,"Nessun obiettivo trovato",-1)),e("button",{onClick:ve,class:"mt-4 inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"},[v(b,{name:"plus",size:"sm",class:"mr-2"}),t[24]||(t[24]=N(" Crea il primo obiettivo "))])])):(s(),r("div",Co,[(s(!0),r(F,null,L(E(n).goals,i=>(s(),r("div",{key:i.id,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors group relative"},[e("div",zo,[e("div",To,[e("button",{onClick:Q(M=>ye(i),["stop"]),class:"p-1 rounded-full bg-blue-600 text-white hover:bg-blue-700 transition-colors",title:"Modifica obiettivo"},[v(b,{name:"pencil",size:"sm"})],8,So),e("button",{onClick:Q(M=>Ye(i),["stop"]),class:"p-1 rounded-full bg-red-600 text-white hover:bg-red-700 transition-colors",title:"Elimina obiettivo"},[v(b,{name:"trash",size:"sm"})],8,Ro)])]),e("div",Do,[e("h3",{class:"text-sm font-medium text-gray-900 dark:text-white cursor-pointer",onClick:M=>ye(i)},d(i.title),9,Mo),e("div",Vo,[v(we,{status:Ue(i),type:"performance","show-dot":!0},null,8,["status"]),e("span",{class:Y(["inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium",pe(i.priority)])},d(i.priority),3)])]),e("p",Ao,d(i.description),1),e("div",Po,[e("div",No,[t[26]||(t[26]=e("span",{class:"text-gray-600 dark:text-gray-400"},"Progresso",-1)),e("span",jo,d(i.progress||0)+"%",1)]),e("div",Io,[e("div",{class:"bg-green-500 h-2 rounded-full transition-all duration-500",style:ee({width:(i.progress||0)+"%"})},null,4)])]),e("div",Eo,[t[27]||(t[27]=e("span",{class:"text-gray-500 dark:text-gray-400"},"Scadenza:",-1)),e("span",Oo,d(le(i.target_date)),1)]),i.achievement_rating>0?(s(),r("div",Fo,[e("div",Uo,[t[28]||(t[28]=e("span",{class:"text-gray-500 dark:text-gray-400"},"Achievement Rating:",-1)),e("div",Lo,[v(lt,{"model-value":i.achievement_rating,readonly:!0,size:"sm","show-value":!0},null,8,["model-value"])])])])):y("",!0)]))),128))]))]))])):y("",!0),C.value==="analytics"?(s(),r("div",Go,[v(yr,{"employee-id":j.value||null,year:f.value},null,8,["employee-id","year"])])):y("",!0),C.value==="templates"?(s(),r("div",Bo,[e("div",qo,[t[30]||(t[30]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Template Performance"),e("p",{class:"text-sm text-gray-600 dark:text-gray-400"},"Gestisci i template per le valutazioni")],-1)),e("button",{onClick:be,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},[v(b,{name:"plus",size:"sm",class:"mr-2"}),t[29]||(t[29]=N(" Nuovo Template "))])]),V.value&&E(n).templates.length===0?(s(),r("div",Qo,t[31]||(t[31]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento template...",-1)]))):(s(),r("div",Yo,[E(n).templates.length===0?(s(),r("div",Ho,[v(b,{name:"document-text",class:"w-16 h-16 mx-auto mb-2"}),t[33]||(t[33]=e("p",null,"Nessun template trovato",-1)),e("button",{onClick:be,class:"mt-4 inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"},[v(b,{name:"plus",size:"sm",class:"mr-2"}),t[32]||(t[32]=N(" Crea il primo template "))])])):(s(),r("div",Ko,[(s(!0),r(F,null,L(E(n).templates,i=>(s(),r("div",{key:i.id,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors group relative"},[e("div",Wo,[e("div",Zo,[e("button",{onClick:Q(M=>xe(i),["stop"]),class:"p-1 rounded-full bg-blue-600 text-white hover:bg-blue-700 transition-colors",title:"Modifica template"},[v(b,{name:"pencil",size:"sm"})],8,Xo),e("button",{onClick:Q(M=>He(i),["stop"]),class:"p-1 rounded-full bg-red-600 text-white hover:bg-red-700 transition-colors",title:"Elimina template"},[v(b,{name:"trash",size:"sm"})],8,Jo)])]),e("div",el,[e("h3",{class:"text-sm font-medium text-gray-900 dark:text-white cursor-pointer",onClick:M=>xe(i)},d(i.name),9,tl),i.is_default?(s(),r("span",al," Default ")):y("",!0)]),e("p",sl,d(i.description),1),e("div",rl,[t[34]||(t[34]=e("span",{class:"text-gray-500 dark:text-gray-400"},"Tipo:",-1)),e("span",ol,d(i.template_type),1)])]))),128))]))]))])):y("",!0)])])]),_:1},8,["loading","error","stats","charts","recent-items","activities","kpis","selected-period","period-options"]),m.value?(s(),K(ct,{key:0,review:E(n).currentReview,loading:E(n).loading,onClose:t[2]||(t[2]=i=>m.value=!1),onEdit:Ke},null,8,["review","loading"])):y("",!0),u.value?(s(),K(ot,{key:1,review:E(n).currentReview,loading:E(n).loading,onClose:We,onSave:Ze,onDelete:Xe},null,8,["review","loading"])):y("",!0),p.value?(s(),K(Dt,{key:2,goal:P.value,loading:E(n).loading,onClose:t[3]||(t[3]=i=>{p.value=!1,P.value=null}),onSave:Je,onDelete:et},null,8,["goal","loading"])):y("",!0),T.value?(s(),K(za,{key:3,template:S.value,loading:E(n).loading,onClose:t[4]||(t[4]=i=>{T.value=!1,S.value=null}),onSave:tt,onDelete:at},null,8,["template","loading"])):y("",!0)],64))}},wl=Z(ll,[["__scopeId","data-v-cda2a58e"]]);export{wl as default};
