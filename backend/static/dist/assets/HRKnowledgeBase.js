import{c as I,b as s,o as r,e as o,s as S,t as p,n as Q,r as A,w as P,x as G,j as e,A as W,l as b,B as z,C as j,H,F as B,p as E,v as X,Q as Y,h as N}from"./vendor.js";import{u as U,_ as R}from"./ConfidenceBadge.js";import{H as u,h as Z}from"./app.js";import{P as ee}from"./Pagination.js";const O={__name:"CategoryBadge",props:{category:{type:String,required:!0}},setup(i){const w=i,f={contracts:{label:"Contratti",icon:"document-text",classes:"bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400"},onboarding:{label:"Onboarding",icon:"user-plus",classes:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"},offboarding:{label:"Offboarding",icon:"user-minus",classes:"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"},leave:{label:"Ferie",icon:"calendar-days",classes:"bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400"},permits:{label:"Permessi",icon:"clock",classes:"bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400"},travel:{label:"Trasferte",icon:"map-pin",classes:"bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400"},benefits:{label:"Benefit",icon:"gift",classes:"bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-400"},tools:{label:"Strumenti",icon:"computer-desktop",classes:"bg-cyan-100 text-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-400"},purchases:{label:"Acquisti",icon:"shopping-cart",classes:"bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400"},training:{label:"Formazione",icon:"academic-cap",classes:"bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400"}},$=I(()=>{var c;return((c=f[w.category])==null?void 0:c.label)||w.category}),h=I(()=>{var c;return((c=f[w.category])==null?void 0:c.icon)||"tag"}),k=I(()=>{var c;return((c=f[w.category])==null?void 0:c.classes)||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"});return(c,y)=>(r(),s("span",{class:Q(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",k.value])},[o(u,{name:h.value,size:"xs",class:"mr-1"},null,8,["name"]),S(" "+p($.value),1)],2))}},te={class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},ae={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},re={class:"flex items-center justify-between mb-6"},se={class:"text-lg font-medium text-gray-900 dark:text-white"},le=["value"],ne={key:0,class:"flex items-center space-x-3"},oe={key:1},ie={key:2},de=["value"],ue={key:3,class:"flex justify-center"},ce=["disabled"],ge={key:0,class:"flex items-center"},ye={key:1},me={class:"flex items-center justify-between mb-2"},be=["disabled"],xe=["required","placeholder"],pe={key:0,class:"mt-2 text-xs text-gray-500 dark:text-gray-400"},ve={key:4,class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4"},fe={class:"flex items-center space-x-2 mb-2"},ke={key:0,class:"text-xs text-blue-700 dark:text-blue-300"},he={class:"list-disc list-inside space-y-1"},we={class:"flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600"},_e=["disabled"],$e={key:0,class:"flex items-center"},Ce={key:1},Ae={__name:"KnowledgeBaseModal",props:{entry:{type:Object,default:null}},emits:["close","save"],setup(i,{emit:w}){const f=i,$=w,h=U(),k=A(!1),c=A(!1),y=A(null),T=A([]),t=A({title:"",category:"",content:"",useAIAssistance:!1,requirements:"",templateId:null,tags:[]}),_=A(""),g=I(()=>!!f.entry),{categoriesList:m}=h,V=I(()=>t.value.category?T.value.filter(x=>x.category===t.value.category):[]);P(()=>t.value.category,async x=>{x&&t.value.useAIAssistance&&await C(x)}),P(()=>t.value.useAIAssistance,x=>{x||(y.value=null,t.value.requirements="",t.value.templateId=null)});async function C(x=null){const a=await h.loadTemplates(x);a.success&&(T.value=a.data)}async function D(){if(!(!t.value.title||!t.value.category)){c.value=!0;try{const x=await h.generateAIContent({title:t.value.title,category:t.value.category,content:t.value.content||"",requirements:t.value.requirements,templateId:t.value.templateId});x.success&&(y.value=x.data,t.value.content=x.data.content)}catch(x){console.error("Error generating AI content:",x)}finally{c.value=!1}}}async function L(){y.value=null,await D()}async function F(){var x,a;k.value=!0;try{t.value.tags=_.value.split(",").map(q=>q.trim()).filter(q=>q.length>0);let l;g.value?l=await h.updateKnowledgeEntry(f.entry.id,{title:t.value.title,category:t.value.category,content:t.value.content,tags:t.value.tags}):l=await h.createKnowledgeEntry({title:t.value.title,category:t.value.category,content:t.value.content,tags:t.value.tags,ai_generated:!!y.value,ai_confidence:(x=y.value)==null?void 0:x.confidence,ai_sources:(a=y.value)==null?void 0:a.sources}),l.success&&$("save")}catch(l){console.error("Error saving knowledge entry:",l)}finally{k.value=!1}}return G(async()=>{g.value&&(t.value={title:f.entry.title,category:f.entry.category,content:f.entry.content,useAIAssistance:!1,requirements:"",templateId:null,tags:f.entry.tags||[]},_.value=t.value.tags.join(", ")),await C()}),(x,a)=>(r(),s("div",te,[e("div",ae,[e("div",re,[e("h3",se,p(g.value?"Modifica Contenuto":"Nuovo Contenuto HR"),1),e("button",{onClick:a[0]||(a[0]=l=>x.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[o(u,{name:"x-mark",class:"h-6 w-6"})])]),e("form",{onSubmit:W(F,["prevent"]),class:"space-y-6"},[e("div",null,[a[9]||(a[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Titolo * ",-1)),z(e("input",{"onUpdate:modelValue":a[1]||(a[1]=l=>t.value.title=l),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Inserisci il titolo del contenuto"},null,512),[[j,t.value.title]])]),e("div",null,[a[11]||(a[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Categoria * ",-1)),z(e("select",{"onUpdate:modelValue":a[2]||(a[2]=l=>t.value.category=l),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[a[10]||(a[10]=e("option",{value:""},"Seleziona categoria",-1)),(r(!0),s(B,null,E(X(m),l=>(r(),s("option",{key:l.key,value:l.key},p(l.label),9,le))),128))],512),[[H,t.value.category]])]),g.value?b("",!0):(r(),s("div",ne,[z(e("input",{id:"useAI","onUpdate:modelValue":a[3]||(a[3]=l=>t.value.useAIAssistance=l),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[Y,t.value.useAIAssistance]]),a[12]||(a[12]=e("label",{for:"useAI",class:"text-sm font-medium text-gray-700 dark:text-gray-300"}," Usa assistenza AI per generare contenuto ",-1))])),t.value.useAIAssistance&&!g.value?(r(),s("div",oe,[a[13]||(a[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Requisiti specifici per l'AI ",-1)),z(e("textarea",{"onUpdate:modelValue":a[4]||(a[4]=l=>t.value.requirements=l),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Descrivi cosa deve includere il contenuto (es: policy aziendale, CCNL, procedure specifiche...)"},null,512),[[j,t.value.requirements]])])):b("",!0),t.value.useAIAssistance&&!g.value&&T.value.length>0?(r(),s("div",ie,[a[15]||(a[15]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Template (opzionale) ",-1)),z(e("select",{"onUpdate:modelValue":a[5]||(a[5]=l=>t.value.templateId=l),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[a[14]||(a[14]=e("option",{value:""},"Nessun template",-1)),(r(!0),s(B,null,E(V.value,l=>(r(),s("option",{key:l.id,value:l.id},p(l.name),9,de))),128))],512),[[H,t.value.templateId]])])):b("",!0),t.value.useAIAssistance&&!g.value&&!y.value&&t.value.title&&t.value.category?(r(),s("div",ue,[e("button",{type:"button",onClick:D,disabled:c.value,class:"inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"},[o(u,{name:"cpu-chip",class:"h-5 w-5 mr-2"}),c.value?(r(),s("div",ge,a[16]||(a[16]=[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),S(" Generando contenuto... ")]))):(r(),s("span",ye,"Genera Contenuto con AI"))],8,ce)])):b("",!0),e("div",null,[e("div",me,[a[18]||(a[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Contenuto * ",-1)),t.value.useAIAssistance&&!g.value&&y.value?(r(),s("button",{key:0,type:"button",onClick:L,disabled:c.value,class:"inline-flex items-center px-3 py-1 text-sm bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 disabled:opacity-50"},[o(u,{name:"arrow-path",class:"h-4 w-4 mr-1"}),a[17]||(a[17]=S(" Rigenera "))],8,be)):b("",!0)]),z(e("textarea",{"onUpdate:modelValue":a[6]||(a[6]=l=>t.value.content=l),required:!t.value.useAIAssistance||!!y.value,rows:"10",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:t.value.useAIAssistance&&!y.value?"Clicca su 'Genera Contenuto con AI' per creare una bozza automatica, poi modifica secondo le tue necessità":"Inserisci il contenuto..."},null,8,xe),[[j,t.value.content]]),t.value.useAIAssistance&&y.value?(r(),s("div",pe," 💡 Contenuto generato con AI - puoi modificarlo liberamente prima di salvare ")):b("",!0)]),e("div",null,[a[19]||(a[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tag (separati da virgola) ",-1)),z(e("input",{"onUpdate:modelValue":a[7]||(a[7]=l=>_.value=l),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"tag1, tag2, tag3"},null,512),[[j,_.value]])]),y.value?(r(),s("div",ve,[e("div",fe,[o(u,{name:"cpu-chip",class:"h-5 w-5 text-blue-600 dark:text-blue-400"}),a[20]||(a[20]=e("span",{class:"text-sm font-medium text-blue-900 dark:text-blue-300"}," Contenuto generato con AI ",-1)),o(R,{confidence:y.value.confidence},null,8,["confidence"])]),y.value.sources&&y.value.sources.length>0?(r(),s("div",ke,[a[21]||(a[21]=e("p",{class:"font-medium mb-1"},"Fonti utilizzate:",-1)),e("ul",he,[(r(!0),s(B,null,E(y.value.sources,l=>(r(),s("li",{key:l},p(l),1))),128))])])):b("",!0)])):b("",!0),e("div",we,[e("button",{type:"button",onClick:a[8]||(a[8]=l=>x.$emit("close")),class:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"}," Annulla "),e("button",{type:"submit",disabled:k.value||!t.value.content,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"},[k.value?(r(),s("div",$e,a[22]||(a[22]=[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),S(" Salvando... ")]))):(r(),s("span",Ce,p(g.value?"Aggiorna":"Salva"),1))],8,_e)])],32)])]))}},Ie={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},ze={class:"card w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto"},Se={class:"flex items-center justify-between p-6 border-b border dark:border-gray-600"},Ve={class:"flex items-center space-x-3"},Be={class:"flex items-center space-x-2"},Ee={key:0,class:"p-6 space-y-6"},Te={class:"space-y-4"},qe={class:"text-2xl font-bold text-gray-900 dark:text-white"},Me={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400"},je={class:"flex items-center space-x-1"},De={class:"flex items-center space-x-1"},Le={key:0,class:"flex items-center space-x-1"},Ne={class:"flex flex-wrap gap-2"},Re={key:0,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"},Fe={class:"prose prose-gray dark:prose-invert max-w-none"},He={class:"whitespace-pre-wrap text-gray-900 dark:text-white leading-relaxed bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},Ue={key:0,class:"space-y-2"},Ke={class:"flex flex-wrap gap-2"},Pe={key:1,class:"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-4"},Ge={class:"flex items-center space-x-2 mb-3"},Oe={class:"space-y-2 text-sm"},Je={class:"flex items-center justify-between"},Qe={key:0},We={class:"list-disc list-inside space-y-1 text-purple-600 dark:text-purple-400"},Xe={class:"pt-4 border-t border dark:border-gray-600"},Ye={class:"grid grid-cols-2 gap-4 text-sm text-gray-500 dark:text-gray-400"},Ze={class:"flex items-center space-x-2"},et={class:"flex items-center space-x-2"},tt={key:1,class:"p-6 text-center"},at={__name:"KnowledgeBaseViewModal",props:{isOpen:{type:Boolean,default:!1},entry:{type:Object,default:null}},emits:["close","edit"],setup(i,{emit:w}){const f=i,$=w,h=U(),{hasPermission:k}=Z(),c=I(()=>k.value("manage_communications")),y=I(()=>{var g;if(!((g=f.entry)!=null&&g.ai_sources))return[];try{return JSON.parse(f.entry.ai_sources)}catch{return[]}}),T=()=>{$("close")},t=g=>g?new Date(g).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}):"N/A",_=g=>{const m=h.categoriesList.find(V=>V.key===g);return m?m.label:g};return(g,m)=>{var V;return i.isOpen?(r(),s("div",Ie,[e("div",ze,[e("div",Se,[e("div",Ve,[o(u,{name:"academic-cap",size:"lg",class:"text-blue-600"}),m[1]||(m[1]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," Dettagli Contenuto HR ",-1))]),e("div",Be,[c.value?(r(),s("button",{key:0,onClick:m[0]||(m[0]=C=>g.$emit("edit",i.entry)),class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"},[o(u,{name:"pencil",size:"md"})])):b("",!0),e("button",{onClick:T,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"},[o(u,{name:"x-mark",size:"md"})])])]),i.entry?(r(),s("div",Ee,[e("div",Te,[e("h1",qe,p(i.entry.title),1),e("div",Me,[e("div",je,[o(u,{name:"user",size:"sm"}),e("span",null,p((V=i.entry.creator)==null?void 0:V.full_name),1)]),e("div",De,[o(u,{name:"calendar",size:"sm"}),e("span",null,p(t(i.entry.created_at)),1)]),i.entry.updated_at&&i.entry.updated_at!==i.entry.created_at?(r(),s("div",Le,[o(u,{name:"clock",size:"sm"}),e("span",null,"Modificato: "+p(t(i.entry.updated_at)),1)])):b("",!0)]),e("div",Ne,[o(O,{category:i.entry.category},null,8,["category"]),i.entry.created_with_ai?(r(),s("div",Re,[o(u,{name:"cpu-chip",size:"sm",class:"mr-1"}),m[2]||(m[2]=S(" Generato con AI "))])):b("",!0),i.entry.ai_confidence?(r(),N(R,{key:1,confidence:i.entry.ai_confidence},null,8,["confidence"])):b("",!0)])]),e("div",Fe,[e("div",He,p(i.entry.content),1)]),i.entry.tags&&i.entry.tags.length>0?(r(),s("div",Ue,[m[3]||(m[3]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Tags",-1)),e("div",Ke,[(r(!0),s(B,null,E(i.entry.tags,C=>(r(),s("span",{key:C,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},p(C),1))),128))])])):b("",!0),i.entry.created_with_ai&&i.entry.ai_sources?(r(),s("div",Pe,[e("div",Ge,[o(u,{name:"cpu-chip",class:"h-5 w-5 text-purple-600 dark:text-purple-400"}),m[4]||(m[4]=e("h4",{class:"text-sm font-medium text-purple-900 dark:text-purple-300"}," Dettagli Generazione AI ",-1))]),e("div",Oe,[e("div",Je,[m[5]||(m[5]=e("span",{class:"text-purple-700 dark:text-purple-300"},"Livello di confidenza:",-1)),o(R,{confidence:i.entry.ai_confidence},null,8,["confidence"])]),y.value.length>0?(r(),s("div",Qe,[m[6]||(m[6]=e("span",{class:"text-purple-700 dark:text-purple-300 block mb-2"},"Fonti utilizzate:",-1)),e("ul",We,[(r(!0),s(B,null,E(y.value,C=>(r(),s("li",{key:C},p(C),1))),128))])])):b("",!0)])])):b("",!0),e("div",Xe,[e("div",Ye,[e("div",Ze,[o(u,{name:"folder",size:"sm"}),e("span",null,"Categoria: "+p(_(i.entry.category)),1)]),e("div",et,[o(u,{name:"document-text",size:"sm"}),e("span",null,p(i.entry.content.length)+" caratteri",1)])])])])):(r(),s("div",tt,[o(u,{name:"cog-6-tooth",size:"lg",class:"animate-spin text-gray-400 mx-auto mb-2"}),m[7]||(m[7]=e("p",{class:"text-gray-500 dark:text-gray-400"},"Caricamento...",-1))]))])])):b("",!0)}}},rt={class:"space-y-6"},st={class:"flex justify-between items-center"},lt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},nt={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ot=["value"],it={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},dt={class:"divide-y divide-gray-200 dark:divide-gray-700"},ut={class:"flex items-start justify-between"},ct={class:"flex-1"},gt={class:"flex items-center space-x-3 mb-2"},yt={class:"text-lg font-medium text-gray-900 dark:text-white"},mt={key:0,class:"flex items-center space-x-1"},bt={class:"text-gray-600 dark:text-gray-400 mb-3 line-clamp-2"},xt={class:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},pt={key:0,class:"mt-2"},vt={class:"flex flex-wrap gap-1"},ft={class:"flex items-center space-x-2 ml-4"},kt=["onClick"],ht=["onClick"],wt=["onClick"],_t={key:0,class:"text-center py-12"},$t={key:1,class:"text-center py-12"},St={__name:"HRKnowledgeBase",setup(i){const w=U(),f=A(!1),$=A(null),h=A(null),k=A({category:"",search:"",page:1}),c=A(null),y=I(()=>w.knowledgeBase),T=I(()=>w.categoriesList),t=I(()=>w.loading);async function _(){const v=await w.loadKnowledgeBase(k.value);v.success&&(c.value=v.data.pagination)}function g(){clearTimeout(g.timer),g.timer=setTimeout(()=>{k.value.page=1,_()},500)}function m(){k.value={category:"",search:"",page:1},_()}function V(v){h.value=v}function C(v){$.value=v}async function D(v){if(confirm(`Sei sicuro di voler eliminare "${v.title}"?`))try{(await w.deleteKnowledgeEntry(v.id)).success}catch(n){console.error("Errore eliminazione:",n)}}function L(){f.value=!1,$.value=null}function F(){h.value=null}function x(v){h.value=null,$.value=v}async function a(){L()}function l(v){k.value.page=v,_()}function q(v,n=150){return v.length<=n?v:v.substring(0,n)+"..."}function J(v){return new Date(v).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"})}return G(()=>{_()}),(v,n)=>(r(),s("div",rt,[e("div",st,[n[5]||(n[5]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"}," Knowledge Base HR "),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci contenuti per l'assistente HR ")],-1)),e("button",{onClick:n[0]||(n[0]=d=>f.value=!0),class:"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"},[o(u,{name:"plus",class:"h-4 w-4 mr-2"}),n[4]||(n[4]=S(" Nuovo Contenuto "))])]),e("div",lt,[e("div",nt,[e("div",null,[n[7]||(n[7]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Categoria ",-1)),z(e("select",{"onUpdate:modelValue":n[1]||(n[1]=d=>k.value.category=d),onChange:_,class:"w-full rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[n[6]||(n[6]=e("option",{value:""},"Tutte le categorie",-1)),(r(!0),s(B,null,E(T.value,d=>(r(),s("option",{key:d.key,value:d.key},p(d.label),9,ot))),128))],544),[[H,k.value.category]])]),e("div",null,[n[8]||(n[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Ricerca ",-1)),z(e("input",{"onUpdate:modelValue":n[2]||(n[2]=d=>k.value.search=d),onInput:g,type:"text",placeholder:"Cerca nei contenuti...",class:"w-full rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,544),[[j,k.value.search]])]),e("div",{class:"flex items-end"},[e("button",{onClick:m,class:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"}," Pulisci Filtri ")])])]),e("div",it,[e("div",dt,[(r(!0),s(B,null,E(y.value,d=>{var K;return r(),s("div",{key:d.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",ut,[e("div",ct,[e("div",gt,[e("h3",yt,p(d.title),1),o(O,{category:d.category},null,8,["category"]),d.created_with_ai?(r(),s("div",mt,[o(u,{name:"cpu-chip",class:"h-4 w-4 text-purple-500"}),n[9]||(n[9]=e("span",{class:"text-xs text-purple-600 dark:text-purple-400"}," AI-Generated ",-1))])):b("",!0)]),e("p",bt,p(q(d.content)),1),e("div",xt,[e("span",null,[o(u,{name:"user",class:"h-4 w-4 inline mr-1"}),S(" "+p((K=d.creator)==null?void 0:K.full_name),1)]),e("span",null,[o(u,{name:"calendar",class:"h-4 w-4 inline mr-1"}),S(" "+p(J(d.created_at)),1)]),d.ai_confidence?(r(),N(R,{key:0,confidence:d.ai_confidence},null,8,["confidence"])):b("",!0)]),d.tags&&d.tags.length>0?(r(),s("div",pt,[e("div",vt,[(r(!0),s(B,null,E(d.tags,M=>(r(),s("span",{key:M,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300"},p(M),1))),128))])])):b("",!0)]),e("div",ft,[e("button",{onClick:M=>V(d),class:"p-2 text-blue-400 hover:text-blue-600 dark:hover:text-blue-300 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20",title:"Visualizza dettagli"},[o(u,{name:"eye",class:"h-4 w-4"})],8,kt),e("button",{onClick:M=>C(d),class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600",title:"Modifica"},[o(u,{name:"pencil",class:"h-4 w-4"})],8,ht),e("button",{onClick:M=>D(d),class:"p-2 text-red-400 hover:text-red-600 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20",title:"Elimina"},[o(u,{name:"trash",class:"h-4 w-4"})],8,wt)])])])}),128))]),y.value.length===0&&!t.value?(r(),s("div",_t,[o(u,{name:"document-text",class:"h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto mb-4"}),n[11]||(n[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"}," Nessun contenuto trovato ",-1)),n[12]||(n[12]=e("p",{class:"text-gray-500 dark:text-gray-400 mb-6"}," Inizia creando il primo contenuto per la knowledge base HR. ",-1)),e("button",{onClick:n[3]||(n[3]=d=>f.value=!0),class:"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700"},[o(u,{name:"plus",class:"h-4 w-4 mr-2"}),n[10]||(n[10]=S(" Crea Primo Contenuto "))])])):b("",!0),t.value?(r(),s("div",$t,n[13]||(n[13]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-500 dark:text-gray-400"},"Caricamento...",-1)]))):b("",!0)]),c.value&&c.value.pages>1?(r(),N(ee,{key:0,"current-page":c.value.page,"total-pages":c.value.pages,"total-items":c.value.total,onPageChange:l},null,8,["current-page","total-pages","total-items"])):b("",!0),f.value||$.value?(r(),N(Ae,{key:1,entry:$.value,onClose:L,onSave:a},null,8,["entry"])):b("",!0),o(at,{"is-open":!!h.value,entry:h.value,onClose:F,onEdit:x},null,8,["is-open","entry"])]))}};export{St as default};
