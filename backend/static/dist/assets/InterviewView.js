import{r as v,c as H,x as O,b as d,l as c,j as t,e as i,t as r,s as k,n as f,h as S,A as w,B as h,C as T,v as N,H as G,u as J,q as K,o as n}from"./vendor.js";import{u as Q}from"./recruiting.js";import{S as o}from"./StandardButton.js";import{_ as W,H as b}from"./app.js";const X={class:"interview-view"},Y={key:0,class:"flex justify-center py-12"},Z={key:1,class:"space-y-6"},ee={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},te={class:"flex items-start justify-between mb-4"},ae={class:"flex items-center space-x-4"},se={class:"text-2xl font-bold text-gray-900 dark:text-white"},re={class:"text-gray-600 dark:text-gray-400"},ie={class:"flex items-center mt-2 space-x-4"},oe={class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},le={class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},ne={class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},de={class:"flex items-center space-x-3"},ue={class:"flex items-center space-x-3"},ce={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},me={class:"lg:col-span-2 space-y-6"},ge={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},xe={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ve={class:"mt-1 text-sm text-gray-900 dark:text-white"},pe={class:"mt-1 text-sm text-gray-900 dark:text-white"},ye={class:"mt-1 text-sm text-gray-900 dark:text-white"},ke={class:"md:col-span-2"},be={class:"mt-1 text-sm text-gray-900 dark:text-white"},fe={key:0,class:"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700"},we={class:"text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap"},he={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},_e={class:"flex items-center justify-between mb-4"},Ce={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ee={class:"mt-1 text-sm text-gray-900 dark:text-white"},Ie={class:"mt-1 text-sm text-gray-900 dark:text-white"},$e={class:"mt-1 text-sm text-gray-900 dark:text-white"},ze={class:"mt-1 text-sm text-gray-900 dark:text-white"},qe={key:0,class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},je={class:"flex items-center justify-between mb-4"},De={key:0,class:"space-y-4"},Se={key:0,class:"flex items-center space-x-3"},Te={class:"flex items-center space-x-1"},Ne={class:"text-2xl font-bold text-primary-600"},Ve={key:1},Ae={class:"text-sm text-gray-900 dark:text-white whitespace-pre-wrap"},Fe={key:1,class:"text-center py-4"},Me={class:"space-y-6"},Be={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},Le={class:"space-y-3"},Pe={class:"flex justify-between"},Re={class:"text-sm text-gray-900 dark:text-white"},Ue={class:"flex justify-between"},He={class:"text-sm text-gray-900 dark:text-white"},Oe={class:"flex justify-between"},Ge={class:"text-sm text-gray-900 dark:text-white"},Je={class:"flex justify-between"},Ke={class:"text-sm text-gray-900 dark:text-white"},Qe={key:0,class:"flex justify-between"},We={class:"text-sm text-gray-900 dark:text-white"},Xe={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},Ye={class:"space-y-3"},Ze={class:"mt-3"},et={class:"space-y-4"},tt={class:"flex justify-end space-x-3 mt-6"},at={class:"mt-3"},st={class:"space-y-4"},rt={class:"text-sm text-gray-600 dark:text-gray-400"},it={class:"flex justify-end space-x-3 mt-6"},ot={__name:"InterviewView",setup(lt){const V=J(),_=K(),u=Q(),a=v(null),g=v(!1),x=v(!1),p=v("confirmation"),m=v({score:"",feedback:""}),A=H(()=>u.loading),y=async()=>{try{const s=parseInt(V.params.id),e=await u.fetchInterview(s);a.value=e,(e.score||e.feedback)&&(m.value={score:e.score||"",feedback:e.feedback||""})}catch(s){console.error("Error loading interview:",s)}},F=async()=>{try{await u.updateInterview(a.value.id,{status:"completed"}),await y()}catch(s){console.error("Error marking interview as completed:",s),alert("Errore nell'aggiornamento dello stato")}},M=async()=>{if(confirm("Sei sicuro di voler annullare questo colloquio?"))try{await u.updateInterview(a.value.id,{status:"cancelled"}),await y()}catch(s){console.error("Error cancelling interview:",s),alert("Errore nell'annullamento del colloquio")}},B=async()=>{if(confirm(`Eliminare il colloquio con "${a.value.candidate.full_name}"?`))try{await u.deleteInterview(a.value.id),_.push("/app/recruiting/interviews")}catch(s){console.error("Error deleting interview:",s),alert(s.message||"Errore nell'eliminazione del colloquio")}},L=async()=>{try{const s={};m.value.score&&(s.score=parseFloat(m.value.score)),m.value.feedback&&(s.feedback=m.value.feedback),await u.updateInterview(a.value.id,s),g.value=!1,await y()}catch(s){console.error("Error saving feedback:",s),alert("Errore nel salvataggio del feedback")}},C=async()=>{try{await u.sendInterviewEmail(a.value.id,p.value),x.value=!1,alert(`Email ${p.value==="confirmation"?"di conferma":"reminder"} inviata con successo!`)}catch(s){console.error("Error sending email:",s),alert("Errore nell'invio dell'email: "+s.message)}},P=async()=>{try{await u.downloadInterviewCalendar(a.value.id)}catch(s){console.error("Error downloading calendar:",s),alert("Errore nel download del calendario: "+s.message)}},R=()=>{_.push("/app/recruiting/interviews")},E=s=>({scheduled:"Programmato",completed:"Completato",cancelled:"Annullato"})[s]||s,I=s=>({scheduled:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",completed:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",cancelled:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"})[s]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",$=s=>({phone:"Telefonico",video:"Video Call",in_person:"Di Persona",technical:"Tecnico",behavioral:"Comportamentale",final:"Finale"})[s]||s,U=s=>({phone:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",video:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",in_person:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",technical:"bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",behavioral:"bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200",final:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"})[s]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",z=s=>s?new Date(s).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",q=s=>s?new Date(s).toLocaleString("it-IT",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"";return O(()=>{y()}),(s,e)=>{var j,D;return n(),d("div",X,[A.value?(n(),d("div",Y,e[17]||(e[17]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary-600"},null,-1)]))):a.value?(n(),d("div",Z,[t("div",ee,[t("div",te,[t("div",ae,[i(o,{variant:"ghost",icon:"arrow-left",onClick:R,size:"sm"}),t("div",null,[t("h1",se," Colloquio con "+r(a.value.candidate.full_name),1),t("p",re,r(a.value.application.job_posting.title),1),t("div",ie,[t("div",oe,[i(b,{name:"calendar",size:"xs",class:"mr-1"}),k(" "+r(q(a.value.scheduled_date)),1)]),t("div",le,[i(b,{name:"clock",size:"xs",class:"mr-1"}),k(" "+r(a.value.duration_minutes)+" minuti ",1)]),t("div",ne,[i(b,{name:"user",size:"xs",class:"mr-1"}),k(" "+r(a.value.interviewer.full_name),1)])])])]),t("div",de,[t("span",{class:f(["inline-flex items-center px-3 py-1 rounded-full text-sm font-medium",I(a.value.status)])},r(E(a.value.status)),3),t("span",{class:f(["inline-flex items-center px-3 py-1 rounded-full text-sm font-medium",U(a.value.interview_type)])},r($(a.value.interview_type)),3)])]),t("div",ue,[i(o,{variant:"primary",icon:"pencil",text:"Modifica",onClick:e[0]||(e[0]=l=>s.$router.push(`/app/recruiting/interviews/${a.value.id}/edit`))}),a.value.status==="scheduled"?(n(),S(o,{key:0,variant:"outline-primary",icon:"check",text:"Segna Completato",onClick:F})):c("",!0),a.value.status==="scheduled"?(n(),S(o,{key:1,variant:"outline-secondary",icon:"x-mark",text:"Annulla",onClick:M})):c("",!0),i(o,{variant:"outline-primary",icon:"chat-bubble-left-ellipsis",text:"Aggiungi Feedback",onClick:e[1]||(e[1]=l=>g.value=!0)}),i(o,{variant:"secondary",icon:"user",text:"Profilo Candidato",onClick:e[2]||(e[2]=l=>s.$router.push(`/app/recruiting/candidates/${a.value.candidate.id}`))}),i(o,{variant:"outline-success",icon:"envelope",text:"Invia Email",onClick:e[3]||(e[3]=l=>x.value=!0)}),i(o,{variant:"outline-primary",icon:"calendar",text:"Esporta Calendario",onClick:P}),i(o,{variant:"outline-danger",icon:"trash",text:"Elimina",onClick:B})])]),t("div",ce,[t("div",me,[t("div",ge,[e[24]||(e[24]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Dettagli Colloquio ",-1)),t("div",xe,[t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Data e Ora",-1)),t("p",ve,r(q(a.value.scheduled_date)),1)]),t("div",null,[e[19]||(e[19]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Durata",-1)),t("p",pe,r(a.value.duration_minutes)+" minuti",1)]),t("div",null,[e[20]||(e[20]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Tipo",-1)),t("p",ye,r($(a.value.interview_type)),1)]),t("div",null,[e[21]||(e[21]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Stato",-1)),t("span",{class:f(["mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",I(a.value.status)])},r(E(a.value.status)),3)]),t("div",ke,[e[22]||(e[22]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Luogo/Link",-1)),t("p",be,r(a.value.location||"Non specificato"),1)])]),a.value.notes?(n(),d("div",fe,[e[23]||(e[23]=t("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-2"},"Note per il Colloquio",-1)),t("p",we,r(a.value.notes),1)])):c("",!0)]),t("div",he,[t("div",_e,[e[25]||(e[25]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Informazioni Candidato ",-1)),i(o,{variant:"ghost",icon:"arrow-top-right-on-square",text:"Profilo Completo",size:"sm",onClick:e[4]||(e[4]=l=>s.$router.push(`/app/recruiting/candidates/${a.value.candidate.id}`))})]),t("div",Ce,[t("div",null,[e[26]||(e[26]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Nome Completo",-1)),t("p",Ee,r(a.value.candidate.full_name),1)]),t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Email",-1)),t("p",Ie,r(a.value.candidate.email),1)]),t("div",null,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Telefono",-1)),t("p",$e,r(a.value.candidate.phone||"N/A"),1)]),t("div",null,[e[29]||(e[29]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400"},"Candidatura",-1)),t("p",ze,r(a.value.application.status==="in_progress"?"In Corso":a.value.application.status),1)])])]),a.value.status==="completed"?(n(),d("div",qe,[t("div",je,[e[30]||(e[30]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Feedback Colloquio ",-1)),i(o,{variant:"outline-primary",icon:"pencil",text:"Aggiungi/Modifica",size:"sm",onClick:e[5]||(e[5]=l=>g.value=!0)})]),a.value.feedback||a.value.score?(n(),d("div",De,[a.value.score?(n(),d("div",Se,[e[32]||(e[32]=t("span",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Valutazione:",-1)),t("div",Te,[t("span",Ne,r(a.value.score),1),e[31]||(e[31]=t("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"/10",-1))])])):c("",!0),a.value.feedback?(n(),d("div",Ve,[e[33]||(e[33]=t("label",{class:"block text-sm font-medium text-gray-500 dark:text-gray-400 mb-2"},"Note di Feedback",-1)),t("p",Ae,r(a.value.feedback),1)])):c("",!0)])):(n(),d("div",Fe,[i(b,{name:"chat-bubble-left-ellipsis",size:"lg",class:"mx-auto text-gray-400 mb-2"}),e[34]||(e[34]=t("p",{class:"text-sm text-gray-500 dark:text-gray-400"},"Nessun feedback ancora disponibile",-1))]))])):c("",!0)]),t("div",Me,[t("div",Be,[e[40]||(e[40]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Riepilogo ",-1)),t("div",Le,[t("div",Pe,[e[35]||(e[35]=t("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Data Creazione",-1)),t("span",Re,r(z(a.value.created_at)),1)]),t("div",Ue,[e[36]||(e[36]=t("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Intervistatore",-1)),t("span",He,r(a.value.interviewer.full_name),1)]),t("div",Oe,[e[37]||(e[37]=t("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Posizione",-1)),t("span",Ge,r(a.value.application.job_posting.title),1)]),t("div",Je,[e[38]||(e[38]=t("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Stato Candidatura",-1)),t("span",Ke,r(a.value.application.current_step||"N/A"),1)]),a.value.updated_at?(n(),d("div",Qe,[e[39]||(e[39]=t("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ultimo Aggiornamento",-1)),t("span",We,r(z(a.value.updated_at)),1)])):c("",!0)])]),t("div",Xe,[e[41]||(e[41]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Azioni Rapide ",-1)),t("div",Ye,[i(o,{variant:"outline-primary",icon:"calendar",text:"Tutti i Colloqui",block:"",onClick:e[6]||(e[6]=l=>s.$router.push("/app/recruiting/interviews"))}),i(o,{variant:"outline-primary",icon:"document-text",text:"Candidatura Collegata",block:"",onClick:e[7]||(e[7]=l=>s.$router.push(`/app/recruiting/applications/${a.value.application.id}`))}),i(o,{variant:"outline-secondary",icon:"envelope",text:"Invia Email",block:"",onClick:C})])])])])])):c("",!0),g.value?(n(),d("div",{key:2,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:e[12]||(e[12]=l=>g.value=!1)},[t("div",{class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:e[11]||(e[11]=w(()=>{},["stop"]))},[t("div",Ze,[e[44]||(e[44]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Feedback Colloquio ",-1)),t("form",{onSubmit:w(L,["prevent"])},[t("div",et,[t("div",null,[e[42]||(e[42]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Valutazione (1-10) ",-1)),h(t("input",{"onUpdate:modelValue":e[8]||(e[8]=l=>m.value.score=l),type:"number",min:"1",max:"10",step:"0.1",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[T,m.value.score]])]),t("div",null,[e[43]||(e[43]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Note di Feedback ",-1)),h(t("textarea",{"onUpdate:modelValue":e[9]||(e[9]=l=>m.value.feedback=l),rows:"4",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Punti di forza, aree di miglioramento, impressioni generali..."},null,512),[[T,m.value.feedback]])])]),t("div",tt,[i(o,{variant:"secondary",text:"Annulla",onClick:e[10]||(e[10]=l=>g.value=!1)}),i(o,{variant:"primary",text:"Salva Feedback",loading:N(u).loading,type:"submit"},null,8,["loading"])])],32)])])])):c("",!0),x.value?(n(),d("div",{key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:e[16]||(e[16]=l=>x.value=!1)},[t("div",{class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:e[15]||(e[15]=w(()=>{},["stop"]))},[t("div",at,[e[48]||(e[48]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Invia Email Colloquio ",-1)),t("div",st,[t("div",null,[e[46]||(e[46]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Email ",-1)),h(t("select",{"onUpdate:modelValue":e[13]||(e[13]=l=>p.value=l),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},e[45]||(e[45]=[t("option",{value:"confirmation"},"Conferma Colloquio",-1),t("option",{value:"reminder"},"Reminder (24h prima)",-1)]),512),[[G,p.value]])]),t("div",rt,[e[47]||(e[47]=k(" L'email sarà inviata a: ")),t("strong",null,r((D=(j=a.value)==null?void 0:j.candidate)==null?void 0:D.email),1)])]),t("div",it,[i(o,{variant:"secondary",text:"Annulla",onClick:e[14]||(e[14]=l=>x.value=!1)}),i(o,{variant:"primary",text:"Invia Email",loading:N(u).loading,onClick:C,icon:"envelope"},null,8,["loading"])])])])])):c("",!0)])}}},mt=W(ot,[["__scopeId","data-v-f01f4c3f"]]);export{mt as default};
