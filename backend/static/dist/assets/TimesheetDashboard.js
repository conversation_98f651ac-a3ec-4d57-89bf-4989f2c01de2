import{r as x,b as c,o as i,l as $,F as ee,p as te,j as e,t as m,e as f,s as v,B as W,C as J,c as D,n as ie,w as je,x as Ce,h as ne,k as K,q as Te,H as Re}from"./vendor.js";import{_ as Se}from"./PageHeader.js";import{_ as Ae}from"./StatsGrid.js";import{T as ze}from"./TabContainer.js";import{H as w,a as de}from"./app.js";/* empty css                                                             */import{_ as De}from"./AlertsSection.js";import{S as ue}from"./StandardButton.js";import{T as Me}from"./TimesheetGrid.js";import{u as Ee}from"./timesheet.js";import{u as qe}from"./personnel.js";const Pe={class:"space-y-4"},Fe={class:"flex items-start justify-between"},Ne={class:"flex items-start space-x-4"},Ie={class:"flex-shrink-0"},Oe={class:"w-12 h-12 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center"},Ue={class:"text-sm font-medium text-gray-700 dark:text-gray-300"},Ve={class:"flex-1 min-w-0"},Be={class:"flex items-center space-x-3 mb-2"},Le={class:"text-lg font-medium text-gray-900 dark:text-white"},He={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"},We={class:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 dark:text-gray-400"},Ge={class:"text-gray-900 dark:text-white"},Xe={class:"text-gray-900 dark:text-white"},Qe={class:"text-gray-900 dark:text-white"},Ye={class:"text-gray-900 dark:text-white"},Je={key:0,class:"mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md"},Ze={class:"text-sm text-gray-700 dark:text-gray-300"},Ke={class:"flex-shrink-0 flex items-center space-x-3"},et=["onClick"],tt=["onClick"],st=["onClick"],at={key:0,class:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"},rt={class:"flex flex-wrap gap-2"},ot={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},nt={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},lt={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},it={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},dt={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},ut={class:"sm:flex sm:items-start"},ct={class:"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900 sm:mx-0 sm:h-10 sm:w-10"},mt={class:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full"},gt={class:"mt-2"},pt={class:"text-sm text-gray-500 dark:text-gray-400"},bt={class:"mt-4"},yt={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},vt=["disabled"],ft={__name:"TimesheetApprovalCards",props:{pendingTimesheets:{type:Array,default:()=>[]}},emits:["approve","reject","view-details"],setup(R,{emit:O}){const h=O,E=x(!1),k=x(null),b=x(""),j=o=>o!=null&&o.full_name?o.full_name.split(" ").map(r=>r.charAt(0)).join("").toUpperCase().slice(0,2):"??",q=o=>{if(!o)return"";if(o.month&&o.year)return new Date(o.year,o.month-1).toLocaleDateString("it-IT",{month:"long",year:"numeric"});if(o.start_date&&o.end_date){const r=new Date(o.start_date).toLocaleDateString("it-IT"),F=new Date(o.end_date).toLocaleDateString("it-IT");return`${r} - ${F}`}return"Periodo non specificato"},P=o=>!o||o===0?"0h":`${o}h`,y=o=>{k.value=o,b.value="",E.value=!0},C=()=>{E.value=!1,k.value=null,b.value=""},G=()=>{b.value.trim()&&(h("reject",k.value.id,b.value.trim()),C())};return(o,r)=>{var F,U;return i(),c("div",Pe,[(i(!0),c(ee,null,te(R.pendingTimesheets,g=>{var I;return i(),c("div",{key:g.id,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},[e("div",Fe,[e("div",Ne,[e("div",Ie,[e("div",Oe,[e("span",Ue,m(j(g.employee)),1)])]),e("div",Ve,[e("div",Be,[e("h3",Le,m(((I=g.employee)==null?void 0:I.full_name)||"Dipendente"),1),e("span",He,[f(w,{name:"clock",size:"xs",class:"mr-1"}),r[1]||(r[1]=v(" In Attesa "))])]),e("div",We,[e("div",null,[r[2]||(r[2]=e("span",{class:"font-medium"},"Periodo:",-1)),e("p",Ge,m(q(g)),1)]),e("div",null,[r[3]||(r[3]=e("span",{class:"font-medium"},"Ore Totali:",-1)),e("p",Xe,m(P(g.total_hours)),1)]),e("div",null,[r[4]||(r[4]=e("span",{class:"font-medium"},"Ore Fatturabili:",-1)),e("p",Qe,m(P(g.billable_hours)),1)]),e("div",null,[r[5]||(r[5]=e("span",{class:"font-medium"},"Progetti:",-1)),e("p",Ye,m(g.projects_count||0),1)])]),g.notes?(i(),c("div",Je,[e("p",Ze,[r[6]||(r[6]=e("span",{class:"font-medium"},"Note:",-1)),v(" "+m(g.notes),1)])])):$("",!0)])]),e("div",Ke,[e("button",{onClick:S=>o.$emit("view-details",g.id),class:"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[f(w,{name:"eye",size:"sm",class:"mr-2"}),r[7]||(r[7]=v(" Dettagli "))],8,et),e("button",{onClick:S=>y(g),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 transition-colors"},[f(w,{name:"x-mark",size:"sm",class:"mr-2"}),r[8]||(r[8]=v(" Rifiuta "))],8,tt),e("button",{onClick:S=>o.$emit("approve",g.id),class:"inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors"},[f(w,{name:"check",size:"sm",class:"mr-2"}),r[9]||(r[9]=v(" Approva "))],8,st)])]),g.recent_projects&&g.recent_projects.length>0?(i(),c("div",at,[r[10]||(r[10]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Progetti recenti:",-1)),e("div",rt,[(i(!0),c(ee,null,te(g.recent_projects.slice(0,3),S=>(i(),c("span",{key:S.id,class:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},m(S.name),1))),128)),g.recent_projects.length>3?(i(),c("span",ot," +"+m(g.recent_projects.length-3)+" altri ",1)):$("",!0)])])):$("",!0)])}),128)),E.value?(i(),c("div",nt,[e("div",lt,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:C}),r[14]||(r[14]=e("span",{class:"hidden sm:inline-block sm:align-middle sm:h-screen"},"​",-1)),e("div",it,[e("div",dt,[e("div",ut,[e("div",ct,[f(w,{name:"exclamation-triangle",size:"md",color:"text-red-600 dark:text-red-400"})]),e("div",mt,[r[13]||(r[13]=e("h3",{class:"text-lg leading-6 font-medium text-gray-900 dark:text-white",id:"modal-title"}," Rifiuta Timesheet ",-1)),e("div",gt,[e("p",pt,[r[11]||(r[11]=v(" Stai per rifiutare il timesheet di ")),e("strong",null,m((U=(F=k.value)==null?void 0:F.employee)==null?void 0:U.full_name),1),v(" per "+m(q(k.value))+". ",1)])]),e("div",bt,[r[12]||(r[12]=e("label",{for:"reject-reason",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},[v(" Motivo del rifiuto "),e("span",{class:"text-red-500"},"*")],-1)),W(e("textarea",{id:"reject-reason","onUpdate:modelValue":r[0]||(r[0]=g=>b.value=g),rows:"3",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500",placeholder:"Inserisci il motivo del rifiuto..."},null,512),[[J,b.value]])])])])]),e("div",yt,[e("button",{type:"button",onClick:G,disabled:!b.value.trim(),class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"}," Conferma Rifiuto ",8,vt),e("button",{type:"button",onClick:C,class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])])])])):$("",!0)])}}},xt={class:"space-y-6"},ht={key:0,class:"grid grid-cols-1 md:grid-cols-3 gap-6"},kt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4 border border-gray-200 dark:border-gray-700"},wt={class:"text-2xl font-bold text-gray-900 dark:text-white"},_t={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4 border border-gray-200 dark:border-gray-700"},$t={class:"text-2xl font-bold text-gray-900 dark:text-white"},jt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4 border border-gray-200 dark:border-gray-700"},Ct={class:"text-2xl font-bold text-gray-900 dark:text-white"},Tt={key:1,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},Rt={class:"flex flex-wrap gap-3"},St={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"},At={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},zt={class:"text-lg font-medium text-gray-900 dark:text-white"},Dt={key:0,class:"p-12 text-center"},Mt={class:"mt-4 text-lg font-medium text-gray-900 dark:text-white"},Et={class:"mt-2 text-sm text-gray-500 dark:text-gray-400"},qt={key:1,class:"divide-y divide-gray-200 dark:divide-gray-700"},Pt={class:"flex items-start justify-between"},Ft={class:"flex items-start space-x-4"},Nt={class:"flex-shrink-0"},It={class:"flex-1 min-w-0"},Ot={class:"flex items-center space-x-3 mb-2"},Ut={class:"text-lg font-medium text-gray-900 dark:text-white"},Vt={key:0,class:"text-sm text-gray-600 dark:text-gray-400 mb-1"},Bt={class:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400"},Lt={class:"text-gray-900 dark:text-white"},Ht={class:"text-gray-900 dark:text-white"},Wt={key:1,class:"mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md"},Gt={class:"text-sm text-gray-700 dark:text-gray-300"},Xt={key:2,class:"mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md"},Qt={class:"text-sm text-red-700 dark:text-red-300"},Yt={key:0,class:"flex-shrink-0 flex items-center space-x-2"},Jt=["onClick"],Zt=["onClick"],Kt={key:2,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},es={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},ts={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},ss={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},as={class:"sm:flex sm:items-start"},rs={class:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full"},os={class:"text-lg leading-6 font-medium text-gray-900 dark:text-white",id:"modal-title"},ns={class:"mt-4 space-y-4"},ls={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},is=["disabled"],ds={key:3,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},us={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},cs={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},ms={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},gs={class:"sm:flex sm:items-start"},ps={class:"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900 sm:mx-0 sm:h-10 sm:w-10"},bs={class:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full"},ys={class:"mt-2"},vs={class:"text-sm text-gray-500 dark:text-gray-400"},fs={key:0},xs={class:"mt-4"},hs={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},ks=["disabled"],ws={__name:"TimesheetRequestsList",props:{requests:{type:Array,default:()=>[]},canManage:{type:Boolean,default:!1}},emits:["approve-request","reject-request","create-request"],setup(R,{emit:O}){const h=O,E=x(!1),k=x(!1),b=x(null),j=x(""),q=x(""),P=x(!1),y=x({start_date:"",end_date:"",notes:""}),C=x(20),G=x(5),o=x(15),r=D(()=>y.value.start_date&&y.value.end_date),F=p=>p?new Date(p).toLocaleDateString("it-IT"):"N/A",U=p=>{if(!p.start_date||!p.end_date)return 0;const s=new Date(p.start_date),L=new Date(p.end_date),H=Math.abs(L-s);return Math.ceil(H/(1e3*60*60*24))+1},g=p=>({vacation:"Ferie",leave:"Permesso",smartworking:"Smart Working"})[p]||p,I=p=>({vacation:"calendar-days",leave:"clock",smartworking:"home"})[p]||"calendar-days",S=p=>({vacation:"bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400",leave:"bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400",smartworking:"bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400"})[p]||"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400",X=p=>({pending:"In Attesa",approved:"Approvato",rejected:"Rifiutato"})[p]||p,V=p=>({pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",approved:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",rejected:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"})[p]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",se=()=>({vacation:"Richiesta Ferie",leave:"Richiesta Permesso",smartworking:"Richiesta Smart Working"})[q.value]||"Nuova Richiesta",B=p=>{q.value=p,y.value={start_date:"",end_date:"",notes:""},E.value=!0},Q=()=>{E.value=!1,q.value="",y.value={start_date:"",end_date:"",notes:""}},ae=async()=>{if(r.value){P.value=!0;try{h("create-request",{...y.value,request_type:q.value}),Q()}catch(p){console.error("Failed to submit request",p)}finally{P.value=!1}}},re=p=>{b.value=p,j.value="",k.value=!0},Y=()=>{k.value=!1,b.value=null,j.value=""},oe=()=>{j.value.trim()&&(h("reject-request",b.value.id,j.value.trim()),Y())};return(p,s)=>{var L,H;return i(),c("div",xt,[R.canManage?$("",!0):(i(),c("div",ht,[e("div",kt,[s[7]||(s[7]=e("h4",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 mb-2"},"Ferie Rimanenti",-1)),e("p",wt,m(C.value)+" giorni",1)]),e("div",_t,[s[8]||(s[8]=e("h4",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 mb-2"},"Permessi Utilizzati",-1)),e("p",$t,m(G.value)+" giorni",1)]),e("div",jt,[s[9]||(s[9]=e("h4",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 mb-2"},"Smart Working",-1)),e("p",Ct,m(o.value)+" giorni",1)])])),R.canManage?$("",!0):(i(),c("div",Tt,[s[13]||(s[13]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Nuova Richiesta",-1)),e("div",Rt,[e("button",{onClick:s[0]||(s[0]=d=>B("vacation")),class:"inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"},[f(w,{name:"calendar-days",size:"sm",class:"mr-2"}),s[10]||(s[10]=v(" Richiedi Ferie "))]),e("button",{onClick:s[1]||(s[1]=d=>B("leave")),class:"inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"},[f(w,{name:"clock",size:"sm",class:"mr-2"}),s[11]||(s[11]=v(" Richiedi Permesso "))]),e("button",{onClick:s[2]||(s[2]=d=>B("smartworking")),class:"inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors"},[f(w,{name:"home",size:"sm",class:"mr-2"}),s[12]||(s[12]=v(" Smart Working "))])])])),e("div",St,[e("div",At,[e("h3",zt,m(R.canManage?"Richieste da Gestire":"Le Mie Richieste"),1)]),R.requests.length===0?(i(),c("div",Dt,[f(w,{name:"calendar-days",size:"2xl",class:"mx-auto text-gray-400"}),e("h3",Mt,m(R.canManage?"Nessuna richiesta da gestire":"Nessuna richiesta"),1),e("p",Et,m(R.canManage?"Tutti i dipendenti sono in regola.":"Non hai ancora fatto richieste."),1)])):(i(),c("div",qt,[(i(!0),c(ee,null,te(R.requests,d=>(i(),c("div",{key:d.id,class:"p-6"},[e("div",Pt,[e("div",Ft,[e("div",Nt,[e("div",{class:ie(["w-10 h-10 rounded-full flex items-center justify-center",S(d.request_type)])},[f(w,{name:I(d.request_type),size:"sm"},null,8,["name"])],2)]),e("div",It,[e("div",Ot,[e("h4",Ut,m(g(d.request_type)),1),e("span",{class:ie(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",V(d.status)])},m(X(d.status)),3)]),R.canManage&&d.employee?(i(),c("p",Vt,[s[14]||(s[14]=e("span",{class:"font-medium"},"Dipendente:",-1)),v(" "+m(d.employee.full_name),1)])):$("",!0),e("div",Bt,[e("div",null,[s[15]||(s[15]=e("span",{class:"font-medium"},"Periodo:",-1)),e("p",Lt,m(F(d.start_date))+" - "+m(F(d.end_date)),1)]),e("div",null,[s[16]||(s[16]=e("span",{class:"font-medium"},"Durata:",-1)),e("p",Ht,m(U(d))+" giorni",1)])]),d.notes?(i(),c("div",Wt,[e("p",Gt,[s[17]||(s[17]=e("span",{class:"font-medium"},"Note:",-1)),v(" "+m(d.notes),1)])])):$("",!0),d.status==="rejected"&&d.rejection_reason?(i(),c("div",Xt,[e("p",Qt,[s[18]||(s[18]=e("span",{class:"font-medium"},"Motivo rifiuto:",-1)),v(" "+m(d.rejection_reason),1)])])):$("",!0)])]),R.canManage&&d.status==="pending"?(i(),c("div",Yt,[e("button",{onClick:M=>p.$emit("approve-request",d.id),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors"},[f(w,{name:"check",size:"sm",class:"mr-1"}),s[19]||(s[19]=v(" Approva "))],8,Jt),e("button",{onClick:M=>re(d),class:"inline-flex items-center px-3 py-2 border border-red-300 text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 transition-colors"},[f(w,{name:"x-mark",size:"sm",class:"mr-1"}),s[20]||(s[20]=v(" Rifiuta "))],8,Zt)])):$("",!0)])]))),128))]))]),E.value?(i(),c("div",Kt,[e("div",es,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:Q}),s[24]||(s[24]=e("span",{class:"hidden sm:inline-block sm:align-middle sm:h-screen"},"​",-1)),e("div",ts,[e("div",ss,[e("div",as,[e("div",rs,[e("h3",os,m(se()),1),e("div",ns,[e("div",null,[s[21]||(s[21]=e("label",{for:"start_date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},[v(" Data Inizio "),e("span",{class:"text-red-500"},"*")],-1)),W(e("input",{type:"date",id:"start_date","onUpdate:modelValue":s[3]||(s[3]=d=>y.value.start_date=d),class:"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-brand-primary-500 focus:ring-brand-primary-500",required:""},null,512),[[J,y.value.start_date]])]),e("div",null,[s[22]||(s[22]=e("label",{for:"end_date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},[v(" Data Fine "),e("span",{class:"text-red-500"},"*")],-1)),W(e("input",{type:"date",id:"end_date","onUpdate:modelValue":s[4]||(s[4]=d=>y.value.end_date=d),class:"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-brand-primary-500 focus:ring-brand-primary-500",required:""},null,512),[[J,y.value.end_date]])]),e("div",null,[s[23]||(s[23]=e("label",{for:"notes",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Note",-1)),W(e("textarea",{id:"notes","onUpdate:modelValue":s[5]||(s[5]=d=>y.value.notes=d),rows:"3",class:"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-brand-primary-500 focus:ring-brand-primary-500",placeholder:"Inserisci eventuali note..."},null,512),[[J,y.value.notes]])])])])])]),e("div",ls,[e("button",{type:"button",onClick:ae,disabled:!r.value||P.value,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-brand-primary-600 text-base font-medium text-white hover:bg-brand-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"},m(P.value?"Salvataggio...":"Invia Richiesta"),9,is),e("button",{type:"button",onClick:Q,class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])])])])):$("",!0),k.value?(i(),c("div",ds,[e("div",us,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:Y}),s[30]||(s[30]=e("span",{class:"hidden sm:inline-block sm:align-middle sm:h-screen"},"​",-1)),e("div",cs,[e("div",ms,[e("div",gs,[e("div",ps,[f(w,{name:"exclamation-triangle",size:"md",color:"text-red-600 dark:text-red-400"})]),e("div",bs,[s[29]||(s[29]=e("h3",{class:"text-lg leading-6 font-medium text-gray-900 dark:text-white",id:"modal-title"}," Rifiuta Richiesta ",-1)),e("div",ys,[e("p",vs,[s[26]||(s[26]=v(" Stai per rifiutare la richiesta di ")),e("strong",null,m(g((L=b.value)==null?void 0:L.request_type)),1),(H=b.value)!=null&&H.employee?(i(),c("span",fs,[s[25]||(s[25]=v("di ")),e("strong",null,m(b.value.employee.full_name),1)])):$("",!0),s[27]||(s[27]=v(". "))])]),e("div",xs,[s[28]||(s[28]=e("label",{for:"reject-request-reason",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},[v(" Motivo del rifiuto "),e("span",{class:"text-red-500"},"*")],-1)),W(e("textarea",{id:"reject-request-reason","onUpdate:modelValue":s[6]||(s[6]=d=>j.value=d),rows:"3",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-red-500 focus:border-red-500",placeholder:"Inserisci il motivo del rifiuto..."},null,512),[[J,j.value]])])])])]),e("div",hs,[e("button",{type:"button",onClick:oe,disabled:!j.value.trim(),class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"}," Conferma Rifiuto ",8,ks),e("button",{type:"button",onClick:Y,class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])])])])):$("",!0)])}}},_s={class:"space-y-6"},$s={class:"flex space-x-3"},js={key:0,class:"space-y-6"},Cs={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},Ts={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Rs=["value"],Ss={class:"flex items-center space-x-2"},As={class:"text-center min-w-[150px]"},zs={class:"text-sm font-medium text-gray-900 dark:text-white"},Ds={class:"flex justify-end"},Ms=["disabled"],Es={key:0},qs={key:1,class:"text-center py-12"},Ps={key:1,class:"space-y-4"},Fs={key:0},Ns={key:0,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12"},Is={class:"text-center"},Os={key:1,class:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4"},Us={class:"flex"},Vs={key:2},ea={__name:"TimesheetDashboard",setup(R){const O=Te(),h=Ee(),E=qe(),k=x(!1),b=x(null),j=x("timesheet"),q=x(new Map),P=x(!1),y=x(new Date().getMonth()+1),C=x(new Date().getFullYear()),G=["Gennaio","Febbraio","Marzo","Aprile","Maggio","Giugno","Luglio","Agosto","Settembre","Ottobre","Novembre","Dicembre"],o=x(""),r=x({tasks:[],daily_totals:{},grand_total:0}),F=x([]),U=x("pending"),g=D(()=>h.stats||{}),I=D(()=>E.users||[]),S=D(()=>h.pendingApprovals||[]),X=D(()=>h.pendingTimeOffRequests||[]),V=D(()=>h.canApprove),se=D(()=>{const t=I.value.find(a=>a.id===o.value);return t?t.full_name:""}),B=D(()=>V.value||o.value&&o.value===h.currentUserId),Q=D(()=>({status:U.value,totalHours:parseFloat(r.value.grand_total||0),billableHours:ae(),pendingChanges:q.value.size})),ae=()=>r.value.tasks?r.value.tasks.reduce((t,a)=>t+Object.entries(a.billing||{}).filter(([n,l])=>l&&a.hours[n]).reduce((n,[l])=>n+parseFloat(a.hours[l]||0),0),0):0,re=D(()=>{const t=Array.isArray(S.value)?S.value.length:0,a=Array.isArray(X.value)?X.value.length:0;return[{id:"timesheet",name:"Timesheet",icon:"table-cells"},{id:"approvals",name:"Approvazioni",icon:"check-circle",badge:t>0?t:null},{id:"requests",name:"Richieste",icon:"calendar-days",badge:a>0?a:null}]}),Y=D(()=>!g.value||typeof g.value!="object"?[]:[{id:"weekly",label:"Ore Questa Settimana",value:g.value.weeklyHours||0,format:"hours",icon:"clock",iconBgColor:"bg-primary-100 dark:bg-primary-900",iconColor:"text-primary-600 dark:text-primary-400"},{id:"monthly",label:"Ore Questo Mese",value:g.value.monthlyHours||0,format:"hours",icon:"calendar",iconBgColor:"bg-green-100 dark:bg-green-900",iconColor:"text-green-600 dark:text-green-400"},{id:"pending",label:"Da Approvare",value:g.value.pendingApprovals||0,format:"number",icon:"exclamation-circle",iconBgColor:"bg-yellow-100 dark:bg-yellow-900",iconColor:"text-yellow-600 dark:text-yellow-400",show:V.value},{id:"efficiency",label:"Efficienza",value:g.value.efficiency||0,format:"percentage",icon:"chart-bar",iconBgColor:"bg-purple-100 dark:bg-purple-900",iconColor:"text-purple-600 dark:text-purple-400"}].filter(a=>a.show!==!1)),oe=D(()=>b.value?[{id:"error",type:"error",title:"Errore",message:b.value,dismissible:!0}]:[]),p=t=>{j.value=t},s=async()=>{try{k.value=!0,q.value.clear(),d(),await h.loadDashboardStats(),o.value&&await M(),console.log("✅ Dati aggiornati con successo")}catch(t){console.error("❌ Errore refresh dashboard:",t),b.value="Errore nel caricamento dei dati"}finally{k.value=!1}},L=()=>{y.value===1?(y.value=12,C.value--):y.value--,d(),M()},H=()=>{y.value===12?(y.value=1,C.value++):y.value++,d(),M()},d=()=>{const t=new Date(C.value,y.value,0).getDate(),a=[],n=new Date;for(let l=1;l<=t;l++){const T=new Date(C.value,y.value-1,l),u=T.getDay(),_=u===0||u===6,A=T.toDateString()===n.toDateString();a.push({key:`${C.value}-${y.value.toString().padStart(2,"0")}-${l.toString().padStart(2,"0")}`,label:l.toString(),sublabel:["Dom","Lun","Mar","Mer","Gio","Ven","Sab"][u],isWeekend:_,isToday:A,date:`${C.value}-${y.value.toString().padStart(2,"0")}-${l.toString().padStart(2,"0")}`})}F.value=a},M=async()=>{try{k.value=!0,console.log("🔄 Caricamento dati timesheet per dipendente:",o.value,"mese:",y.value,"anno:",C.value);const[t,a]=await Promise.all([h.loadMonthlyData(C.value,y.value,o.value),ce(o.value)]);console.log("📋 Risposta loadMonthlyData:",t),console.log("📋 Task assegnati:",a),t&&t.entries?(console.log("📋 Entries trovate:",Object.keys(t.entries).length),Object.entries(t.entries).forEach(([u,_])=>{console.log(`📅 ${u}:`,_)})):console.log("⚠️ Nessuna entry trovata nella risposta");const n=me(t,a),l={};t&&t.entries&&Object.entries(t.entries).forEach(([u,_])=>{const A=Object.values(_).reduce((N,z)=>{const Z=typeof z=="object"?z.hours:z;return N+(Z||0)},0);A>0&&(l[u]=A.toFixed(1))});const T=Object.values(l).reduce((u,_)=>u+parseFloat(_||0),0);r.value={tasks:n,daily_totals:l,grand_total:T.toFixed(1)},U.value=t&&t.status||"pending",console.log("✅ Dati combinati per TimesheetGrid:",r.value)}catch(t){console.error("Errore caricamento timesheet:",t),b.value="Errore nel caricamento dei dati timesheet",r.value={tasks:[],daily_totals:{},grand_total:0}}finally{k.value=!1}},ce=async t=>{try{const a=de(),n=await fetch(`/api/tasks?assignee_id=${t}&status=in-progress,todo&per_page=100`,{headers:{"X-CSRFToken":a.csrfToken}});if(!n.ok)throw new Error(`Errore caricamento task assegnati: ${n.status}`);const l=await n.json();return l.success&&l.data&&l.data.tasks?(console.log("📋 Task assegnati caricati:",l.data.tasks.length),l.data.tasks):[]}catch(a){return console.error("❌ Errore caricamento task assegnati:",a),[]}},me=(t,a)=>{var T;const n=[],l=new Set;return console.log("🔗 Combinando dati:",{timesheetResponse:t,assignedTasks:a}),t&&t.projects&&t.projects.forEach(u=>{const _={id:u.id,projectId:u.project_id,taskId:u.task_id,projectName:u.project_name,taskName:u.task_name,name:`${u.project_name}${u.task_name?` - ${u.task_name}`:""}`,hours:{},billing:{},total:"0.0"};Object.entries(t.entries||{}).forEach(([A,N])=>{if(N[u.id]){const z=N[u.id],Z=typeof z=="object"?z.hours:z,$e=typeof z=="object"?z.billable:!1;Z>0&&(_.hours[A]=Z.toFixed(1),_.billing[A]=$e)}}),_.total=Object.values(_.hours).reduce((A,N)=>A+parseFloat(N),0).toFixed(1),n.push(_),u.task_id&&l.add(u.task_id)}),a.forEach(u=>{if(l.has(u.id))return;const _={id:`task-${u.id}`,projectId:u.project_id,taskId:u.id,projectName:"Progetto Attivo",taskName:u.name,name:`Progetto Attivo - ${u.name}`,hours:{},billing:{},total:"0.0",isAssignedTask:!0};n.push(_)}),console.log("🔗 Task combinati:",n.length,"(con ore:",((T=t==null?void 0:t.projects)==null?void 0:T.length)||0,", assegnati:",a.length,")"),n},ge=()=>{console.log("👥 Cambio dipendente:",o.value),M()};je(o,()=>{o.value&&M()});const pe=(t,a)=>{console.log("🖱️ Clic su cella:",t.name,a.label||a.key),!B.value&&O.push({path:"/app/timesheet/entry",query:{employee:o.value,date:a.date||a.key,task:t.id}})},be=async(t,a,n)=>{console.log("📝 Cell update:",t,a,n);try{const l=r.value.tasks.find(T=>T.id===parseInt(t)||T.id===t);if(!l){console.error("❌ Task non trovato:",t);return}await le({user_id:o.value,project_id:l.projectId||l.project_id,task_id:l.taskId||l.task_id||null,date:a,hours:parseFloat(n)}),await M()}catch(l){console.error("❌ Errore nel salvataggio singolo:",l)}},ye=t=>{console.log("⏰ Ore totali cambiate:",t)},ve=async t=>{console.log("💾 Salvataggio batch:",t.length,"modifiche");try{P.value=!0;const a=[];t.forEach(n=>{console.log("📤 Salvando:",n.taskId,n.date,n.hours);const l=le({project_id:n.projectId,task_id:n.taskId,date:n.date,hours:n.hours,user_id:o.value});a.push(l)}),console.log(`📤 Salvando ${a.length} entries...`),a.length>0&&(await Promise.all(a),console.log("✅ Salvataggio batch completato")),await M()}catch(a){console.error("❌ Errore salvataggio batch:",a),b.value="Errore nel salvataggio delle ore"}finally{P.value=!1}},le=async t=>{const a=de();try{const n=new URLSearchParams({project_id:t.project_id,user_id:t.user_id,start_date:t.date,end_date:t.date,limit:1});t.task_id&&n.append("task_id",t.task_id);const l=await fetch(`/api/timesheets?${n}`,{headers:{"X-CSRFToken":a.csrfToken}});if(!l.ok)throw new Error(`Errore ricerca entry esistenti: ${l.status}`);const T=await l.json();let u,_="POST",A="/api/timesheets";if(T.success&&T.data&&T.data.length>0){const z=T.data[0];_="PUT",A=`/api/timesheets/${z.id}`,console.log("🔄 Aggiornamento entry esistente:",z.id)}else console.log("➕ Creazione nuovo entry");if(u=await fetch(A,{method:_,headers:{"Content-Type":"application/json","X-CSRFToken":a.csrfToken},body:JSON.stringify({date:t.date,project_id:t.project_id,task_id:t.task_id,hours:t.hours,description:`Ore inserite tramite dashboard - ${t.date}`,user_id:t.user_id,billable:!0})}),!u.ok)throw new Error(`HTTP ${u.status}: ${u.statusText}`);const N=await u.json();if(!N.success)throw new Error(N.message||"Errore durante il salvataggio");return N}catch(n){throw console.error("❌ Errore saveTimesheetEntry:",n),n}},fe=async t=>{try{await h.approveTimesheet(t),await h.loadPendingApprovals()}catch{b.value="Errore nell'approvazione del timesheet"}},xe=async(t,a)=>{try{await h.rejectTimesheet(t,a),await h.loadPendingApprovals()}catch{b.value="Errore nel rifiuto del timesheet"}},he=t=>{O.push(`/app/timesheet/view/${t}`)},ke=async t=>{try{await h.approveTimeOffRequest(t),await h.loadPendingTimeOffRequests()}catch{b.value="Errore nell'approvazione della richiesta"}},we=async(t,a)=>{try{await h.rejectTimeOffRequest(t,a),await h.loadPendingTimeOffRequests()}catch{b.value="Errore nel rifiuto della richiesta"}},_e=()=>{O.push("/app/timesheet/requests/create")};return Ce(async()=>{try{d(),await h.loadDashboardStats(),await E.fetchUsers(),I.value.length>0&&!o.value&&(o.value=I.value[0].id),await M()}catch(t){console.error("Errore inizializzazione dashboard:",t),b.value="Errore nel caricamento dei dati iniziali"}}),(t,a)=>(i(),c("div",_s,[b.value?(i(),ne(De,{key:0,alerts:oe.value},null,8,["alerts"])):$("",!0),f(Se,{title:"Dashboard Timesheet",subtitle:"Panoramica ore e approvazioni del team",icon:"clock","icon-color":"text-primary-600"},{actions:K(()=>[e("div",$s,[f(ue,{variant:"primary",icon:"plus",to:"/app/timesheet/entry"},{default:K(()=>a[1]||(a[1]=[v(" Registra Ore ")])),_:1,__:[1]}),f(ue,{variant:"secondary",icon:"arrow-path",onClick:s,disabled:k.value,loading:k.value},{default:K(()=>a[2]||(a[2]=[v(" Aggiorna ")])),_:1,__:[2]},8,["disabled","loading"])])]),_:1}),g.value?(i(),ne(Ae,{key:1,stats:Y.value},null,8,["stats"])):$("",!0),f(ze,{tabs:re.value,"active-tab":j.value,onTabChange:p},{default:K(()=>[j.value==="timesheet"?(i(),c("div",js,[e("div",Cs,[e("div",Ts,[e("div",null,[a[4]||(a[4]=e("label",{for:"employee",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Dipendente ",-1)),W(e("select",{id:"employee","onUpdate:modelValue":a[0]||(a[0]=n=>o.value=n),onChange:ge,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-primary-500 focus:border-transparent"},[a[3]||(a[3]=e("option",{value:""},"Seleziona dipendente",-1)),(i(!0),c(ee,null,te(I.value,n=>(i(),c("option",{key:n.id,value:n.id},m(n.full_name),9,Rs))),128))],544),[[Re,o.value]])]),e("div",null,[a[5]||(a[5]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Mese/Anno ",-1)),e("div",Ss,[e("button",{onClick:L,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md transition-colors"},[f(w,{name:"chevron-left",size:"md"})]),e("div",As,[e("span",zs,m(G[y.value-1])+" "+m(C.value),1)]),e("button",{onClick:H,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md transition-colors"},[f(w,{name:"chevron-right",size:"md"})])])]),e("div",Ds,[e("button",{onClick:M,disabled:!o.value||k.value,class:"px-4 py-2 bg-brand-primary-600 hover:bg-brand-primary-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-md transition-colors"},[k.value?(i(),ne(w,{key:0,name:"arrow-path",size:"sm",className:"animate-spin mr-2"})):$("",!0),v(" "+m(k.value?"Caricamento...":"Reset"),1)],8,Ms)])])]),o.value?(i(),c("div",Es,[f(Me,{title:`Timesheet Dettaglio - ${se.value}`,tasks:r.value.tasks||[],days:F.value,"daily-totals":r.value.daily_totals||{},"grand-total":r.value.grand_total||0,loading:k.value,error:b.value,editable:B.value,"show-stats":!0,"show-day-totals":!0,"show-indicators":!0,"show-legend":!0,status:Q.value,"row-header-label":"Progetto/Task","empty-message":"Nessun timesheet registrato per il periodo selezionato",onCellClick:pe,onCellUpdate:be,onBulkSave:ve,onHoursChanged:ye},null,8,["title","tasks","days","daily-totals","grand-total","loading","error","editable","status"])])):(i(),c("div",qs,[f(w,{name:"users",size:"lg",class:"mx-auto text-gray-400 mb-4"}),a[6]||(a[6]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Seleziona un dipendente",-1)),a[7]||(a[7]=e("p",{class:"text-gray-500 dark:text-gray-400"}," Scegli un dipendente dal menu a tendina per visualizzare il suo timesheet ",-1))]))])):j.value==="approvals"?(i(),c("div",Ps,[V.value?(i(),c("div",Fs,[f(ft,{"pending-timesheets":S.value,onApprove:fe,onReject:xe,onViewDetails:he},null,8,["pending-timesheets"]),S.value.length===0?(i(),c("div",Ns,[e("div",Is,[f(w,{name:"check-circle",size:"2xl",class:"mx-auto text-green-400"}),a[8]||(a[8]=e("h3",{class:"mt-4 text-lg font-medium text-gray-900 dark:text-white"},"Tutto approvato!",-1)),a[9]||(a[9]=e("p",{class:"mt-2 text-sm text-gray-500 dark:text-gray-400"}," Non ci sono timesheet in attesa di approvazione. ",-1))])])):$("",!0)])):(i(),c("div",Os,[e("div",Us,[f(w,{name:"exclamation-triangle",size:"sm",color:"text-yellow-400",class:"mr-2 mt-0.5"}),a[10]||(a[10]=e("div",null,[e("h3",{class:"text-sm font-medium text-yellow-800 dark:text-yellow-200"},"Accesso limitato"),e("p",{class:"text-sm text-yellow-700 dark:text-yellow-300 mt-1"}," Non hai i permessi per approvare i timesheet. ")],-1))])]))])):j.value==="requests"?(i(),c("div",Vs,[f(ws,{requests:X.value,"can-manage":V.value,onApproveRequest:ke,onRejectRequest:we,onCreateRequest:_e},null,8,["requests","can-manage"])])):$("",!0)]),_:1},8,["tabs","active-tab"])]))}};export{ea as default};
