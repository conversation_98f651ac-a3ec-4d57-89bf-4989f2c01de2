import{u as v,H as i,g}from"./app.js";import{c as f,x as h,b as o,j as e,l as d,I as _,s as c,F as u,p as y,e as l,t as r,o as a,v as b}from"./vendor.js";const w={class:"min-h-screen bg-gray-50 py-16"},z={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},k={key:0,class:"mb-16"},I={class:"grid grid-cols-2 md:grid-cols-4 gap-8"},j={class:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4"},S={class:"text-sm font-medium text-gray-900 mb-2"},L={class:"text-xs text-gray-500 mb-2"},C={class:"text-xs bg-gray-100 px-2 py-1 rounded"},N={key:1,class:"mb-16"},E={class:"grid grid-cols-1 md:grid-cols-3 gap-8"},F={class:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4"},V={class:"text-sm font-medium text-gray-900 mb-2"},B={class:"text-xs bg-gray-100 px-2 py-1 rounded"},H={key:2,class:"mb-16"},T={class:"grid grid-cols-2 md:grid-cols-4 gap-6"},P={key:0,class:"bg-white p-4 rounded-lg shadow-sm border text-center"},D={class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-3"},M={class:"text-xs text-gray-500"},O={key:1,class:"bg-white p-4 rounded-lg shadow-sm border text-center"},R={class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-3"},q={class:"text-xs text-gray-500"},A={key:2,class:"bg-white p-4 rounded-lg shadow-sm border text-center"},G={class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-3"},J={class:"text-xs text-gray-500"},K={key:3,class:"bg-white p-4 rounded-lg shadow-sm border text-center"},Q={class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-3"},U={class:"text-xs text-gray-500"},$={__name:"IconSystemExample",setup(W){const m=v(),s=f(()=>m.config||{});return h(()=>{m.loadConfig()}),(X,t)=>{var p,x;return a(),o("div",w,[e("div",z,[t[14]||(t[14]=e("h1",{class:"text-3xl font-bold text-gray-900 text-center mb-4"}," Icon System Example ",-1)),t[15]||(t[15]=e("p",{class:"text-lg text-gray-600 text-center mb-12"}," Esempi di utilizzo del sistema di icone con configurazione tenant ",-1)),(p=s.value.company)!=null&&p.platform_features?(a(),o("div",k,[t[0]||(t[0]=e("h2",{class:"text-2xl font-semibold text-gray-800 mb-4"},"Platform Features (da tenant_config.json)",-1)),t[1]||(t[1]=e("p",{class:"text-sm text-gray-600 mb-8"},[c(" Le icone vengono lette direttamente dal file "),e("code",{class:"bg-gray-100 px-2 py-1 rounded"},"tenant_config.json"),c(" nella proprietà "),e("code",{class:"bg-gray-100 px-2 py-1 rounded"},"company.platform_features[].icon")],-1)),e("div",I,[(a(!0),o(u,null,y(s.value.company.platform_features,n=>(a(),o("div",{key:n.title,class:"bg-white p-6 rounded-lg shadow-sm border text-center"},[e("div",j,[l(i,{name:n.icon,class:"w-8 h-8 text-primary-600"},null,8,["name"])]),e("h3",S,r(n.title),1),e("p",L,r(n.description),1),e("code",C,r(n.icon),1)]))),128))])])):d("",!0),(x=s.value.company)!=null&&x.expertise?(a(),o("div",N,[t[2]||(t[2]=e("h2",{class:"text-2xl font-semibold text-gray-800 mb-4"},"Services (da tenant_config.json)",-1)),t[3]||(t[3]=e("p",{class:"text-sm text-gray-600 mb-8"},[c(" Le icone per i servizi vengono mappate tramite la funzione "),e("code",{class:"bg-gray-100 px-2 py-1 rounded"},"getServiceIcon()"),c(" che utilizza i nomi dei servizi da "),e("code",{class:"bg-gray-100 px-2 py-1 rounded"},"company.expertise[]")],-1)),e("div",E,[(a(!0),o(u,null,y(s.value.company.expertise,n=>(a(),o("div",{key:n,class:"bg-white p-6 rounded-lg shadow-sm border text-center"},[e("div",F,[l(i,{name:b(g)(n),class:"w-8 h-8 text-primary-600"},null,8,["name"])]),e("h3",V,r(n),1),e("code",B,r(b(g)(n)),1)]))),128))])])):d("",!0),s.value.contact?(a(),o("div",H,[t[12]||(t[12]=e("h2",{class:"text-2xl font-semibold text-gray-800 mb-4"},"Contact Info (da tenant_config.json)",-1)),t[13]||(t[13]=e("p",{class:"text-sm text-gray-600 mb-8"}," Le icone per i contatti utilizzano il mapping standard del sistema ",-1)),e("div",T,[s.value.contact.address?(a(),o("div",P,[e("div",D,[l(i,{name:"map-pin",class:"w-6 h-6 text-primary-600"})]),t[4]||(t[4]=e("p",{class:"text-xs text-gray-900 font-medium"},"Indirizzo",-1)),e("p",M,r(s.value.contact.address),1),t[5]||(t[5]=e("code",{class:"text-xs bg-gray-100 px-2 py-1 rounded"},"map-pin",-1))])):d("",!0),s.value.contact.phone?(a(),o("div",O,[e("div",R,[l(i,{name:"phone",class:"w-6 h-6 text-primary-600"})]),t[6]||(t[6]=e("p",{class:"text-xs text-gray-900 font-medium"},"Telefono",-1)),e("p",q,r(s.value.contact.phone),1),t[7]||(t[7]=e("code",{class:"text-xs bg-gray-100 px-2 py-1 rounded"},"phone",-1))])):d("",!0),s.value.contact.email?(a(),o("div",A,[e("div",G,[l(i,{name:"envelope",class:"w-6 h-6 text-primary-600"})]),t[8]||(t[8]=e("p",{class:"text-xs text-gray-900 font-medium"},"Email",-1)),e("p",J,r(s.value.contact.email),1),t[9]||(t[9]=e("code",{class:"text-xs bg-gray-100 px-2 py-1 rounded"},"envelope",-1))])):d("",!0),s.value.contact.hours?(a(),o("div",K,[e("div",Q,[l(i,{name:"clock",class:"w-6 h-6 text-primary-600"})]),t[10]||(t[10]=e("p",{class:"text-xs text-gray-900 font-medium"},"Orari",-1)),e("p",U,r(s.value.contact.hours),1),t[11]||(t[11]=e("code",{class:"text-xs bg-gray-100 px-2 py-1 rounded"},"clock",-1))])):d("",!0)])])):d("",!0),t[16]||(t[16]=_('<div class="bg-white p-8 rounded-lg shadow-sm border"><h2 class="text-2xl font-semibold text-gray-800 mb-4">Come Funziona il Sistema</h2><div class="space-y-4 text-sm text-gray-600"><div><h3 class="font-medium text-gray-900 mb-2">1. Platform Features</h3><p>Le icone vengono lette direttamente dal campo <code class="bg-gray-100 px-1 rounded">icon</code> in <code class="bg-gray-100 px-1 rounded">company.platform_features[]</code></p></div><div><h3 class="font-medium text-gray-900 mb-2">2. Services</h3><p>Le icone vengono mappate tramite la funzione <code class="bg-gray-100 px-1 rounded">getServiceIcon(serviceName)</code> che converte i nomi dei servizi in icone appropriate</p></div><div><h3 class="font-medium text-gray-900 mb-2">3. Contact Info</h3><p>Le icone utilizzano nomi standard: <code class="bg-gray-100 px-1 rounded">map-pin</code>, <code class="bg-gray-100 px-1 rounded">phone</code>, <code class="bg-gray-100 px-1 rounded">envelope</code>, <code class="bg-gray-100 px-1 rounded">clock</code></p></div><div><h3 class="font-medium text-gray-900 mb-2">4. Icon Resolution</h3><p>Il sistema utilizza <code class="bg-gray-100 px-1 rounded">IconLibrary.js</code> per mappare nomi comuni alle icone HeroIcons effettive</p></div></div></div>',1))])])}}};export{$ as default};
