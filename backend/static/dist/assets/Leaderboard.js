import{h as N,k,r as y,c as _,x as T,f as U,o as d,j as e,b as c,l as p,t as o,e as m,F as B,p as E,n as C,s as w,B as P,H as z}from"./vendor.js";import{u as M}from"./engagement.js";import{_ as V,H,d as Q}from"./app.js";import{D as R}from"./DashboardTemplate.js";const q={name:"Leaderboard",components:{DashboardTemplate:R,HeroIcon:H},setup(){const b=M(),{showToast:s}=Q(),u=y("month"),t=y("points"),x=y(1),n=y(null),i=_(()=>b.leaderboard),f=_(()=>b.loading),a=_(()=>i.value.slice(0,3)),l=async(r=1)=>{var g;try{const h={page:r,per_page:25,period:u.value,category:t.value},v=await b.fetchLeaderboard(h);v?(n.value=v.pagination||((g=v.data)==null?void 0:g.pagination)||null,x.value=r):n.value=null}catch(h){console.error("Error loading leaderboard data:",h),s("Errore nel caricamento della classifica","error"),n.value=null}},j=()=>{var r;(r=n.value)!=null&&r.has_prev&&l(x.value-1)},D=()=>{var r;(r=n.value)!=null&&r.has_next&&l(x.value+1)},I=()=>{if(!n.value)return"0-0";const r=(n.value.page-1)*n.value.per_page+1,g=Math.min(n.value.page*n.value.per_page,n.value.total);return`${r}-${g}`},A=r=>r?r.split(" ").map(g=>g.charAt(0)).join("").toUpperCase().substring(0,2):"?",L=()=>({points:"Punti",activities:"Attività",streak:"Giorni",achievements:"Achievement"})[t.value]||"Punti",S=r=>({Novizio:"bg-gray-100 text-gray-800",Beginner:"bg-blue-100 text-blue-800",Intermediate:"bg-green-100 text-green-800",Advanced:"bg-purple-100 text-purple-800",Expert:"bg-orange-100 text-orange-800",Master:"bg-red-100 text-red-800"})[r]||"bg-gray-100 text-gray-800";return T(()=>{l()}),{loading:f,selectedPeriod:u,selectedCategory:t,currentPage:x,leaderboardUsers:i,pagination:n,topUsers:a,loadData:l,previousPage:j,nextPage:D,getItemRange:I,getInitials:A,getScoreLabel:L,getLevelBadgeClass:S}}},F={class:"flex space-x-3"},G={class:"mb-8"},J={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},K={key:0,class:"bg-white shadow-lg rounded-lg p-6 text-center order-2 md:order-1 border-2 border-gray-200"},O={class:"relative mb-4"},W={class:"mx-auto h-16 w-16 rounded-full bg-gray-400 flex items-center justify-center text-xl font-bold text-white"},X={class:"text-lg font-semibold text-gray-900"},Y={class:"text-sm text-gray-500 mb-2"},Z={class:"flex items-center justify-center"},$={class:"text-2xl font-bold text-gray-600"},ee={class:"text-sm text-gray-500 ml-1"},te={key:1,class:"bg-gradient-to-br from-yellow-400 to-yellow-600 shadow-xl rounded-lg p-6 text-center transform scale-105 order-1 md:order-2 border-2 border-yellow-300"},se={class:"relative mb-4"},oe={class:"mx-auto h-20 w-20 rounded-full bg-white bg-opacity-20 flex items-center justify-center text-2xl font-bold text-white shadow-lg"},ae={class:"absolute -top-2 -right-2 bg-yellow-500 text-white rounded-full w-10 h-10 flex items-center justify-center border-2 border-white shadow-lg"},re={class:"text-xl font-bold text-white"},ne={class:"text-yellow-100 mb-3"},ie={class:"flex items-center justify-center"},le={class:"text-3xl font-bold text-white"},de={class:"text-yellow-100 ml-1"},ce={key:2,class:"bg-white shadow-lg rounded-lg p-6 text-center order-3 border-2 border-orange-200"},me={class:"relative mb-4"},ge={class:"mx-auto h-16 w-16 rounded-full bg-orange-400 flex items-center justify-center text-xl font-bold text-white"},xe={class:"text-lg font-semibold text-gray-900"},pe={class:"text-sm text-gray-500 mb-2"},be={class:"flex items-center justify-center"},ye={class:"text-2xl font-bold text-orange-600"},ue={class:"text-sm text-gray-500 ml-1"},fe={class:"bg-white rounded-lg shadow overflow-hidden"},he={key:0,class:"text-center py-12"},ve={class:"overflow-x-auto"},_e={class:"min-w-full divide-y divide-gray-200"},we={class:"bg-gray-50"},ke={scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},Ue={class:"bg-white divide-y divide-gray-200"},Ce={class:"px-6 py-4 whitespace-nowrap"},Pe={class:"flex items-center"},ze={key:0,class:"text-yellow-500 mr-2"},je={key:1,class:"text-gray-400 mr-2"},De={key:2,class:"text-orange-500 mr-2"},Ie={key:3,class:"text-sm font-medium text-gray-500 mr-2"},Ae={class:"text-sm font-medium text-gray-900"},Le={class:"px-6 py-4 whitespace-nowrap"},Se={class:"flex items-center"},Ne={class:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-white mr-4"},Te={class:"text-sm font-medium text-gray-900"},Be={class:"text-sm text-gray-500"},Ee={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Me={class:"px-6 py-4 whitespace-nowrap"},Ve={class:"text-lg font-bold text-blue-600"},He={class:"px-6 py-4 whitespace-nowrap"},Qe={class:"px-6 py-4 whitespace-nowrap"},Re={class:"flex items-center"},qe={class:"text-sm text-gray-900"},Fe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Ge={class:"flex items-center"},Je={key:1,class:"bg-gray-50 px-6 py-3 flex items-center justify-between border-t border-gray-200"},Ke={class:"flex-1 flex justify-between sm:hidden"},Oe=["disabled"],We=["disabled"],Xe={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Ye={class:"text-sm text-gray-700"},Ze={class:"font-medium"},$e={class:"font-medium"},et={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},tt=["disabled"],st={class:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"},ot=["disabled"];function at(b,s,u,t,x,n){const i=U("HeroIcon"),f=U("DashboardTemplate");return d(),N(f,{title:"Classifiche Engagement",subtitle:"Classifiche utenti e team per gamification aziendale",loading:t.loading,"show-refresh-button":!0,onRefresh:t.loadData},{"header-actions":k(()=>[e("div",F,[P(e("select",{"onUpdate:modelValue":s[0]||(s[0]=a=>t.selectedPeriod=a),onChange:s[1]||(s[1]=(...a)=>t.loadData&&t.loadData(...a)),class:"bg-white border border-gray-300 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 focus:outline-none focus:ring-brand-primary-500 focus:border-brand-primary-500"},s[8]||(s[8]=[e("option",{value:"week"},"Questa Settimana",-1),e("option",{value:"month"},"Questo Mese",-1),e("option",{value:"quarter"},"Questo Trimestre",-1),e("option",{value:"year"},"Quest'Anno",-1),e("option",{value:"all"},"Tutto il Tempo",-1)]),544),[[z,t.selectedPeriod]]),P(e("select",{"onUpdate:modelValue":s[2]||(s[2]=a=>t.selectedCategory=a),onChange:s[3]||(s[3]=(...a)=>t.loadData&&t.loadData(...a)),class:"bg-white border border-gray-300 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 focus:outline-none focus:ring-brand-primary-500 focus:border-brand-primary-500"},s[9]||(s[9]=[e("option",{value:"points"},"Punti Totali",-1),e("option",{value:"activities"},"Attività Completate",-1),e("option",{value:"streak"},"Giorni Consecutivi",-1),e("option",{value:"achievements"},"Achievement",-1)]),544),[[z,t.selectedCategory]])])]),widget:k(()=>[e("div",G,[s[12]||(s[12]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"🏆 Top Performers",-1)),e("div",J,[t.topUsers[1]?(d(),c("div",K,[e("div",O,[e("div",W,o(t.getInitials(t.topUsers[1].username)),1),s[10]||(s[10]=e("div",{class:"absolute -top-2 -right-2 bg-gray-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold shadow-lg"}," 2 ",-1))]),e("h3",X,o(t.topUsers[1].username),1),e("p",Y,o(t.topUsers[1].department_name||"N/A"),1),e("div",Z,[e("span",$,o(t.topUsers[1].score),1),e("span",ee,o(t.getScoreLabel()),1)])])):p("",!0),t.topUsers[0]?(d(),c("div",te,[e("div",se,[e("div",oe,o(t.getInitials(t.topUsers[0].username)),1),e("div",ae,[m(i,{name:"trophy",size:"md"})])]),e("h3",re,o(t.topUsers[0].username),1),e("p",ne,o(t.topUsers[0].department_name||"N/A"),1),e("div",ie,[e("span",le,o(t.topUsers[0].score),1),e("span",de,o(t.getScoreLabel()),1)])])):p("",!0),t.topUsers[2]?(d(),c("div",ce,[e("div",me,[e("div",ge,o(t.getInitials(t.topUsers[2].username)),1),s[11]||(s[11]=e("div",{class:"absolute -top-2 -right-2 bg-orange-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold shadow-lg"}," 3 ",-1))]),e("h3",xe,o(t.topUsers[2].username),1),e("p",pe,o(t.topUsers[2].department_name||"N/A"),1),e("div",be,[e("span",ye,o(t.topUsers[2].score),1),e("span",ue,o(t.getScoreLabel()),1)])])):p("",!0)])]),e("div",fe,[s[24]||(s[24]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Classifica Completa")],-1)),t.leaderboardUsers.length===0&&!t.loading?(d(),c("div",he,[m(i,{name:"trophy",size:"xl",class:"mx-auto text-gray-400 mb-4"}),s[13]||(s[13]=e("p",{class:"text-gray-500 text-lg mb-2"},"Nessun dato disponibile",-1)),s[14]||(s[14]=e("p",{class:"text-gray-400 text-sm"},"I dati della classifica verranno mostrati qui una volta che gli utenti inizieranno a partecipare alle attività di engagement.",-1))])):p("",!0),e("div",ve,[e("table",_e,[e("thead",we,[e("tr",null,[s[15]||(s[15]=e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Posizione ",-1)),s[16]||(s[16]=e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Utente ",-1)),s[17]||(s[17]=e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Dipartimento ",-1)),e("th",ke,o(t.getScoreLabel()),1),s[18]||(s[18]=e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Livello ",-1)),s[19]||(s[19]=e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Streak ",-1)),s[20]||(s[20]=e("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Achievement ",-1))])]),e("tbody",Ue,[(d(!0),c(B,null,E(t.leaderboardUsers,(a,l)=>(d(),c("tr",{key:a.user_id||l,class:C([l<3?"bg-yellow-50":"hover:bg-gray-50","transition-colors"])},[e("td",Ce,[e("div",Pe,[l===0?(d(),c("span",ze,[m(i,{name:"trophy",size:"md"})])):l===1?(d(),c("span",je,[m(i,{name:"star",size:"md"})])):l===2?(d(),c("span",De,[m(i,{name:"star",size:"md"})])):(d(),c("span",Ie,o(l+1),1)),e("span",Ae," #"+o(l+1),1)])]),e("td",Le,[e("div",Se,[e("div",Ne,o(t.getInitials(a.username)),1),e("div",null,[e("div",Te,o(a.username),1),e("div",Be,o(a.email||"N/A"),1)])])]),e("td",Ee,o(a.department_name||"-"),1),e("td",Me,[e("span",Ve,o(a.score||0),1)]),e("td",He,[e("span",{class:C(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",t.getLevelBadgeClass(a.current_level)])},o(a.current_level||"Novizio"),3)]),e("td",Qe,[e("div",Re,[m(i,{name:"fire",size:"sm",class:"text-orange-500 mr-1"}),e("span",qe,o(a.streak_days||0),1)])]),e("td",Fe,[e("div",Ge,[m(i,{name:"star",size:"sm",class:"text-yellow-500 mr-1"}),e("span",null,o(a.achievements_count||0),1)])])],2))),128))])])]),t.pagination&&t.pagination.pages>1?(d(),c("div",Je,[e("div",Ke,[e("button",{onClick:s[4]||(s[4]=(...a)=>t.previousPage&&t.previousPage(...a)),disabled:!t.pagination.has_prev,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Precedente ",8,Oe),e("button",{onClick:s[5]||(s[5]=(...a)=>t.nextPage&&t.nextPage(...a)),disabled:!t.pagination.has_next,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Successivo ",8,We)]),e("div",Xe,[e("div",null,[e("p",Ye,[s[21]||(s[21]=w(" Mostrando ")),e("span",Ze,o(t.getItemRange()),1),s[22]||(s[22]=w(" di ")),e("span",$e,o(t.pagination.total),1),s[23]||(s[23]=w(" utenti "))])]),e("div",null,[e("nav",et,[e("button",{onClick:s[6]||(s[6]=(...a)=>t.previousPage&&t.previousPage(...a)),disabled:!t.pagination.has_prev,class:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"},[m(i,{name:"chevron-left",size:"sm"})],8,tt),e("span",st,o(t.pagination.page)+" / "+o(t.pagination.pages),1),e("button",{onClick:s[7]||(s[7]=(...a)=>t.nextPage&&t.nextPage(...a)),disabled:!t.pagination.has_next,class:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"},[m(i,{name:"chevron-right",size:"sm"})],8,ot)])])])])):p("",!0)])]),_:1},8,["loading","onRefresh"])}const dt=V(q,[["render",at],["__scopeId","data-v-f62e7333"]]);export{dt as default};
