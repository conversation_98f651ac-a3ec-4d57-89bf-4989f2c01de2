import{r as m,c as J,x as Q,b as n,l as g,j as e,e as l,t as i,s as D,n as _,F as w,p as h,h as T,A as M,B as R,H as W,C as X,u as Y,q as Z,o}from"./vendor.js";import{u as tt}from"./recruiting.js";import{S as d}from"./StandardButton.js";import{_ as et,H as C}from"./app.js";const at={class:"job-posting-view"},st={key:0,class:"flex justify-center py-12"},rt={key:1,class:"space-y-6"},it={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},ot={class:"flex items-center justify-between mb-4"},nt={class:"flex items-center space-x-3"},lt={class:"text-2xl font-bold text-gray-900 dark:text-white"},dt={class:"text-gray-600 dark:text-gray-400 flex items-center"},ut={class:"flex items-center space-x-3"},ct={key:0,class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},gt={class:"flex items-center space-x-3"},pt={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},xt={class:"lg:col-span-2 space-y-6"},yt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},mt={class:"text-sm text-gray-900 dark:text-white whitespace-pre-wrap bg-gray-50 dark:bg-gray-700 rounded-md p-4"},vt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},bt={class:"text-sm text-gray-900 dark:text-white whitespace-pre-wrap bg-gray-50 dark:bg-gray-700 rounded-md p-4"},ft={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},kt={class:"flex items-center justify-between mb-4"},_t={class:"text-lg font-medium text-gray-900 dark:text-white"},wt={key:0,class:"space-y-3"},ht=["onClick"],Ct={class:"flex items-center justify-between"},jt={class:"flex items-center space-x-3"},Pt={class:"flex-shrink-0 h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},zt={class:"text-xs font-medium text-gray-700 dark:text-gray-300"},St={class:"text-sm font-medium text-gray-900 dark:text-white"},Et={class:"text-xs text-gray-500 dark:text-gray-400"},$t={class:"flex items-center space-x-3"},At={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Nt={key:1,class:"text-center py-6"},It={class:"space-y-6"},Jt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},Dt={class:"space-y-3"},Tt={class:"flex justify-between"},Mt={class:"text-sm text-gray-900 dark:text-white"},Rt={class:"flex justify-between"},Vt={class:"text-sm text-gray-900 dark:text-white"},Bt={key:0,class:"flex justify-between"},qt={class:"text-sm text-gray-900 dark:text-white"},Lt={key:1,class:"flex justify-between"},Ut={class:"text-sm text-gray-900 dark:text-white"},Ft={key:2,class:"flex justify-between"},Ht={class:"text-sm text-gray-900 dark:text-white"},Ot={class:"flex justify-between"},Gt={key:3,class:"flex justify-between"},Kt={class:"text-sm text-gray-900 dark:text-white"},Qt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},Wt={class:"space-y-3"},Xt={class:"flex justify-between"},Yt={class:"text-sm font-medium text-gray-900 dark:text-white"},Zt={class:"text-sm text-gray-500 dark:text-gray-400"},te={class:"text-sm text-gray-900 dark:text-white"},ee={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},ae={class:"space-y-3"},se={class:"mt-3"},re={class:"space-y-4"},ie=["value"],oe={class:"flex justify-end space-x-3 mt-6"},ne={__name:"JobPostingView",setup(le){const j=Y(),v=Z(),u=tt(),s=m(null),c=m([]),P=m([]),x=m(!1),p=m({candidate_id:"",cover_letter:""}),z=J(()=>u.loading),V=J(()=>{const a={application_received:0,screening:0,interview_1:0,interview_2:0,offer:0};return Array.isArray(c.value)&&c.value.forEach(t=>{t&&t.current_step&&a.hasOwnProperty(t.current_step)&&a[t.current_step]++}),a}),b=async()=>{try{const a=parseInt(j.params.id),t=await u.fetchJobPosting(a);s.value=t}catch(a){console.error("Error loading job posting:",a)}},S=async()=>{try{const a=parseInt(j.params.id),t=await u.fetchJobPostingApplications(a);c.value=t||[]}catch(a){console.error("Error loading applications:",a),c.value=[]}},B=async()=>{try{const a=await u.fetchCandidates();P.value=(a==null?void 0:a.candidates)||[]}catch(a){console.error("Error loading candidates:",a)}},q=async()=>{try{await u.createApplication({...p.value,job_posting_id:s.value.id}),x.value=!1,p.value={candidate_id:"",cover_letter:""},await S()}catch(a){console.error("Error creating application:",a),alert("Errore nella creazione della candidatura")}},L=async()=>{try{await u.updateJobPosting(s.value.id,{is_public:!s.value.is_public}),await b()}catch(a){console.error("Error toggling public status:",a),alert("Errore nell'aggiornamento dello stato pubblico")}},E=async a=>{try{await u.updateJobPosting(s.value.id,{status:a}),await b()}catch(t){console.error("Error updating status:",t),alert("Errore nell'aggiornamento dello stato")}},U=async()=>{try{const a={...s.value,title:`${s.value.title} (Copia)`,status:"draft",is_public:!1};delete a.id,delete a.created_at,delete a.updated_at,await u.createJobPosting(a),v.push("/app/recruiting/job-postings")}catch(a){console.error("Error duplicating job posting:",a),alert("Errore nella duplicazione della posizione")}},F=async()=>{if(confirm(`Eliminare la posizione "${s.value.title}"?`))try{await u.deleteJobPosting(s.value.id),v.push("/app/recruiting/job-postings")}catch(a){console.error("Error deleting job posting:",a),alert("Errore nell'eliminazione della posizione")}},H=()=>{v.push("/app/recruiting/job-postings")},O=a=>a?(a.full_name||"").split(" ").filter(r=>r.length>0).map(r=>r[0]).join("").toUpperCase().substr(0,2):"N/A",$=a=>({draft:"Bozza",active:"Attiva",paused:"In pausa",closed:"Chiusa"})[a]||a,A=a=>({draft:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",active:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",paused:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",closed:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"})[a]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",G=a=>({full_time:"Tempo Pieno",part_time:"Part-time",contract:"Contratto",intern:"Stage"})[a]||a,N=a=>({application_received:"Ricevute",screening:"Screening",interview_1:"Primo Colloquio",interview_2:"Secondo Colloquio",offer:"Offerte"})[a]||a,K=a=>({application_received:"bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200",screening:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",interview_1:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",interview_2:"bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",offer:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"})[a]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",f=a=>a?new Date(a).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",I=a=>a?new Intl.NumberFormat("it-IT").format(a):"";return Q(async()=>{await Promise.all([b(),S(),B()])}),(a,t)=>{var k;return o(),n("div",at,[z.value?(o(),n("div",st,t[11]||(t[11]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary-600"},null,-1)]))):s.value?(o(),n("div",rt,[e("div",it,[e("div",ot,[e("div",nt,[l(d,{variant:"ghost",icon:"arrow-left",onClick:H,size:"sm"}),e("div",null,[e("h1",lt,i(s.value.title),1),e("p",dt,[l(C,{name:"map-pin",size:"xs",class:"mr-1"}),D(" "+i(s.value.location),1)])])]),e("div",ut,[e("span",{class:_(["inline-flex items-center px-3 py-1 rounded-full text-sm font-medium",A(s.value.status)])},i($(s.value.status)),3),s.value.is_public?(o(),n("span",ct,[l(C,{name:"globe-alt",size:"xs",class:"mr-1"}),t[12]||(t[12]=D(" Pubblica "))])):g("",!0)])]),e("div",gt,[l(d,{variant:"primary",icon:"pencil",text:"Modifica",onClick:t[0]||(t[0]=r=>a.$router.push(`/app/recruiting/job-postings/${s.value.id}/edit`))}),l(d,{variant:"outline-primary",icon:s.value.is_public?"eye-slash":"globe-alt",text:s.value.is_public?"Rendi Privata":"Pubblica",onClick:L},null,8,["icon","text"]),l(d,{variant:"secondary",icon:"document-duplicate",text:"Duplica",onClick:U}),l(d,{variant:"outline-danger",icon:"trash",text:"Elimina",onClick:F})])]),e("div",pt,[e("div",xt,[e("div",yt,[t[13]||(t[13]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Descrizione Posizione ",-1)),e("div",mt,i(s.value.description),1)]),e("div",vt,[t[14]||(t[14]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Requisiti ",-1)),e("div",bt,i(s.value.requirements),1)]),e("div",ft,[e("div",kt,[e("h3",_t," Candidature ("+i(c.value.length)+") ",1),l(d,{variant:"outline-primary",icon:"plus",text:"Nuova Candidatura",size:"sm",onClick:t[1]||(t[1]=r=>x.value=!0)})]),c.value&&c.value.length?(o(),n("div",wt,[(o(!0),n(w,null,h(c.value||[],r=>{var y;return o(),n("div",{key:r.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",onClick:de=>a.$router.push(`/app/recruiting/applications/${r.id}`)},[e("div",Ct,[e("div",jt,[e("div",Pt,[e("span",zt,i(O(r.candidate)),1)]),e("div",null,[e("div",St,i(((y=r.candidate)==null?void 0:y.full_name)||"N/A"),1),e("div",Et,i(f(r.applied_at)),1)])]),e("div",$t,[e("span",{class:_(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",K(r.current_step)])},i(N(r.current_step)),3),r.overall_score?(o(),n("span",At,i(r.overall_score)+"/10 ",1)):g("",!0)])])],8,ht)}),128))])):(o(),n("div",Nt,[l(C,{name:"user-group",size:"lg",class:"mx-auto text-gray-400 mb-2"}),t[15]||(t[15]=e("p",{class:"text-sm text-gray-500 dark:text-gray-400"}," Nessuna candidatura per questa posizione ",-1))]))])]),e("div",It,[e("div",Jt,[t[23]||(t[23]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Riepilogo Posizione ",-1)),e("div",Dt,[e("div",Tt,[t[16]||(t[16]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Data Creazione",-1)),e("span",Mt,i(f(s.value.created_at)),1)]),e("div",Rt,[t[17]||(t[17]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Tipo Contratto",-1)),e("span",Vt,i(G(s.value.employment_type)),1)]),s.value.department?(o(),n("div",Bt,[t[18]||(t[18]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Dipartimento",-1)),e("span",qt,i(s.value.department.name),1)])):g("",!0),s.value.project?(o(),n("div",Lt,[t[19]||(t[19]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Progetto",-1)),e("span",Ut,i(s.value.project.name),1)])):g("",!0),s.value.salary_min&&s.value.salary_max?(o(),n("div",Ft,[t[20]||(t[20]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Stipendio",-1)),e("span",Ht," €"+i(I(s.value.salary_min))+" - €"+i(I(s.value.salary_max)),1)])):g("",!0),e("div",Ot,[t[21]||(t[21]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Stato",-1)),e("span",{class:_(["text-sm px-2 py-1 rounded-full",A(s.value.status)])},i($(s.value.status)),3)]),s.value.updated_at?(o(),n("div",Gt,[t[22]||(t[22]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ultimo Aggiornamento",-1)),e("span",Kt,i(f(s.value.updated_at)),1)])):g("",!0)])]),e("div",Qt,[t[25]||(t[25]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Statistiche Candidature ",-1)),e("div",Wt,[e("div",Xt,[t[24]||(t[24]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Totale Candidature",-1)),e("span",Yt,i(((k=c.value)==null?void 0:k.length)||0),1)]),(o(!0),n(w,null,h(V.value,(r,y)=>(o(),n("div",{key:y,class:"flex justify-between"},[e("span",Zt,i(N(y)),1),e("span",te,i(r),1)]))),128))])]),e("div",ee,[t[26]||(t[26]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Azioni Rapide ",-1)),e("div",ae,[l(d,{variant:"outline-primary",icon:"users",text:"Visualizza Tutti i Candidati",block:"",onClick:t[2]||(t[2]=r=>a.$router.push("/app/recruiting/candidates"))}),l(d,{variant:"outline-primary",icon:"document-text",text:"Tutte le Candidature",block:"",onClick:t[3]||(t[3]=r=>a.$router.push(`/app/recruiting/applications?job_posting_id=${s.value.id}`))}),s.value.status==="active"?(o(),T(d,{key:0,variant:"outline-secondary",icon:"pause",text:"Metti in Pausa",block:"",onClick:t[4]||(t[4]=r=>E("paused"))})):s.value.status==="paused"?(o(),T(d,{key:1,variant:"outline-primary",icon:"play",text:"Riattiva",block:"",onClick:t[5]||(t[5]=r=>E("active"))})):g("",!0)])])])])])):g("",!0),x.value?(o(),n("div",{key:2,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:t[10]||(t[10]=r=>x.value=!1)},[e("div",{class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[9]||(t[9]=M(()=>{},["stop"]))},[e("div",se,[t[30]||(t[30]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Nuova Candidatura",-1)),e("form",{onSubmit:M(q,["prevent"])},[e("div",re,[e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Candidato * ",-1)),R(e("select",{"onUpdate:modelValue":t[6]||(t[6]=r=>p.value.candidate_id=r),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[27]||(t[27]=e("option",{value:""},"Seleziona candidato",-1)),(o(!0),n(w,null,h(P.value,r=>(o(),n("option",{key:r.id,value:r.id},i(r.full_name)+" - "+i(r.email),9,ie))),128))],512),[[W,p.value.candidate_id]])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Lettera di Motivazione ",-1)),R(e("textarea",{"onUpdate:modelValue":t[7]||(t[7]=r=>p.value.cover_letter=r),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Motivazione del candidato..."},null,512),[[X,p.value.cover_letter]])])]),e("div",oe,[l(d,{variant:"secondary",text:"Annulla",onClick:t[8]||(t[8]=r=>x.value=!1)}),l(d,{variant:"primary",text:"Crea Candidatura",loading:z.value,type:"submit"},null,8,["loading"])])],32)])])])):g("",!0)])}}},xe=et(ne,[["__scopeId","data-v-aa506129"]]);export{xe as default};
