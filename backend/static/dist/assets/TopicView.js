import{b as i,o,j as e,l as p,F as S,p as z,n as V,t as u,e as b,f as H,r as y,c as M,x as q,h as C,v as j,s as w,A as Z,B as W,C as G,u as J,q as K}from"./vendor.js";import{_ as Q,i as X,a as E,e as U,H as x}from"./app.js";import{u as Y}from"./useFormatters.js";import{E as ee}from"./EditTopicModal.js";import{C as te}from"./ConfirmationModal.js";import"./formatters.js";function se(){const a=r=>{if(!r)return"";let d=r.replace(/\n/g,"<br>").replace(/\r\n/g,"<br>").replace(/\r/g,"<br>");const c=/(https?:\/\/[^\s]+)/g;d=d.replace(c,'<a href="$1" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:text-blue-800 underline">$1</a>');const h=/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;return d=d.replace(h,'<a href="mailto:$1" class="text-blue-600 hover:text-blue-800 underline">$1</a>'),d},t=r=>r?r.replace(/<[^>]*>/g,""):"";return{formatContent:a,stripHtml:t,truncateContent:(r,d=150)=>{if(!r)return"";const c=t(r);return c.length<=d?c:c.substring(0,d)+"..."}}}const re={name:"ReactionBar",components:{LoadingSpinner:X},props:{itemType:{type:String,required:!0,validator:a=>["topic","comment","poll","news","event"].includes(a)},itemId:{type:[String,Number],required:!0},reactions:{type:Array,default:()=>[]}},data(){return{isLoading:!1,availableReactions:[{type:"like",emoji:"👍",label:"Mi piace"},{type:"love",emoji:"❤️",label:"Amo"},{type:"laugh",emoji:"😂",label:"Divertente"},{type:"sad",emoji:"😢",label:"Triste"},{type:"angry",emoji:"😠",label:"Arrabbiato"}]}},computed:{currentUser(){return E().user},userReaction(){const a=E();if(!a.user||!this.reactions)return null;const t=this.reactions.find(l=>l.user_id===a.user.id);return t?t.reaction_type:null},totalReactions(){return this.reactions?this.reactions.length:0},reactionsWithUsers(){if(!this.reactions)return[];const a=this.reactions.reduce((t,l)=>{const r=l.reaction_type;return t[r]||(t[r]={type:r,count:0,users:[]}),t[r].count++,t[r].users.push(l.user),t},{});return Object.values(a).sort((t,l)=>l.count-t.count)},recentReactors(){if(!this.reactions)return[];const a=[],t=new Set;for(const l of this.reactions.slice().reverse())t.has(l.user_id)||(a.push(l.user),t.add(l.user_id));return a}},methods:{getReactionCount(a){return this.reactions?this.reactions.filter(t=>t.reaction_type===a).length:0},getReactionEmoji(a){const t=this.availableReactions.find(l=>l.type===a);return t?t.emoji:"👍"},getReactionLabel(a){const t=this.availableReactions.find(l=>l.type===a);return t?t.label:a},async toggleReaction(a){const t=E();if(!(this.isLoading||!t.user)){this.isLoading=!0;try{const l=U();if(this.userReaction===a){const r=this.reactions.find(d=>d.user_id===t.user.id&&d.reaction_type===a);r&&await l.removeReaction(this.itemType,this.itemId,r.id)}else await l.addReaction(this.itemType,this.itemId,a);this.$emit("reaction-updated")}catch(l){console.error("Error toggling reaction:",l)}finally{this.isLoading=!1}}}}},oe={class:"reaction-bar flex items-center space-x-4 py-3 border-t dark:border-gray-600"},ae={class:"flex items-center space-x-2"},ne=["onClick","disabled"],ie={class:"text-lg"},le={key:0,class:"text-xs"},ce={key:0,class:"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400"},de={class:"relative group"},ue={class:"absolute bottom-full left-0 mb-2 w-64 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10"},me={class:"space-y-2"},ge={class:"flex items-center space-x-2"},pe={class:"text-lg"},xe={class:"text-sm font-medium text-gray-700 dark:text-gray-300"},be={class:"text-sm text-gray-500 dark:text-gray-400"},fe={key:0,class:"border-t dark:border-gray-600 pt-2 mt-2"},ye={class:"flex flex-wrap gap-1"},ve={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},he={key:1,class:"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400"};function _e(a,t,l,r,d,c){const h=H("LoadingSpinner");return o(),i("div",oe,[e("div",ae,[(o(!0),i(S,null,z(d.availableReactions,m=>(o(),i("button",{key:m.type,onClick:_=>c.toggleReaction(m.type),class:V(["flex items-center space-x-1 px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200",c.userReaction===m.type?"bg-blue-100 text-blue-700 border border-blue-300 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-700":"bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 dark:border-gray-600"]),disabled:d.isLoading},[e("span",ie,u(m.emoji),1),c.getReactionCount(m.type)>0?(o(),i("span",le,u(c.getReactionCount(m.type)),1)):p("",!0)],10,ne))),128))]),c.totalReactions>0?(o(),i("div",ce,[e("span",null,u(c.totalReactions)+" "+u(c.totalReactions===1?"reazione":"reazioni"),1),e("div",de,[t[1]||(t[1]=e("button",{class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline"}," Dettagli ",-1)),e("div",ue,[e("div",me,[(o(!0),i(S,null,z(c.reactionsWithUsers,m=>(o(),i("div",{key:m.type,class:"flex items-center justify-between"},[e("div",ge,[e("span",pe,u(c.getReactionEmoji(m.type)),1),e("span",xe,u(c.getReactionLabel(m.type)),1)]),e("span",be,u(m.count),1)]))),128)),c.recentReactors.length>0?(o(),i("div",fe,[t[0]||(t[0]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400 mb-1"},"Utenti che hanno reagito:",-1)),e("div",ye,[(o(!0),i(S,null,z(c.recentReactors.slice(0,5),m=>(o(),i("span",{key:m.id,class:"inline-block px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs rounded-full text-gray-700 dark:text-gray-300"},u(m.name),1))),128)),c.recentReactors.length>5?(o(),i("span",ve," +"+u(c.recentReactors.length-5)+" altri ",1)):p("",!0)])])):p("",!0)])])])])):p("",!0),d.isLoading?(o(),i("div",he,[b(h,{size:"sm"}),t[2]||(t[2]=e("span",null,"Aggiornamento...",-1))])):p("",!0)])}const ke=Q(re,[["render",_e],["__scopeId","data-v-6d260275"]]),we={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},Re={key:0,class:"flex justify-center items-center h-64"},Ce={key:1,class:"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4"},Se={class:"flex"},ze={class:"ml-3"},Ee={class:"text-sm text-red-800 dark:text-red-200"},Te={key:2,class:"space-y-6"},Me={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},je={class:"flex items-start justify-between"},$e={class:"flex-1"},Le={class:"flex items-center space-x-2 mb-2"},Ae={class:"text-2xl font-bold text-gray-900 dark:text-white"},Ue={class:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},Ie={class:"flex items-center space-x-1"},De={class:"flex items-center space-x-1"},Oe={class:"flex items-center space-x-1"},Ne={class:"flex items-center space-x-1"},Pe={class:"flex items-center space-x-2"},Be={class:"mt-6 prose prose-lg max-w-none dark:prose-invert"},Fe=["innerHTML"],Ve={class:"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700"},He={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},qe={class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},Ze={key:0,class:"mb-6"},We={class:"flex justify-end"},Ge=["disabled"],Je={class:"space-y-4"},Ke={class:"flex items-start space-x-3"},Qe={class:"flex-shrink-0"},Xe={class:"w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center"},Ye={class:"flex-1"},et={class:"flex items-center justify-between"},tt={class:"text-sm font-medium text-gray-900 dark:text-white"},st={class:"text-xs text-gray-500 dark:text-gray-400"},rt={class:"mt-1 text-sm text-gray-700 dark:text-gray-300"},ot={key:1,class:"text-center py-8"},ut={__name:"TopicView",setup(a){const t=J(),l=K(),r=U(),d=E(),{formatDate:c}=Y(),{formatContent:h}=se(),m=y(!0),_=y(""),s=y(null),k=y([]),v=y(""),R=y(!1),T=y(!1),I=M(()=>!s.value||!d.user?!1:s.value.author_id===d.user.id||d.hasPermission("PERMISSION_MODERATE_FORUM")),D=M(()=>d.hasPermission("PERMISSION_MODERATE_FORUM")),$=M(()=>d.hasPermission("PERMISSION_MODERATE_FORUM")),L=async()=>{m.value=!0,_.value="";try{const g=t.params.id;s.value=await r.fetchTopic(g),await r.fetchTopicComments(g),k.value=r.comments,await r.incrementTopicViews(g)}catch(g){_.value="Errore nel caricamento del topic",console.error("Error loading topic:",g)}finally{m.value=!1}},O=async()=>{if(v.value.trim())try{await r.createTopicComment(s.value.id,{content:v.value}),v.value="",k.value=r.comments}catch(g){console.error("Errore nell'aggiunta del commento:",g)}},N=async()=>{try{await r.toggleTopicPin(s.value.id),s.value.is_pinned=!s.value.is_pinned}catch(g){console.error("Errore nel toggle pin:",g)}},P=async()=>{try{await r.toggleTopicLock(s.value.id),s.value.is_locked=!s.value.is_locked}catch(g){console.error("Errore nel toggle lock:",g)}},B=g=>{s.value=g,R.value=!1},F=async()=>{try{await r.deleteTopic(s.value.id),l.push("/app/communications/forum")}catch(g){console.error("Errore nell'eliminazione del topic:",g)}};return q(()=>{L()}),(g,n)=>{var A;return o(),i("div",we,[m.value?(o(),i("div",Re,n[5]||(n[5]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):_.value?(o(),i("div",Ce,[e("div",Se,[b(x,{name:"exclamation-triangle",size:"md",class:"text-red-400"}),e("div",ze,[e("p",Ee,u(_.value),1)])])])):s.value?(o(),i("div",Te,[e("div",Me,[e("div",je,[e("div",$e,[e("div",Le,[s.value.is_pinned?(o(),C(x,{key:0,name:"bookmark",size:"sm",class:"text-yellow-500"})):p("",!0),s.value.is_locked?(o(),C(x,{key:1,name:"lock-closed",size:"sm",class:"text-red-500"})):p("",!0),e("h1",Ae,u(s.value.title),1)]),e("div",Ue,[e("div",Ie,[b(x,{name:"user",size:"xs"}),e("span",null,u(s.value.author_name||"Anonimo"),1)]),e("div",De,[b(x,{name:"tag",size:"xs"}),e("span",null,u(s.value.category||"Generale"),1)]),e("div",Oe,[b(x,{name:"calendar",size:"xs"}),e("span",null,u(j(c)(s.value.created_at)),1)]),e("div",Ne,[b(x,{name:"eye",size:"xs"}),e("span",null,u(s.value.view_count||0)+" visualizzazioni",1)])])]),e("div",Pe,[$.value?(o(),i("button",{key:0,onClick:N,class:"inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[b(x,{name:s.value.is_pinned?"bookmark-slash":"bookmark",size:"xs",class:"mr-1"},null,8,["name"]),w(" "+u(s.value.is_pinned?"Rimuovi Pin":"Fissa"),1)])):p("",!0),$.value?(o(),i("button",{key:1,onClick:P,class:"inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[b(x,{name:s.value.is_locked?"lock-open":"lock-closed",size:"xs",class:"mr-1"},null,8,["name"]),w(" "+u(s.value.is_locked?"Sblocca":"Blocca"),1)])):p("",!0),I.value?(o(),i("button",{key:2,onClick:n[0]||(n[0]=f=>R.value=!0),class:"inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[b(x,{name:"pencil",size:"xs",class:"mr-1"}),n[6]||(n[6]=w(" Modifica "))])):p("",!0),D.value?(o(),i("button",{key:3,onClick:n[1]||(n[1]=f=>T.value=!0),class:"inline-flex items-center px-3 py-1 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},[b(x,{name:"trash",size:"xs",class:"mr-1"}),n[7]||(n[7]=w(" Elimina "))])):p("",!0)])]),e("div",Be,[e("div",{innerHTML:j(h)(s.value.content)},null,8,Fe)]),e("div",Ve,[b(ke,{"item-type":"topic","item-id":s.value.id,reactions:s.value.reactions||[],onReactionUpdated:L},null,8,["item-id","reactions"])])]),e("div",He,[e("h2",qe," Commenti ("+u(k.value.length)+") ",1),s.value.is_locked?p("",!0):(o(),i("div",Ze,[e("form",{onSubmit:Z(O,["prevent"]),class:"space-y-4"},[e("div",null,[n[8]||(n[8]=e("label",{class:"sr-only"},"Aggiungi un commento",-1)),W(e("textarea",{"onUpdate:modelValue":n[2]||(n[2]=f=>v.value=f),rows:"3",class:"block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"Scrivi un commento..."},null,512),[[G,v.value]])]),e("div",We,[e("button",{type:"submit",disabled:!v.value.trim(),class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[b(x,{name:"chat-bubble-left",size:"sm",class:"mr-2"}),n[9]||(n[9]=w(" Commenta "))],8,Ge)])],32)])),e("div",Je,[(o(!0),i(S,null,z(k.value,f=>(o(),i("div",{key:f.id,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},[e("div",Ke,[e("div",Qe,[e("div",Xe,[b(x,{name:"user",size:"sm",class:"text-gray-500"})])]),e("div",Ye,[e("div",et,[e("h4",tt,u(f.author_name||"Anonimo"),1),e("span",st,u(j(c)(f.created_at)),1)]),e("p",rt,u(f.content),1)])])]))),128))]),k.value.length===0?(o(),i("div",ot,[b(x,{name:"chat-bubble-oval-left",size:"xl",class:"mx-auto text-gray-300 mb-4"}),n[10]||(n[10]=e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun commento ancora",-1)),n[11]||(n[11]=e("p",{class:"text-sm text-gray-400 dark:text-gray-500"},"Sii il primo a commentare!",-1))])):p("",!0)])])):p("",!0),R.value?(o(),C(ee,{key:3,topic:s.value,onClose:n[3]||(n[3]=f=>R.value=!1),onUpdated:B},null,8,["topic"])):p("",!0),T.value?(o(),C(te,{key:4,title:"Elimina Topic",message:`Sei sicuro di voler eliminare il topic '${(A=s.value)==null?void 0:A.title}'?`,"confirm-text":"Elimina","confirm-class":"bg-red-600 hover:bg-red-700",onConfirm:F,onCancel:n[4]||(n[4]=f=>T.value=!1)},null,8,["message"])):p("",!0)])}}};export{ut as default};
