import{d as R,r as l,c as m}from"./vendor.js";import{d as _,c as o}from"./app.js";const Z=R("engagement",()=>{const{showToast:r}=_(),c=l([]),s=l([]),d=l(null),g=l([]),u=l([]),t=l(!1),p=l(null),h=l(null),w=l({user_stats:{},active_campaigns:[],recent_points:[],available_rewards:[],leaderboard_position:null}),v=m(()=>c.value.length),y=m(()=>s.value.length),E=m(()=>{var a;return((a=d.value)==null?void 0:a.total_points)||0}),C=m(()=>{var a;return((a=d.value)==null?void 0:a.current_level)||1}),b=m(()=>c.value.filter(a=>a.status==="active")),P=m(()=>s.value.filter(a=>a.is_active&&a.points_required<=E.value));return{campaigns:c,rewards:s,userProfile:d,leaderboard:g,userPoints:u,loading:t,currentCampaign:p,currentReward:h,dashboardData:w,campaignsCount:v,rewardsCount:y,totalPoints:E,currentLevel:C,activeCampaigns:b,availableRewards:P,fetchDashboard:async()=>{t.value=!0;try{const a=await o.get("/api/engagement/dashboard");if(a.data.success)return w.value=a.data.data,d.value=a.data.data.user_profile,a.data.data;throw new Error(a.data.message||"Errore nel caricamento dashboard engagement")}catch(a){throw console.error("Errore caricamento dashboard engagement:",a),r("Errore nel caricamento della dashboard engagement","error"),a}finally{t.value=!1}},fetchCampaigns:async(a={})=>{t.value=!0;try{const e=await o.get("/api/engagement/campaigns",{params:a});if(e.data.success)return c.value=e.data.data.campaigns||[],e.data;throw new Error(e.data.message||"Errore nel caricamento campagne")}catch(e){throw console.error("Errore caricamento campagne:",e),r("Errore nel caricamento delle campagne","error"),e}finally{t.value=!1}},getCampaign:async a=>{t.value=!0;try{const e=await o.get(`/api/engagement/campaigns/${a}`);if(e.data.success)return p.value=e.data.data.campaign,e.data.data.campaign;throw new Error(e.data.message||"Errore nel caricamento campagna")}catch(e){throw console.error("Errore caricamento campagna:",e),r("Errore nel caricamento della campagna","error"),e}finally{t.value=!1}},createCampaign:async a=>{t.value=!0;try{const e=await o.post("/api/engagement/campaigns",a);if(e.data.success)return c.value.unshift(e.data.data.campaign||e.data.data),r("Campagna creata con successo","success"),e.data.data;throw new Error(e.data.message||"Errore nella creazione della campagna")}catch(e){throw console.error("Errore creazione campagna:",e),r("Errore nella creazione della campagna","error"),e}finally{t.value=!1}},updateCampaign:async(a,e)=>{t.value=!0;try{const n=await o.put(`/api/engagement/campaigns/${a}`,e);if(n.data.success){const i=c.value.findIndex(f=>f.id===a);return i!==-1&&(c.value[i]={...c.value[i],...e}),r("Campagna aggiornata con successo","success"),n.data.data}else throw new Error(n.data.message||"Errore nell'aggiornamento della campagna")}catch(n){throw console.error("Errore aggiornamento campagna:",n),r("Errore nell'aggiornamento della campagna","error"),n}finally{t.value=!1}},deleteCampaign:async a=>{t.value=!0;try{const e=await o.delete(`/api/engagement/campaigns/${a}`);if(e.data.success)return c.value=c.value.filter(n=>n.id!==a),r("Campagna eliminata con successo","success"),!0;throw new Error(e.data.message||"Errore nell'eliminazione della campagna")}catch(e){throw console.error("Errore eliminazione campagna:",e),r("Errore nell'eliminazione della campagna","error"),e}finally{t.value=!1}},fetchRewards:async(a={})=>{t.value=!0;try{const e=await o.get("/api/engagement/rewards",{params:a});if(e.data.success)return s.value=e.data.data.rewards||[],e.data;throw new Error(e.data.message||"Errore nel caricamento premi")}catch(e){throw console.error("Errore caricamento premi:",e),r("Errore nel caricamento dei premi","error"),e}finally{t.value=!1}},createReward:async a=>{t.value=!0;try{const e=await o.post("/api/engagement/rewards",a);if(e.data.success)return s.value.unshift(e.data.data.reward||e.data.data),r("Premio creato con successo","success"),e.data.data;throw new Error(e.data.message||"Errore nella creazione del premio")}catch(e){throw console.error("Errore creazione premio:",e),r("Errore nella creazione del premio","error"),e}finally{t.value=!1}},updateReward:async(a,e)=>{t.value=!0;try{const n=await o.put(`/api/engagement/rewards/${a}`,e);if(n.data.success){const i=s.value.findIndex(f=>f.id===a);return i!==-1&&(s.value[i]={...s.value[i],...e}),r("Premio aggiornato con successo","success"),n.data.data}else throw new Error(n.data.message||"Errore nell'aggiornamento del premio")}catch(n){throw console.error("Errore aggiornamento premio:",n),r("Errore nell'aggiornamento del premio","error"),n}finally{t.value=!1}},deleteReward:async a=>{t.value=!0;try{const e=await o.delete(`/api/engagement/rewards/${a}`);if(e.data.success)return s.value=s.value.filter(n=>n.id!==a),r("Premio eliminato con successo","success"),!0;throw new Error(e.data.message||"Errore nell'eliminazione del premio")}catch(e){throw console.error("Errore eliminazione premio:",e),r("Errore nell'eliminazione del premio","error"),e}finally{t.value=!1}},redeemReward:async a=>{t.value=!0;try{const e=await o.post(`/api/engagement/rewards/${a}/redeem`);if(e.data.success){if(d.value){const n=s.value.find(i=>i.id===a);n&&(d.value.total_points-=n.points_required)}return r("Premio riscattato con successo","success"),e.data.data}else throw new Error(e.data.message||"Errore nel riscatto del premio")}catch(e){throw console.error("Errore riscatto premio:",e),r("Errore nel riscatto del premio","error"),e}finally{t.value=!1}},fetchUserPoints:async(a={})=>{try{const e=await o.get("/api/engagement/points",{params:a});if(e.data.success)return u.value=e.data.data.points||[],e.data;throw new Error(e.data.message||"Errore nel caricamento punti")}catch(e){throw console.error("Errore caricamento punti:",e),r("Errore nel caricamento dei punti","error"),e}},fetchLeaderboard:async(a={})=>{t.value=!0;try{const e=await o.get("/api/engagement/leaderboard",{params:a});if(e.data.success)return g.value=e.data.data.users||[],e.data;throw new Error(e.data.message||"Errore nel caricamento classifica")}catch(e){throw console.error("Errore caricamento classifica:",e),r("Errore nel caricamento della classifica","error"),e}finally{t.value=!1}},fetchAnalytics:async(a={})=>{t.value=!0;try{const e=await o.get("/api/engagement/analytics",{params:a});if(e.data.success)return e.data.data;throw new Error(e.data.message||"Errore nel caricamento analytics")}catch(e){throw console.error("Errore caricamento analytics:",e),r("Errore nel caricamento delle analytics","error"),e}finally{t.value=!1}},fetchAdminStats:async()=>{try{const a=await o.get("/api/engagement/admin/stats");if(a.data.success)return a.data.data;throw new Error(a.data.message||"Errore nel caricamento statistiche admin")}catch(a){throw console.error("💥 [Engagement] Admin stats error:",a),r("Errore nel caricamento delle statistiche","error"),a}},fetchAdminCampaigns:async(a={})=>{t.value=!0;try{const e=await o.get("/api/engagement/admin/campaigns",{params:a});if(e.data.success){const n=e.data.data.campaigns||[];return c.value=n,e.data.data}else throw new Error(e.data.message||"Errore nel caricamento campagne admin")}catch(e){throw console.error("💥 [Engagement] Admin campaigns error:",e),r("Errore nel caricamento delle campagne","error"),e}finally{t.value=!1}},fetchAdminRewards:async(a={})=>{t.value=!0;try{const e=await o.get("/api/engagement/admin/rewards",{params:a});if(e.data.success){const n=e.data.data.rewards||[];return s.value=n,e.data.data}else throw new Error(e.data.message||"Errore nel caricamento premi admin")}catch(e){throw console.error("💥 [Engagement] Admin rewards error:",e),r("Errore nel caricamento dei premi","error"),e}finally{t.value=!1}},fetchPointsConfig:async()=>{try{const a=await o.get("/api/engagement/admin/points/config");if(a.data.success)return a.data.data;throw new Error(a.data.message||"Errore nel caricamento configurazione punti")}catch(a){throw console.error("💥 [Engagement] Points config error:",a),r("Errore nel caricamento della configurazione","error"),a}},generatePoints:async(a={})=>{try{const e=await o.post("/api/engagement/admin/points/generate",a);if(e.data.success)return r("Punti generati con successo","success"),e.data.data;throw new Error(e.data.message||"Errore nella generazione punti")}catch(e){throw console.error("💥 [Engagement] Generate points error:",e),r("Errore nella generazione dei punti","error"),e}},recalculatePoints:async(a={})=>{try{const e=await o.post("/api/engagement/admin/points/recalculate",a);if(e.data.success)return r("Punti ricalcolati con successo","success"),e.data.data;throw new Error(e.data.message||"Errore nel ricalcolo punti")}catch(e){throw console.error("💥 [Engagement] Recalculate points error:",e),r("Errore nel ricalcolo dei punti","error"),e}},fetchCampaignLeaderboard:async a=>{try{const e=await o.get(`/api/engagement/campaigns/${a}/leaderboard`);if(e.data.success)return e.data.data;throw new Error(e.data.message||"Errore nel caricamento classifica campagna")}catch(e){throw console.error("💥 [Engagement] Campaign leaderboard error:",e),r("Errore nel caricamento della classifica","error"),e}},fetchCampaignRewards:async a=>{try{const e=await o.get(`/api/engagement/campaigns/${a}/rewards`);if(e.data.success)return e.data.data;throw new Error(e.data.message||"Errore nel caricamento premi campagna")}catch(e){throw console.error("💥 [Engagement] Campaign rewards error:",e),r("Errore nel caricamento dei premi","error"),e}},fetchUserCampaignData:async a=>{try{const e=await o.get(`/api/engagement/campaigns/${a}/user-data`);if(e.data.success)return e.data.data;throw new Error(e.data.message||"Errore nel caricamento dati utente")}catch(e){throw console.error("💥 [Engagement] User campaign data error:",e),r("Errore nel caricamento dei dati utente","error"),e}},reset:()=>{c.value=[],s.value=[],d.value=null,g.value=[],u.value=[],p.value=null,h.value=null,w.value={user_stats:{},active_campaigns:[],recent_points:[],available_rewards:[],leaderboard_position:null}}}});export{Z as u};
