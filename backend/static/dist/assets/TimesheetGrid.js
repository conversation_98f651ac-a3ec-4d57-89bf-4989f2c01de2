import{r as Q,G as X,c as T,b as n,o,j as a,l as u,t as c,K as $,n as _,e as C,s as I,F as S,p as E,B as be,C as xe,D as K,A as me,z as pe}from"./vendor.js";import{_ as fe,H as j}from"./app.js";const he={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ke={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},ve={class:"flex items-center justify-between"},we={class:"text-lg font-medium text-gray-900 dark:text-white"},_e={class:"flex items-center space-x-3"},Te={key:0,class:"flex items-center space-x-2"},$e=["disabled"],Ce=["disabled"],je={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Ie={key:0},Fe={key:1,class:"ml-2"},Oe={key:2,class:"ml-3"},Se={key:0,class:"flex justify-center py-8"},Ee={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4 m-6"},ze={class:"text-red-600"},Ne={key:2,class:"p-6"},Ve={class:"overflow-x-auto"},He={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Ae={class:"bg-gray-50 dark:bg-gray-700"},De={class:"px-2 py-1.5 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky left-0 bg-gray-50 dark:bg-gray-700 min-w-[140px] max-w-[160px] w-[150px] z-20 border-r border-gray-200 dark:border-gray-600"},Pe={class:"flex flex-col leading-none"},Be={class:"text-xs"},Ke={key:0,class:"text-[10px] text-gray-400 font-normal"},Me={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Le={class:"px-2 py-1.5 sticky left-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 min-w-[140px] max-w-[160px] w-[150px] z-10"},We={class:"relative group"},Ge=["title"],Re=["title"],qe=["title"],Ue={class:"absolute z-50 invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-gray-900 text-white text-xs rounded-lg px-3 py-2 -top-2 left-full ml-2 min-w-[200px] max-w-[300px] shadow-xl border border-gray-700"},Je={class:"font-medium"},Qe={key:0,class:"text-gray-300 mt-1"},Xe={key:1,class:"text-gray-300 mt-1"},Ye=["onClick"],Ze={key:0,class:"relative"},et=["onUpdate:modelValue","onInput","onKeydown"],tt={key:0,class:"absolute -bottom-6 left-0 right-0 text-[10px] text-red-500 text-center"},rt={key:1,class:"flex flex-col items-center"},st={class:"text-xs font-medium text-brand-primary-600 dark:text-brand-primary-400"},at={key:0,class:"flex space-x-1 mt-1"},nt=["title"],ot={key:2,class:"text-gray-300 dark:text-gray-600"},lt={class:"px-1 py-1 text-center bg-gray-50 dark:bg-gray-700 min-w-[42px] max-w-[45px] w-[43px]"},it={class:"text-sm font-medium text-gray-900 dark:text-white"},dt={key:0,class:"text-[10px] text-blue-600 dark:text-blue-400"},ct={key:0,class:"bg-gray-100 dark:bg-gray-600 font-medium"},ut={key:0,class:"text-[10px] text-blue-600 dark:text-blue-400"},gt={class:"px-1 py-1.5 text-center text-xs font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-600 min-w-[42px] max-w-[45px] w-[43px]"},yt={key:0,class:"text-[10px] text-blue-600 dark:text-blue-400"},bt={key:3,class:"text-center py-8"},xt={class:"text-gray-500 dark:text-gray-400"},mt={key:4,class:"bg-primary-50 dark:bg-primary-900 border-t border-primary-200 dark:border-primary-700 p-4"},pt={class:"flex items-center justify-between"},ft={class:"flex items-center space-x-4"},ht={class:"text-sm"},kt={class:"font-medium text-blue-900 dark:text-blue-100"},vt={class:"text-blue-700 dark:text-blue-300 ml-2"},wt={class:"flex space-x-2"},_t=["disabled"],Tt={key:5,class:"mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"},$t={class:"grid grid-cols-2 md:grid-cols-4 gap-3 text-xs text-gray-600 dark:text-gray-400"},Ct={key:0,class:"flex items-center space-x-2"},jt={key:1,class:"flex items-center space-x-2"},It={key:2,class:"flex items-center space-x-2"},Ft={key:3,class:"flex items-center space-x-2"},Ot={key:6,class:"border-t border-gray-200 dark:border-gray-600"},St={__name:"TimesheetGrid",props:{title:{type:String,default:"Griglia Timesheet"},tasks:{type:Array,required:!0},days:{type:Array,required:!0},dailyTotals:{type:Object,default:()=>({})},grandTotal:{type:Number,default:0},rowHeaderLabel:{type:String,default:"Task"},emptyMessage:{type:String,default:"Nessun task trovato"},loading:{type:Boolean,default:!1},error:{type:String,default:""},editable:{type:Boolean,default:!1},showStats:{type:Boolean,default:!0},showDayTotals:{type:Boolean,default:!0},showIndicators:{type:Boolean,default:!0},showLegend:{type:Boolean,default:!0},status:{type:String,default:""}},emits:["cell-click","bulk-save","bulk-cancel"],setup(i,{emit:Y}){const l=i,z=Y,g=Q(!1),y=X({}),x=X({}),w=Q(!1),M=T(()=>!l.tasks||!Array.isArray(l.tasks)?0:l.tasks.reduce((e,r)=>e+G(r),0)),L=T(()=>!l.tasks||!Array.isArray(l.tasks)?0:l.tasks.reduce((e,r)=>e+l.days.reduce((t,s)=>{const d=h(r,s),b=V(r,s);return t+(b&&d?parseFloat(d):0)},0),0)),N=T(()=>Object.keys(y).length>0),W=T(()=>Object.values(y).filter(e=>e&&parseFloat(e)>0).length),F=T(()=>Object.values(y).reduce((e,r)=>e+parseFloat(r||0),0)),Z=T(()=>l.grandTotal+F.value),h=(e,r)=>{const t=r.key||r.day;return e.daily_hours&&e.daily_hours[t]?parseFloat(e.daily_hours[t]||0):e.hours&&e.hours[t]?parseFloat(e.hours[t]||0):0},V=(e,r)=>{const t=r.key||r.day;return e.daily_billing&&e.daily_billing[t]!==void 0?e.daily_billing[t]:e.billing&&e.billing[t]!==void 0?e.billing[t]:null},G=e=>{if(e.total_hours!==void 0)return parseFloat(e.total_hours||0);if(e.total!==void 0)return parseFloat(e.total||0);const r=e.daily_hours||e.hours||{};return Object.values(r).reduce((t,s)=>t+parseFloat(s||0),0)},R=e=>{const r=G(e),t=H(e);return r+t},H=e=>l.days.reduce((r,t)=>{const s=`${e.id}-${t.key}`,d=y[s];return r+parseFloat(d||0)},0),ee=e=>{const r=e.key||e.day;return l.dailyTotals&&l.dailyTotals[r]?parseFloat(l.dailyTotals[r]||0):l.tasks.reduce((t,s)=>t+h(s,e),0)},te=e=>{const r=ee(e),t=A(e);return r+t},A=e=>l.tasks.reduce((r,t)=>{const s=`${t.id}-${e.key}`,d=y[s];return r+parseFloat(d||0)},0),k=e=>parseFloat(e||0).toFixed(1),D=(e,r)=>e?e.length<=r?e:e.substring(0,r-3)+"...":"",re=e=>{let r=e.name||e.title||"Task senza nome";return e.subtitle&&(r+=` - ${e.subtitle}`),e.workers&&e.workers.length&&(r+=` (${e.workers.join(", ")})`),r},se=()=>{g.value&&N.value?O():g.value?P():ae()},ae=()=>{console.log("🖊️ Avvio modalità edit massiva"),console.log("📋 Tasks disponibili:",l.tasks.length,l.tasks),console.log("📅 Giorni disponibili:",l.days.length,l.days),l.tasks.length>0&&(console.log("🔍 Primo task esempio:",l.tasks[0]),console.log("🔍 Chiavi task disponibili:",Object.keys(l.tasks[0]))),l.days.length>0&&(console.log("🔍 Primo giorno esempio:",l.days[0]),console.log("🔍 Chiavi giorno disponibili:",Object.keys(l.days[0]))),g.value=!0,l.tasks.forEach(e=>{l.days.forEach(r=>{const t=`${e.id}-${r.key}`,s=h(e,r);s>0&&(console.log(`💾 Inizializzando ${t} = ${s}`),y[t]=s.toString())})}),console.log("💾 Pending changes inizializzati:",Object.keys(y).length)},P=()=>{g.value=!1,Object.keys(y).forEach(e=>{delete y[e]}),Object.keys(x).forEach(e=>{delete x[e]}),z("bulk-cancel")},O=async()=>{if(console.log("💾 Tentativo salvataggio bulk..."),console.log("📝 Pending changes:",y),!oe()){console.log("❌ Validazione fallita");return}w.value=!0;try{const e=[];Object.entries(y).forEach(([r,t])=>{if(console.log(`🔍 Processando ${r} = ${t}`),t&&parseFloat(t)>0){const s=r.split("-");let d=-1;for(let f=0;f<s.length;f++)if(s[f].length===4&&parseInt(s[f])>=2e3){d=f;break}if(d===-1){console.warn(`❌ Impossibile parsare la chiave: ${r}`);return}const b=s.slice(0,d).join("-"),m=s.slice(d).join("-"),p=l.tasks.find(f=>f.id.toString()===b),v=l.days.find(f=>f.key===m);if(console.log(`🔍 TaskId: ${b}, DayKey: ${m}`),console.log("🔍 Task trovato:",p?p.name:"NON TROVATO"),console.log("🔍 Day trovato:",v?v.date:"NON TROVATO"),p&&v){const f={taskId:p.taskId||p.task_id||null,taskName:p.name||p.title,projectId:p.projectId||p.project_id,dayKey:m,date:v.date,hours:parseFloat(t),originalHours:h(p,v)};console.log("✅ Aggiungendo change:",f),e.push(f)}}}),console.log(`💾 Preparate ${e.length} modifiche per il salvataggio`),z("bulk-save",e),g.value=!1,Object.keys(y).forEach(r=>{delete y[r]}),Object.keys(x).forEach(r=>{delete x[r]})}catch(e){console.error("Errore durante il salvataggio:",e)}finally{w.value=!1}},ne=(e,r,t)=>{const s=`${e.id}-${r.key}`;!t||t===""||parseFloat(t)===0?(delete y[s],delete x[s]):(y[s]=t,q(s,t))},q=(e,r)=>{const t=parseFloat(r);return isNaN(t)||t<0?(x[e]="Ore non valide",!1):t>24?(x[e]="Max 24 ore",!1):(delete x[e],!0)},oe=()=>{let e=!0;return Object.entries(y).forEach(([r,t])=>{q(r,t)||(e=!1)}),e},U=(e,r)=>{const t=`${e.id}-${r.key}`;return x[t]},le=(e,r)=>{const t=`${e.id}-${r.key}`,s=y[t]!==void 0;if(x[t])return"bg-red-50 dark:bg-red-900";if(s){const b=h(e,r),m=parseFloat(y[t]||0);if(b===0&&m>0)return"bg-green-50 dark:bg-green-900";if(b!==m)return"bg-primary-50 dark:bg-primary-900"}return""},ie=(e,r)=>{const t=`${e.id}-${r.key}`;if(x[t])return"border-red-500 focus:ring-red-500 focus:border-red-500";const d=h(e,r),b=parseFloat(y[t]||0);return d===0&&b>0?"border-green-500 focus:ring-green-500 focus:border-green-500":d!==b?"border-blue-500 focus:ring-blue-500 focus:border-blue-500":""},J=(e,r)=>{const t=l.tasks.findIndex(m=>m.id===e.id),s=l.days.findIndex(m=>m.key===r.key);let d=t,b=s+1;b>=l.days.length&&(b=0,d=t+1),d>=l.tasks.length&&(d=0),pe(()=>{const m=l.tasks[d],p=l.days[b],v=`input-${m.id}-${p.key}`;refs[v]&&refs[v][0]&&refs[v][0].focus()})},de=(e,r)=>{const t=`${e.id}-${r.key}`,s=h(e,r);s>0?y[t]=s.toString():delete y[t],delete x[t]},B=e=>{if(e.isWeekend!==void 0)return e.isWeekend;if(e.isHoliday!==void 0)return e.isHoliday;if(e.date){const s=new Date(e.date).getDay();return s===0||s===6}const r=(e.label||"").toLowerCase();return r.includes("sab")||r.includes("dom")||r.includes("sat")||r.includes("sun")},ce=(e,r)=>{z("cell-click",e,r)},ue=e=>{const r=typeof e=="string"?e:(e==null?void 0:e.status)||"pending";return{pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",approved:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",rejected:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",submitted:"bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200"}[r]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"},ge=e=>{const r=typeof e=="string"?e:(e==null?void 0:e.status)||"pending";return{pending:"clock",approved:"check-circle",rejected:"x-circle",submitted:"paper-airplane"}[r]||"information-circle"},ye=e=>{const r=typeof e=="string"?e:(e==null?void 0:e.status)||"pending",t={pending:"In Attesa",approved:"Approvato",rejected:"Rifiutato",submitted:"Inviato"};if(typeof e=="object"&&e){const s=t[r]||r,d=e.totalHours||0,b=e.pendingChanges||0;return b>0?`${s} (${d}h, ${b} modifiche)`:`${s} (${d}h)`}return t[r]||r};return(e,r)=>(o(),n("div",he,[a("div",ke,[a("div",ve,[a("h3",we,c(i.title),1),a("div",_e,[i.editable?(o(),n("div",Te,[a("button",{onClick:r[0]||(r[0]=t=>g.value?O():se()),class:_(["px-3 py-1 text-sm font-medium rounded-md transition-colors",g.value?"bg-green-600 text-white hover:bg-green-700":"bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"]),disabled:g.value&&w.value},[C(j,{name:g.value?"check":"pencil",size:"xs",class:"mr-1"},null,8,["name"]),I(" "+c(g.value?w.value?"Salvando...":"Salva Tutto":"Modifica Massiva"),1)],10,$e),g.value&&N.value?(o(),n("button",{key:0,onClick:O,class:"px-3 py-1 text-sm font-medium bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors",disabled:w.value},[C(j,{name:"check-circle",size:"xs",class:"mr-1"}),I(" "+c(w.value?"Salvando...":`Salva ${W.value} modifiche`),1)],8,Ce)):u("",!0),g.value?(o(),n("button",{key:1,onClick:P,class:"px-3 py-1 text-sm font-medium bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"},[C(j,{name:"x-mark",size:"xs",class:"mr-1"}),r[1]||(r[1]=I(" Annulla "))])):u("",!0)])):u("",!0),i.showStats?(o(),n("div",je,[M.value>0?(o(),n("span",Ie,"Totale: "+c(k(M.value)),1)):u("",!0),L.value>0?(o(),n("span",Fe,"| Fatturabili: "+c(k(L.value)),1)):u("",!0)])):u("",!0),i.status?(o(),n("div",Oe,[a("span",{class:_(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",ue(i.status)])},[C(j,{name:ge(i.status),size:"xs",class:"mr-1"},null,8,["name"]),I(" "+c(ye(i.status)),1)],2)])):u("",!0),$(e.$slots,"header-actions",{},void 0,!0)])])]),i.loading?(o(),n("div",Se,r[2]||(r[2]=[a("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary-600"},null,-1)]))):u("",!0),i.error?(o(),n("div",Ee,[a("p",ze,c(i.error),1)])):u("",!0),!i.loading&&i.tasks&&i.tasks.length>0?(o(),n("div",Ne,[a("div",Ve,[a("table",He,[a("thead",Ae,[a("tr",null,[a("th",De,c(i.rowHeaderLabel),1),(o(!0),n(S,null,E(i.days,t=>(o(),n("th",{key:t.key,class:_(["px-1 py-1.5 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-tighter min-w-[42px] max-w-[45px] w-[43px]",{"bg-primary-50 dark:bg-primary-900":t.isToday,"bg-gray-100 dark:bg-gray-600":B(t)}])},[a("div",Pe,[a("span",Be,c(t.day||t.label),1),t.sublabel?(o(),n("span",Ke,c(t.sublabel),1)):u("",!0)])],2))),128)),r[3]||(r[3]=a("th",{class:"px-1 py-1.5 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700 min-w-[45px] max-w-[48px] w-[46px]"}," Tot ",-1))])]),a("tbody",Me,[(o(!0),n(S,null,E(i.tasks,t=>(o(),n("tr",{key:t.id},[a("td",Le,[$(e.$slots,"row-header",{task:t},()=>[a("div",We,[a("div",{class:"text-xs font-medium text-gray-900 dark:text-white truncate cursor-help leading-tight",title:re(t)},c(D(t.name||t.title,20)),9,Ge),t.workers&&t.workers.length?(o(),n("div",{key:0,class:"text-xs text-gray-500 dark:text-gray-400 truncate cursor-help leading-tight",title:t.workers.join(", ")},c(D(t.workers.join(", "),22)),9,Re)):t.subtitle?(o(),n("div",{key:1,class:"text-xs text-gray-500 dark:text-gray-400 truncate cursor-help leading-tight",title:t.subtitle},c(D(t.subtitle,22)),9,qe)):u("",!0),a("div",Ue,[a("div",Je,c(t.name||t.title),1),t.subtitle?(o(),n("div",Qe,c(t.subtitle),1)):u("",!0),t.workers&&t.workers.length?(o(),n("div",Xe,[r[4]||(r[4]=a("span",{class:"font-medium"},"Team:",-1)),I(" "+c(t.workers.join(", ")),1)])):u("",!0),r[5]||(r[5]=a("div",{class:"absolute top-2 -left-1 w-2 h-2 bg-gray-900 transform rotate-45 border-l border-b border-gray-700"},null,-1))])])],!0)]),(o(!0),n(S,null,E(i.days,s=>(o(),n("td",{key:s.key,class:_(["px-0.5 py-1 text-center min-w-[42px] max-w-[45px] w-[43px] relative",[{"bg-primary-50 dark:bg-primary-900":s.isToday},B(s)?"bg-gray-100 dark:bg-gray-600":"",g.value?"cursor-text":i.editable?"cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700":"",le(t,s)]]),onClick:d=>!g.value&&i.editable&&ce(t,s)},[g.value?(o(),n("div",Ze,[be(a("input",{ref_for:!0,ref:`input-${t.id}-${s.key}`,"onUpdate:modelValue":d=>y[`${t.id}-${s.key}`]=d,type:"number",step:"0.1",min:"0",max:"24",class:_(["w-full h-8 text-xs text-center border border-gray-300 rounded focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",ie(t,s)]),onInput:d=>ne(t,s,d.target.value),onKeydown:[K(d=>J(t,s),["enter"]),K(me(d=>J(t,s),["prevent"]),["tab"]),K(d=>de(t,s),["escape"])]},null,42,et),[[xe,y[`${t.id}-${s.key}`]]]),U(t,s)?(o(),n("div",tt,c(U(t,s)),1)):u("",!0)])):h(t,s)?(o(),n("div",rt,[$(e.$slots,"cell",{task:t,day:s,value:h(t,s)},()=>[a("span",st,c(k(h(t,s))),1)],!0),i.showIndicators&&h(t,s)>0?(o(),n("div",at,[a("div",{class:_(["w-1.5 h-1.5 rounded-full",V(t,s)?"bg-green-500":"bg-gray-300"]),title:V(t,s)?"Fatturabile":"Non fatturabile"},null,10,nt)])):u("",!0)])):(o(),n("span",ot,c(g.value?"":"-"),1))],10,Ye))),128)),a("td",lt,[$(e.$slots,"row-total",{task:t,total:R(t)},()=>[a("span",it,c(k(R(t))),1),g.value&&H(t)>0?(o(),n("div",dt," +"+c(k(H(t))),1)):u("",!0)],!0)])]))),128)),i.showDayTotals?(o(),n("tr",ct,[r[6]||(r[6]=a("td",{class:"px-2 py-1.5 text-xs font-semibold text-gray-900 dark:text-white sticky left-0 bg-gray-100 dark:bg-gray-600 min-w-[140px] max-w-[160px] w-[150px]"}," Totale Giorno ",-1)),(o(!0),n(S,null,E(i.days,t=>(o(),n("td",{key:`total-${t.key}`,class:_(["px-0.5 py-1.5 text-center text-xs font-semibold text-gray-900 dark:text-white min-w-[42px] max-w-[45px] w-[43px]",[{"bg-primary-100 dark:bg-primary-800":t.isToday},B(t)?"bg-gray-200 dark:bg-gray-500":""]])},[a("div",null,c(k(te(t))),1),g.value&&A(t)>0?(o(),n("div",ut," +"+c(k(A(t))),1)):u("",!0)],2))),128)),a("td",gt,[a("div",null,c(k(Z.value)),1),g.value&&F.value>0?(o(),n("div",yt," +"+c(k(F.value)),1)):u("",!0)])])):u("",!0)])])])])):u("",!0),!i.loading&&(!i.tasks||i.tasks.length===0)?(o(),n("div",bt,[$(e.$slots,"empty-state",{},()=>[C(j,{name:"clock",size:"lg",class:"mx-auto text-gray-400 mb-4"}),a("p",xt,c(i.emptyMessage),1)],!0)])):u("",!0),g.value&&N.value?(o(),n("div",mt,[a("div",pt,[a("div",ft,[C(j,{name:"information-circle",class:"text-blue-600 dark:text-blue-400",size:"sm"}),a("div",ht,[a("span",kt,c(W.value)+" modifiche in sospeso",1),a("span",vt," Totale ore aggiunte: "+c(k(F.value)),1)])]),a("div",wt,[a("button",{onClick:O,class:"px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700 transition-colors",disabled:w.value},c(w.value?"Salvando...":"Salva Tutto"),9,_t),a("button",{onClick:P,class:"px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-400 transition-colors"}," Annulla ")])])])):u("",!0),i.showLegend&&(i.showIndicators||g.value)?(o(),n("div",Tt,[r[11]||(r[11]=a("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-2"},"Legenda",-1)),a("div",$t,[i.showIndicators?(o(),n("div",Ct,r[7]||(r[7]=[a("div",{class:"w-2 h-2 rounded-full bg-green-500"},null,-1),a("span",null,"Fatturabile",-1)]))):u("",!0),i.showIndicators?(o(),n("div",jt,r[8]||(r[8]=[a("div",{class:"w-2 h-2 rounded-full bg-gray-300"},null,-1),a("span",null,"Non fatturabile",-1)]))):u("",!0),g.value?(o(),n("div",It,r[9]||(r[9]=[a("div",{class:"w-2 h-2 rounded bg-primary-200 border border-primary-400"},null,-1),a("span",null,"Modificato",-1)]))):u("",!0),g.value?(o(),n("div",Ft,r[10]||(r[10]=[a("div",{class:"w-2 h-2 rounded bg-green-200 border border-green-400"},null,-1),a("span",null,"Nuovo",-1)]))):u("",!0)])])):u("",!0),e.$slots.footer?(o(),n("div",Ot,[$(e.$slots,"footer",{},void 0,!0)])):u("",!0)]))}},Nt=fe(St,[["__scopeId","data-v-f47cb57f"]]);export{Nt as T};
