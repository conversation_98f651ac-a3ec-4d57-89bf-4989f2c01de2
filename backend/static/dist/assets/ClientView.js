import{r as p,c as P,u as dt,x as ut,b as s,e as n,l as m,k as _,j as e,t as d,v as gt,F as S,p as U,f as mt,A as q,B as c,C as y,o,s as C,n as O}from"./vendor.js";import{u as ct}from"./crm.js";import{H as x,d as yt}from"./app.js";import{g as pt}from"./industries.js";import{_ as xt}from"./PageHeader.js";import{_ as bt}from"./StatsGrid.js";import{T as vt}from"./TabContainer.js";import{S as I}from"./StatusBadge.js";/* empty css                                                           */const ft={class:"space-y-6"},kt={key:0,class:"flex space-x-4"},wt={key:0,class:"bg-white dark:bg-gray-800 rounded-lg shadow p-8"},ht={key:1,class:"space-y-6"},_t={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Ct={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},Et={class:"p-6 border-b border-gray-200 dark:border-gray-700"},Nt={class:"flex items-center justify-between"},jt={class:"p-6"},zt={class:"grid grid-cols-1 gap-4"},Vt={class:"mt-1 text-sm text-gray-900 dark:text-white"},$t={key:0},Dt={class:"mt-1 text-sm text-gray-900 dark:text-white"},Tt={key:1},Pt={class:"mt-1 text-sm text-gray-900 dark:text-white"},St=["href"],Ut={key:2},At={class:"mt-1 text-sm text-gray-900 dark:text-white whitespace-pre-line"},qt={key:3},It={class:"mt-1 text-sm text-gray-900 dark:text-white"},Bt={class:"mt-1 text-sm text-gray-900 dark:text-white"},Lt={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},Mt={class:"p-6"},Rt={key:0,class:"space-y-4"},Ot={class:"flex-shrink-0"},Ft={class:"flex-1 min-w-0"},Jt={class:"text-sm font-medium text-gray-900 dark:text-white"},Ht={class:"text-sm text-gray-500 dark:text-gray-400"},Gt={class:"text-xs text-gray-400 dark:text-gray-500 mt-1"},Wt={class:"flex-shrink-0"},Kt={key:1,class:"text-center py-8 text-gray-500 dark:text-gray-400"},Qt={key:0},Xt={class:"flex justify-between items-center mb-4"},Yt={class:"text-lg font-medium text-gray-900 dark:text-white"},Zt={key:0,class:"space-y-4"},te={class:"flex justify-between items-start"},ee={class:"font-medium text-gray-900 dark:text-white"},re={key:0,class:"text-sm text-gray-600 dark:text-gray-400"},ae={class:"mt-2 space-y-1"},se={key:0,class:"flex items-center text-sm text-gray-600 dark:text-gray-400"},oe=["href"],le={key:1,class:"flex items-center text-sm text-gray-600 dark:text-gray-400"},ne=["href"],ie={class:"flex space-x-2"},de=["onClick"],ue=["onClick"],ge={key:1,class:"text-center py-8 text-gray-500 dark:text-gray-400"},me={key:1},ce={class:"flex justify-between items-center mb-4"},ye={class:"text-lg font-medium text-gray-900 dark:text-white"},pe={class:"flex items-center space-x-3"},xe={key:0,class:"space-y-4"},be={class:"flex justify-between items-start"},ve={class:"font-medium text-gray-900 dark:text-white"},fe={key:0,class:"text-sm text-gray-600 dark:text-gray-400"},ke={class:"mt-2"},we={class:"flex space-x-2"},he={key:1,class:"text-center py-8 text-gray-500 dark:text-gray-400"},_e={key:2},Ce={class:"flex justify-between items-center mb-4"},Ee={class:"text-lg font-medium text-gray-900 dark:text-white"},Ne={key:0,class:"space-y-4"},je={class:"flex justify-between items-start"},ze={class:"font-medium text-gray-900 dark:text-white"},Ve={class:"text-sm text-gray-600 dark:text-gray-400"},$e={class:"mt-2"},De={class:"flex space-x-2"},Te={key:1,class:"text-center py-8 text-gray-500 dark:text-gray-400"},Pe={key:2,class:"bg-white dark:bg-gray-800 rounded-lg shadow p-8"},Se={class:"text-center"},Ue={key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Ae={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},qe={class:"mt-3"},Ie={class:"grid grid-cols-2 gap-4"},Be={key:4,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Le={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},Me={class:"mt-3"},Re={class:"grid grid-cols-2 gap-4"},Oe={key:5,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Fe={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},Je={class:"mt-3"},He={class:"grid grid-cols-2 gap-4"},ar={__name:"ClientView",setup(Ge){const F=dt(),J=ct(),{showToast:k}=yt(),A=p(!1),i=p(null),v=p([]),w=p([]),h=p([]),z=p([]),N=p("contacts"),V=p(!1),$=p(!1),D=p(!1),u=p(null),b=p({first_name:"",last_name:"",email:"",phone:"",position:""}),f=p({title:"",description:"",budget:null,duration_days:null}),E=P(()=>F.params.id),H=P(()=>[{label:"Contatti",value:v.value.length,icon:"users",iconClass:"text-blue-500"},{label:"Proposte",value:w.value.length,icon:"document-text",iconClass:"text-green-500"},{label:"Contratti",value:h.value.length,icon:"document-text",iconClass:"text-purple-500"},{label:"Valore Totale",value:`€${B(G.value)}`,icon:"currency-euro",iconClass:"text-yellow-500"}]),G=P(()=>h.value.reduce((a,t)=>t.contract_type==="hourly"&&t.budget_hours?a+t.hourly_rate*t.budget_hours:a+(t.budget_amount||t.total_budget||0),0)),W=P(()=>[{id:"contacts",name:"Contatti",iconName:"users",count:v.value.length},{id:"proposals",name:"Proposte",iconName:"document-text",count:w.value.length},{id:"contracts",name:"Contratti",iconName:"document-text",count:h.value.length}]),K=async()=>{try{if(A.value=!0,i.value=await J.getClient(E.value),!i.value)throw new Error("Cliente non trovato")}catch(a){console.error("Error loading client:",a),k("Errore nel caricamento del cliente","error")}finally{A.value=!1}},Q=async()=>{var a,t,g;try{const l=await fetch(`/api/contacts/?client_id=${E.value}`);if(l.ok){const j=await l.json();v.value=((a=j.data)==null?void 0:a.contacts)||[]}const r=await fetch(`/api/proposals/?client_id=${E.value}`);if(r.ok){const j=await r.json();w.value=((t=j.data)==null?void 0:t.proposals)||[]}const T=await fetch(`/api/contracts/?client_id=${E.value}`);if(T.ok){const j=await T.json();h.value=((g=j.data)==null?void 0:g.contracts)||[]}await X()}catch(l){console.error("Error loading client data:",l)}},X=async()=>{try{const a=[],t=w.value.filter(l=>l.created_at).sort((l,r)=>new Date(r.created_at)-new Date(l.created_at)).slice(0,3).map(l=>({id:`proposal-${l.id}`,type:"proposal",title:l.title,description:`Proposta ${l.status}`,date:l.created_at,status:l.status})),g=h.value.filter(l=>l.created_at).sort((l,r)=>new Date(r.created_at)-new Date(l.created_at)).slice(0,3).map(l=>({id:`contract-${l.id}`,type:"contract",title:l.title,description:`Contratto ${l.status}`,date:l.created_at,status:l.status}));a.push(...t,...g),a.sort((l,r)=>new Date(r.date)-new Date(l.date)),z.value=a.slice(0,5)}catch(a){console.error("Error loading recent activities:",a),z.value=[]}},B=a=>new Intl.NumberFormat("it-IT").format(a||0),L=a=>a?new Date(a).toLocaleDateString("it-IT"):"N/A",Y=a=>({proposal:"document-text",contract:"document-check",pre_invoice:"receipt-refund",project:"folder"})[a]||"document-text",Z=a=>({proposal:"bg-primary-500",contract:"bg-green-500",pre_invoice:"bg-yellow-500",project:"bg-purple-500"})[a]||"bg-gray-500",tt=a=>({draft:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",sent:"bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400",accepted:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",rejected:"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",active:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",expired:"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"})[a]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",et=a=>{u.value={...a},D.value=!0},rt=async()=>{try{const a=await fetch(`/api/contacts/${u.value.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({first_name:u.value.first_name,last_name:u.value.last_name,email:u.value.email,phone:u.value.phone,position:u.value.position})});if(a.ok){const g=(await a.json()).data,l=v.value.findIndex(r=>r.id===u.value.id);l!==-1&&(v.value[l]={...g}),D.value=!1,u.value=null,k("Contatto aggiornato con successo","success")}else throw new Error("Errore nell'aggiornamento")}catch(a){console.error("Error updating contact:",a),k("Errore nell'aggiornamento del contatto","error")}},at=()=>{D.value=!1,u.value=null},st=async()=>{try{const a=await fetch("/api/contacts/",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...b.value,client_id:E.value})});if(a.ok){const g=(await a.json()).data;v.value.push(g),$.value=!1,M(),k("Contatto creato con successo","success")}else throw new Error("Errore nella creazione")}catch(a){console.error("Error creating contact:",a),k("Errore nella creazione del contatto","error")}},ot=()=>{$.value=!1,M()},M=()=>{b.value={first_name:"",last_name:"",email:"",phone:"",position:""}},lt=async()=>{try{const a=await fetch("/api/proposals/",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...f.value,client_id:E.value,status:"draft"})});if(a.ok){const g=(await a.json()).data;w.value.push(g),V.value=!1,R(),k("Proposta creata con successo","success")}else throw new Error("Errore nella creazione")}catch(a){console.error("Error creating proposal:",a),k("Errore nella creazione della proposta","error")}},nt=()=>{V.value=!1,R()},R=()=>{f.value={title:"",description:"",budget:null,duration_days:null}},it=async a=>{if(confirm("Sei sicuro di voler eliminare questo contatto?"))try{if((await fetch(`/api/contacts/${a}`,{method:"DELETE"})).ok)v.value=v.value.filter(g=>g.id!==a),k("Contatto eliminato con successo","success");else throw new Error("Errore nella eliminazione")}catch(t){console.error("Error deleting contact:",t),k("Errore nell'eliminazione del contatto","error")}};return ut(async()=>{await K(),i.value&&await Q()}),(a,t)=>{var l;const g=mt("router-link");return o(),s("div",ft,[n(xt,{title:((l=i.value)==null?void 0:l.name)||"Caricamento...",subtitle:"Dettaglio informazioni cliente","show-back":!0,"back-route":"/app/crm/clients"},{actions:_(()=>[i.value?(o(),s("div",kt,[n(g,{to:`/app/crm/clients/${i.value.id}/edit`,class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"},{default:_(()=>[n(x,{name:"pencil",class:"w-4 h-4 mr-2"}),t[17]||(t[17]=C(" Modifica Cliente "))]),_:1,__:[17]},8,["to"]),e("button",{onClick:t[0]||(t[0]=r=>V.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"},[n(x,{name:"plus",class:"w-4 h-4 mr-2"}),t[18]||(t[18]=C(" Nuova Proposta "))])])):m("",!0)]),_:1},8,["title"]),A.value?(o(),s("div",wt,t[19]||(t[19]=[e("div",{class:"flex justify-center"},[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})],-1)]))):i.value?(o(),s("div",ht,[n(bt,{stats:H.value},null,8,["stats"]),e("div",_t,[e("div",Ct,[e("div",Et,[e("div",Nt,[t[20]||(t[20]=e("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Informazioni Base",-1)),n(I,{status:i.value.status,type:"client"},null,8,["status"])])]),e("div",jt,[e("dl",zt,[e("div",null,[t[21]||(t[21]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Nome",-1)),e("dd",Vt,d(i.value.name),1)]),i.value.industry?(o(),s("div",$t,[t[22]||(t[22]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Settore",-1)),e("dd",Dt,d(gt(pt)(i.value.industry)),1)])):m("",!0),i.value.website?(o(),s("div",Tt,[t[23]||(t[23]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Sito Web",-1)),e("dd",Pt,[e("a",{href:i.value.website,target:"_blank",class:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"},d(i.value.website),9,St)])])):m("",!0),i.value.address?(o(),s("div",Ut,[t[24]||(t[24]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Indirizzo",-1)),e("dd",At,d(i.value.address),1)])):m("",!0),i.value.description?(o(),s("div",qt,[t[25]||(t[25]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Descrizione",-1)),e("dd",It,d(i.value.description),1)])):m("",!0),e("div",null,[t[26]||(t[26]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Data Creazione",-1)),e("dd",Bt,d(L(i.value.created_at)),1)])])])]),e("div",Lt,[t[29]||(t[29]=e("div",{class:"p-6 border-b border-gray-200 dark:border-gray-700"},[e("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Attività Recenti")],-1)),e("div",Mt,[z.value.length>0?(o(),s("div",Rt,[(o(!0),s(S,null,U(z.value,r=>(o(),s("div",{key:r.id,class:"flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"},[e("div",Ot,[e("div",{class:O([Z(r.type),"w-8 h-8 rounded-full flex items-center justify-center"])},[n(x,{name:Y(r.type),class:"w-4 h-4 text-white"},null,8,["name"])],2)]),e("div",Ft,[e("div",Jt,d(r.title),1),e("div",Ht,d(r.description),1),e("div",Gt,d(L(r.date)),1)]),e("div",Wt,[e("span",{class:O([tt(r.status),"inline-flex px-2 py-1 text-xs font-medium rounded-full"])},d(r.status),3)])]))),128))])):(o(),s("div",Kt,[n(x,{name:"document-text",class:"w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600"}),t[27]||(t[27]=e("p",null,"Nessuna attività recente",-1)),t[28]||(t[28]=e("p",{class:"text-sm"},"Le attività del cliente appariranno qui",-1))]))])])]),n(vt,{tabs:W.value,"active-tab":N.value,onTabChange:t[2]||(t[2]=r=>N.value=r)},{default:_(()=>[N.value==="contacts"?(o(),s("div",Qt,[e("div",Xt,[e("h3",Yt,"Contatti ("+d(v.value.length)+")",1),e("button",{onClick:t[1]||(t[1]=r=>$.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 dark:text-primary-400 bg-primary-100 dark:bg-primary-900/20 hover:bg-primary-200 dark:hover:bg-primary-900/30"},[n(x,{name:"plus",class:"w-4 h-4 mr-2"}),t[30]||(t[30]=C(" Nuovo Contatto "))])]),v.value.length>0?(o(),s("div",Zt,[(o(!0),s(S,null,U(v.value,r=>(o(),s("div",{key:r.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4"},[e("div",te,[e("div",null,[e("h4",ee,d(r.full_name),1),r.position?(o(),s("p",re,d(r.position),1)):m("",!0),e("div",ae,[r.email?(o(),s("div",se,[n(x,{name:"envelope",class:"w-4 h-4 mr-2"}),e("a",{href:`mailto:${r.email}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"},d(r.email),9,oe)])):m("",!0),r.phone?(o(),s("div",le,[n(x,{name:"phone",class:"w-4 h-4 mr-2"}),e("a",{href:`tel:${r.phone}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"},d(r.phone),9,ne)])):m("",!0)])]),e("div",ie,[e("button",{onClick:T=>et(r),class:"inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600"}," Modifica ",8,de),e("button",{onClick:T=>it(r.id),class:"inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-red-700 dark:text-red-400 bg-red-100 dark:bg-red-900/20 hover:bg-red-200 dark:hover:bg-red-900/30"}," Elimina ",8,ue)])])]))),128))])):(o(),s("div",ge,[n(x,{name:"users",class:"w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600"}),t[31]||(t[31]=e("p",null,"Nessun contatto",-1)),t[32]||(t[32]=e("p",{class:"text-sm"},"Aggiungi un contatto per questo cliente",-1))]))])):m("",!0),N.value==="proposals"?(o(),s("div",me,[e("div",ce,[e("h3",ye,"Proposte ("+d(w.value.length)+")",1),e("div",pe,[n(g,{to:`/app/crm/proposals/new?client_id=${i.value.id}&ai=true`,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"},{default:_(()=>[n(x,{name:"plus",class:"w-4 h-4 mr-2"}),t[33]||(t[33]=C(" ✨ Genera con AI "))]),_:1,__:[33]},8,["to"]),n(g,{to:`/app/crm/proposals/new?client_id=${i.value.id}`,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 dark:text-primary-400 bg-primary-100 dark:bg-primary-900/20 hover:bg-primary-200 dark:hover:bg-primary-900/30"},{default:_(()=>[n(x,{name:"plus",class:"w-4 h-4 mr-2"}),t[34]||(t[34]=C(" Nuova Proposta "))]),_:1,__:[34]},8,["to"])])]),w.value.length>0?(o(),s("div",xe,[(o(!0),s(S,null,U(w.value,r=>(o(),s("div",{key:r.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4"},[e("div",be,[e("div",null,[e("h4",ve,d(r.title),1),r.value?(o(),s("p",fe,"€"+d(B(r.value)),1)):m("",!0),e("div",ke,[n(I,{status:r.status,type:"proposal"},null,8,["status"])])]),e("div",we,[n(g,{to:`/app/crm/proposals/${r.id}`,class:"inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-primary-700 dark:text-primary-400 bg-primary-100 dark:bg-primary-900/20 hover:bg-primary-200 dark:hover:bg-primary-900/30"},{default:_(()=>t[35]||(t[35]=[C(" Visualizza ")])),_:2,__:[35]},1032,["to"])])])]))),128))])):(o(),s("div",he,[n(x,{name:"document-text",class:"w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600"}),t[36]||(t[36]=e("p",null,"Nessuna proposta",-1)),t[37]||(t[37]=e("p",{class:"text-sm"},"Crea una proposta per questo cliente",-1))]))])):m("",!0),N.value==="contracts"?(o(),s("div",_e,[e("div",Ce,[e("h3",Ee,"Contratti ("+d(h.value.length)+")",1)]),h.value.length>0?(o(),s("div",Ne,[(o(!0),s(S,null,U(h.value,r=>(o(),s("div",{key:r.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4"},[e("div",je,[e("div",null,[e("h4",ze,d(r.title),1),e("p",Ve,d(r.contract_number),1),e("div",$e,[n(I,{status:r.status,type:"contract"},null,8,["status"])])]),e("div",De,[n(g,{to:`/app/crm/contracts/${r.id}`,class:"inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-primary-700 dark:text-primary-400 bg-primary-100 dark:bg-primary-900/20 hover:bg-primary-200 dark:hover:bg-primary-900/30"},{default:_(()=>t[38]||(t[38]=[C(" Visualizza ")])),_:2,__:[38]},1032,["to"])])])]))),128))])):(o(),s("div",Te,[n(x,{name:"document-text",class:"w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600"}),t[39]||(t[39]=e("p",null,"Nessun contratto",-1)),t[40]||(t[40]=e("p",{class:"text-sm"},"I contratti per questo cliente appariranno qui",-1))]))])):m("",!0)]),_:1},8,["tabs","active-tab"])])):(o(),s("div",Pe,[e("div",Se,[n(x,{name:"exclamation-triangle",class:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600"}),t[42]||(t[42]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Cliente non trovato",-1)),t[43]||(t[43]=e("p",{class:"text-gray-500 dark:text-gray-400 mb-4"},"Il cliente richiesto non esiste o non è accessibile",-1)),n(g,{to:"/app/crm/clients",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"},{default:_(()=>t[41]||(t[41]=[C(" Torna alla Lista Clienti ")])),_:1,__:[41]})])])),D.value?(o(),s("div",Ue,[e("div",Ae,[e("div",qe,[t[50]||(t[50]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Modifica Contatto",-1)),e("form",{onSubmit:q(rt,["prevent"]),class:"space-y-4"},[e("div",Ie,[e("div",null,[t[44]||(t[44]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Nome *",-1)),c(e("input",{"onUpdate:modelValue":t[3]||(t[3]=r=>u.value.first_name=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[y,u.value.first_name]])]),e("div",null,[t[45]||(t[45]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Cognome *",-1)),c(e("input",{"onUpdate:modelValue":t[4]||(t[4]=r=>u.value.last_name=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[y,u.value.last_name]])])]),e("div",null,[t[46]||(t[46]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Email *",-1)),c(e("input",{"onUpdate:modelValue":t[5]||(t[5]=r=>u.value.email=r),type:"email",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[y,u.value.email]])]),e("div",null,[t[47]||(t[47]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Telefono",-1)),c(e("input",{"onUpdate:modelValue":t[6]||(t[6]=r=>u.value.phone=r),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[y,u.value.phone]])]),e("div",null,[t[48]||(t[48]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Posizione",-1)),c(e("input",{"onUpdate:modelValue":t[7]||(t[7]=r=>u.value.position=r),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[y,u.value.position]])]),e("div",{class:"flex justify-end space-x-3 pt-4"},[e("button",{type:"button",onClick:at,class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md"}," Annulla "),t[49]||(t[49]=e("button",{type:"submit",class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md"}," Aggiorna ",-1))])],32)])])])):m("",!0),$.value?(o(),s("div",Be,[e("div",Le,[e("div",Me,[t[57]||(t[57]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Nuovo Contatto",-1)),e("form",{onSubmit:q(st,["prevent"]),class:"space-y-4"},[e("div",Re,[e("div",null,[t[51]||(t[51]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Nome *",-1)),c(e("input",{"onUpdate:modelValue":t[8]||(t[8]=r=>b.value.first_name=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[y,b.value.first_name]])]),e("div",null,[t[52]||(t[52]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Cognome *",-1)),c(e("input",{"onUpdate:modelValue":t[9]||(t[9]=r=>b.value.last_name=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[y,b.value.last_name]])])]),e("div",null,[t[53]||(t[53]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Email *",-1)),c(e("input",{"onUpdate:modelValue":t[10]||(t[10]=r=>b.value.email=r),type:"email",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[y,b.value.email]])]),e("div",null,[t[54]||(t[54]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Telefono",-1)),c(e("input",{"onUpdate:modelValue":t[11]||(t[11]=r=>b.value.phone=r),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[y,b.value.phone]])]),e("div",null,[t[55]||(t[55]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Posizione",-1)),c(e("input",{"onUpdate:modelValue":t[12]||(t[12]=r=>b.value.position=r),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[y,b.value.position]])]),e("div",{class:"flex justify-end space-x-3 pt-4"},[e("button",{type:"button",onClick:ot,class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md"}," Annulla "),t[56]||(t[56]=e("button",{type:"submit",class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md"}," Crea Contatto ",-1))])],32)])])])):m("",!0),V.value?(o(),s("div",Oe,[e("div",Fe,[e("div",Je,[t[63]||(t[63]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Nuova Proposta",-1)),e("form",{onSubmit:q(lt,["prevent"]),class:"space-y-4"},[e("div",null,[t[58]||(t[58]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Titolo *",-1)),c(e("input",{"onUpdate:modelValue":t[13]||(t[13]=r=>f.value.title=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Es. Sviluppo sito web aziendale"},null,512),[[y,f.value.title]])]),e("div",null,[t[59]||(t[59]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),c(e("textarea",{"onUpdate:modelValue":t[14]||(t[14]=r=>f.value.description=r),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Descrizione dettagliata della proposta..."},null,512),[[y,f.value.description]])]),e("div",He,[e("div",null,[t[60]||(t[60]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Budget (€)",-1)),c(e("input",{"onUpdate:modelValue":t[15]||(t[15]=r=>f.value.budget=r),type:"number",min:"0",step:"100",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"10000"},null,512),[[y,f.value.budget,void 0,{number:!0}]])]),e("div",null,[t[61]||(t[61]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Durata (giorni)",-1)),c(e("input",{"onUpdate:modelValue":t[16]||(t[16]=r=>f.value.duration_days=r),type:"number",min:"1",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"30"},null,512),[[y,f.value.duration_days,void 0,{number:!0}]])])]),e("div",{class:"flex justify-end space-x-3 pt-4"},[e("button",{type:"button",onClick:nt,class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md"}," Annulla "),t[62]||(t[62]=e("button",{type:"submit",class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md"}," Crea Proposta ",-1))])],32)])])])):m("",!0)])}}};export{ar as default};
