import{r,c as b,x as T,u as D,b as i,j as l,l as N,e as m,t as P,q as j,o as d}from"./vendor.js";import{_ as q}from"./PageHeader.js";import{F as H}from"./FormBuilder.js";import{_ as V,H as A}from"./app.js";import"./AlertsSection.js";/* empty css                                                           */const B={class:"min-h-screen bg-gray-50 dark:bg-gray-900 py-6"},S={class:"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8"},z={key:0,class:"flex justify-center items-center h-64"},I={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},F={class:"flex"},M={class:"text-sm text-red-700 dark:text-red-300 mt-1"},U={key:2},C={__name:"DepartmentEdit",setup(R){const _=D(),h=j(),t=r(null),n=r({name:"",description:"",parent_id:"",manager_id:"",budget:null}),v=r([]),f=r([]),u=r(!1),c=r(!1),o=r(null),p=r(null),g=r({}),y=b(()=>[{id:"name",type:"text",label:"Nome Dipartimento",placeholder:"Es. Sviluppo Software",required:!0},{id:"description",type:"textarea",label:"Descrizione",placeholder:"Descrizione del dipartimento e delle sue responsabilità",rows:3,required:!1},{id:"parent_id",type:"select",label:"Dipartimento Padre",placeholder:"Nessun dipartimento padre (livello radice)",required:!1,options:x.value},{id:"manager_id",type:"select",label:"Manager",placeholder:"Nessun manager assegnato",required:!1,options:f.value.map(e=>({value:e.id,label:`${e.first_name} ${e.last_name} (${e.email})`}))},{id:"budget",type:"number",label:"Budget Annuale (€)",placeholder:"0",min:0,step:1e3,required:!1}]),x=b(()=>t.value?v.value.filter(e=>e.id!==t.value.id&&e.parent_id!==t.value.id).map(e=>({value:e.id,label:e.name})):[]),w=async e=>{u.value=!0,o.value=null;try{const a=await fetch(`/api/personnel/departments/${e}`,{credentials:"include"});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const s=await a.json();if(s.success)t.value=s.data.department,n.value={name:t.value.name||"",description:t.value.description||"",parent_id:t.value.parent_id||"",manager_id:t.value.manager_id||"",budget:t.value.budget||null};else throw new Error(s.message||"Errore nel caricamento del dipartimento")}catch(a){console.error("Error fetching department:",a),o.value=a.message}finally{u.value=!1}},E=async()=>{try{const e=await fetch("/api/personnel/departments",{credentials:"include"});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const a=await e.json();a.success&&(v.value=a.data.departments||[])}catch(e){console.error("Error fetching departments:",e)}},$=async()=>{try{const e=await fetch("/api/personnel/users",{credentials:"include"});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const a=await e.json();a.success&&(f.value=a.data.users||[])}catch(e){console.error("Error fetching users:",e)}},k=async()=>{c.value=!0,p.value=null,g.value={};try{const e={name:n.value.name,description:n.value.description,parent_id:n.value.parent_id||null,manager_id:n.value.manager_id||null,budget:n.value.budget||null},a=await fetch(`/api/personnel/departments/${t.value.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const s=await a.json();if(s.success)h.push(`/app/personnel/departments/${t.value.id}`);else throw new Error(s.message||"Errore nell'aggiornamento del dipartimento")}catch(e){console.error("Error updating department:",e),p.value=e.message}finally{c.value=!1}};return T(async()=>{const e=_.params.id;if(!e){o.value="ID dipartimento non specificato";return}await Promise.all([w(e),E(),$()])}),(e,a)=>(d(),i("div",B,[l("div",S,[u.value&&!t.value?(d(),i("div",z,a[1]||(a[1]=[l("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-brand-primary-600"},null,-1)]))):o.value&&!t.value?(d(),i("div",I,[l("div",F,[m(A,{name:"exclamation-triangle",size:"sm",color:"text-red-400",className:"mr-2 mt-0.5"}),l("div",null,[a[2]||(a[2]=l("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento del dipartimento",-1)),l("p",M,P(o.value),1)])])])):t.value?(d(),i("div",U,[m(q,{title:`Modifica ${t.value.name}`,subtitle:"Aggiorna le informazioni del dipartimento",icon:"building-office-2","icon-color":"text-brand-primary-600","show-back":!0,"back-route":`/app/personnel/departments/${t.value.id}`},null,8,["title","back-route"]),m(H,{modelValue:n.value,"onUpdate:modelValue":a[0]||(a[0]=s=>n.value=s),fields:y.value,errors:g.value,"global-error":p.value,loading:c.value,"submit-label":"Aggiorna Dipartimento","loading-label":"Aggiornamento...","cancel-label":"Annulla","cancel-route":`/app/personnel/departments/${t.value.id}`,onSubmit:k},null,8,["modelValue","fields","errors","global-error","loading","cancel-route"])])):N("",!0)])]))}},W=V(C,[["__scopeId","data-v-9cc6400e"]]);export{W as default};
