import{q as U,r as F,c as m,w as W,x as J,b as s,o,e as i,j as e,k as $,B as K,l as b,C as O,s as X,t as n,F as g,p as u,n as Y,E as P}from"./vendor.js";import{u as Z}from"./help.js";import{d as ee,H as l}from"./app.js";import{u as te}from"./useDebounce.js";import{_ as re}from"./PageHeader.js";import{S as ae}from"./StandardButton.js";const se={class:"p-6"},oe={class:"flex items-center space-x-4"},ne={class:"relative w-80"},ie={class:"space-y-8 mt-8"},le={key:0,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},de={class:"flex items-center justify-between mb-6"},ce={class:"text-lg font-semibold text-gray-900 dark:text-white"},ge={class:"text-sm text-gray-600 dark:text-gray-400"},ue={key:0,class:"space-y-4"},pe={class:"animate-pulse space-y-4"},he={key:1,class:"space-y-6"},ye={key:0},me={class:"text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2 mb-4"},xe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},be=["onClick"],_e={class:"flex items-start justify-between mb-2"},ke={class:"font-medium text-gray-900 dark:text-white flex-1 pr-2"},ve={class:"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded-full flex-shrink-0"},fe={class:"text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3"},we={class:"flex items-center justify-between"},Ce={class:"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded"},Se={class:"text-xs text-gray-500 dark:text-gray-400"},qe={key:1},Ee={class:"text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2 mb-4"},De={class:"space-y-3"},He=["onClick"],Ae={class:"flex-1"},Be={class:"font-medium text-gray-900 dark:text-white"},Fe={class:"text-sm text-gray-600 dark:text-gray-400"},$e={class:"text-sm text-gray-500 dark:text-gray-400 flex-shrink-0"},Pe={key:2,class:"text-center py-12"},Re={key:1},je={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},Ie={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Ne={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Te=["onClick"],Ve={class:"flex items-center space-x-2 mb-4"},ze={class:"bg-brand-primary-100 text-brand-primary-800 text-xs px-2 py-1 rounded-full"},Le={class:"text-lg font-semibold text-gray-900 dark:text-white mb-2"},Qe={class:"text-gray-600 dark:text-gray-400 line-clamp-3 mb-4"},Ge={class:"flex items-center justify-between"},Me={class:"flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400"},Ue={key:2,class:"text-center py-12"},We={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},Je={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},Ke={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},Oe=["onClick"],Xe={class:"flex items-center space-x-3 mb-3"},Ye={class:"font-semibold text-gray-900 dark:text-white"},Ze={class:"text-sm text-gray-600 dark:text-gray-400 mb-4"},et={class:"flex items-center justify-between"},tt={class:"text-sm text-gray-500 dark:text-gray-400"},rt={key:2,class:"text-center py-12"},at={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},st={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ot=["onClick"],nt={class:"w-12 h-12 bg-gray-100 dark:bg-gray-600 rounded-lg flex items-center justify-center flex-shrink-0"},it={class:"flex-1"},lt={class:"font-semibold text-gray-900 dark:text-white mb-1"},dt={class:"text-sm text-gray-600 dark:text-gray-400"},ct={__name:"HelpCenter",setup(R){const p=U(),d=Z(),k=ee(),h=F(""),v=m(()=>h.value.length>0),_=m(()=>d.loading),f=m(()=>d.categories),w=m(()=>d.featuredContent),c=m(()=>d.searchResults),j=m(()=>{var a,x;const r=c.value;return(((a=r==null?void 0:r.content)==null?void 0:a.length)||0)+(((x=r==null?void 0:r.categories)==null?void 0:x.length)||0)}),I=F([{id:"getting-started",title:"Primi Passi",description:"Guida introduttiva a DatPortal",icon:"academic-cap",color:"#3B82F6",action:"category",target:"primi-passi"},{id:"contact-support",title:"Contatta il Supporto",description:"Hai bisogno di aiuto diretto?",icon:"phone",color:"#10B981",action:"contact",target:"/help/contact"},{id:"report-error",title:"Segnala Errore",description:"Segnala un problema o bug",icon:"exclamation-triangle",color:"#EF4444",action:"navigate",target:"/app/help/report-error"},{id:"video-tutorials",title:"Video Tutorial",description:"Impara guardando i video",icon:"play-circle",color:"#F59E0B",action:"filter",target:"video"},{id:"faq",title:"FAQ",description:"Domande frequenti e risposte",icon:"question-mark-circle",color:"#EF4444",action:"category",target:"faq"}]),N=()=>{},T=()=>{h.value="",d.clearSearchResults()},V=te(async r=>{try{await d.searchHelp(r)}catch(a){k.showError("Errore nella ricerca: "+a.message)}},300);W(h,r=>{r.trim()?V(r):d.clearSearchResults()});const C=r=>{p.push(`/app/help/content/${r.id}`)},S=r=>{p.push({path:"/app/help/content",query:{category:r.slug}})},z=()=>{d.openChatWidget("help")},L=r=>{switch(r.action){case"category":p.push({path:"/app/help/content",query:{category:r.target}});break;case"contact":p.push(r.target);break;case"navigate":p.push(r.target);break;case"filter":p.push({path:"/app/help/content",query:{type:r.target}});break}},q=r=>({guide:"Guida",tutorial:"Tutorial",faq:"FAQ",video:"Video",api_doc:"API",how_to:"Come Fare"})[r]||r,Q=r=>({guide:"book-open",tutorial:"academic-cap",faq:"question-mark-circle",video:"play-circle",api_doc:"code-bracket",how_to:"light-bulb"})[r]||"document-text",G=r=>({beginner:"Principiante",intermediate:"Intermedio",advanced:"Avanzato"})[r]||r,M=r=>({beginner:"text-green-600 dark:text-green-400",intermediate:"text-yellow-600 dark:text-yellow-400",advanced:"text-red-600 dark:text-red-400"})[r]||"text-gray-600 dark:text-gray-400";return J(async()=>{try{await d.loadHelpCenter()}catch{k.showError("Errore nel caricamento del help center")}}),(r,a)=>{var x,E,D,H,A,B;return o(),s("div",se,[i(re,{title:"Help Center",subtitle:"Trova risposte, guide e ottieni supporto per DatPortal",icon:"academic-cap","icon-color":"text-brand-primary-600"},{actions:$(()=>[e("div",oe,[e("div",ne,[i(l,{name:"magnifying-glass",class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),K(e("input",{"onUpdate:modelValue":a[0]||(a[0]=t=>h.value=t),onInput:N,type:"text",placeholder:"Cerca nella documentazione...",class:"w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,544),[[O,h.value]]),h.value?(o(),s("button",{key:0,onClick:T,class:"absolute right-3 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 dark:hover:bg-gray-600 rounded"},[i(l,{name:"x-mark",class:"w-4 h-4"})])):b("",!0)]),i(ae,{onClick:z,variant:"primary",icon:"chat-bubble-left-right"},{default:$(()=>a[1]||(a[1]=[X(" Chiedi all'AI ")])),_:1,__:[1]})])]),_:1}),e("div",ie,[v.value?(o(),s("div",le,[e("div",de,[e("h2",ce,' Risultati per "'+n(h.value)+'" ',1),e("span",ge,n(j.value)+" risultati trovati ",1)]),_.value?(o(),s("div",ue,[e("div",pe,[(o(),s(g,null,u(3,t=>e("div",{key:t,class:"p-4 border border-gray-200 dark:border-gray-600 rounded-lg"},a[2]||(a[2]=[e("div",{class:"h-4 bg-gray-200 dark:bg-gray-600 rounded w-3/4 mb-2"},null,-1),e("div",{class:"h-3 bg-gray-200 dark:bg-gray-600 rounded w-full mb-1"},null,-1),e("div",{class:"h-3 bg-gray-200 dark:bg-gray-600 rounded w-2/3"},null,-1)]))),64))])])):(E=(x=c.value)==null?void 0:x.content)!=null&&E.length||(H=(D=c.value)==null?void 0:D.categories)!=null&&H.length?(o(),s("div",he,[(A=c.value.content)!=null&&A.length?(o(),s("div",ye,[e("h3",me,[i(l,{name:"document-text",class:"w-5 h-5"}),e("span",null,"Contenuti ("+n(c.value.content.length)+")",1)]),e("div",xe,[(o(!0),s(g,null,u(c.value.content,t=>{var y;return o(),s("div",{key:t.id,class:"bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:border-brand-primary-300 hover:bg-white dark:hover:bg-gray-600",onClick:pt=>C(t)},[e("div",_e,[e("h4",ke,n(t.title),1),e("span",ve,n(q(t.content_type)),1)]),e("p",fe,n(t.excerpt),1),e("div",we,[e("span",Ce,n((y=t.category)==null?void 0:y.name),1),e("span",Se,n(t.estimated_read_time)+"min",1)])],8,be)}),128))])])):b("",!0),(B=c.value.categories)!=null&&B.length?(o(),s("div",qe,[e("h3",Ee,[i(l,{name:"folder",class:"w-5 h-5"}),e("span",null,"Categorie ("+n(c.value.categories.length)+")",1)]),e("div",De,[(o(!0),s(g,null,u(c.value.categories,t=>(o(),s("div",{key:t.id,class:"bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:border-brand-primary-300 hover:bg-white dark:hover:bg-gray-600 flex items-center space-x-4",onClick:y=>S(t)},[i(l,{name:t.icon||"folder",class:"w-8 h-8 flex-shrink-0 text-brand-primary-600"},null,8,["name"]),e("div",Ae,[e("h4",Be,n(t.name),1),e("p",Fe,n(t.description),1)]),e("span",$e,n(t.content_count)+" articoli",1)],8,He))),128))])])):b("",!0)])):(o(),s("div",Pe,[i(l,{name:"magnifying-glass",class:"mx-auto h-12 w-12 text-gray-400"}),a[3]||(a[3]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun risultato",-1)),a[4]||(a[4]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Prova con termini di ricerca diversi",-1))]))])):b("",!0),v.value?b("",!0):(o(),s("div",Re,[e("div",je,[a[7]||(a[7]=e("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white mb-6"},"Contenuti in Evidenza",-1)),_.value?(o(),s("div",Ie,[(o(),s(g,null,u(6,t=>e("div",{key:t,class:"animate-pulse"},a[5]||(a[5]=[e("div",{class:"bg-gray-200 dark:bg-gray-600 rounded-lg h-48"},null,-1)]))),64))])):w.value.length?(o(),s("div",Ne,[(o(!0),s(g,null,u(w.value,t=>(o(),s("div",{key:t.id,class:"bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl p-6 shadow-sm cursor-pointer transition-all duration-200 hover:border-brand-primary-300 hover:shadow-md",onClick:y=>C(t)},[e("div",Ve,[i(l,{name:Q(t.type),class:"w-5 h-5 text-brand-primary-600"},null,8,["name"]),e("span",ze,n(q(t.type)),1)]),e("h3",Le,n(t.title),1),e("p",Qe,n(t.excerpt),1),e("div",Ge,[e("div",Me,[i(l,{name:"clock",class:"w-4 h-4"}),e("span",null,n(t.read_time)+"min",1)]),e("span",{class:Y(["text-sm font-medium",M(t.difficulty)])},n(G(t.difficulty)),3)])],8,Te))),128))])):(o(),s("div",Ue,[i(l,{name:"document-text",class:"mx-auto h-12 w-12 text-gray-400"}),a[6]||(a[6]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun contenuto disponibile",-1))]))]),e("div",We,[a[10]||(a[10]=e("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white mb-6"},"Esplora per Categoria",-1)),_.value?(o(),s("div",Je,[(o(),s(g,null,u(8,t=>e("div",{key:t,class:"animate-pulse"},a[8]||(a[8]=[e("div",{class:"bg-gray-200 dark:bg-gray-600 rounded-lg h-32"},null,-1)]))),64))])):f.value.length?(o(),s("div",Ke,[(o(!0),s(g,null,u(f.value,t=>(o(),s("div",{key:t.id,class:"bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl p-6 shadow-sm cursor-pointer transition-all duration-200 hover:border-brand-primary-300 hover:shadow-md",onClick:y=>S(t)},[e("div",Xe,[i(l,{name:t.icon||"folder",class:"w-6 h-6",style:P({color:t.color})},null,8,["name","style"]),e("h3",Ye,n(t.name),1)]),e("p",Ze,n(t.description),1),e("div",et,[e("span",tt,n(t.content_count)+" articoli",1),i(l,{name:"arrow-right",class:"w-4 h-4 text-gray-400"})])],8,Oe))),128))])):(o(),s("div",rt,[i(l,{name:"folder",class:"mx-auto h-12 w-12 text-gray-400"}),a[9]||(a[9]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna categoria disponibile",-1))]))]),e("div",at,[a[11]||(a[11]=e("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white mb-6"},"Azioni Rapide",-1)),e("div",st,[(o(!0),s(g,null,u(I.value,t=>(o(),s("button",{key:t.id,onClick:y=>L(t),class:"bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl p-6 shadow-sm cursor-pointer transition-all duration-200 hover:border-brand-primary-300 hover:bg-gray-50 dark:hover:bg-gray-600 hover:shadow-md flex items-center space-x-4 text-left"},[e("div",nt,[i(l,{name:t.icon,class:"w-6 h-6",style:P({color:t.color})},null,8,["name","style"])]),e("div",it,[e("h3",lt,n(t.title),1),e("p",dt,n(t.description),1)])],8,ot))),128))])])]))])])}}},gt={class:"min-h-screen bg-gray-50"},kt={__name:"HelpDashboard",setup(R){return document.title="Help Center - DatPortal",(p,d)=>(o(),s("div",gt,[i(ct)]))}};export{kt as default};
