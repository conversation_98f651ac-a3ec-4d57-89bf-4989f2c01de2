import{_ as y,i as k,H as D,e as h}from"./app.js";import{b as f,l as m,o as u,j as e,e as w,f as c,A as S,B as r,C as a,t as g,H as b,I,Q as d,h as V,s as z}from"./vendor.js";const C={name:"EventEditModal",components:{HeroIcon:D,LoadingSpinner:k},props:{event:{type:Object,required:!0}},emits:["close","event-updated"],data(){return{isSubmitting:!1,tagsInput:"",form:{title:"",description:"",category:"",priority:"medium",startDate:"",endDate:"",location:"",maxParticipants:null,requiresRegistration:!0,registrationDeadline:"",isPublic:!0,sendReminders:!0,allowComments:!0,tags:[]}}},computed:{isFormValid(){const s=this.form.title.trim().length>0,t=this.form.description.trim().length>0,l=this.form.category.length>0,p=this.form.startDate.length>0,i=!this.form.endDate||new Date(this.form.endDate)>new Date(this.form.startDate),n=!this.form.registrationDeadline||new Date(this.form.registrationDeadline)<new Date(this.form.startDate);return s&&t&&l&&p&&i&&n},minDateTime(){return new Date().toISOString().slice(0,16)},parsedTags(){return this.tagsInput.split(",").map(s=>s.trim()).filter(s=>s.length>0)}},watch:{event:{handler(){this.initializeForm()},immediate:!0}},methods:{initializeForm(){this.event&&(this.form={title:this.event.title||"",description:this.event.description||"",category:this.event.category||"",priority:this.event.priority||"medium",startDate:this.event.start_date?new Date(this.event.start_date).toISOString().slice(0,16):"",endDate:this.event.end_date?new Date(this.event.end_date).toISOString().slice(0,16):"",location:this.event.location||"",maxParticipants:this.event.max_participants||null,requiresRegistration:this.event.requires_registration!==void 0?this.event.requires_registration:!0,registrationDeadline:this.event.registration_deadline?new Date(this.event.registration_deadline).toISOString().slice(0,16):"",isPublic:this.event.is_public!==void 0?this.event.is_public:!0,sendReminders:this.event.send_reminders!==void 0?this.event.send_reminders:!0,allowComments:this.event.allow_comments!==void 0?this.event.allow_comments:!0,tags:this.event.tags||[]},this.tagsInput=this.event.tags?this.event.tags.join(", "):"")},async submitEvent(){if(!(!this.isFormValid||this.isSubmitting)){this.isSubmitting=!0;try{const s={...this.form,tags:this.parsedTags,startDate:new Date(this.form.startDate).toISOString(),endDate:this.form.endDate?new Date(this.form.endDate).toISOString():null,registrationDeadline:this.form.registrationDeadline?new Date(this.form.registrationDeadline).toISOString():null},l=await h().updateEvent({id:this.event.id,data:s});this.$emit("event-updated",l),this.closeModal()}catch(s){console.error("Errore durante l'aggiornamento dell'evento:",s)}finally{this.isSubmitting=!1}}},closeModal(){this.$emit("close"),setTimeout(()=>{this.initializeForm(),this.isSubmitting=!1},300)}}},E={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},R={class:"card w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto"},M={class:"flex items-center justify-between p-6 border-b dark:border-gray-600"},U={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},q={class:"md:col-span-2"},P={class:"text-right text-xs text-gray-500 dark:text-gray-400 mt-1"},T={class:"text-right text-xs text-gray-500 dark:text-gray-400 mt-1"},O={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},F=["min"],j=["min"],B={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},N={key:0},A=["min","max"],H={class:"space-y-3"},L={class:"flex items-center"},Q={class:"flex items-center"},W={class:"flex items-center"},X={class:"flex items-center"},G={class:"flex justify-end space-x-3 pt-4 border-t dark:border-gray-600"},J=["disabled"];function K(s,t,l,p,i,n){const x=c("HeroIcon"),v=c("LoadingSpinner");return s.isOpen?(u(),f("div",E,[e("div",R,[e("div",M,[t[17]||(t[17]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," Modifica Evento ",-1)),e("button",{onClick:t[0]||(t[0]=(...o)=>n.closeModal&&n.closeModal(...o)),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"},[w(x,{name:"XMarkIcon",class:"w-6 h-6"})])]),e("form",{onSubmit:t[16]||(t[16]=S((...o)=>n.submitEvent&&n.submitEvent(...o),["prevent"])),class:"p-6 space-y-6"},[e("div",U,[e("div",q,[t[18]||(t[18]=e("label",{for:"title",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Titolo * ",-1)),r(e("input",{id:"title","onUpdate:modelValue":t[1]||(t[1]=o=>i.form.title=o),type:"text",required:"",maxlength:"200",class:"input-field",placeholder:"Inserisci il titolo dell'evento"},null,512),[[a,i.form.title]]),e("div",P,g(i.form.title.length)+"/200 ",1)]),e("div",null,[t[20]||(t[20]=e("label",{for:"category",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Categoria * ",-1)),r(e("select",{id:"category","onUpdate:modelValue":t[2]||(t[2]=o=>i.form.category=o),required:"",class:"input-field"},t[19]||(t[19]=[I('<option value="" data-v-ec44e1e6>Seleziona categoria</option><option value="meeting" data-v-ec44e1e6>Riunione</option><option value="training" data-v-ec44e1e6>Formazione</option><option value="social" data-v-ec44e1e6>Evento sociale</option><option value="conference" data-v-ec44e1e6>Conferenza</option><option value="workshop" data-v-ec44e1e6>Workshop</option><option value="other" data-v-ec44e1e6>Altro</option>',7)]),512),[[b,i.form.category]])]),e("div",null,[t[22]||(t[22]=e("label",{for:"priority",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Priorità ",-1)),r(e("select",{id:"priority","onUpdate:modelValue":t[3]||(t[3]=o=>i.form.priority=o),class:"input-field"},t[21]||(t[21]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"urgent"},"Urgente",-1)]),512),[[b,i.form.priority]])])]),e("div",null,[t[23]||(t[23]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Descrizione * ",-1)),r(e("textarea",{id:"description","onUpdate:modelValue":t[4]||(t[4]=o=>i.form.description=o),rows:"4",required:"",maxlength:"2000",class:"input-field resize-vertical",placeholder:"Descrizione dell'evento"},null,512),[[a,i.form.description]]),e("div",T,g(i.form.description.length)+"/2000 ",1)]),e("div",O,[e("div",null,[t[24]||(t[24]=e("label",{for:"startDate",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data e ora inizio * ",-1)),r(e("input",{id:"startDate","onUpdate:modelValue":t[5]||(t[5]=o=>i.form.startDate=o),type:"datetime-local",required:"",min:n.minDateTime,class:"input-field"},null,8,F),[[a,i.form.startDate]])]),e("div",null,[t[25]||(t[25]=e("label",{for:"endDate",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data e ora fine ",-1)),r(e("input",{id:"endDate","onUpdate:modelValue":t[6]||(t[6]=o=>i.form.endDate=o),type:"datetime-local",min:i.form.startDate,class:"input-field"},null,8,j),[[a,i.form.endDate]])])]),e("div",null,[t[26]||(t[26]=e("label",{for:"location",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Luogo ",-1)),r(e("input",{id:"location","onUpdate:modelValue":t[7]||(t[7]=o=>i.form.location=o),type:"text",maxlength:"200",class:"input-field",placeholder:"Inserisci il luogo dell'evento"},null,512),[[a,i.form.location]])]),e("div",B,[e("div",null,[t[27]||(t[27]=e("label",{for:"maxParticipants",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Numero massimo partecipanti ",-1)),r(e("input",{id:"maxParticipants","onUpdate:modelValue":t[8]||(t[8]=o=>i.form.maxParticipants=o),type:"number",min:"1",max:"1000",class:"input-field",placeholder:"Illimitato"},null,512),[[a,i.form.maxParticipants,void 0,{number:!0}]])]),i.form.requiresRegistration?(u(),f("div",N,[t[28]||(t[28]=e("label",{for:"registrationDeadline",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Scadenza registrazione ",-1)),r(e("input",{id:"registrationDeadline","onUpdate:modelValue":t[9]||(t[9]=o=>i.form.registrationDeadline=o),type:"datetime-local",min:n.minDateTime,max:i.form.startDate,class:"input-field"},null,8,A),[[a,i.form.registrationDeadline]])])):m("",!0)]),e("div",H,[t[33]||(t[33]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Opzioni evento",-1)),e("div",L,[r(e("input",{id:"requiresRegistration","onUpdate:modelValue":t[10]||(t[10]=o=>i.form.requiresRegistration=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"},null,512),[[d,i.form.requiresRegistration]]),t[29]||(t[29]=e("label",{for:"requiresRegistration",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Richiede registrazione ",-1))]),e("div",Q,[r(e("input",{id:"isPublic","onUpdate:modelValue":t[11]||(t[11]=o=>i.form.isPublic=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"},null,512),[[d,i.form.isPublic]]),t[30]||(t[30]=e("label",{for:"isPublic",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Evento pubblico ",-1))]),e("div",W,[r(e("input",{id:"sendReminders","onUpdate:modelValue":t[12]||(t[12]=o=>i.form.sendReminders=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"},null,512),[[d,i.form.sendReminders]]),t[31]||(t[31]=e("label",{for:"sendReminders",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Invia promemoria ",-1))]),e("div",X,[r(e("input",{id:"allowComments","onUpdate:modelValue":t[13]||(t[13]=o=>i.form.allowComments=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"},null,512),[[d,i.form.allowComments]]),t[32]||(t[32]=e("label",{for:"allowComments",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Permetti commenti ",-1))])]),e("div",null,[t[34]||(t[34]=e("label",{for:"tags",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tag ",-1)),r(e("input",{id:"tags","onUpdate:modelValue":t[14]||(t[14]=o=>i.tagsInput=o),type:"text",class:"input-field",placeholder:"Inserisci i tag separati da virgola"},null,512),[[a,i.tagsInput]]),t[35]||(t[35]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400 mt-1"}," Separa i tag con virgole (es: formazione, team building, obbligatorio) ",-1))]),e("div",G,[e("button",{type:"button",onClick:t[15]||(t[15]=(...o)=>n.closeModal&&n.closeModal(...o)),class:"btn-secondary"}," Annulla "),e("button",{type:"submit",disabled:i.isSubmitting||!n.isFormValid,class:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center"},[i.isSubmitting?(u(),V(v,{key:0,class:"mr-2",size:"sm"})):m("",!0),z(" "+g(i.isSubmitting?"Salvataggio...":"Aggiorna Evento"),1)],8,J)])],32)])])):m("",!0)}const _=y(C,[["render",K],["__scopeId","data-v-ec44e1e6"]]);export{_ as E};
