import{r as m,c as p,w as ae,x as ne,y as oe,b as o,o as n,l as r,h as ie,n as v,e as i,j as t,t as d,F as z,p as M,s as S,B as $,C as V,D as le,A as R,k as U,z as K,u as re}from"./vendor.js";import{u as ce}from"./help.js";import{_ as de,H as c,d as ue}from"./app.js";import{M as me}from"./MarkdownContent.js";import{_ as pe}from"./BaseModal.js";import{S as he}from"./StandardButton.js";const ve={class:"help-chat-widget"},ge={key:0,class:"absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full border-2 border-white"},be={class:"flex items-center justify-between p-4 bg-gradient-to-r from-brand-primary-600 to-brand-primary-700 text-white"},fe={class:"flex items-center space-x-3 flex-1"},xe={key:0,class:"px-2 py-1 text-xs bg-white/20 rounded-full"},ye={class:"flex items-center space-x-1"},_e=["aria-label"],ke={key:0,class:"flex-1 flex flex-col overflow-hidden"},we={key:0,class:"p-6 text-center"},Ce={class:"max-w-xs mx-auto"},ze={class:"space-y-2"},Me=["onClick"],Se={key:0,class:"user-message"},Te={class:"message-content"},Ae={class:"message-time"},Fe={key:1,class:"assistant-message"},We={class:"message-avatar"},De={class:"message-bubble"},Ee={class:"message-content"},Ie={key:0,class:"message-meta"},je={key:0,class:"confidence-indicator"},Be={key:1,class:"category-tag"},He={key:1,class:"suggested-actions"},Le={class:"actions-grid"},Ne=["onClick"],$e={key:2,class:"escalation-warning"},Ve={class:"message-time"},Re={key:0,class:"typing-indicator"},Ue={class:"message-avatar"},Ke={key:1,class:"chat-input"},Pe={class:"input-wrapper"},qe=["disabled","onKeydown"],Oe=["disabled"],Qe={class:"input-footer"},Ge={class:"char-count"},Je={class:"feedback-form"},Xe={class:"mb-4"},Ye={class:"rating-buttons"},Ze=["onClick"],et={class:"mb-4"},tt={class:"flex justify-end space-x-3"},st={__name:"HelpChatWidget",setup(at){const P=re(),l=ce(),f=ue(),u=m(""),g=m(!1),b=m(5),x=m(""),T=m(!1),y=m(null),A=m(null),_=p(()=>l.chatWidget.isOpen),h=p(()=>l.chatWidget.isMinimized),F=p(()=>l.chatWidget.messages),k=p(()=>l.chatLoading),W=p(()=>l.chatWidget.contextModule),E=p(()=>l.hasUnreadMessages),q=p(()=>{const e=[{text:"Come posso iniziare?"},{text:"Dove trovo le guide?"}],s={projects:[{text:"Come creo un progetto?"},{text:"Come assegno task?"}],timesheets:[{text:"Come registro le ore?"},{text:"Come funziona l'approvazione?"}],personnel:[{text:"Come aggiorno il profilo?"},{text:"Come vedo le competenze?"}]};return[...e,...s[W.value]||[]].slice(0,3)}),I=async()=>{const e=Q();l.openChatWidget(e),await K(),A.value&&A.value.focus()},j=()=>{l.closeChatWidget()},O=()=>{h.value?l.maximizeChatWidget():l.minimizeChatWidget()},Q=()=>{const e=P.path;return e.includes("/projects")?"projects":e.includes("/personnel")?"personnel":e.includes("/timesheet")?"timesheets":e.includes("/crm")?"crm":e.includes("/recruiting")?"recruiting":e.includes("/certifications")?"certifications":e.includes("/ceo")?"ceo":e.includes("/funding")?"funding":e.includes("/engagement")?"engagement":e.includes("/communication")?"communication":e.includes("/admin")?"admin":"dashboard"},G=e=>({dashboard:"Dashboard",projects:"Progetti",personnel:"Personale",timesheets:"Timesheet",crm:"CRM",recruiting:"Recruiting",certifications:"Certificazioni",ceo:"CEO",funding:"Bandi",engagement:"Engagement",communication:"Comunicazione",admin:"Admin"})[e]||e,D=async()=>{if(!u.value.trim()||k.value)return;const e=u.value.trim();u.value="";try{await l.sendChatMessage(e),await H()}catch(s){f.error("Errore nell'invio del messaggio: "+s.message)}},J=async e=>{u.value=e,await D()},X=e=>{switch(e.type){case"view_content":case"module_help":case"search_help":e.url&&window.open(e.url,"_blank");break;case"contact_support":B();break}},B=()=>{f.info("Reindirizzamento al sistema di supporto...")},Y=async()=>{var e;T.value=!0;try{await l.submitFeedback({feedback_type:"ai_response",rating:b.value,feedback_text:x.value,conversation_id:(e=l.currentConversation)==null?void 0:e.id}),f.success("Grazie per il feedback!"),g.value=!1,b.value=5,x.value=""}catch{f.error("Errore nell'invio del feedback")}finally{T.value=!1}},H=async()=>{await K(),y.value&&(y.value.scrollTop=y.value.scrollHeight)},L=e=>new Date(e).toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit"}),Z=e=>e>=.8?"check-circle":e>=.6?"information-circle":"exclamation-triangle",ee=e=>e>=.8?"text-green-500":e>=.6?"text-blue-500":"text-yellow-500",te=e=>e>=.8?"Molto sicuro":e>=.6?"Abbastanza sicuro":"Poco sicuro",se=e=>({view_content:"document-text",module_help:"book-open",search_help:"magnifying-glass",contact_support:"chat-bubble-left-ellipsis"})[e]||"arrow-right";ae(F,()=>{H()},{deep:!0});const N=e=>{e.key==="Escape"&&_.value&&j(),e.key==="F1"&&(e.preventDefault(),_.value||I())};return ne(()=>{document.addEventListener("keydown",N)}),oe(()=>{document.removeEventListener("keydown",N)}),(e,s)=>(n(),o("div",ve,[_.value?r("",!0):(n(),o("button",{key:0,onClick:I,class:v(["w-14 h-14 bg-brand-primary-600 hover:bg-brand-primary-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center relative",{"with-notification":E.value}]),style:{animation:"pulse-soft 2s infinite"},"aria-label":"Apri assistente help"},[i(c,{name:"question-mark-circle",class:"w-6 h-6"}),E.value?(n(),o("span",ge)):r("",!0)],2)),_.value?(n(),o("div",{key:1,class:v(["w-96 bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col overflow-hidden",h.value?"h-14":"h-[500px]"]),style:{"margin-right":"-16px","margin-bottom":"16px"}},[t("div",be,[t("div",fe,[i(c,{name:"academic-cap",class:"w-5 h-5 text-brand-primary-600"}),s[5]||(s[5]=t("h3",{class:"font-semibold text-sm"},"Assistente Help",-1)),W.value?(n(),o("span",xe,d(G(W.value)),1)):r("",!0)]),t("div",ye,[t("button",{onClick:O,class:"p-1 hover:bg-white/20 rounded transition-colors duration-150","aria-label":h.value?"Espandi chat":"Minimizza chat"},[i(c,{name:h.value?"chevron-up":"chevron-down",class:"w-4 h-4"},null,8,["name"])],8,_e),t("button",{onClick:j,class:"p-1 hover:bg-white/20 rounded transition-colors duration-150","aria-label":"Chiudi chat"},[i(c,{name:"x-mark",class:"w-4 h-4"})])])]),h.value?r("",!0):(n(),o("div",ke,[F.value.length===0?(n(),o("div",we,[t("div",Ce,[i(c,{name:"chat-bubble-left-right",class:"w-12 h-12 text-brand-primary-300 mx-auto mb-3"}),s[6]||(s[6]=t("h4",{class:"text-sm font-medium text-gray-900 mb-2"}," Ciao! Sono il tuo assistente help 👋 ",-1)),s[7]||(s[7]=t("p",{class:"text-xs text-gray-600 mb-4"}," Chiedi qualsiasi cosa su DatPortal. Ti aiuterò a trovare le risposte! ",-1)),t("div",ze,[(n(!0),o(z,null,M(q.value,a=>(n(),o("button",{key:a.text,onClick:w=>J(a.text),class:"w-full px-3 py-2 text-xs bg-gray-50 hover:bg-gray-100 text-gray-700 rounded-lg transition-colors duration-150"},d(a.text),9,Me))),128))])])])):(n(),o("div",{key:1,class:"flex-1 overflow-y-auto p-4 space-y-4 chat-messages",ref_key:"messagesContainer",ref:y},[(n(!0),o(z,null,M(F.value,a=>{var w;return n(),o("div",{key:a.id,class:v(["message",a.type])},[a.type==="user"?(n(),o("div",Se,[t("div",Te,d(a.content),1),t("div",Ae,d(L(a.timestamp)),1)])):(n(),o("div",Fe,[t("div",We,[i(c,{name:"academic-cap",class:"w-4 h-4 text-brand-primary-600"})]),t("div",De,[t("div",Ee,[i(me,{content:a.content,class:"text-sm"},null,8,["content"])]),a.confidence||a.category?(n(),o("div",Ie,[a.confidence?(n(),o("span",je,[i(c,{name:Z(a.confidence),class:v(["w-3 h-3",ee(a.confidence)])},null,8,["name","class"]),S(" "+d(te(a.confidence)),1)])):r("",!0),a.category?(n(),o("span",Be,d(a.category),1)):r("",!0)])):r("",!0),(w=a.suggested_actions)!=null&&w.length?(n(),o("div",He,[s[8]||(s[8]=t("h5",{class:"text-xs font-medium text-gray-700 mb-2"},"Azioni suggerite:",-1)),t("div",Le,[(n(!0),o(z,null,M(a.suggested_actions,C=>(n(),o("button",{key:C.label,onClick:nt=>X(C),class:"action-btn"},[i(c,{name:se(C.type),class:"w-3 h-3"},null,8,["name"]),S(" "+d(C.label),1)],8,Ne))),128))])])):r("",!0),a.escalation_recommended?(n(),o("div",$e,[i(c,{name:"exclamation-triangle",class:"w-4 h-4 text-yellow-500"}),s[9]||(s[9]=t("span",{class:"text-xs text-yellow-700"}," Questo problema potrebbe richiedere assistenza umana. ",-1)),t("button",{onClick:B,class:"escalate-btn"}," Contatta Supporto ")])):r("",!0),t("div",Ve,d(L(a.timestamp)),1)])]))],2)}),128)),k.value?(n(),o("div",Re,[t("div",Ue,[i(c,{name:"academic-cap",class:"w-4 h-4 text-brand-primary-600"})]),s[10]||(s[10]=t("div",{class:"typing-dots"},[t("span"),t("span"),t("span")],-1))])):r("",!0)],512))])),h.value?r("",!0):(n(),o("div",Ke,[t("form",{onSubmit:R(D,["prevent"]),class:"input-form"},[t("div",Pe,[$(t("input",{"onUpdate:modelValue":s[0]||(s[0]=a=>u.value=a),type:"text",placeholder:"Scrivi la tua domanda...",class:"message-input",disabled:k.value,maxlength:"500",ref_key:"messageInput",ref:A,onKeydown:le(R(D,["prevent"]),["enter"])},null,40,qe),[[V,u.value]]),t("button",{type:"submit",disabled:!u.value.trim()||k.value,class:"send-btn","aria-label":"Invia messaggio"},[i(c,{name:"paper-airplane",class:"w-4 h-4"})],8,Oe)]),t("div",Qe,[t("span",Ge,d(u.value.length)+"/500",1),t("button",{type:"button",onClick:s[1]||(s[1]=a=>g.value=!0),class:"feedback-btn"},[i(c,{name:"hand-thumb-up",class:"w-3 h-3"}),s[11]||(s[11]=S(" Feedback "))])])],32)]))],2)):r("",!0),g.value?(n(),ie(pe,{key:2,onClose:s[4]||(s[4]=a=>g.value=!1),title:"Feedback sull'Assistente",size:"sm"},{default:U(()=>[t("div",Je,[t("div",Xe,[s[12]||(s[12]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Come valuteresti l'assistente? ",-1)),t("div",Ye,[(n(),o(z,null,M([1,2,3,4,5],a=>t("button",{key:a,onClick:w=>b.value=a,class:v(["rating-btn",{selected:b.value===a}])},[i(c,{name:"star",class:v(["w-4 h-4",b.value>=a?"text-yellow-500":"text-gray-300"])},null,8,["class"])],10,Ze)),64))])]),t("div",et,[s[13]||(s[13]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Commenti (opzionale) ",-1)),$(t("textarea",{"onUpdate:modelValue":s[2]||(s[2]=a=>x.value=a),rows:"3",class:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm",placeholder:"Aiutaci a migliorare l'assistente..."},null,512),[[V,x.value]])]),t("div",tt,[t("button",{onClick:s[3]||(s[3]=a=>g.value=!1),class:"px-4 py-2 text-sm text-gray-600 hover:text-gray-800"}," Annulla "),i(he,{onClick:Y,loading:T.value,variant:"primary",size:"sm"},{default:U(()=>s[14]||(s[14]=[S(" Invia Feedback ")])),_:1,__:[14]},8,["loading"])])])]),_:1})):r("",!0)]))}},ut=de(st,[["__scopeId","data-v-24ccab8b"]]);export{ut as H};
