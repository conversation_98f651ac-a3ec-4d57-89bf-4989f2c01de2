import{r as n,c as p,u as k,x as I,b,e as f,j as i,l as S,v as F,q as E,z as y,o as C}from"./vendor.js";import{u as N}from"./crm.js";import{d as R}from"./app.js";import{a as V}from"./industries.js";import{_ as B}from"./PageHeader.js";import{F as L}from"./FormBuilder.js";import"./AlertsSection.js";/* empty css                                                           */const M={class:"min-h-screen bg-gray-50 dark:bg-gray-900"},T={class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},U={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 dark:bg-gray-900 dark:bg-opacity-70 flex items-center justify-center z-50"},H={__name:"ClientForm",setup(j){const u=k(),c=E(),d=N(),{showToast:r}=R(),m=n(!1),s=n(!1),o=n({}),a=n({name:"",industry:"",description:"",website:"",address:"",status:"lead"}),l=p(()=>u.params.id&&u.params.id!=="new"),v=p(()=>l.value?parseInt(u.params.id):null),w=p(()=>[{id:"name",type:"text",label:"Nome Cliente",required:!0,placeholder:"Es. TechCorp Solutions"},{id:"industry",type:"select",label:"Settore",placeholder:"Seleziona settore",options:V()},{id:"website",type:"text",label:"Sito Web",placeholder:"https://esempio.com"},{id:"status",type:"select",label:"Stato",options:[{value:"lead",label:"Lead"},{value:"prospect",label:"Prospect"},{value:"client",label:"Cliente"},{value:"inactive",label:"Inattivo"}]},{id:"address",type:"textarea",label:"Indirizzo",placeholder:"Indirizzo completo del cliente",rows:3},{id:"description",type:"textarea",label:"Descrizione",placeholder:"Note e informazioni aggiuntive sul cliente",rows:4}]),h=async()=>{if(l.value)try{s.value=!0;const e=await d.getClient(v.value);if(console.log("Client data loaded:",e),e){const t={name:e.name||"",industry:e.industry||"",description:e.description||"",website:e.website||"",address:e.address||"",status:e.status||"lead"};console.log("Setting form data:",t),console.log("Industry value:",e.industry),await y(),a.value=t,await y(),console.log("Form after setting:",a.value)}}catch{r("Errore nel caricamento del cliente","error"),c.push("/app/crm/clients")}finally{s.value=!1}},x=()=>(o.value={},a.value.name.trim()||(o.value.name="Il nome del cliente è obbligatorio"),a.value.website&&!_(a.value.website)&&(o.value.website="Inserisci un URL valido"),Object.keys(o.value).length===0),_=e=>{try{return new URL(e),!0}catch{return!1}},z=async()=>{if(!x()){r("Correggi gli errori nel form","error");return}try{m.value=!0,l.value?(await d.updateClient(v.value,a.value),r("Cliente aggiornato con successo","success")):(await d.createClient(a.value),r("Cliente creato con successo","success")),c.push("/app/crm/clients")}catch(e){console.error("Error saving client:",e),r(l.value?"Errore nell'aggiornamento del cliente":"Errore nella creazione del cliente","error")}finally{m.value=!1}};return I(()=>{h()}),(e,t)=>(C(),b("div",M,[f(B,{title:l.value?"Modifica Cliente":"Nuovo Cliente",subtitle:l.value?"Aggiorna le informazioni del cliente":"Inserisci i dati del nuovo cliente",breadcrumbs:[{name:"CRM",href:"/app/crm"},{name:"Clienti",href:"/app/crm/clients"},{name:l.value?"Modifica":"Nuovo",href:"#",current:!0}],loading:s.value},null,8,["title","subtitle","breadcrumbs","loading"]),i("div",T,[f(L,{fields:w.value,"model-value":a.value,loading:m.value,errors:o.value,"onUpdate:modelValue":t[0]||(t[0]=g=>a.value=g),onSubmit:z,onCancel:t[1]||(t[1]=g=>F(c).push("/app/crm/clients")),"submit-label":l.value?"Aggiorna Cliente":"Crea Cliente","cancel-label":"Annulla"},null,8,["fields","model-value","loading","errors","submit-label"])]),s.value?(C(),b("div",U,t[2]||(t[2]=[i("div",{class:"bg-white dark:bg-gray-800 rounded-lg p-6"},[i("div",{class:"flex items-center"},[i("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4"}),i("span",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Caricamento...")])],-1)]))):S("",!0)]))}};export{H as default};
