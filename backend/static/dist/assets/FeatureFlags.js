import{r as m,c as O,w as Q,z as de,b as v,l as f,o as d,j as e,e as u,t as o,A as ue,B as F,s as y,C as q,V as X,k as $,x as ce,h as S,v as c,H as me,F as ge,p as fe,n as Y}from"./vendor.js";import{H as x,k as pe,d as ve}from"./app.js";import{u as ye}from"./useDebounce.js";import{S as C}from"./StandardButton.js";import{A as xe}from"./ActionButtonGroup.js";import{S as _e}from"./StatusBadge.js";import{P as be}from"./Pagination.js";import{C as he}from"./ConfirmationModal.js";const ke={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},we={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Fe={class:"relative inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full sm:p-6"},ze={class:"flex items-center justify-between mb-6"},$e={class:"flex items-center"},Ce={class:"flex-shrink-0 flex items-center justify-center h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900"},Ee={class:"ml-4"},De={class:"text-lg leading-6 font-medium text-gray-900 dark:text-white",id:"modal-title"},Ae={class:"text-sm text-gray-500 dark:text-gray-400"},Ne={key:0,class:"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4"},Se={class:"flex"},Me={class:"flex-shrink-0"},Ve={class:"ml-3"},Be={class:"mt-2 text-sm text-red-700 dark:text-red-300"},Te=["disabled"],je={class:"mt-1 text-xs text-gray-500 dark:text-gray-400"},Ie={key:0,class:"mt-1 text-xs text-red-600"},Le={class:"flex items-center space-x-6"},Pe={class:"flex items-center"},Ue={class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},qe={class:"flex items-center"},He={class:"flex items-center"},Ge={class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},Oe={class:"flex items-center"},Qe={class:"mt-1 text-xs text-gray-500 dark:text-gray-400"},Re={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700"},Ke={__name:"FeatureFlagModal",props:{show:{type:Boolean,default:!1},flag:{type:Object,default:null}},emits:["close","save"],setup(H,{emit:z}){const g=H,M=z,k=m(!1),w=m(""),_=m(""),r=m({feature_key:"",display_name:"",description:"",is_enabled:!0}),h=O(()=>!!g.flag),V=O(()=>r.value.feature_key&&r.value.display_name&&!_.value&&r.value.feature_key.length>=2),E=()=>{k.value||(B(),M("close"))},B=()=>{r.value={feature_key:"",display_name:"",description:"",is_enabled:!0},w.value="",_.value=""},T=()=>{const i=r.value.feature_key;if(_.value="",!(!i||typeof i!="string")){if(!/^[a-z][a-z0-9_]*$/.test(i)){_.value="La chiave deve iniziare con una lettera e contenere solo lettere minuscole, numeri e underscore";return}if(i.length<2){_.value="La chiave deve essere lunga almeno 2 caratteri";return}i.length>100&&(_.value="La chiave non può superare i 100 caratteri")}},p=async()=>{if(V.value){k.value=!0,w.value="";try{const i={feature_key:String(r.value.feature_key||""),display_name:String(r.value.display_name||"").trim(),description:r.value.description&&typeof r.value.description=="string"&&r.value.description.trim()?r.value.description.trim():null,is_enabled:!!r.value.is_enabled};M("save",i)}catch(i){w.value=i.message||"Errore nel salvataggio del feature flag"}finally{k.value=!1}}};return Q(()=>g.show,i=>{i&&(g.flag?r.value={feature_key:g.flag.feature_key||"",display_name:g.flag.display_name||"",description:g.flag.description&&typeof g.flag.description=="string"?g.flag.description:"",is_enabled:!!g.flag.is_enabled}:B(),de(()=>{const t=document.getElementById(h.value?"display_name":"feature_key");t&&t.focus()}))}),Q(()=>r.value.feature_key,T),(i,t)=>H.show?(d(),v("div",ke,[e("div",we,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:E}),t[15]||(t[15]=e("span",{class:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true"},"​",-1)),e("div",Fe,[e("div",ze,[e("div",$e,[e("div",Ce,[u(x,{name:"flag",size:"md",class:"text-primary-600 dark:text-primary-400"})]),e("div",Ee,[e("h3",De,o(h.value?"Modifica Feature Flag":"Nuovo Feature Flag"),1),e("p",Ae,o(h.value?"Aggiorna le impostazioni del feature flag":"Crea un nuovo feature flag per controllare le funzionalità"),1)])]),e("button",{type:"button",class:"rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500",onClick:E},[u(x,{name:"x-mark",size:"md"})])]),e("form",{onSubmit:ue(p,["prevent"]),class:"space-y-6"},[w.value?(d(),v("div",Ne,[e("div",Se,[e("div",Me,[u(x,{name:"exclamation-circle",size:"md",color:"text-red-400"})]),e("div",Ve,[t[5]||(t[5]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore",-1)),e("div",Be,o(w.value),1)])])])):f("",!0),e("div",null,[t[6]||(t[6]=e("label",{for:"feature_key",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},[y(" Chiave Feature "),e("span",{class:"text-red-500"},"*")],-1)),F(e("input",{id:"feature_key","onUpdate:modelValue":t[0]||(t[0]=n=>r.value.feature_key=n),type:"text",required:"",disabled:h.value,placeholder:"es. oauth_enabled",pattern:"^[a-z][a-z0-9_]*$",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-600 disabled:text-gray-500",onInput:T},null,40,Te),[[q,r.value.feature_key]]),e("p",je,o(h.value?"La chiave non può essere modificata":"Solo lettere minuscole, numeri e underscore. Deve iniziare con una lettera."),1),_.value?(d(),v("p",Ie,o(_.value),1)):f("",!0)]),e("div",null,[t[7]||(t[7]=e("label",{for:"display_name",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},[y(" Nome Visualizzato "),e("span",{class:"text-red-500"},"*")],-1)),F(e("input",{id:"display_name","onUpdate:modelValue":t[1]||(t[1]=n=>r.value.display_name=n),type:"text",required:"",placeholder:"es. OAuth Abilitato",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[q,r.value.display_name]]),t[8]||(t[8]=e("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Nome human-readable che apparirà nell'interfaccia admin ",-1))]),e("div",null,[t[9]||(t[9]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),F(e("textarea",{id:"description","onUpdate:modelValue":t[2]||(t[2]=n=>r.value.description=n),rows:"3",placeholder:"Descrizione opzionale del feature flag...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[q,r.value.description]]),t[10]||(t[10]=e("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Descrizione opzionale per spiegare a cosa serve questo feature flag ",-1))]),e("div",null,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"}," Stato Iniziale ",-1)),e("div",Le,[e("label",Pe,[F(e("input",{"onUpdate:modelValue":t[3]||(t[3]=n=>r.value.is_enabled=n),type:"radio",value:!0,class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"},null,512),[[X,r.value.is_enabled]]),e("span",Ue,[e("span",qe,[u(x,{name:"check-circle",size:"sm",className:"text-green-500 mr-1"}),t[11]||(t[11]=y(" Abilitato "))])])]),e("label",He,[F(e("input",{"onUpdate:modelValue":t[4]||(t[4]=n=>r.value.is_enabled=n),type:"radio",value:!1,class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"},null,512),[[X,r.value.is_enabled]]),e("span",Ge,[e("span",Oe,[u(x,{name:"x-circle",size:"sm",className:"text-red-500 mr-1"}),t[12]||(t[12]=y(" Disabilitato "))])])])]),e("p",Qe,o(h.value?"Puoi modificare lo stato dopo aver salvato":"Lo stato può essere modificato in qualsiasi momento"),1)]),e("div",Re,[u(C,{onClick:E,variant:"secondary",disabled:k.value},{default:$(()=>t[14]||(t[14]=[y(" Annulla ")])),_:1,__:[14]},8,["disabled"]),u(C,{type:"submit",variant:"primary",loading:k.value,disabled:!V.value},{default:$(()=>[y(o(h.value?"Aggiorna":"Crea")+" Feature Flag ",1)]),_:1},8,["loading","disabled"])])],32)])])])):f("",!0)}},Je={class:"flex justify-between items-center mb-6"},We={class:"flex space-x-3"},Xe={class:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6"},Ye={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Ze={class:"p-5"},et={class:"flex items-center"},tt={class:"flex-shrink-0"},at={class:"ml-5 w-0 flex-1"},st={class:"text-lg font-medium text-gray-900 dark:text-white"},rt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},lt={class:"p-5"},it={class:"flex items-center"},ot={class:"flex-shrink-0"},nt={class:"ml-5 w-0 flex-1"},dt={class:"text-lg font-medium text-gray-900 dark:text-white"},ut={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},ct={class:"p-5"},mt={class:"flex items-center"},gt={class:"flex-shrink-0"},ft={class:"ml-5 w-0 flex-1"},pt={class:"text-lg font-medium text-gray-900 dark:text-white"},vt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6"},yt={class:"flex flex-col md:flex-row md:items-center md:space-x-4 space-y-4 md:space-y-0"},xt={class:"flex-1"},_t={class:"relative"},bt={class:"w-full md:w-48"},ht={key:0,class:"flex justify-center py-12"},kt={key:1,class:"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4 mb-6"},wt={class:"flex"},Ft={class:"flex-shrink-0"},zt={class:"ml-3"},$t={class:"mt-2 text-sm text-red-700 dark:text-red-300"},Ct={class:"ml-auto pl-3"},Et={key:2,class:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md"},Dt={class:"divide-y divide-gray-200 dark:divide-gray-700"},At={class:"flex items-center justify-between"},Nt={class:"flex-1 min-w-0"},St={class:"flex items-center space-x-3"},Mt={class:"flex-1"},Vt={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},Bt={class:"text-sm text-gray-500 dark:text-gray-400"},Tt={class:"bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs"},jt={key:0,class:"ml-2"},It={class:"flex items-center space-x-4 mt-1"},Lt={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Pt={class:"font-medium"},Ut={class:"text-xs text-gray-500 dark:text-gray-400"},qt={class:"flex items-center space-x-3"},Ht=["onClick","disabled"],Gt={key:3,class:"text-center py-12"},Ot={class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},Qt={class:"text-gray-500 dark:text-gray-400 mb-6"},ta={__name:"FeatureFlags",setup(H){const{flagsDetails:z,isLoading:g,hasError:M,error:k,fetchAdminFlags:w,updateFlag:_,createFlag:r,deleteFlag:h,toggleFlag:V,getEnabledCount:E,getDisabledCount:B,getTotalCount:T}=pe(),{showToast:p}=ve(),i=m(""),t=m(""),n=m(null),j=m(1),G=m(!1),I=m(null),D=m(!1),L=m(!1),A=m(!1),N=m(null),P=m(null),U=O(()=>i.value||t.value),R=ye(()=>{b()},300),b=async()=>{try{const l={page:j.value,perPage:20};i.value&&i.value.trim()&&(l.search=i.value.trim());const a=await w(l);n.value=a.pagination}catch{p("Errore nel caricamento feature flags","error")}},Z=async()=>{G.value=!0;try{await b(),p("Feature flags aggiornati","success")}catch{p("Errore nell'aggiornamento","error")}finally{G.value=!1}},ee=()=>{i.value="",t.value="",j.value=1,b()},te=()=>{},ae=async l=>{I.value=l.id;try{await V(l),p(`Feature flag ${l.is_enabled?"disabilitato":"abilitato"}`,"success"),await b()}catch{p("Errore nel toggle del feature flag","error")}finally{I.value=null}},se=l=>{N.value={...l},L.value=!0},re=l=>{P.value=l,A.value=!0},le=async()=>{try{await h(P.value.id),p("Feature flag eliminato","success"),A.value=!1,P.value=null,await b()}catch{p("Errore nell'eliminazione","error")}},K=()=>{D.value=!1,L.value=!1,N.value=null},ie=async l=>{try{N.value?(await _(N.value.id,l),p("Feature flag aggiornato","success")):(await r(l),p("Feature flag creato","success")),K(),await b()}catch{p("Errore nel salvataggio","error")}},oe=l=>{j.value=l,b()},ne=l=>{if(!l||typeof l!="string")return"N/A";try{return new Date(l).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})}catch{return"N/A"}};return Q([t],()=>{j.value=1,b()}),ce(()=>{b()}),(l,a)=>{var J;return d(),v("div",null,[e("div",Je,[a[8]||(a[8]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Feature Flags"),e("p",{class:"text-gray-600 dark:text-gray-400"}," Gestisci le funzionalità del sistema per controllo granulare delle features ")],-1)),e("div",We,[u(C,{onClick:a[0]||(a[0]=s=>D.value=!0),variant:"primary",size:"md",icon:{name:"plus",position:"left"}},{default:$(()=>a[6]||(a[6]=[y(" Nuovo Feature Flag ")])),_:1,__:[6]}),u(C,{onClick:Z,variant:"secondary",size:"md",icon:{name:"arrow-path",position:"left"},loading:G.value},{default:$(()=>a[7]||(a[7]=[y(" Aggiorna ")])),_:1,__:[7]},8,["loading"])])]),e("div",Xe,[e("div",Ye,[e("div",Ze,[e("div",et,[e("div",tt,[u(x,{name:"flag",size:"md",className:"text-gray-400"})]),e("div",at,[e("dl",null,[a[9]||(a[9]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Totale Feature Flags ",-1)),e("dd",st,o(c(T)),1)])])])])]),e("div",rt,[e("div",lt,[e("div",it,[e("div",ot,[u(x,{name:"check-circle",size:"md",className:"text-green-400"})]),e("div",nt,[e("dl",null,[a[10]||(a[10]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Abilitati ",-1)),e("dd",dt,o(c(E)),1)])])])])]),e("div",ut,[e("div",ct,[e("div",mt,[e("div",gt,[u(x,{name:"x-circle",size:"md",className:"text-red-400"})]),e("div",ft,[e("dl",null,[a[11]||(a[11]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Disabilitati ",-1)),e("dd",pt,o(c(B)),1)])])])])])]),e("div",vt,[e("div",yt,[e("div",xt,[e("div",_t,[u(x,{name:"magnifying-glass",size:"sm",className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),F(e("input",{"onUpdate:modelValue":a[1]||(a[1]=s=>i.value=s),type:"text",placeholder:"Cerca feature flags...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",onInput:a[2]||(a[2]=(...s)=>c(R)&&c(R)(...s))},null,544),[[q,i.value]])])]),e("div",bt,[F(e("select",{"onUpdate:modelValue":a[3]||(a[3]=s=>t.value=s),onChange:b,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},a[12]||(a[12]=[e("option",{value:""},"Tutti gli stati",-1),e("option",{value:"enabled"},"Solo abilitati",-1),e("option",{value:"disabled"},"Solo disabilitati",-1)]),544),[[me,t.value]])]),U.value?(d(),S(C,{key:0,onClick:ee,variant:"secondary",size:"sm",icon:{name:"x-mark",position:"left"}},{default:$(()=>a[13]||(a[13]=[y(" Reset ")])),_:1,__:[13]})):f("",!0)])]),c(g)?(d(),v("div",ht,a[14]||(a[14]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"},null,-1)]))):f("",!0),c(M)?(d(),v("div",kt,[e("div",wt,[e("div",Ft,[u(x,{name:"exclamation-circle",size:"md",color:"text-red-400"})]),e("div",zt,[a[15]||(a[15]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore",-1)),e("div",$t,o(c(k)),1)]),e("div",Ct,[e("button",{onClick:te,class:"text-red-400 hover:text-red-600"},[u(x,{name:"x-mark",size:"md"})])])])])):f("",!0),!c(g)&&c(z).length>0?(d(),v("div",Et,[e("ul",Dt,[(d(!0),v(ge,null,fe(c(z),s=>(d(),v("li",{key:s.id,class:"px-6 py-4"},[e("div",At,[e("div",Nt,[e("div",St,[u(_e,{status:s.is_enabled?"active":"inactive",text:s.is_enabled?"Abilitato":"Disabilitato"},null,8,["status","text"]),e("div",Mt,[e("p",Vt,o(s.display_name),1),e("p",Bt,[e("code",Tt,o(s.feature_key),1),s.description&&typeof s.description=="string"?(d(),v("span",jt,o(s.description),1)):f("",!0)]),e("div",It,[s.updated_by&&typeof s.updated_by=="string"?(d(),v("span",Lt,[a[16]||(a[16]=y(" Modificato da: ")),e("span",Pt,o(s.updated_by),1)])):f("",!0),e("span",Ut,o(ne(s.updated_at)),1)])])])]),e("div",qt,[e("button",{onClick:W=>ae(s),disabled:I.value===s.id,class:Y(["relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",s.is_enabled?"bg-primary-600":"bg-gray-200",I.value===s.id?"opacity-50":""])},[e("span",{class:Y([s.is_enabled?"translate-x-5":"translate-x-0","pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"])},null,2)],10,Ht),u(xe,{"show-view":!1,onEdit:W=>se(s),onDelete:W=>re(s),"delete-message":`Sei sicuro di voler eliminare il feature flag '${s.display_name}'?`},null,8,["onEdit","onDelete","delete-message"])])])]))),128))])])):f("",!0),!c(g)&&c(z).length===0?(d(),v("div",Gt,[e("h3",Ot,o(U.value?"Nessun flag trovato":"Nessun feature flag configurato"),1),e("p",Qt,o(U.value?"Prova a modificare i filtri di ricerca":"Inizia creando il tuo primo feature flag per controllare le funzionalità del sistema"),1),U.value?f("",!0):(d(),S(C,{key:0,onClick:a[4]||(a[4]=s=>D.value=!0),variant:"primary",icon:{name:"plus",position:"left"}},{default:$(()=>a[17]||(a[17]=[y(" Crea il primo Feature Flag ")])),_:1,__:[17]}))])):f("",!0),!c(g)&&c(z).length>0&&n.value&&n.value.pages>1?(d(),S(be,{key:4,"current-page":n.value.page,"total-pages":n.value.pages,"total-items":n.value.total,"per-page":n.value.per_page,onPageChange:oe,class:"mt-6"},null,8,["current-page","total-pages","total-items","per-page"])):f("",!0),D.value||L.value?(d(),S(Ke,{key:5,show:D.value||L.value,flag:N.value,onClose:K,onSave:ie},null,8,["show","flag"])):f("",!0),A.value?(d(),S(he,{key:6,show:A.value,title:"Elimina Feature Flag",message:`Sei sicuro di voler eliminare il feature flag '${(J=P.value)==null?void 0:J.display_name}'? Questa azione non può essere annullata.`,"confirm-text":"Elimina",variant:"danger",onConfirm:le,onCancel:a[5]||(a[5]=s=>A.value=!1)},null,8,["show","message"])):f("",!0)])}}};export{ta as default};
