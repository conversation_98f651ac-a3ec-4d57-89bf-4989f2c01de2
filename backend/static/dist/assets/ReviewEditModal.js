import{d as Te,r as p,c as M,w as ce,b as y,o as u,j as e,l as k,F as ae,p as ne,n as ue,t as v,z as je,e as f,E as he,s as G,B as K,C as oe,k as Y,x as Ee,h as H,A as ze,y as Ne,H as we,I as $e}from"./vendor.js";import{c as O,_ as ke,H as C,d as Be,a as Oe}from"./app.js";import{u as Ue}from"./personnel.js";import{C as Se}from"./ConfirmationModal.js";import{S as ie}from"./StatusBadge.js";import{S as q}from"./StandardButton.js";const Ve=Te("performance",()=>{const d=p([]),A=p([]),m=p([]),T=p({}),b=p({}),h=p(null),t=p(!1),s=p(null),$=p(new Map),B=p({page:1,per_page:20,total:0,total_pages:0}),_=p({search:"",status:"",employee_id:"",year:new Date().getFullYear(),review_type:""}),U=M(()=>{let a=d.value;if(_.value.search){const l=_.value.search.toLowerCase();a=a.filter(i=>{var o,n,R,N,D;return((n=(o=i.employee)==null?void 0:o.first_name)==null?void 0:n.toLowerCase().includes(l))||((N=(R=i.employee)==null?void 0:R.last_name)==null?void 0:N.toLowerCase().includes(l))||((D=i.review_type)==null?void 0:D.toLowerCase().includes(l))})}return _.value.status&&(a=a.filter(l=>l.status===_.value.status)),_.value.employee_id&&(a=a.filter(l=>l.employee_id===parseInt(_.value.employee_id))),_.value.year&&(a=a.filter(l=>l.review_year===parseInt(_.value.year))),_.value.review_type&&(a=a.filter(l=>l.review_type===_.value.review_type)),a}),I=M(()=>{const a={};return d.value.forEach(l=>{a[l.status]||(a[l.status]=[]),a[l.status].push(l)}),a}),z=(a,l=0)=>{const i=(a==null?void 0:a.per_page)||l||10;return{page:(a==null?void 0:a.page)||1,per_page:i,total:(a==null?void 0:a.total)||l||0,total_pages:(a==null?void 0:a.total_pages)||Math.ceil(((a==null?void 0:a.total)||l||0)/i)||1}},w=async(a={})=>{var l,i;t.value=!0,s.value=null;try{const o=new URLSearchParams({employee_id:a.employee_id||_.value.employee_id||"",year:a.year||_.value.year||new Date().getFullYear()}),n=await O.get(`/api/performance/dashboard?${o}`);n.data.success&&(T.value=n.data.data)}catch(o){s.value=((i=(l=o.response)==null?void 0:l.data)==null?void 0:i.message)||"Errore nel caricamento dashboard performance",console.error("Error fetching performance dashboard:",o)}finally{t.value=!1}},P=async(a={})=>{var l,i;t.value=!0,s.value=null;try{const o=new URLSearchParams({page:a.page||B.value.page,per_page:a.per_page||B.value.per_page,search:a.search||_.value.search,status:a.status||_.value.status,employee_id:a.employee_id||_.value.employee_id||"",year:a.year||_.value.year||"",review_type:a.review_type||_.value.review_type}),n=await O.get(`/api/performance/reviews?${o}`);n.data.success&&(n.data.data.reviews?(d.value=n.data.data.reviews,B.value=z(n.data.data.pagination,n.data.data.reviews.length)):Array.isArray(n.data.data)?(d.value=n.data.data,B.value=z(null,n.data.data.length)):(d.value=[],B.value=z(null,0)))}catch(o){s.value=((i=(l=o.response)==null?void 0:l.data)==null?void 0:i.message)||"Errore nel caricamento recensioni performance",console.error("Error fetching performance reviews:",o)}finally{t.value=!1}},V=async(a,l=!1)=>{var i,o;if(!l&&$.value.has(a)){const n=$.value.get(a);return h.value=n,n}t.value=!0,s.value=null;try{const n=await O.get(`/api/performance/reviews/${a}`);if(n.data.success){const R=n.data.data.review;return h.value=R,$.value.set(a,R),R}}catch(n){throw s.value=((o=(i=n.response)==null?void 0:i.data)==null?void 0:o.message)||"Errore nel caricamento review",console.error("Error fetching performance review:",n),n}finally{t.value=!1}};return{reviews:d,goals:A,templates:m,dashboardData:T,analyticsData:b,currentReview:h,loading:t,error:s,pagination:B,filters:_,filteredReviews:U,reviewsByStatus:I,fetchDashboardData:w,fetchReviews:P,fetchReview:V,fetchGoals:async(a={})=>{var l,i;t.value=!0,s.value=null;try{const o=new URLSearchParams({employee_id:a.employee_id||_.value.employee_id||"",year:a.year||_.value.year||""}),n=await O.get(`/api/performance/goals?${o}`);n.data.success&&(A.value=n.data.data.goals||[])}catch(o){s.value=((i=(l=o.response)==null?void 0:l.data)==null?void 0:i.message)||"Errore nel caricamento obiettivi",console.error("Error fetching performance goals:",o)}finally{t.value=!1}},fetchTemplates:async()=>{var a,l;t.value=!0,s.value=null;try{const i=await O.get("/api/performance/templates");i.data.success&&(m.value=i.data.data.templates||[])}catch(i){s.value=((l=(a=i.response)==null?void 0:a.data)==null?void 0:l.message)||"Errore nel caricamento template",console.error("Error fetching performance templates:",i)}finally{t.value=!1}},createReview:async a=>{var l,i;t.value=!0,s.value=null;try{const o=await O.post("/api/performance/reviews",a);if(o.data.success){const n=o.data.data.review;return d.value.unshift(n),n}}catch(o){throw s.value=((i=(l=o.response)==null?void 0:l.data)==null?void 0:i.message)||"Errore nella creazione review",console.error("Error creating performance review:",o),o}finally{t.value=!1}},updateReview:async(a,l)=>{var i,o,n;t.value=!0,s.value=null;try{const R=await O.put(`/api/performance/reviews/${a}`,l);if(R.data.success){const N=R.data.data.review,D=d.value.findIndex(se=>se.id===a);return D!==-1&&(d.value[D]=N),((i=h.value)==null?void 0:i.id)===a&&(h.value=N),$.value.set(a,N),N}}catch(R){throw s.value=((n=(o=R.response)==null?void 0:o.data)==null?void 0:n.message)||"Errore nell'aggiornamento review",console.error("Error updating performance review:",R),R}finally{t.value=!1}},deleteReview:async a=>{var l,i,o;t.value=!0,s.value=null;try{if((await O.delete(`/api/performance/reviews/${a}`)).data.success)return d.value=d.value.filter(R=>R.id!==a),((l=h.value)==null?void 0:l.id)===a&&(h.value=null),$.value.delete(a),!0}catch(n){throw s.value=((o=(i=n.response)==null?void 0:i.data)==null?void 0:o.message)||"Errore nell'eliminazione review",console.error("Error deleting performance review:",n),n}finally{t.value=!1}},createGoal:async a=>{var l,i;t.value=!0,s.value=null;try{const o=await O.post("/api/performance/goals",a);if(o.data.success){const n=o.data.data.goal;return A.value.unshift(n),n}}catch(o){throw s.value=((i=(l=o.response)==null?void 0:l.data)==null?void 0:i.message)||"Errore nella creazione obiettivo",console.error("Error creating performance goal:",o),o}finally{t.value=!1}},updateGoal:async(a,l)=>{var i,o;t.value=!0,s.value=null;try{if((await O.put(`/api/performance/goals/${a}`,l)).data.success){const R=A.value.findIndex(N=>N.id===a);return R!==-1&&(A.value[R]={...A.value[R],...l,id:a}),l}}catch(n){throw s.value=((o=(i=n.response)==null?void 0:i.data)==null?void 0:o.message)||"Errore nell'aggiornamento obiettivo",console.error("Error updating performance goal:",n),n}finally{t.value=!1}},deleteGoal:async a=>{var l,i;t.value=!0,s.value=null;try{if((await O.delete(`/api/performance/goals/${a}`)).data.success)return A.value=A.value.filter(n=>n.id!==a),!0}catch(o){throw s.value=((i=(l=o.response)==null?void 0:l.data)==null?void 0:i.message)||"Errore nell'eliminazione obiettivo",console.error("Error deleting performance goal:",o),o}finally{t.value=!1}},createTemplate:async a=>{var l,i;t.value=!0,s.value=null;try{const o=await O.post("/api/performance/templates",a);if(o.data.success){const n=o.data.data.template;return m.value.unshift(n),n}}catch(o){throw s.value=((i=(l=o.response)==null?void 0:l.data)==null?void 0:i.message)||"Errore nella creazione template",console.error("Error creating performance template:",o),o}finally{t.value=!1}},updateTemplate:async(a,l)=>{var i,o;t.value=!0,s.value=null;try{if((await O.put(`/api/performance/templates/${a}`,l)).data.success){const R=m.value.findIndex(N=>N.id===a);return R!==-1&&(m.value[R]={...m.value[R],...l,id:a}),l}}catch(n){throw s.value=((o=(i=n.response)==null?void 0:i.data)==null?void 0:o.message)||"Errore nell'aggiornamento template",console.error("Error updating performance template:",n),n}finally{t.value=!1}},deleteTemplate:async a=>{var l,i;t.value=!0,s.value=null;try{if((await O.delete(`/api/performance/templates/${a}`)).data.success)return m.value=m.value.filter(n=>n.id!==a),!0}catch(o){throw s.value=((i=(l=o.response)==null?void 0:l.data)==null?void 0:i.message)||"Errore nell'eliminazione template",console.error("Error deleting performance template:",o),o}finally{t.value=!1}},fetchAnalytics:async(a={})=>{var l,i;t.value=!0,s.value=null;try{const o=new URLSearchParams({employee_id:a.employee_id||"",year:a.year||new Date().getFullYear(),period:a.period||12}),n=await O.get(`/api/performance/analytics?${o}`);n.data.success&&(b.value=n.data.data)}catch(o){s.value=((i=(l=o.response)==null?void 0:l.data)==null?void 0:i.message)||"Errore nel caricamento analytics",console.error("Error fetching performance analytics:",o)}finally{t.value=!1}},fetchReviewGoals:async a=>{var l,i;t.value=!0,s.value=null;try{const o=await O.get(`/api/performance/reviews/${a}/goals`);if(o.data.success)return o.data.data.goals}catch(o){throw s.value=((i=(l=o.response)==null?void 0:l.data)==null?void 0:i.message)||"Errore nel caricamento obiettivi review",console.error("Error fetching review goals:",o),o}finally{t.value=!1}},linkGoalsToReview:async(a,l)=>{var i,o;t.value=!0,s.value=null;try{const n=await O.post(`/api/performance/reviews/${a}/link-goals`,{goal_ids:l});if(n.data.success)return n.data.data.linked_count}catch(n){throw s.value=((o=(i=n.response)==null?void 0:i.data)==null?void 0:o.message)||"Errore nel collegamento obiettivi",console.error("Error linking goals to review:",n),n}finally{t.value=!1}},updateGoalReviewLink:async(a,l)=>{var i,o;t.value=!0,s.value=null;try{if((await O.put(`/api/performance/goals/${a}/review-link`,{review_id:l})).data.success){const R=A.value.findIndex(N=>N.id===a);return R!==-1&&(A.value[R].review_id=l),!0}}catch(n){throw s.value=((o=(i=n.response)==null?void 0:i.data)==null?void 0:o.message)||"Errore nell'aggiornamento collegamento",console.error("Error updating goal review link:",n),n}finally{t.value=!1}},evaluateReviewGoals:async(a,l)=>{var i,o;t.value=!0,s.value=null;try{const n=await O.put(`/api/performance/reviews/${a}/evaluate-goals`,{evaluations:l});if(n.data.success)return(n.data.data.updated_goals||[]).forEach(N=>{const D=A.value.findIndex(se=>se.id===N.goal_id);D!==-1&&(A.value[D].achievement_rating=N.achievement_rating,A.value[D].manager_assessment=N.manager_assessment)}),h.value&&h.value.id===a&&(h.value.goals_achievement_rating=n.data.data.review_goals_rating),n.data.data}catch(n){throw s.value=((o=(i=n.response)==null?void 0:i.data)==null?void 0:o.message)||"Errore nella valutazione degli obiettivi",console.error("Error evaluating review goals:",n),n}finally{t.value=!1}},setFilters:a=>{_.value={..._.value,...a}},clearFilters:()=>{_.value={search:"",status:"",employee_id:"",year:new Date().getFullYear(),review_type:""}},setCurrentReview:a=>{h.value=a},clearCurrentReview:()=>{h.value=null},clearCache:()=>{$.value.clear()},refreshReview:async a=>await V(a,!0),getCachedReview:a=>$.value.get(a),$reset:()=>{d.value=[],A.value=[],m.value=[],T.value={},b.value={},h.value=null,t.value=!1,s.value=null,$.value.clear(),B.value={page:1,per_page:20,total:0,total_pages:0},_.value={search:"",status:"",employee_id:"",year:new Date().getFullYear(),review_type:""}}}}),Fe={class:"flex items-center"},Le={class:"flex"},Ie=["disabled","onClick","onMouseenter","aria-label"],Ye={key:0,class:"ml-2 text-sm font-medium text-gray-700 dark:text-gray-300"},qe={key:1,class:"ml-2 text-sm text-gray-600 dark:text-gray-400"},De={__name:"StarRating",props:{modelValue:{type:Number,default:0},maxStars:{type:Number,default:5,validator:d=>d>0&&d<=10},readonly:{type:Boolean,default:!1},size:{type:String,default:"md",validator:d=>["sm","md","lg"].includes(d)},showValue:{type:Boolean,default:!1},label:{type:String,default:""},allowHalf:{type:Boolean,default:!0},color:{type:String,default:"yellow",validator:d=>["yellow","orange","red","green","blue"].includes(d)}},emits:["update:modelValue"],setup(d,{emit:A}){const m=d,T=A,b=p(m.modelValue),h=p(0);ce(()=>m.modelValue,z=>{b.value=z});const t=z=>{if(m.readonly)return;let w=z;m.allowHalf&&b.value===z&&(w=z-.5),b.value=w,T("update:modelValue",w)},s=z=>{m.readonly||(h.value=z)},$=()=>{m.readonly||(h.value=0)},B=M(()=>h.value||b.value),_=()=>({sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"})[m.size],U=z=>{const w={yellow:{active:"text-yellow-400",inactive:"text-gray-300 dark:text-gray-600"},orange:{active:"text-orange-400",inactive:"text-gray-300 dark:text-gray-600"},red:{active:"text-red-400",inactive:"text-gray-300 dark:text-gray-600"},green:{active:"text-green-400",inactive:"text-gray-300 dark:text-gray-600"},blue:{active:"text-blue-400",inactive:"text-gray-300 dark:text-gray-600"}},P=w[m.color]||w.yellow,V=B.value,j=z<=Math.ceil(V);return m.allowHalf&&z===Math.ceil(V)&&V%1,j?P.active:P.inactive},I=z=>m.allowHalf?z.toFixed(1):Math.round(z);return(z,w)=>(u(),y("div",Fe,[e("div",Le,[(u(!0),y(ae,null,ne(d.maxStars,P=>(u(),y("button",{key:P,type:"button",disabled:d.readonly,onClick:V=>t(P),onMouseenter:V=>s(P),onMouseleave:$,class:ue(["transition-colors duration-200",d.readonly?"cursor-default":"cursor-pointer hover:scale-110 transform",_(),U(P)]),"aria-label":`Valutazione ${P} stelle su ${d.maxStars}`},w[0]||(w[0]=[e("svg",{class:"fill-current",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},[e("path",{"fill-rule":"evenodd",d:"M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401z","clip-rule":"evenodd"})],-1)]),42,Ie))),128))]),d.showValue&&b.value>0?(u(),y("span",Ye,v(I(b.value))+"/"+v(d.maxStars),1)):k("",!0),d.label?(u(),y("span",qe,v(d.label),1)):k("",!0)]))}},Re=ke(De,[["__scopeId","data-v-1266193d"]]),He={class:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200"},Qe={class:"flex justify-between items-start mb-4"},Je={class:"flex-1"},Ke={class:"flex items-center mb-2"},We={class:"text-lg font-medium text-gray-900 dark:text-white"},Xe={key:0,class:"text-sm text-gray-600 dark:text-gray-400 mb-3"},Ze={class:"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400"},ea={class:"flex items-center"},aa={class:"flex items-center"},ta={key:0,class:"flex items-center"},ra={class:"mb-6"},la={class:"flex justify-between text-sm mb-2"},sa={class:"font-medium text-gray-900 dark:text-white"},oa={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},na={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},ia={class:"text-sm font-medium text-gray-900 dark:text-white mb-4 flex items-center"},ua={class:"mb-4"},da={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},va={class:"mb-4"},ca=["readonly"],ma={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},ga={key:0,class:"mb-4"},ya={class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3"},fa={class:"text-sm text-blue-800 dark:text-blue-200"},pa={key:1,class:"mb-4"},xa={class:"bg-gray-100 dark:bg-gray-600 rounded-md p-3"},ba={class:"text-sm text-gray-700 dark:text-gray-300"},_a={key:0,class:"flex justify-end space-x-3 mt-4"},wa={__name:"GoalEvaluationCard",props:{goal:{type:Object,required:!0},readonly:{type:Boolean,default:!1},saving:{type:Boolean,default:!1}},emits:["update:evaluation","save","reset"],setup(d,{emit:A}){const m=d,T=A,b=p({achievement_rating:m.goal.achievement_rating||0,manager_assessment:m.goal.manager_assessment||""}),h=p({}),t=p({achievement_rating:m.goal.achievement_rating||0,manager_assessment:m.goal.manager_assessment||""}),s=M(()=>b.value.achievement_rating>0?"evaluated":b.value.manager_assessment.length>0?"in_evaluation":"not_evaluated"),$=M(()=>b.value.achievement_rating!==t.value.achievement_rating||b.value.manager_assessment!==t.value.manager_assessment),B=M(()=>Object.keys(h.value).length===0&&b.value.achievement_rating>0);ce(()=>m.goal,E=>{b.value={achievement_rating:E.achievement_rating||0,manager_assessment:E.manager_assessment||""},t.value={...b.value}},{deep:!0});const _=E=>E?new Date(E).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"Non specificata",U=E=>({technical:"Competenze Tecniche",soft_skills:"Soft Skills",business:"Business",career_development:"Sviluppo Carriera",leadership:"Leadership",teamwork:"Lavoro di Squadra"})[E]||E,I=E=>E>=90?"bg-green-500":E>=70?"bg-yellow-500":E>=50?"bg-orange-500":"bg-red-500",z=()=>{const E={};return(!b.value.achievement_rating||b.value.achievement_rating<1)&&(E.achievement_rating="Il rating è richiesto (minimo 1 stella)"),b.value.achievement_rating>5&&(E.achievement_rating="Il rating non può essere superiore a 5 stelle"),h.value=E,Object.keys(E).length===0},w=E=>{b.value.achievement_rating=E,z(),V()},P=()=>{z(),V()},V=()=>{T("update:evaluation",{goal_id:m.goal.id,...b.value})},j=()=>{z()&&T("save",{goal_id:m.goal.id,...b.value})},Q=()=>{b.value={...t.value},h.value={},T("reset",m.goal.id)};return je(()=>{m.readonly||z()}),(E,S)=>(u(),y("div",He,[e("div",Qe,[e("div",Je,[e("div",Ke,[f(C,{name:"flag",size:"sm",class:"text-blue-500 mr-2"}),e("h3",We,v(d.goal.title),1)]),d.goal.description?(u(),y("p",Xe,v(d.goal.description),1)):k("",!0),e("div",Ze,[e("div",ea,[f(C,{name:"calendar",size:"xs",class:"mr-1"}),e("span",null,v(_(d.goal.target_date)),1)]),e("div",aa,[f(ie,{status:d.goal.priority,type:"generic","show-dot":!1},null,8,["status"])]),d.goal.category?(u(),y("div",ta,[f(C,{name:"tag",size:"xs",class:"mr-1"}),e("span",null,v(U(d.goal.category)),1)])):k("",!0)])]),f(ie,{status:s.value,type:"performance","show-dot":!0},null,8,["status"])]),e("div",ra,[e("div",la,[S[2]||(S[2]=e("span",{class:"text-gray-600 dark:text-gray-400"},"Progresso obiettivo",-1)),e("span",sa,v(d.goal.progress_percentage||0)+"%",1)]),e("div",oa,[e("div",{class:ue(["h-2 rounded-full transition-all duration-500",I(d.goal.progress_percentage)]),style:he({width:`${Math.min(d.goal.progress_percentage||0,100)}%`})},null,6)])]),e("div",na,[e("h4",ia,[f(C,{name:"star",size:"sm",class:"text-yellow-500 mr-2"}),S[3]||(S[3]=G(" Valutazione Achievement "))]),e("div",ua,[S[4]||(S[4]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Rating Raggiungimento (1-5 stelle) ",-1)),f(Re,{modelValue:b.value.achievement_rating,"onUpdate:modelValue":[S[0]||(S[0]=F=>b.value.achievement_rating=F),w],readonly:d.readonly,"show-value":!0,size:"md",color:"yellow"},null,8,["modelValue","readonly"]),h.value.achievement_rating?(u(),y("p",da,v(h.value.achievement_rating),1)):k("",!0)]),e("div",va,[S[5]||(S[5]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Valutazione del Manager ",-1)),K(e("textarea",{"onUpdate:modelValue":S[1]||(S[1]=F=>b.value.manager_assessment=F),readonly:d.readonly,rows:"3",placeholder:"Descrivi il raggiungimento dell'obiettivo, i punti di forza e le aree di miglioramento...",class:ue(["w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-600 dark:text-white resize-none",{"bg-gray-100 dark:bg-gray-700":d.readonly}]),onInput:P},null,42,ca),[[oe,b.value.manager_assessment]]),h.value.manager_assessment?(u(),y("p",ma,v(h.value.manager_assessment),1)):k("",!0)]),d.goal.employee_self_assessment?(u(),y("div",ga,[S[6]||(S[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Auto-valutazione del Dipendente ",-1)),e("div",ya,[e("p",fa,v(d.goal.employee_self_assessment),1)])])):k("",!0),d.goal.success_criteria?(u(),y("div",pa,[S[7]||(S[7]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Criteri di Successo ",-1)),e("div",xa,[e("p",ba,v(d.goal.success_criteria),1)])])):k("",!0)]),d.readonly?k("",!0):(u(),y("div",_a,[f(q,{variant:"outline-secondary",size:"sm",onClick:Q,disabled:!$.value},{default:Y(()=>S[8]||(S[8]=[G(" Reset ")])),_:1,__:[8]},8,["disabled"]),f(q,{variant:"primary",size:"sm",onClick:j,loading:d.saving,disabled:!B.value||!$.value},{default:Y(()=>S[9]||(S[9]=[G(" Salva Valutazione ")])),_:1,__:[9]},8,["loading","disabled"])]))]))}},ha=ke(wa,[["__scopeId","data-v-7abf773e"]]),ka={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},za={class:"text-lg font-medium text-gray-900 dark:text-white"},Ca={class:"text-sm text-gray-500 dark:text-gray-400 mt-1"},$a={class:"flex items-center space-x-3"},Ea={class:"flex items-center text-sm text-gray-600 dark:text-gray-400"},Sa={class:"w-24 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mr-2"},Va={key:0,class:"flex items-center text-amber-600 dark:text-amber-400 text-sm"},Ra={class:"mt-6"},Ga={class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6"},Ma={class:"flex items-center justify-between"},Pa={class:"flex items-center"},Aa={class:"flex space-x-2"},Ta={key:0,class:"text-center py-12"},ja={key:1,class:"text-center py-12"},Na={class:"text-red-600 mb-4"},Ba={class:"text-red-600 dark:text-red-400 mb-4"},Oa={key:2,class:"text-center py-12"},Ua={key:3,class:"space-y-6 max-h-96 overflow-y-auto pr-2"},Fa={class:"flex items-center justify-between pt-6 mt-8 border-t border-gray-200 dark:border-gray-700"},La={class:"flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400"},Ia={class:"flex items-center"},Ya={class:"flex items-center"},qa={class:"flex items-center"},Da={class:"flex space-x-3"},Ha={__name:"GoalsEvaluationModal",props:{reviewId:{type:Number,required:!0},reviewTitle:{type:String,default:"Performance Review"},readonly:{type:Boolean,default:!1}},emits:["close","saved","completed"],setup(d,{emit:A}){const m=d,T=A,b=Ve(),{showError:h,showSuccess:t}=Be(),s=p([]),$=p({}),B=p(!1),_=p(""),U=p(!1),I=p(!1),z=p([]),w=p(!1),P=p(!1),V=p(""),j=p(""),Q=p(""),E=p(!1),S=p(null),F=M(()=>s.value.filter(x=>{var a;return x.achievement_rating>0||((a=$.value[x.id])==null?void 0:a.achievement_rating)>0}).length),L=M(()=>s.value.filter(x=>{var i,o;const a=x.achievement_rating>0||((i=$.value[x.id])==null?void 0:i.achievement_rating)>0,l=x.manager_assessment||((o=$.value[x.id])==null?void 0:o.manager_assessment);return!a&&l}).length),X=M(()=>s.value.length-F.value-L.value),de=M(()=>s.value.length===0?0:F.value/s.value.length*100),J=M(()=>F.value===s.value.length),W=M(()=>Object.values($.value).some(x=>x.achievement_rating>0)),me=M(()=>m.readonly?!1:J.value||W.value),re=async()=>{B.value=!0,_.value="";try{const x=await b.fetchReviewGoals(m.reviewId);s.value=x||[]}catch(x){_.value="Errore nel caricamento degli obiettivi",h("Errore nel caricamento degli obiettivi della performance review"),console.error("Error loading goals:",x)}finally{B.value=!1}},Z=()=>{re()},le=x=>{$.value[x.goal_id]=x,w.value=!0},ge=async x=>{const a=x.goal_id;z.value.push(a);try{await b.updateGoal(a,{achievement_rating:x.achievement_rating,manager_assessment:x.manager_assessment});const l=s.value.findIndex(i=>i.id===a);l!==-1&&(s.value[l].achievement_rating=x.achievement_rating,s.value[l].manager_assessment=x.manager_assessment),delete $.value[a],w.value=Object.keys($.value).length>0}catch(l){console.error("Error saving goal evaluation:",l),h("Errore nel salvare la valutazione dell'obiettivo")}finally{z.value=z.value.filter(l=>l!==a)}},ye=x=>{delete $.value[x],w.value=Object.keys($.value).length>0},fe=async()=>{await te()},te=async()=>{if(W.value){I.value=!0;try{const x=Object.values($.value).filter(a=>a.achievement_rating>0||a.manager_assessment);if(x.length===0)return;await b.evaluateReviewGoals(m.reviewId,x),x.forEach(a=>{const l=s.value.findIndex(i=>i.id===a.goal_id);l!==-1&&(s.value[l].achievement_rating=a.achievement_rating,s.value[l].manager_assessment=a.manager_assessment),delete $.value[a.goal_id]}),w.value=Object.keys($.value).length>0,T("saved",{draft:!0,count:x.length})}catch(x){console.error("Error saving draft evaluations:",x),h("Errore nel salvare le valutazioni in bozza")}finally{I.value=!1}}},pe=()=>{J.value?(V.value="Conferma Valutazioni",j.value=`Confermi di voler finalizzare la valutazione di tutti i ${s.value.length} obiettivi? Questa azione aggiornerà la performance review.`,Q.value="Conferma",S.value=()=>ve()):(V.value="Valutazione Incompleta",j.value=`Solo ${F.value} su ${s.value.length} obiettivi sono stati valutati. Vuoi salvare le valutazioni parziali e continuare in seguito?`,Q.value="Salva Parziale",S.value=()=>xe()),P.value=!0},ve=async()=>{E.value=!0,U.value=!0;try{W.value&&await te(),T("completed",{total:s.value.length,evaluated:F.value,average_rating:s.value.reduce((x,a)=>x+(a.achievement_rating||0),0)/s.value.length}),ee()}catch(x){console.error("Error finalizing evaluations:",x),h("Errore nel finalizzare le valutazioni degli obiettivi")}finally{E.value=!1,U.value=!1,P.value=!1}},xe=async()=>{E.value=!0,U.value=!0;try{await te(),T("saved",{draft:!1,count:F.value,total:s.value.length}),ee()}catch(x){console.error("Error saving partial evaluations:",x),h("Errore nel salvare le valutazioni parziali")}finally{E.value=!1,U.value=!1,P.value=!1}},be=()=>{S.value&&S.value()},ee=()=>{w.value?(V.value="Modifiche Non Salvate",j.value="Hai modifiche non salvate. Vuoi uscire senza salvare?",Q.value="Esci Senza Salvare",S.value=()=>{P.value=!1,T("close")},P.value=!0):T("close")};return Ee(()=>{re()}),ce(()=>m.reviewId,()=>{m.reviewId&&re()}),(x,a)=>(u(),y(ae,null,[e("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:ee},[e("div",{class:"relative top-5 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:a[0]||(a[0]=ze(()=>{},["stop"]))},[e("div",ka,[e("div",null,[e("h3",za," Valutazione Obiettivi - "+v(d.reviewTitle),1),e("p",Ca,v(s.value.length)+" obiettivi collegati alla review - "+v(F.value)+"/"+v(s.value.length)+" valutati ",1)]),e("div",$a,[e("div",Ea,[e("div",Sa,[e("div",{class:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:he({width:`${de.value}%`})},null,4)]),e("span",null,v(Math.round(de.value))+"%",1)]),w.value?(u(),y("div",Va,[f(C,{name:"exclamation-triangle",size:"sm",class:"mr-1"}),a[2]||(a[2]=G(" Modifiche non salvate "))])):k("",!0),e("button",{onClick:ee,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[f(C,{name:"x-mark",size:"lg"})])])]),e("div",Ra,[e("div",Ga,[e("div",Ma,[e("div",Pa,[f(C,{name:"information-circle",size:"md",class:"text-blue-500 mr-3"}),a[3]||(a[3]=e("div",null,[e("p",{class:"text-sm font-medium text-blue-800 dark:text-blue-200"}," Valuta il raggiungimento degli obiettivi "),e("p",{class:"text-xs text-blue-600 dark:text-blue-300"}," Assegna un rating da 1 a 5 stelle e fornisci un feedback dettagliato per ogni obiettivo ")],-1))]),e("div",Aa,[f(q,{variant:"outline-primary",size:"sm",onClick:fe,loading:I.value,disabled:!w.value},{default:Y(()=>[f(C,{name:"document",size:"sm",class:"mr-1"}),a[4]||(a[4]=G(" Salva Bozza "))]),_:1,__:[4]},8,["loading","disabled"])])])]),B.value?(u(),y("div",Ta,a[5]||(a[5]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"},null,-1),e("p",{class:"mt-4 text-gray-600 dark:text-gray-400"},"Caricamento obiettivi...",-1)]))):_.value?(u(),y("div",ja,[e("div",Na,[f(C,{name:"exclamation-triangle",size:"lg",class:"mx-auto"})]),e("p",Ba,v(_.value),1),f(q,{variant:"primary",size:"sm",onClick:Z},{default:Y(()=>a[6]||(a[6]=[G(" Riprova ")])),_:1,__:[6]})])):s.value.length===0?(u(),y("div",Oa,[f(C,{name:"flag",size:"lg",class:"mx-auto text-gray-400 mb-4"}),a[7]||(a[7]=e("p",{class:"text-gray-500 dark:text-gray-400"}," Nessun obiettivo collegato a questa review ",-1))])):(u(),y("div",Ua,[(u(!0),y(ae,null,ne(s.value,l=>(u(),H(ha,{key:l.id,goal:l,readonly:d.readonly,saving:z.value.includes(l.id),"onUpdate:evaluation":le,onSave:ge,onReset:ye},null,8,["goal","readonly","saving"]))),128))]))]),e("div",Fa,[e("div",La,[e("div",Ia,[a[8]||(a[8]=e("div",{class:"w-3 h-3 bg-green-500 rounded-full mr-2"},null,-1)),e("span",null,v(F.value)+" valutati",1)]),e("div",Ya,[a[9]||(a[9]=e("div",{class:"w-3 h-3 bg-yellow-500 rounded-full mr-2"},null,-1)),e("span",null,v(L.value)+" in corso",1)]),e("div",qa,[a[10]||(a[10]=e("div",{class:"w-3 h-3 bg-gray-300 rounded-full mr-2"},null,-1)),e("span",null,v(X.value)+" da valutare",1)])]),e("div",Da,[f(q,{variant:"outline-secondary",onClick:ee,disabled:U.value},{default:Y(()=>[G(v(w.value?"Annulla":"Chiudi"),1)]),_:1},8,["disabled"]),J.value?k("",!0):(u(),H(q,{key:0,variant:"warning",onClick:te,loading:I.value,disabled:!W.value||U.value},{default:Y(()=>[f(C,{name:"document",size:"sm",class:"mr-1"}),a[11]||(a[11]=G(" Salva Bozza "))]),_:1,__:[11]},8,["loading","disabled"])),f(q,{variant:"success",onClick:pe,loading:U.value,disabled:!me.value},{default:Y(()=>[f(C,{name:"check-circle",size:"sm",class:"mr-1"}),G(" "+v(J.value?"Conferma Valutazioni":`Completa Valutazione (${F.value}/${s.value.length})`),1)]),_:1},8,["loading","disabled"])])])])]),P.value?(u(),H(Se,{key:0,title:V.value,message:j.value,"confirm-text":Q.value,loading:E.value,onConfirm:be,onCancel:a[1]||(a[1]=l=>P.value=!1)},null,8,["title","message","confirm-text","loading"])):k("",!0)],64))}},Qa=ke(Ha,[["__scopeId","data-v-0614f47c"]]),Ja={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},Ka={class:"text-lg font-medium text-gray-900 dark:text-white"},Wa={key:0,class:"text-sm text-gray-500 dark:text-gray-400 mt-1"},Xa={class:"flex items-center space-x-3"},Za={key:0,class:"flex items-center text-amber-600 dark:text-amber-400 text-sm"},et={class:"mt-6"},at={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6"},tt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},rt=["disabled"],lt=["value"],st={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},ot={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},nt={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},it=["value"],ut={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},dt={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},vt={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},ct={class:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"},mt={class:"text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center"},gt={class:"mb-6"},yt={class:"flex items-center space-x-4"},ft={class:"flex space-x-1"},pt=["onClick"],xt={class:"text-sm text-gray-600 dark:text-gray-400"},bt={class:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"},_t={class:"space-y-4"},wt={key:0,class:"space-y-6"},ht={class:"bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800 p-6"},kt={class:"flex items-center justify-between mb-4"},zt={class:"text-md font-medium text-gray-900 dark:text-white flex items-center"},Ct={key:0,class:"bg-white dark:bg-gray-700 rounded-lg p-4 mb-4"},$t={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Et={class:"text-center"},St={class:"text-2xl font-bold text-gray-900 dark:text-white"},Vt={class:"text-sm text-gray-600 dark:text-gray-400"},Rt={class:"text-center"},Gt={class:"text-2xl font-bold text-green-600"},Mt={class:"text-center"},Pt={class:"text-2xl font-bold text-amber-600"},At={class:"mt-4"},Tt={class:"flex justify-between text-sm mb-2"},jt={class:"font-medium text-gray-900 dark:text-white"},Nt={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},Bt={key:1,class:"text-center py-8 text-gray-500 dark:text-gray-400"},Ot={class:"flex space-x-3"},Ut={class:"bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-6"},Ft={class:"flex items-center justify-between mb-4"},Lt={class:"text-md font-medium text-gray-900 dark:text-white flex items-center"},It={key:0,class:"bg-white dark:bg-gray-700 rounded-lg p-4 mb-4"},Yt={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},qt={class:"text-center"},Dt={class:"text-2xl font-bold text-gray-900 dark:text-white"},Ht={class:"text-sm text-gray-600 dark:text-gray-400"},Qt={class:"text-center"},Jt={class:"text-2xl font-bold text-blue-600"},Kt={class:"text-center"},Wt={class:"text-2xl font-bold text-purple-600"},Xt={key:1,class:"text-center py-8 text-gray-500 dark:text-gray-400"},Zt={class:"flex space-x-3"},er={key:1,class:"bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-6"},ar={class:"flex items-center justify-between mb-4"},tr={class:"text-md font-medium text-gray-900 dark:text-white flex items-center"},rr={key:0,class:"bg-white dark:bg-gray-700 rounded-lg p-4 mb-4"},lr={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},sr={class:"text-center"},or={class:"text-2xl font-bold text-gray-900 dark:text-white"},nr={class:"text-center"},ir={class:"text-2xl font-bold text-green-600"},ur={class:"text-center"},dr={class:"text-2xl font-bold text-blue-600"},vr={class:"mt-4"},cr={class:"flex justify-between text-sm mb-2"},mr={class:"font-medium text-gray-900 dark:text-white"},gr={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},yr={key:0,class:"mt-4 space-y-2"},fr={class:"flex items-center"},pr={class:"ml-2 text-gray-700 dark:text-gray-300"},xr={key:0,class:"flex items-center"},br={key:1,class:"text-center py-8 text-gray-500 dark:text-gray-400"},_r={class:"flex space-x-3"},wr={key:2,class:"flex items-center justify-center text-sm text-gray-500 dark:text-gray-400"},hr={class:"flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700 mt-6"},kr={class:"flex space-x-3"},zr=["disabled"],Cr=["disabled"],$r={__name:"ReviewEditModal",props:{review:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["close","save","delete","link-goals"],setup(d,{emit:A}){const m=d,T=A,b=Ve(),h=Ue(),t=p({id:null,employee_id:"",reviewer_id:"",review_year:new Date().getFullYear(),review_type:"annual",review_period_start:"",review_period_end:"",status:"draft",overall_rating:null,review_content:"",manager_comments:""}),s=p({}),$=p(!1),B=p(!1),_=p(!1),U=p(null),I=p(!0),z=p(!1),w=p([]),P=p(!1),V=p([]),j=p([]),Q=p(!1),E=p(!1),S=p(!1),F=p(!1),L=M(()=>{var c;return!!((c=m.review)!=null&&c.id)}),X=M(()=>h.users||[]),de=M(()=>t.value.employee_id&&t.value.reviewer_id&&t.value.review_year&&t.value.review_type&&t.value.review_period_start&&t.value.review_period_end),J=M(()=>w.value.filter(c=>c.achievement_rating>0).length),W=M(()=>w.value.length===0?0:J.value/w.value.length*100),me=M(()=>w.value.length===0?"not_evaluated":J.value===w.value.length?"evaluated":J.value>0?"in_evaluation":"not_evaluated"),re=M(()=>{const c=w.value.filter(g=>g.achievement_rating>0);return c.length===0?0:c.reduce((g,_e)=>g+_e.achievement_rating,0)/c.length}),Z=M(()=>V.value.filter(c=>c.achievement_rating>0).length),le=M(()=>V.value.length===0?0:Z.value/V.value.length*100),ge=M(()=>V.value.length===0?"not_evaluated":Z.value===V.value.length?"evaluated":Z.value>0?"in_evaluation":"not_evaluated"),ye=M(()=>{const c=V.value.filter(g=>g.achievement_rating>0);return c.length===0?0:c.reduce((g,_e)=>g+_e.achievement_rating,0)/c.length}),fe=M(()=>{if(j.value.length===0)return"not_evaluated";const c=j.value.filter(r=>r.category&&r.target_date);return c.length===j.value.length?"evaluated":c.length>0?"in_evaluation":"not_evaluated"});ce(t,()=>{I.value||(_.value=!0,clearTimeout(U.value),U.value=setTimeout(be,3e3))},{deep:!0});const te=()=>{I.value=!0,m.review&&(t.value={id:m.review.id||null,employee_id:m.review.employee_id||"",reviewer_id:m.review.reviewer_id||"",review_year:m.review.review_year||new Date().getFullYear(),review_type:m.review.review_type||"annual",review_period_start:m.review.review_period_start||"",review_period_end:m.review.review_period_end||"",status:m.review.status||"draft",overall_rating:m.review.overall_rating||null,review_content:m.review.review_content||"",manager_comments:m.review.manager_comments||""}),_.value=!1,setTimeout(()=>{I.value=!1},100)},pe=()=>(s.value={},t.value.employee_id||(s.value.employee_id="Dipendente richiesto"),t.value.reviewer_id||(s.value.reviewer_id="Reviewer richiesto"),t.value.review_year||(s.value.review_year="Anno richiesto"),t.value.review_type||(s.value.review_type="Tipo valutazione richiesto"),t.value.review_period_start||(s.value.review_period_start="Data inizio richiesta"),t.value.review_period_end||(s.value.review_period_end="Data fine richiesta"),t.value.review_period_start&&t.value.review_period_end&&new Date(t.value.review_period_start)>=new Date(t.value.review_period_end)&&(s.value.review_period_end="Data fine deve essere successiva alla data inizio"),Object.keys(s.value).length===0),ve=async()=>{if(pe())try{const c={...t.value};L.value?await b.updateReview(m.review.id,c):await b.createReview(c),_.value=!1,T("save")}catch(c){console.error("Error saving review:",c)}},xe=async()=>{const c={...t.value,status:"draft"};try{L.value?await b.updateReview(m.review.id,c):await b.createReview(c),_.value=!1,T("save")}catch(r){console.error("Error saving draft:",r)}},be=async()=>{if(!(!L.value||!_.value)){B.value=!0;try{const c={...t.value};await b.updateReview(m.review.id,c),_.value=!1}catch(c){console.error("Auto-save failed:",c)}finally{B.value=!1}}},ee=async()=>{console.log("Delete review:",m.review.id),$.value=!1,T("delete")},x=()=>{_.value?confirm("Ci sono modifiche non salvate. Vuoi davvero chiudere?")&&T("close"):T("close")},a=async()=>{if(!(!L.value||!t.value.id))try{const c=await b.fetchReviewGoals(t.value.id);w.value=c||[]}catch(c){console.error("Error loading linked goals:",c),w.value=[]}},l=async()=>{P.value=!0,await a(),P.value=!1},i=()=>{z.value=!0},o=c=>{a(),console.log("Goals evaluation saved:",c)},n=c=>{a(),c.average_rating&&(t.value.goals_achievement_rating=c.average_rating),console.log("Goals evaluation completed:",c)},R=()=>`${N()} - ${t.value.review_year}`,N=()=>{const c=X.value.find(r=>r.id===t.value.employee_id);return c?`${c.first_name} ${c.last_name}`:""},D=async()=>{if(!(!t.value.employee_id||!t.value.review_year))try{const c={employee_id:t.value.employee_id,year:t.value.review_year-1},r=await b.fetchGoals(c);V.value=r||[]}catch(c){console.error("Error loading previous year goals:",c),V.value=[]}},se=async()=>{if(!(!t.value.employee_id||!t.value.review_year))try{const c={employee_id:t.value.employee_id,year:t.value.review_year+1},r=await b.fetchGoals(c);j.value=r||[]}catch(c){console.error("Error loading next year goals:",c),j.value=[]}},Ge=async()=>{Q.value=!0,await D(),Q.value=!1},Me=async()=>{E.value=!0,await se(),E.value=!1},Pe=()=>{S.value=!0},Ae=()=>{F.value=!0},Ce=async()=>{t.value.review_type==="annual"&&t.value.employee_id&&t.value.review_year&&await Promise.all([D(),se()])};return ce([()=>t.value.employee_id,()=>t.value.review_year,()=>t.value.review_type],async()=>{I.value||await Ce()}),Ee(async()=>{if(te(),X.value.length===0&&h.fetchUsers(),L.value&&(await a(),await Ce()),!L.value&&!t.value.reviewer_id){const c=Oe();c.user&&["admin","manager","hr"].includes(c.user.role)&&(t.value.reviewer_id=c.user.id)}}),Ne(()=>{clearTimeout(U.value)}),(c,r)=>(u(),y(ae,null,[e("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:x},[e("div",{class:"relative top-5 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:r[11]||(r[11]=ze(()=>{},["stop"]))},[e("div",Ja,[e("div",null,[e("h3",Ka,v(L.value?"Modifica Valutazione Performance":"Nuova Valutazione Performance"),1),t.value.employee_id&&X.value.length?(u(),y("p",Wa,v(N())+" - "+v(t.value.review_year),1)):k("",!0)]),e("div",Xa,[_.value?(u(),y("div",Za,[f(C,{name:"exclamation-triangle",size:"sm",class:"mr-1"}),r[14]||(r[14]=G(" Modifiche non salvate "))])):k("",!0),e("button",{onClick:x,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[f(C,{name:"x-mark",size:"lg"})])])]),e("div",et,[e("form",{onSubmit:ze(ve,["prevent"]),class:"space-y-6"},[e("div",at,[r[26]||(r[26]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Base",-1)),e("div",tt,[e("div",null,[r[16]||(r[16]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Dipendente * ",-1)),K(e("select",{"onUpdate:modelValue":r[0]||(r[0]=g=>t.value.employee_id=g),disabled:L.value,required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white disabled:opacity-50"},[r[15]||(r[15]=e("option",{value:""},"Seleziona dipendente",-1)),(u(!0),y(ae,null,ne(X.value,g=>(u(),y("option",{key:g.id,value:g.id},v(g.first_name)+" "+v(g.last_name),9,lt))),128))],8,rt),[[we,t.value.employee_id]]),s.value.employee_id?(u(),y("p",st,v(s.value.employee_id),1)):k("",!0)]),e("div",null,[r[17]||(r[17]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Anno Valutazione * ",-1)),K(e("input",{"onUpdate:modelValue":r[1]||(r[1]=g=>t.value.review_year=g),type:"number",min:2020,max:2030,required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[oe,t.value.review_year,void 0,{number:!0}]]),s.value.review_year?(u(),y("p",ot,v(s.value.review_year),1)):k("",!0)]),e("div",null,[r[19]||(r[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tipo Valutazione * ",-1)),K(e("select",{"onUpdate:modelValue":r[2]||(r[2]=g=>t.value.review_type=g),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},r[18]||(r[18]=[$e('<option value="" data-v-9c051e86>Seleziona tipo</option><option value="annual" data-v-9c051e86>Annuale</option><option value="quarterly" data-v-9c051e86>Trimestrale</option><option value="probation" data-v-9c051e86>Periodo di Prova</option><option value="project" data-v-9c051e86>Fine Progetto</option>',5)]),512),[[we,t.value.review_type]]),s.value.review_type?(u(),y("p",nt,v(s.value.review_type),1)):k("",!0)]),e("div",null,[r[21]||(r[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Reviewer * ",-1)),K(e("select",{"onUpdate:modelValue":r[3]||(r[3]=g=>t.value.reviewer_id=g),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[r[20]||(r[20]=e("option",{value:""},"Seleziona reviewer",-1)),(u(!0),y(ae,null,ne(X.value,g=>(u(),y("option",{key:g.id,value:g.id},v(g.first_name)+" "+v(g.last_name),9,it))),128))],512),[[we,t.value.reviewer_id]]),s.value.reviewer_id?(u(),y("p",ut,v(s.value.reviewer_id),1)):k("",!0)]),e("div",null,[r[22]||(r[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data Inizio * ",-1)),K(e("input",{"onUpdate:modelValue":r[4]||(r[4]=g=>t.value.review_period_start=g),type:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[oe,t.value.review_period_start]]),s.value.review_period_start?(u(),y("p",dt,v(s.value.review_period_start),1)):k("",!0)]),e("div",null,[r[23]||(r[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data Fine * ",-1)),K(e("input",{"onUpdate:modelValue":r[5]||(r[5]=g=>t.value.review_period_end=g),type:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[oe,t.value.review_period_end]]),s.value.review_period_end?(u(),y("p",vt,v(s.value.review_period_end),1)):k("",!0)]),e("div",null,[r[25]||(r[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Status ",-1)),K(e("select",{"onUpdate:modelValue":r[6]||(r[6]=g=>t.value.status=g),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},r[24]||(r[24]=[$e('<option value="draft" data-v-9c051e86>Bozza</option><option value="in_progress" data-v-9c051e86>In Corso</option><option value="pending" data-v-9c051e86>In Attesa</option><option value="completed" data-v-9c051e86>Completata</option><option value="approved" data-v-9c051e86>Approvata</option>',5)]),512),[[we,t.value.status]])])])]),e("div",ct,[e("h4",mt,[f(C,{name:"star",size:"sm",class:"mr-2 text-yellow-500"}),r[27]||(r[27]=G(" Valutazione Complessiva "))]),e("div",gt,[r[29]||(r[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"}," Rating Complessivo (1-5) ",-1)),e("div",yt,[e("div",ft,[(u(),y(ae,null,ne(5,g=>e("button",{key:g,type:"button",onClick:_e=>t.value.overall_rating=g,class:"focus:outline-none"},[(u(),y("svg",{class:ue(["w-8 h-8 transition-colors cursor-pointer",g<=(t.value.overall_rating||0)?"text-yellow-400 fill-current":"text-gray-300 hover:text-yellow-200"]),viewBox:"0 0 20 20",fill:"currentColor"},r[28]||(r[28]=[e("path",{"fill-rule":"evenodd",d:"M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401z","clip-rule":"evenodd"},null,-1)]),2))],8,pt)),64))]),e("span",xt,v(t.value.overall_rating?`${t.value.overall_rating}/5`:"Non valutato"),1)])])]),e("div",bt,[r[33]||(r[33]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Contenuto Valutazione",-1)),e("div",_t,[e("div",null,[r[30]||(r[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Contenuto Valutazione ",-1)),K(e("textarea",{"onUpdate:modelValue":r[7]||(r[7]=g=>t.value.review_content=g),rows:"6",placeholder:"Inserisci il contenuto della valutazione...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[oe,t.value.review_content]]),r[31]||(r[31]=e("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-1"}," Supporta Markdown per formattazione ",-1))]),e("div",null,[r[32]||(r[32]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Commenti Manager ",-1)),K(e("textarea",{"onUpdate:modelValue":r[8]||(r[8]=g=>t.value.manager_comments=g),rows:"4",placeholder:"Commenti e osservazioni del manager...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[oe,t.value.manager_comments]])])])]),L.value&&t.value.review_type==="annual"?(u(),y("div",wt,[e("div",ht,[e("div",kt,[e("div",null,[e("h4",zt,[f(C,{name:"flag",size:"md",class:"text-amber-500 mr-2"}),G(" Valutazione Obiettivi "+v(t.value.review_year-1),1)]),r[34]||(r[34]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400 mt-1"}," Valuta il raggiungimento degli obiettivi dell'anno precedente ",-1))]),f(ie,{status:ge.value,type:"performance","show-dot":!0},null,8,["status"])]),V.value.length>0?(u(),y("div",Ct,[e("div",$t,[e("div",Et,[e("div",St,v(V.value.length),1),e("div",Vt,"Obiettivi "+v(t.value.review_year-1),1)]),e("div",Rt,[e("div",Gt,v(Z.value),1),r[35]||(r[35]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Valutati",-1))]),e("div",Mt,[e("div",Pt,v(ye.value.toFixed(1)),1),r[36]||(r[36]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Rating Medio",-1))])]),e("div",At,[e("div",Tt,[r[37]||(r[37]=e("span",{class:"text-gray-600 dark:text-gray-400"},"Progresso Valutazione",-1)),e("span",jt,v(Math.round(le.value))+"%",1)]),e("div",Nt,[e("div",{class:ue(["h-2 rounded-full transition-all duration-500",le.value===100?"bg-green-500":"bg-amber-500"]),style:he({width:`${le.value}%`})},null,6)])])])):(u(),y("div",Bt,[f(C,{name:"flag",size:"lg",class:"mx-auto mb-2 opacity-50"}),e("p",null,"Nessun obiettivo trovato per l'anno "+v(t.value.review_year-1),1)])),e("div",Ot,[V.value.length>0?(u(),H(q,{key:0,variant:"primary",size:"md",onClick:Pe,disabled:t.value.status==="approved"},{default:Y(()=>[f(C,{name:"star",size:"sm",class:"mr-2"}),G(" "+v(Z.value===V.value.length?"Rivedi Valutazioni":"Valuta Obiettivi"),1)]),_:1},8,["disabled"])):k("",!0),f(q,{variant:"ghost",size:"md",onClick:Ge,loading:Q.value},{default:Y(()=>[f(C,{name:"arrow-path",size:"sm",class:"mr-1"}),r[38]||(r[38]=G(" Aggiorna "))]),_:1,__:[38]},8,["loading"])])]),e("div",Ut,[e("div",Ft,[e("div",null,[e("h4",Lt,[f(C,{name:"target",size:"md",class:"text-blue-500 mr-2"}),G(" Pianificazione Obiettivi "+v(t.value.review_year+1),1)]),r[39]||(r[39]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400 mt-1"}," Imposta gli obiettivi per l'anno successivo ",-1))]),f(ie,{status:fe.value,type:"performance","show-dot":!0},null,8,["status"])]),j.value.length>0?(u(),y("div",It,[e("div",Yt,[e("div",qt,[e("div",Dt,v(j.value.length),1),e("div",Ht,"Obiettivi "+v(t.value.review_year+1),1)]),e("div",Qt,[e("div",Jt,v(j.value.filter(g=>g.category).length),1),r[40]||(r[40]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Categorizzati",-1))]),e("div",Kt,[e("div",Wt,v(j.value.filter(g=>g.priority==="high").length),1),r[41]||(r[41]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Alta Priorità",-1))])])])):(u(),y("div",Xt,[f(C,{name:"target",size:"lg",class:"mx-auto mb-2 opacity-50"}),e("p",null,"Nessun obiettivo pianificato per il "+v(t.value.review_year+1),1),r[42]||(r[42]=e("p",{class:"text-xs mt-1"},"Crea obiettivi per guidare la crescita dell'anno successivo",-1))])),e("div",Zt,[f(q,{variant:"primary",size:"md",onClick:Ae,disabled:t.value.status==="approved"},{default:Y(()=>[f(C,{name:"plus",size:"sm",class:"mr-2"}),G(" "+v(j.value.length>0?"Gestisci Obiettivi":"Crea Obiettivi"),1)]),_:1},8,["disabled"]),j.value.length>0?(u(),H(q,{key:0,variant:"ghost",size:"md",onClick:Me,loading:E.value},{default:Y(()=>[f(C,{name:"arrow-path",size:"sm",class:"mr-1"}),r[43]||(r[43]=G(" Aggiorna "))]),_:1,__:[43]},8,["loading"])):k("",!0)])])])):L.value?(u(),y("div",er,[e("div",ar,[e("div",null,[e("h4",tr,[f(C,{name:"flag",size:"md",class:"text-blue-500 mr-2"}),r[44]||(r[44]=G(" Valutazione Obiettivi "))]),r[45]||(r[45]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400 mt-1"}," Valuta il raggiungimento degli obiettivi collegati a questa performance review ",-1))]),f(ie,{status:me.value,type:"performance","show-dot":!0},null,8,["status"])]),w.value.length>0?(u(),y("div",rr,[e("div",lr,[e("div",sr,[e("div",or,v(w.value.length),1),r[46]||(r[46]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Obiettivi Totali",-1))]),e("div",nr,[e("div",ir,v(J.value),1),r[47]||(r[47]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Valutati",-1))]),e("div",ur,[e("div",dr,v(re.value.toFixed(1)),1),r[48]||(r[48]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Rating Medio",-1))])]),e("div",vr,[e("div",cr,[r[49]||(r[49]=e("span",{class:"text-gray-600 dark:text-gray-400"},"Progresso Valutazione",-1)),e("span",mr,v(Math.round(W.value))+"%",1)]),e("div",gr,[e("div",{class:ue(["h-2 rounded-full transition-all duration-500",W.value===100?"bg-green-500":"bg-blue-500"]),style:he({width:`${W.value}%`})},null,6)])]),w.value.length<=3?(u(),y("div",yr,[(u(!0),y(ae,null,ne(w.value,g=>(u(),y("div",{key:g.id,class:"flex items-center justify-between text-sm"},[e("div",fr,[f(ie,{status:g.achievement_rating>0?"evaluated":"not_evaluated",type:"performance","show-dot":!0},null,8,["status"]),e("span",pr,v(g.title),1)]),g.achievement_rating>0?(u(),y("div",xr,[f(Re,{"model-value":g.achievement_rating,readonly:!0,size:"sm","show-value":!0},null,8,["model-value"])])):k("",!0)]))),128))])):k("",!0)])):(u(),y("div",br,[f(C,{name:"flag",size:"lg",class:"mx-auto mb-2 opacity-50"}),r[50]||(r[50]=e("p",null,"Nessun obiettivo collegato a questa review",-1)),r[51]||(r[51]=e("p",{class:"text-xs mt-1"},"Collega obiettivi nella sezione Goals per poterli valutare",-1))])),e("div",_r,[w.value.length>0?(u(),H(q,{key:0,variant:"primary",size:"md",onClick:i,disabled:t.value.status==="approved"},{default:Y(()=>[f(C,{name:"star",size:"sm",class:"mr-2"}),G(" "+v(J.value===w.value.length?"Rivedi Valutazioni":"Valuta Obiettivi"),1)]),_:1},8,["disabled"])):k("",!0),w.value.length===0?(u(),H(q,{key:1,variant:"outline-primary",size:"md",onClick:r[9]||(r[9]=g=>c.$emit("link-goals"))},{default:Y(()=>[f(C,{name:"link",size:"sm",class:"mr-2"}),r[52]||(r[52]=G(" Collega Obiettivi "))]),_:1,__:[52]})):k("",!0),w.value.length>0?(u(),H(q,{key:2,variant:"ghost",size:"md",onClick:l,loading:P.value},{default:Y(()=>[f(C,{name:"arrow-path",size:"sm",class:"mr-1"}),r[53]||(r[53]=G(" Aggiorna "))]),_:1,__:[53]},8,["loading"])):k("",!0)])])):k("",!0),B.value?(u(),y("div",wr,[f(C,{name:"arrow-path",size:"sm",class:"animate-spin mr-2"}),r[54]||(r[54]=G(" Salvataggio automatico... "))])):k("",!0)],32)]),e("div",hr,[e("div",null,[L.value?(u(),y("button",{key:0,type:"button",onClick:r[10]||(r[10]=g=>$.value=!0),class:"inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 text-sm font-medium rounded-md text-red-700 dark:text-red-400 bg-white dark:bg-gray-700 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"},[f(C,{name:"trash",size:"sm",class:"mr-2"}),r[55]||(r[55]=G(" Elimina "))])):k("",!0)]),e("div",kr,[e("button",{type:"button",onClick:x,class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"}," Annulla "),e("button",{type:"button",onClick:xe,disabled:d.loading,class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"},[d.loading?(u(),H(C,{key:0,name:"arrow-path",size:"sm",class:"animate-spin mr-2"})):k("",!0),r[56]||(r[56]=G(" Salva Bozza "))],8,zr),e("button",{type:"submit",onClick:ve,disabled:d.loading||!de.value,class:"btn-primary disabled:opacity-50"},[d.loading?(u(),H(C,{key:0,name:"arrow-path",size:"sm",class:"animate-spin mr-2"})):k("",!0),G(" "+v(L.value?"Aggiorna Valutazione":"Crea Valutazione"),1)],8,Cr)])])])]),$.value?(u(),H(Se,{key:0,title:"Elimina Valutazione",message:"Sei sicuro di voler eliminare questa valutazione? Questa azione non può essere annullata.","confirm-text":"Elimina","confirm-variant":"danger",onConfirm:ee,onCancel:r[12]||(r[12]=g=>$.value=!1)})):k("",!0),z.value?(u(),H(Qa,{key:1,"review-id":t.value.id,"review-title":R(),readonly:t.value.status==="approved",onClose:r[13]||(r[13]=g=>z.value=!1),onSaved:o,onCompleted:n},null,8,["review-id","review-title","readonly"])):k("",!0)],64))}},Pr=ke($r,[["__scopeId","data-v-9c051e86"]]);export{Pr as R,Re as S,Ve as u};
