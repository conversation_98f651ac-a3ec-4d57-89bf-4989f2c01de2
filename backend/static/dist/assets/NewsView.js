import{r as k,c as v,x as R,b as n,j as e,e as a,k as f,f as B,t as x,l as m,s as l,v as N,u as V,q as j,o as i}from"./vendor.js";import{_ as H,e as D,a as G,H as o,d as L}from"./app.js";import{u as U}from"./useFormatters.js";import{S as $}from"./StatusBadge.js";import"./formatters.js";const q={class:"min-h-screen bg-gray-50 dark:bg-gray-900"},F={key:0,class:"flex justify-center items-center h-64"},J={key:1,class:"max-w-4xl mx-auto px-4 py-8"},K={class:"mb-8"},Q={class:"flex","aria-label":"Breadcrumb"},W={class:"flex items-center space-x-4"},X={class:"text-gray-900 dark:text-white"},Y={class:"bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden"},Z={key:0,class:"aspect-w-16 aspect-h-9"},ee=["src","alt"],te={class:"p-8"},se={class:"mb-8"},ae={class:"flex items-center justify-between mb-4"},re={class:"flex items-center space-x-4"},oe={class:"text-3xl font-bold text-gray-900 dark:text-white mb-4"},ne={class:"flex items-center text-sm text-gray-500 dark:text-gray-400 space-x-6"},ie={class:"flex items-center"},le={class:"flex items-center"},ce={key:0,class:"flex items-center"},de={key:1,class:"flex items-center"},ue={class:"prose prose-lg max-w-none dark:prose-invert"},me=["innerHTML"],xe={key:0,class:"mt-8 pt-8 border-t border-gray-200 dark:border-gray-700"},ve={class:"flex items-center"},ge={class:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},pe={class:"mt-8 pt-8 border-t border-gray-200 dark:border-gray-700"},ye={class:"flex items-center justify-between"},fe={class:"flex items-center space-x-4"},_e={key:2,class:"text-center py-12"},we={class:"mt-6"},be={__name:"NewsView",setup(he){const z=V(),g=j(),d=D(),p=G(),{formatDate:_}=U(),{showToast:c}=L(),y=k(!0),t=k(null),C=v(()=>p.hasPermission("PERMISSION_MANAGE_COMMUNICATION")),E=v(()=>p.hasPermission("PERMISSION_MANAGE_COMMUNICATION")),w=v(()=>p.hasPermission("PERMISSION_MANAGE_COMMUNICATION")),I=v(()=>{var r;return(r=t.value)!=null&&r.content?t.value.content.replace(/\n/g,"<br>"):""}),M=async()=>{const r=parseInt(z.params.id);y.value=!0;try{const s=d.news.find(u=>u.id===r);s?t.value=s:(await d.fetchNews(),t.value=d.news.find(u=>u.id===r)),t.value&&await b()}catch(s){console.error("Errore nel caricamento della news:",s),c("Errore nel caricamento della news","error")}finally{y.value=!1}},b=async()=>{if(!(!t.value||t.value.is_read))try{await d.markNewsAsRead(t.value.id),c("News segnata come letta","success")}catch(r){console.error("Errore nel segnare come letta:",r)}},S=async()=>{try{await d.pinNews(t.value.id),t.value.is_pinned=!0,c("News messa in evidenza","success")}catch(r){console.error("Errore nel mettere in evidenza:",r),c("Errore nel mettere in evidenza","error")}},A=async()=>{try{await d.unpinNews(t.value.id),t.value.is_pinned=!1,c("News rimossa da evidenza","success")}catch(r){console.error("Errore nel rimuovere da evidenza:",r),c("Errore nel rimuovere da evidenza","error")}},T=()=>{g.push(`/app/communications/news/${t.value.id}/edit`)},O=async()=>{if(confirm(`Sei sicuro di voler eliminare la news "${t.value.title}"?`))try{await d.deleteNews(t.value.id),c("News eliminata con successo","success"),g.push("/app/communications/news")}catch(r){console.error("Errore nell'eliminazione:",r),c("Errore nell'eliminazione della news","error")}},P=()=>{g.push("/app/communications/news")};return R(()=>{M()}),(r,s)=>{var h;const u=B("router-link");return i(),n("div",q,[y.value?(i(),n("div",F,s[0]||(s[0]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):t.value?(i(),n("div",J,[e("div",K,[e("nav",Q,[e("ol",W,[e("li",null,[a(u,{to:"/app/communications",class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"},{default:f(()=>s[1]||(s[1]=[l(" Comunicazioni ")])),_:1,__:[1]})]),a(o,{name:"chevron-right",size:"sm",class:"text-gray-400"}),e("li",null,[a(u,{to:"/app/communications/news",class:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"},{default:f(()=>s[2]||(s[2]=[l(" News ")])),_:1,__:[2]})]),a(o,{name:"chevron-right",size:"sm",class:"text-gray-400"}),e("li",X,x(t.value.title),1)])])]),e("article",Y,[t.value.image_url?(i(),n("div",Z,[e("img",{src:t.value.image_url,alt:t.value.title,class:"w-full h-96 object-cover"},null,8,ee)])):m("",!0),e("div",te,[e("header",se,[e("div",ae,[a($,{status:t.value.is_published?"active":"draft",label:t.value.is_published?"Pubblicata":"Bozza"},null,8,["status","label"]),e("div",re,[C.value?(i(),n("button",{key:0,onClick:T,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[a(o,{name:"pencil",size:"sm",class:"mr-1"}),s[3]||(s[3]=l(" Modifica "))])):m("",!0),E.value?(i(),n("button",{key:1,onClick:O,class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},[a(o,{name:"trash",size:"sm",class:"mr-1"}),s[4]||(s[4]=l(" Elimina "))])):m("",!0)])]),e("h1",oe,x(t.value.title),1),e("div",ne,[e("div",ie,[a(o,{name:"user",size:"sm",class:"mr-2"}),e("span",null,x(((h=t.value.author)==null?void 0:h.full_name)||"Anonimo"),1)]),e("div",le,[a(o,{name:"calendar",size:"sm",class:"mr-2"}),e("span",null,x(N(_)(t.value.created_at)),1)]),t.value.updated_at!==t.value.created_at?(i(),n("div",ce,[a(o,{name:"arrow-path",size:"sm",class:"mr-2"}),e("span",null,"Aggiornata "+x(N(_)(t.value.updated_at)),1)])):m("",!0),t.value.is_pinned?(i(),n("div",de,[a(o,{name:"bookmark",size:"sm",class:"mr-2 text-yellow-500"}),s[5]||(s[5]=e("span",{class:"text-yellow-600 dark:text-yellow-400"},"In evidenza",-1))])):m("",!0)])]),e("div",ue,[e("div",{innerHTML:I.value},null,8,me)]),t.value.category?(i(),n("div",xe,[e("div",ve,[a(o,{name:"tag",size:"sm",class:"mr-2 text-gray-400"}),s[6]||(s[6]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Categoria:",-1)),e("span",ge,x(t.value.category),1)])])):m("",!0),e("div",pe,[e("div",ye,[e("div",fe,[e("button",{onClick:b,class:"inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"},[a(o,{name:"eye",size:"sm",class:"mr-1"}),s[7]||(s[7]=l(" Segna come letta "))]),!t.value.is_pinned&&w.value?(i(),n("button",{key:0,onClick:S,class:"inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"},[a(o,{name:"bookmark",size:"sm",class:"mr-1"}),s[8]||(s[8]=l(" Metti in evidenza "))])):t.value.is_pinned&&w.value?(i(),n("button",{key:1,onClick:A,class:"inline-flex items-center text-sm text-yellow-600 hover:text-yellow-700 dark:text-yellow-400 dark:hover:text-yellow-300"},[a(o,{name:"bookmark",size:"sm",class:"mr-1"}),s[9]||(s[9]=l(" Rimuovi da evidenza "))])):m("",!0)]),e("button",{onClick:P,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[a(o,{name:"arrow-left",size:"sm",class:"mr-2"}),s[10]||(s[10]=l(" Torna alla lista "))])])])])])])):(i(),n("div",_e,[a(o,{name:"newspaper",size:"2xl",class:"mx-auto text-gray-400 mb-4"}),s[12]||(s[12]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"News non trovata",-1)),s[13]||(s[13]=e("p",{class:"text-gray-500 dark:text-gray-400"},"La news richiesta non esiste o è stata rimossa.",-1)),e("div",we,[a(u,{to:"/app/communications/news",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"},{default:f(()=>s[11]||(s[11]=[l(" Torna alle news ")])),_:1,__:[11]})])]))])}}},Ie=H(be,[["__scopeId","data-v-740c92a5"]]);export{Ie as default};
