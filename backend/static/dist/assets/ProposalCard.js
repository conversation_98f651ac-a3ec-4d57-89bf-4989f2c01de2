import{b as d,o as m,j as t,l as x,t as a,A as l,e as c,s as b}from"./vendor.js";import{_ as v,H as u}from"./app.js";const w={class:"flex justify-between items-start mb-2"},k={class:"font-medium text-gray-900 text-sm line-clamp-2"},h={class:"relative"},$={key:0,class:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200"},C={class:"py-1"},D={class:"flex items-center space-x-2 mb-2"},z={class:"text-sm text-gray-600"},N={key:0,class:"flex items-center space-x-2 mb-2"},I={class:"text-sm font-medium text-gray-900"},j={class:"flex justify-between items-center text-xs text-gray-500"},M={key:1,class:"mt-2"},V={class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800"},B={__name:"ProposalCard",props:{proposal:{type:Object,required:!0},showMenu:{type:Boolean,default:!1}},emits:["click","toggle-menu","view","edit","duplicate","delete"],setup(o,{emit:T}){const y=s=>new Intl.NumberFormat("it-IT").format(s||0),f=s=>s?new Date(s).toLocaleDateString("it-IT"):"N/A",g=s=>{if(!s.expiry_date||s.status==="accepted"||s.status==="rejected")return!1;const n=new Date(s.expiry_date)-new Date,i=Math.ceil(n/(1e3*60*60*24));return i<=7&&i>=0};return(s,e)=>{var p,n,i;return m(),d("div",{class:"bg-white rounded-lg border border-gray-200 p-4 cursor-pointer hover:shadow-md transition-shadow",onClick:e[5]||(e[5]=r=>s.$emit("click",o.proposal))},[t("div",w,[t("h4",k,a(o.proposal.title),1),t("div",h,[t("button",{onClick:e[0]||(e[0]=l(r=>s.$emit("toggle-menu",o.proposal.id),["stop"])),class:"text-gray-400 hover:text-gray-600"},[c(u,{name:"ellipsis-vertical",size:"sm"})]),o.showMenu?(m(),d("div",$,[t("div",C,[t("button",{onClick:e[1]||(e[1]=l(r=>s.$emit("view",o.proposal.id),["stop"])),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Visualizza "),t("button",{onClick:e[2]||(e[2]=l(r=>s.$emit("edit",o.proposal.id),["stop"])),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Modifica "),t("button",{onClick:e[3]||(e[3]=l(r=>s.$emit("duplicate",o.proposal.id),["stop"])),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Duplica "),e[6]||(e[6]=t("div",{class:"border-t border-gray-100"},null,-1)),t("button",{onClick:e[4]||(e[4]=l(r=>s.$emit("delete",o.proposal.id),["stop"])),class:"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"}," Elimina ")])])):x("",!0)])]),t("div",D,[c(u,{name:"building-office-2",size:"sm",class:"text-gray-400"}),t("span",z,a(((p=o.proposal.client)==null?void 0:p.name)||"N/A"),1)]),o.proposal.value?(m(),d("div",N,[c(u,{name:"currency-euro",size:"sm",class:"text-gray-400"}),t("span",I," €"+a(y(o.proposal.value)),1)])):x("",!0),t("div",j,[t("span",null,a((n=o.proposal.creator)==null?void 0:n.first_name)+" "+a((i=o.proposal.creator)==null?void 0:i.last_name),1),t("span",null,a(f(o.proposal.created_at)),1)]),g(o.proposal)?(m(),d("div",M,[t("span",V,[c(u,{name:"exclamation-circle",size:"xs",class:"mr-1"}),e[7]||(e[7]=b(" In scadenza "))])])):x("",!0)])}}},P=v(B,[["__scopeId","data-v-5252b3d9"]]);export{P};
