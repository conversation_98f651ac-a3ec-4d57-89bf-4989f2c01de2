import{u as j}from"./engagement.js";import{_ as N,H as O,d as W}from"./app.js";import{D as X}from"./DashboardTemplate.js";import{T as Y}from"./TabContainer.js";import{S as Z}from"./StandardButton.js";import{A as $}from"./ActionButtonGroup.js";import{S as ee}from"./StatusBadge.js";import{c as C,r as S,w as H,b as m,l as z,j as e,t as d,e as f,f as D,A as G,B as r,C as p,H as P,s as E,o as u,I as te,F as U,p as I,Q as oe,k as q,x as ae}from"./vendor.js";const ie={name:"CampaignFormModal",components:{HeroIcon:O},props:{isOpen:{type:Boolean,default:!1},campaign:{type:Object,default:null}},emits:["close","saved"],setup(n,{emit:t}){const b=j(),o=C(()=>b.loading),g=S({name:"",description:"",campaign_type:"",status:"draft",start_date:"",end_date:"",points_per_participation:10,points_per_completion:50,winner_bonus_points:100,rules:"",participation_requirements:""}),_=C(()=>{var s;return!!((s=n.campaign)!=null&&s.id)});H(()=>n.isOpen,s=>{s&&(v(),n.campaign&&a(n.campaign))});const v=()=>{g.value={name:"",description:"",campaign_type:"",status:"draft",start_date:"",end_date:"",points_per_participation:10,points_per_completion:50,winner_bonus_points:100,rules:"",participation_requirements:""}},a=s=>{g.value={name:s.name||"",description:s.description||"",campaign_type:s.campaign_type||"",status:s.status||"draft",start_date:s.start_date?s.start_date.split("T")[0]:"",end_date:s.end_date?s.end_date.split("T")[0]:"",points_per_participation:s.points_per_participation||10,points_per_completion:s.points_per_completion||50,winner_bonus_points:s.winner_bonus_points||100,rules:s.rules||"",participation_requirements:s.participation_requirements||""}},h=async()=>{try{const s={...g.value};["rules","participation_requirements"].forEach(x=>{s[x]===""&&(s[x]=null)});let k;_.value?k=await b.updateCampaign(n.campaign.id,s):k=await b.createCampaign(s),k&&(t("saved",k),w())}catch(s){console.error("Error saving campaign:",s)}},w=()=>{t("close")};return{loading:o,formData:g,editMode:_,submitForm:h,closeModal:w}}},se={key:0,class:"fixed inset-0 bg-gray-50 bg-opacity-75 flex items-center justify-center z-50"},re={class:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-screen overflow-y-auto m-4"},ne={class:"px-6 py-4 border-b border-gray-200"},le={class:"flex items-center justify-between"},de={class:"text-lg font-medium text-gray-900"},me={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ue={class:"md:col-span-2"},ce={class:"md:col-span-2"},pe={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ge={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},fe={class:"space-y-4"},be={class:"flex justify-end space-x-3 pt-4 border-t border-gray-200"},ve=["disabled"],ye={key:0,class:"flex items-center"},_e={key:1};function xe(n,t,b,o,g,_){const v=D("HeroIcon");return b.isOpen?(u(),m("div",se,[e("div",re,[e("div",ne,[e("div",le,[e("h3",de,d(o.editMode?"Modifica Campagna":"Nuova Campagna"),1),e("button",{onClick:t[0]||(t[0]=(...a)=>o.closeModal&&o.closeModal(...a)),class:"text-gray-400 hover:text-gray-600"},[f(v,{name:"x-mark",size:"md"})])])]),e("form",{onSubmit:t[13]||(t[13]=G((...a)=>o.submitForm&&o.submitForm(...a),["prevent"])),class:"p-6 space-y-6"},[e("div",me,[e("div",ue,[t[14]||(t[14]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Nome Campagna * ",-1)),r(e("input",{"onUpdate:modelValue":t[1]||(t[1]=a=>o.formData.name=a),type:"text",required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Es. Sfida Produttività Q1"},null,512),[[p,o.formData.name]])]),e("div",ce,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Descrizione * ",-1)),r(e("textarea",{"onUpdate:modelValue":t[2]||(t[2]=a=>o.formData.description=a),rows:"3",required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Descrizione della campagna e obiettivi"},null,512),[[p,o.formData.description]])]),e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Tipo Campagna * ",-1)),r(e("select",{"onUpdate:modelValue":t[3]||(t[3]=a=>o.formData.campaign_type=a),required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[16]||(t[16]=[e("option",{value:""},"Seleziona tipo",-1),e("option",{value:"individual"},"Individuale",-1),e("option",{value:"team"},"Team",-1),e("option",{value:"company"},"Aziendale",-1)]),512),[[P,o.formData.campaign_type]])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Status * ",-1)),r(e("select",{"onUpdate:modelValue":t[4]||(t[4]=a=>o.formData.status=a),required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[18]||(t[18]=[e("option",{value:"draft"},"Bozza",-1),e("option",{value:"active"},"Attiva",-1),e("option",{value:"upcoming"},"In Arrivo",-1),e("option",{value:"ended"},"Terminata",-1)]),512),[[P,o.formData.status]])])]),e("div",pe,[e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Data Inizio * ",-1)),r(e("input",{"onUpdate:modelValue":t[5]||(t[5]=a=>o.formData.start_date=a),type:"date",required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[p,o.formData.start_date]])]),e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Data Fine * ",-1)),r(e("input",{"onUpdate:modelValue":t[6]||(t[6]=a=>o.formData.end_date=a),type:"date",required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[p,o.formData.end_date]])])]),e("div",ge,[e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Punti per Partecipazione ",-1)),r(e("input",{"onUpdate:modelValue":t[7]||(t[7]=a=>o.formData.points_per_participation=a),type:"number",min:"0",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[p,o.formData.points_per_participation,void 0,{number:!0}]])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Punti per Completamento ",-1)),r(e("input",{"onUpdate:modelValue":t[8]||(t[8]=a=>o.formData.points_per_completion=a),type:"number",min:"0",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[p,o.formData.points_per_completion,void 0,{number:!0}]])]),e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Bonus Vincitore ",-1)),r(e("input",{"onUpdate:modelValue":t[9]||(t[9]=a=>o.formData.winner_bonus_points=a),type:"number",min:"0",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[p,o.formData.winner_bonus_points,void 0,{number:!0}]])])]),e("div",fe,[e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Regole e Criteri ",-1)),r(e("textarea",{"onUpdate:modelValue":t[10]||(t[10]=a=>o.formData.rules=a),rows:"4",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Regole della campagna, criteri di valutazione, requisiti di partecipazione..."},null,512),[[p,o.formData.rules]])]),e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Requisiti di Partecipazione ",-1)),r(e("textarea",{"onUpdate:modelValue":t[11]||(t[11]=a=>o.formData.participation_requirements=a),rows:"3",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Chi può partecipare, requisiti minimi, dipartimenti coinvolti..."},null,512),[[p,o.formData.participation_requirements]])])]),e("div",be,[e("button",{type:"button",onClick:t[12]||(t[12]=(...a)=>o.closeModal&&o.closeModal(...a)),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"}," Annulla "),e("button",{type:"submit",disabled:o.loading,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[o.loading?(u(),m("div",ye,t[27]||(t[27]=[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),E(" Salvando... ")]))):(u(),m("span",_e,d(o.editMode?"Aggiorna Campagna":"Crea Campagna"),1))],8,ve)])],32)])])):z("",!0)}const we=N(ie,[["render",xe]]),De={name:"RewardFormModal",components:{HeroIcon:O},props:{isOpen:{type:Boolean,default:!1},reward:{type:Object,default:null}},emits:["close","saved"],setup(n,{emit:t}){const b=j(),o=C(()=>b.loading),g=S({name:"",description:"",reward_type:"",points_cost:"",available_from:"",available_until:"",max_redemptions:"",per_user_limit:1,estimated_value:"",campaign_id:"",redemption_instructions:"",requirements:"",is_active:!0}),_=C(()=>{var i;return!!((i=n.reward)!=null&&i.id)}),v=C(()=>b.campaigns.filter(i=>i.status!=="ended"));H(()=>n.isOpen,i=>{i&&(a(),n.reward&&h(n.reward))});const a=()=>{g.value={name:"",description:"",reward_type:"",points_cost:"",available_from:"",available_until:"",max_redemptions:"",per_user_limit:1,estimated_value:"",campaign_id:"",redemption_instructions:"",requirements:"",is_active:!0}},h=i=>{g.value={name:i.name||"",description:i.description||"",reward_type:i.reward_type||"",points_cost:i.points_cost||"",available_from:i.available_from?i.available_from.split("T")[0]:"",available_until:i.available_until?i.available_until.split("T")[0]:"",max_redemptions:i.max_redemptions||"",per_user_limit:i.per_user_limit||1,estimated_value:i.estimated_value||"",campaign_id:i.campaign_id||"",redemption_instructions:i.redemption_instructions||"",requirements:i.requirements||"",is_active:i.is_active!==void 0?i.is_active:!0}},w=async()=>{try{const i={...g.value};["available_from","available_until","max_redemptions","estimated_value","campaign_id","redemption_instructions","requirements"].forEach(y=>{i[y]===""&&(i[y]=null)});let x;_.value?x=await b.updateReward(n.reward.id,i):x=await b.createReward(i),x&&(t("saved",x),s())}catch(i){console.error("Error saving reward:",i)}},s=()=>{t("close")};return{loading:o,formData:g,editMode:_,availableCampaigns:v,submitForm:w,closeModal:s}}},Ce={key:0,class:"fixed inset-0 bg-gray-50 bg-opacity-75 flex items-center justify-center z-50"},he={class:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-screen overflow-y-auto m-4"},ke={class:"px-6 py-4 border-b border-gray-200"},Me={class:"flex items-center justify-between"},Se={class:"text-lg font-medium text-gray-900"},Re={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ve={class:"md:col-span-2"},qe={class:"md:col-span-2"},ze={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Te={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ae=["value"],Fe={class:"space-y-4"},Ue={class:"flex items-center"},Pe={class:"flex justify-end space-x-3 pt-4 border-t border-gray-200"},Ee=["disabled"],Be={key:0,class:"flex items-center"},Ie={key:1};function je(n,t,b,o,g,_){const v=D("HeroIcon");return b.isOpen?(u(),m("div",Ce,[e("div",he,[e("div",ke,[e("div",Me,[e("h3",Se,d(o.editMode?"Modifica Premio":"Nuovo Premio"),1),e("button",{onClick:t[0]||(t[0]=(...a)=>o.closeModal&&o.closeModal(...a)),class:"text-gray-400 hover:text-gray-600"},[f(v,{name:"x-mark",size:"md"})])])]),e("form",{onSubmit:t[15]||(t[15]=G((...a)=>o.submitForm&&o.submitForm(...a),["prevent"])),class:"p-6 space-y-6"},[e("div",Re,[e("div",Ve,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Nome Premio * ",-1)),r(e("input",{"onUpdate:modelValue":t[1]||(t[1]=a=>o.formData.name=a),type:"text",required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Es. Voucher Amazon 25€"},null,512),[[p,o.formData.name]])]),e("div",qe,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Descrizione * ",-1)),r(e("textarea",{"onUpdate:modelValue":t[2]||(t[2]=a=>o.formData.description=a),rows:"3",required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Descrizione dettagliata del premio"},null,512),[[p,o.formData.description]])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Tipo Premio * ",-1)),r(e("select",{"onUpdate:modelValue":t[3]||(t[3]=a=>o.formData.reward_type=a),required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[18]||(t[18]=[te('<option value="">Seleziona tipo</option><option value="physical">Fisico</option><option value="digital">Digitale</option><option value="experience">Esperienza</option><option value="monetary">Monetario</option><option value="time_off">Permesso</option>',6)]),512),[[P,o.formData.reward_type]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Costo in Punti * ",-1)),r(e("input",{"onUpdate:modelValue":t[4]||(t[4]=a=>o.formData.points_cost=a),type:"number",required:"",min:"1",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[p,o.formData.points_cost,void 0,{number:!0}]])])]),e("div",ze,[e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Disponibile da ",-1)),r(e("input",{"onUpdate:modelValue":t[5]||(t[5]=a=>o.formData.available_from=a),type:"date",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[p,o.formData.available_from]])]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Disponibile fino a ",-1)),r(e("input",{"onUpdate:modelValue":t[6]||(t[6]=a=>o.formData.available_until=a),type:"date",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[p,o.formData.available_until]])])]),e("div",Te,[e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Riscatti Massimi ",-1)),r(e("input",{"onUpdate:modelValue":t[7]||(t[7]=a=>o.formData.max_redemptions=a),type:"number",min:"1",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Illimitati se vuoto"},null,512),[[p,o.formData.max_redemptions,void 0,{number:!0}]])]),e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Limite per Utente ",-1)),r(e("input",{"onUpdate:modelValue":t[8]||(t[8]=a=>o.formData.per_user_limit=a),type:"number",min:"1",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"1"},null,512),[[p,o.formData.per_user_limit,void 0,{number:!0}]])]),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Valore Stimato (€) ",-1)),r(e("input",{"onUpdate:modelValue":t[9]||(t[9]=a=>o.formData.estimated_value=a),type:"number",step:"0.01",min:"0",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[p,o.formData.estimated_value,void 0,{number:!0}]])])]),e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Campagna Associata ",-1)),r(e("select",{"onUpdate:modelValue":t[10]||(t[10]=a=>o.formData.campaign_id=a),class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},[t[26]||(t[26]=e("option",{value:""},"Nessuna campagna specifica",-1)),(u(!0),m(U,null,I(o.availableCampaigns,a=>(u(),m("option",{key:a.id,value:a.id},d(a.name),9,Ae))),128))],512),[[P,o.formData.campaign_id]]),t[28]||(t[28]=e("p",{class:"text-xs text-gray-500 mt-1"}," Se associato a una campagna, il premio sarà disponibile solo ai partecipanti ",-1))]),e("div",Fe,[e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Istruzioni per il Riscatto ",-1)),r(e("textarea",{"onUpdate:modelValue":t[11]||(t[11]=a=>o.formData.redemption_instructions=a),rows:"3",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Come riscattare il premio, contatti, procedure..."},null,512),[[p,o.formData.redemption_instructions]])]),e("div",null,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Requisiti Aggiuntivi ",-1)),r(e("textarea",{"onUpdate:modelValue":t[12]||(t[12]=a=>o.formData.requirements=a),rows:"2",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Requisiti specifici, limitazioni, condizioni..."},null,512),[[p,o.formData.requirements]])])]),e("div",Ue,[r(e("input",{id:"is-active","onUpdate:modelValue":t[13]||(t[13]=a=>o.formData.is_active=a),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[oe,o.formData.is_active]]),t[31]||(t[31]=e("label",{for:"is-active",class:"ml-2 block text-sm text-gray-900"}," Premio attivo e disponibile per il riscatto ",-1))]),e("div",Pe,[e("button",{type:"button",onClick:t[14]||(t[14]=(...a)=>o.closeModal&&o.closeModal(...a)),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"}," Annulla "),e("button",{type:"submit",disabled:o.loading,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[o.loading?(u(),m("div",Be,t[32]||(t[32]=[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),E(" Salvando... ")]))):(u(),m("span",Ie,d(o.editMode?"Aggiorna Premio":"Crea Premio"),1))],8,Ee)])],32)])])):z("",!0)}const Ne=N(De,[["render",je]]),Oe={name:"EngagementAdmin",components:{DashboardTemplate:X,TabContainer:Y,HeroIcon:O,StandardButton:Z,ActionButtonGroup:$,StatusBadge:ee,CampaignFormModal:we,RewardFormModal:Ne},setup(){const n=j(),{showToast:t}=W(),b=S("campaigns"),o=S(!1),g=S(!1),_=S(null),v=S(null),a=S({}),h=C(()=>n.campaigns),w=C(()=>n.rewards),s=C(()=>n.loading),i=C(()=>{var c;return((c=a.value.overview)==null?void 0:c.active_campaigns)||h.value.filter(M=>M.status==="active").length}),k=C(()=>{var c;return((c=a.value.overview)==null?void 0:c.active_rewards)||w.value.filter(M=>M.is_active).length}),x=[{id:"campaigns",name:"Campagne",icon:"megaphone"},{id:"rewards",name:"Premi",icon:"gift"},{id:"analytics",name:"Analytics",icon:"chart-bar"}],y=async()=>{try{const[c,M,B]=await Promise.all([n.fetchAdminCampaigns(),n.fetchAdminRewards(),n.fetchAdminStats()]);a.value=B||{}}catch(c){console.error("Error loading data:",c),t("Errore nel caricamento dei dati","error")}},T=c=>{_.value=c,o.value=!0},R=async c=>{if(confirm("Sei sicuro di voler eliminare questa campagna?"))try{await n.deleteCampaign(c),await y(),t("Campagna eliminata con successo","success")}catch{t("Errore nell'eliminazione della campagna","error")}},A=c=>{v.value=c,g.value=!0},F=async c=>{if(confirm("Sei sicuro di voler eliminare questo premio?"))try{await n.deleteReward(c),await y(),t("Premio eliminato con successo","success")}catch{t("Errore nell'eliminazione del premio","error")}},l=()=>{o.value=!1,_.value=null},V=()=>{g.value=!1,v.value=null},L=()=>{l(),y()},Q=()=>{V(),y()},J=(c,M)=>{if(!c||!M)return"Date non definite";const B=new Date(c).toLocaleDateString("it-IT"),K=new Date(M).toLocaleDateString("it-IT");return`${B} - ${K}`};return ae(()=>{y()}),{loading:s,activeTab:b,showCreateCampaignModal:o,showCreateRewardModal:g,editingCampaign:_,editingReward:v,campaigns:h,rewards:w,adminStats:a,activeCampaignsCount:i,activeRewardsCount:k,tabs:x,loadData:y,editCampaign:T,deleteCampaign:R,editReward:A,deleteReward:F,closeCampaignModal:l,closeRewardModal:V,onCampaignSaved:L,onRewardSaved:Q,formatDateRange:J}}},He={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"},Ge={class:"bg-white rounded-lg shadow p-6"},Le={class:"flex items-center"},Qe={class:"p-3 rounded-lg bg-primary-100 mr-4"},Je={class:"text-2xl font-bold text-gray-900"},Ke={class:"bg-white rounded-lg shadow p-6"},We={class:"flex items-center"},Xe={class:"p-3 rounded-lg bg-green-100 mr-4"},Ye={class:"text-2xl font-bold text-gray-900"},Ze={class:"bg-white rounded-lg shadow p-6"},$e={class:"flex items-center"},et={class:"p-3 rounded-lg bg-yellow-100 mr-4"},tt={class:"text-2xl font-bold text-gray-900"},ot={class:"bg-white rounded-lg shadow p-6"},at={class:"flex items-center"},it={class:"p-3 rounded-lg bg-purple-100 mr-4"},st={class:"text-2xl font-bold text-gray-900"},rt={key:0},nt={class:"space-y-6"},lt={class:"bg-white rounded-lg shadow overflow-hidden"},dt={class:"p-6"},mt={key:0,class:"text-center py-8 text-gray-500"},ut={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},ct={class:"flex items-center justify-between mb-3"},pt={class:"font-medium text-gray-900"},gt={class:"text-sm text-gray-600 mb-3"},ft={class:"flex items-center justify-between"},bt={class:"text-sm text-gray-500"},vt={key:1},yt={class:"space-y-6"},_t={class:"bg-white rounded-lg shadow overflow-hidden"},xt={class:"px-6 py-4 border-b border-gray-200 flex justify-between items-center"},wt={class:"p-6"},Dt={key:0,class:"text-center py-8 text-gray-500"},Ct={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},ht={class:"flex items-center justify-between mb-3"},kt={class:"font-medium text-gray-900"},Mt={class:"text-sm text-gray-600 mb-3"},St={class:"flex items-center justify-between"},Rt={class:"text-sm font-medium text-primary-600"},Vt={key:2},qt={class:"space-y-6"},zt={class:"bg-white rounded-lg shadow p-6"},Tt={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},At={class:"text-center"},Ft={class:"text-3xl font-bold text-primary-600"},Ut={class:"text-center"},Pt={class:"text-3xl font-bold text-green-600"},Et={class:"text-center"},Bt={class:"text-3xl font-bold text-purple-600"};function It(n,t,b,o,g,_){const v=D("StandardButton"),a=D("HeroIcon"),h=D("StatusBadge"),w=D("ActionButtonGroup"),s=D("TabContainer"),i=D("DashboardTemplate"),k=D("CampaignFormModal"),x=D("RewardFormModal");return u(),m(U,null,[f(i,{title:"Amministrazione Engagement",subtitle:"Gestione campagne, premi e sistema di gamification",loading:o.loading,"show-refresh-button":!0,onRefresh:o.loadData},{"header-actions":q(()=>[f(v,{variant:"primary",icon:"plus",onClick:t[0]||(t[0]=y=>o.showCreateCampaignModal=!0)},{default:q(()=>t[3]||(t[3]=[E(" Nuova Campagna ")])),_:1,__:[3]})]),widget:q(()=>{var y,T;return[e("div",He,[e("div",Ge,[e("div",Le,[e("div",Qe,[f(a,{name:"megaphone",size:"md",class:"text-primary-600"})]),e("div",null,[t[4]||(t[4]=e("p",{class:"text-sm font-medium text-gray-600"},"Campagne Attive",-1)),e("p",Je,d(o.activeCampaignsCount)+" (total: "+d(o.campaigns.length)+")",1)])])]),e("div",Ke,[e("div",We,[e("div",Xe,[f(a,{name:"users",size:"md",class:"text-green-600"})]),e("div",null,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-600"},"Partecipanti Totali",-1)),e("p",Ye,d(((y=o.adminStats.overview)==null?void 0:y.total_users)||0),1)])])]),e("div",Ze,[e("div",$e,[e("div",et,[f(a,{name:"gift",size:"md",class:"text-yellow-600"})]),e("div",null,[t[6]||(t[6]=e("p",{class:"text-sm font-medium text-gray-600"},"Premi Attivi",-1)),e("p",tt,d(o.activeRewardsCount)+" (total: "+d(o.rewards.length)+")",1)])])]),e("div",ot,[e("div",at,[e("div",it,[f(a,{name:"star",size:"md",class:"text-purple-600"})]),e("div",null,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-600"},"Punti Distribuiti",-1)),e("p",st,d(((T=o.adminStats.overview)==null?void 0:T.total_points_issued)||0),1)])])])]),f(s,{tabs:o.tabs,"active-tab":o.activeTab,onTabChange:t[2]||(t[2]=R=>o.activeTab=R)},{default:q(()=>{var R,A,F;return[o.activeTab==="campaigns"?(u(),m("div",rt,[e("div",nt,[e("div",lt,[t[8]||(t[8]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Campagne")],-1)),e("div",dt,[o.campaigns.length===0?(u(),m("div",mt," Nessuna campagna trovata ")):(u(),m("div",ut,[(u(!0),m(U,null,I(o.campaigns,l=>(u(),m("div",{key:l.id,class:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"},[e("div",ct,[e("h4",pt,d(l.name),1),f(h,{status:l.status,type:"engagement"},null,8,["status"])]),e("p",gt,d(l.description),1),e("div",ft,[e("span",bt,d(o.formatDateRange(l.start_date,l.end_date)),1),f(w,{onEdit:V=>o.editCampaign(l),onDelete:V=>o.deleteCampaign(l.id),"show-view":!1},null,8,["onEdit","onDelete"])])]))),128))]))])])])])):z("",!0),o.activeTab==="rewards"?(u(),m("div",vt,[e("div",yt,[e("div",_t,[e("div",xt,[t[10]||(t[10]=e("h3",{class:"text-lg font-medium text-gray-900"},"Premi",-1)),f(v,{variant:"secondary",icon:"plus",onClick:t[1]||(t[1]=l=>o.showCreateRewardModal=!0)},{default:q(()=>t[9]||(t[9]=[E(" Nuovo Premio ")])),_:1,__:[9]})]),e("div",wt,[o.rewards.length===0?(u(),m("div",Dt," Nessun premio trovato ")):(u(),m("div",Ct,[(u(!0),m(U,null,I(o.rewards,l=>(u(),m("div",{key:l.id,class:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"},[e("div",ht,[e("h4",kt,d(l.name),1),f(h,{status:l.is_active?"active":"inactive",type:"generic"},null,8,["status"])]),e("p",Mt,d(l.description),1),e("div",St,[e("span",Rt,d(l.points_cost)+" punti ",1),f(w,{onEdit:V=>o.editReward(l),onDelete:V=>o.deleteReward(l.id),"show-view":!1},null,8,["onEdit","onDelete"])])]))),128))]))])])])])):z("",!0),o.activeTab==="analytics"?(u(),m("div",Vt,[e("div",qt,[e("div",zt,[t[14]||(t[14]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Analytics Engagement",-1)),e("div",Tt,[e("div",At,[e("p",Ft,d(((R=o.adminStats.overview)==null?void 0:R.engagement_adoption_rate)||0)+"%",1),t[11]||(t[11]=e("p",{class:"text-sm text-gray-600"},"Tasso di Engagement",-1))]),e("div",Ut,[e("p",Pt,d(((A=o.adminStats.overview)==null?void 0:A.users_with_engagement)||0),1),t[12]||(t[12]=e("p",{class:"text-sm text-gray-600"},"Utenti Attivi (Mese)",-1))]),e("div",Et,[e("p",Bt,d(((F=o.adminStats.rewards)==null?void 0:F.total_redemptions)||0),1),t[13]||(t[13]=e("p",{class:"text-sm text-gray-600"},"Premi Riscattati",-1))])])])])])):z("",!0)]}),_:1},8,["tabs","active-tab"])]}),_:1},8,["loading","onRefresh"]),f(k,{isOpen:o.showCreateCampaignModal,campaign:o.editingCampaign,onClose:o.closeCampaignModal,onSaved:o.onCampaignSaved},null,8,["isOpen","campaign","onClose","onSaved"]),f(x,{isOpen:o.showCreateRewardModal,reward:o.editingReward,onClose:o.closeRewardModal,onSaved:o.onRewardSaved},null,8,["isOpen","reward","onClose","onSaved"])],64)}const Kt=N(Oe,[["render",It]]);export{Kt as default};
