import{_ as nt,H as w,i as rt,j as dt}from"./app.js";import{r as j,c as D,w as ut,x as pt,b as n,e as c,l as y,j as t,k as z,t as a,v as x,F as C,p as A,s as E,u as ct,q as mt,o as l,h as Q,n as xt}from"./vendor.js";import{u as gt}from"./funding.js";import{u as vt}from"./useFormatters.js";import{_ as _t}from"./PageHeader.js";import{S as yt}from"./StatusBadge.js";import{A as ft}from"./ActionButtonGroup.js";import{S as N}from"./StandardButton.js";import{S as B}from"./StandardInput.js";import"./formatters.js";/* empty css                                                             */const ht={key:0,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6"},bt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},wt=["value"],kt={key:1,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6"},Dt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},St={class:"bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4"},zt={class:"flex items-center"},Rt={class:"ml-3"},jt={class:"text-2xl font-semibold text-primary-900 dark:text-primary-100"},It={class:"bg-green-50 rounded-lg p-4"},Ct={class:"flex items-center"},Et={class:"ml-3"},At={class:"text-2xl font-semibold text-green-900"},Ft={class:"text-xs text-green-700"},Tt={class:"bg-orange-50 rounded-lg p-4"},Pt={class:"flex items-center"},Vt={class:"ml-3"},Mt={class:"text-2xl font-semibold text-orange-900"},$t={class:"bg-purple-50 rounded-lg p-4"},Nt={class:"flex items-center"},Bt={class:"ml-3"},Lt={class:"text-2xl font-semibold text-purple-900"},Ot={class:"text-xs text-purple-700"},Qt={key:2,class:"bg-white rounded-lg shadow-sm border p-6 mb-6"},Ut={key:0,class:"mb-6"},Yt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},qt={class:"flex items-center justify-between"},Gt={class:"font-medium text-gray-900"},Ht={class:"text-sm text-gray-600"},Xt={class:"text-right"},Wt={class:"text-lg font-semibold text-gray-900"},Jt={class:"text-sm text-gray-600"},Kt={class:"overflow-x-auto"},Zt={class:"min-w-full divide-y divide-gray-200"},te={class:"bg-white divide-y divide-gray-200"},ee={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},se={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ae={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},oe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ie={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},le={class:"px-6 py-4 text-sm text-gray-900"},ne={class:"font-medium"},re={class:"text-gray-600 text-xs"},de={key:0,class:"mt-4 text-center"},ue={class:"text-sm text-gray-600"},pe={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},ce={class:"bg-white rounded-lg shadow-sm border p-6"},me={class:"flex items-center"},xe={class:"flex-shrink-0"},ge={class:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center"},ve={class:"ml-4"},_e={class:"text-sm font-medium text-gray-600"},ye={class:"text-2xl font-semibold text-gray-900"},fe={key:0,class:"text-xs text-gray-500"},he={class:"bg-white rounded-lg shadow-sm border p-6"},be={class:"flex items-center"},we={class:"flex-shrink-0"},ke={class:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center"},De={class:"ml-4"},Se={class:"text-2xl font-semibold text-gray-900"},ze={class:"bg-white rounded-lg shadow-sm border p-6"},Re={class:"flex items-center"},je={class:"flex-shrink-0"},Ie={class:"w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center"},Ce={class:"ml-4"},Ee={class:"text-2xl font-semibold text-gray-900"},Ae={class:"bg-white rounded-lg shadow-sm border p-6"},Fe={class:"flex items-center"},Te={class:"flex-shrink-0"},Pe={class:"w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center"},Ve={class:"ml-4"},Me={class:"text-2xl font-semibold text-gray-900"},$e={class:"bg-white rounded-lg shadow-sm border"},Ne={key:0,class:"flex justify-center items-center py-12"},Be={key:1,class:"text-center py-12"},Le={class:"text-gray-600 mb-4"},Oe={key:2,class:"overflow-x-auto"},Qe={class:"min-w-full divide-y divide-gray-200"},Ue={class:"bg-white divide-y divide-gray-200"},Ye={class:"px-6 py-4 whitespace-nowrap"},qe={class:"text-sm font-medium text-gray-900"},Ge={key:0,class:"text-sm text-gray-500"},He={class:"px-6 py-4 whitespace-nowrap"},Xe={class:"text-sm text-gray-900"},We={class:"text-sm text-gray-500"},Je={class:"px-6 py-4 whitespace-nowrap"},Ke={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 capitalize"},Ze={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ts={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},es={class:"px-6 py-4 whitespace-nowrap"},ss={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},as={key:3,class:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6"},os={class:"flex items-center justify-between"},is={class:"flex-1 flex justify-between sm:hidden"},ls=["disabled"],ns=["disabled"],rs={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},ds={class:"text-sm text-gray-700"},us={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},ps=["disabled"],cs=["onClick"],ms=["disabled"],xs={key:4,class:"bg-white rounded-lg shadow-sm border"},gs={class:"px-6 py-4 border-b border-gray-200"},vs={class:"text-sm text-gray-600 mt-1"},_s={class:"p-6"},ys={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6"},fs={class:"bg-blue-50 rounded-lg p-4 text-center"},hs={class:"text-2xl font-bold text-blue-900"},bs={class:"bg-green-50 rounded-lg p-4 text-center"},ws={class:"text-2xl font-bold text-green-900"},ks={class:"bg-purple-50 rounded-lg p-4 text-center"},Ds={class:"text-2xl font-bold text-purple-900"},Ss={key:0},zs={class:"overflow-x-auto"},Rs={class:"min-w-full divide-y divide-gray-200"},js={class:"bg-white divide-y divide-gray-200"},Is={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},Cs={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-600"},Es={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},As={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},Fs={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-600"},Ts={class:"mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg"},Ps={class:"flex"},T=10,Vs={__name:"FundingReporting",setup(Ms){const L=mt(),q=ct(),k=gt(),{formatDate:U,formatCurrency:g}=vt(),R=j(!1),O=j(!1),v=j(1),r=j(null),_=j(null),u=j({applicationId:"",status:"",category:"",dateRange:""}),P=j([]),F=j([]),G=D(()=>_.value?`Rendicontazione: ${_.value.project_title}`:"Rendicontazione Finanziaria"),H=D(()=>_.value?"Dati completi di bando, progetto e consuntivazione":"Gestisci le spese dei progetti e i report di finanziamento"),f=D(()=>{let o=[...P.value];if(u.value.applicationId&&(o=o.filter(e=>e.application_id===parseInt(u.value.applicationId))),u.value.status&&(o=o.filter(e=>e.approval_status===u.value.status)),u.value.category&&(o=o.filter(e=>e.category===u.value.category)),u.value.dateRange){const e=new Date,p=tt(u.value.dateRange,e);o=o.filter(h=>{const i=new Date(h.expense_date);return i>=p&&i<=e})}return o.sort((e,p)=>new Date(p.expense_date)-new Date(e.expense_date))}),X=D(()=>u.value.applicationId||u.value.status||u.value.category||u.value.dateRange),V=D(()=>Math.ceil(f.value.length/T)),W=D(()=>(v.value-1)*T+1),J=D(()=>Math.min(v.value*T,f.value.length)),K=D(()=>{const o=(v.value-1)*T,e=o+T;return f.value.slice(o,e)}),Z=D(()=>{const o=V.value,e=v.value,p=2,h=[],i=[];for(let m=Math.max(2,e-p);m<=Math.min(o-1,e+p);m++)h.push(m);return e-p>2?i.push(1,"..."):i.push(1),i.push(...h),e+p<o-1?i.push("...",o):o>1&&i.push(o),i.filter(m=>m!=="..."||i.length>3)}),S=D(()=>{const o=f.value.reduce((i,m)=>i+(m.amount||0),0),e=f.value.filter(i=>i.approval_status==="approved").reduce((i,m)=>i+(m.amount||0),0),p=f.value.filter(i=>i.approval_status==="pending").reduce((i,m)=>i+(m.amount||0),0),h=f.value.filter(i=>i.approval_status==="rejected").reduce((i,m)=>i+(m.amount||0),0);return{totalExpenses:o,approvedExpenses:e,pendingExpenses:p,rejectedExpenses:h}});function tt(o,e){switch(o){case"thisMonth":return new Date(e.getFullYear(),e.getMonth(),1);case"lastMonth":return new Date(e.getFullYear(),e.getMonth()-1,1);case"thisQuarter":const p=Math.floor(e.getMonth()/3);return new Date(e.getFullYear(),p*3,1);case"thisYear":return new Date(e.getFullYear(),0,1);default:return new Date(0)}}async function et(){R.value=!0;try{const o=q.query.applicationId;o?await st(parseInt(o)):(await Promise.all([k.fetchExpenses(),k.fetchApplications()]),P.value=k.expenses||[],F.value=k.applications||[])}catch(o){console.error("Failed to load funding data:",o)}finally{R.value=!1}}async function st(o){try{r.value=await k.fetchReportingData(o),_.value=r.value.application,P.value=r.value.expenses||[]}catch(e){console.error("Failed to load reporting data:",e),await Promise.all([k.fetchExpenses(),k.fetchApplications()]),P.value=k.expenses||[],F.value=k.applications||[]}}function Y(){L.push("/app/funding/expenses/new")}function at(o){L.push(`/app/funding/expenses/${o.id}`)}function ot(o){L.push(`/app/funding/expenses/${o.id}/edit`)}async function it(){if(_.value)try{R.value=!0,await k.exportReport(_.value.id)}catch(o){console.error("Failed to export application report:",o)}finally{R.value=!1}}async function lt(){try{R.value=!0;const o={expenses:f.value,applications:F.value,summary:S.value,filters:u.value,generated_at:new Date().toISOString()},e=await dt(()=>import("https://unpkg.com/xlsx/xlsx.mjs"),[]),p=e.utils.book_new(),h=[["Riepilogo Spese di Finanziamento",""],["Data Generazione",new Date().toLocaleDateString("it-IT")],["",""],["Spese Totali",`€ ${S.value.totalExpenses.toLocaleString("it-IT",{minimumFractionDigits:2})}`],["Spese Approvate",`€ ${S.value.approvedExpenses.toLocaleString("it-IT",{minimumFractionDigits:2})}`],["Spese in Attesa",`€ ${S.value.pendingExpenses.toLocaleString("it-IT",{minimumFractionDigits:2})}`],["Spese Rifiutate",`€ ${S.value.rejectedExpenses.toLocaleString("it-IT",{minimumFractionDigits:2})}`]],i=e.utils.aoa_to_sheet(h);e.utils.book_append_sheet(p,i,"Riepilogo");const m=[["Data","Candidatura","Descrizione","Categoria","Importo (€)","Stato Approvazione","Note"]];f.value.forEach(d=>{var I;m.push([new Date(d.expense_date).toLocaleDateString("it-IT"),((I=d.application)==null?void 0:I.project_title)||"N/A",d.description,d.category||"Altro",d.amount,d.approval_status==="approved"?"Approvato":d.approval_status==="pending"?"In Attesa":"Rifiutato",d.notes||""])});const M=e.utils.aoa_to_sheet(m);e.utils.book_append_sheet(p,M,"Dettaglio Spese");const s=[["ID","Titolo Progetto","Bando","Importo Richiesto (€)","Stato","Data Candidatura"]];F.value.forEach(d=>{var I;s.push([d.id,d.project_title,((I=d.opportunity)==null?void 0:I.title)||"N/A",d.requested_amount||0,d.status==="draft"?"Bozza":d.status==="submitted"?"Inviata":d.status==="approved"?"Approvata":d.status==="rejected"?"Rifiutata":d.status,d.submission_date?new Date(d.submission_date).toLocaleDateString("it-IT"):"Non inviata"])});const b=e.utils.aoa_to_sheet(s);e.utils.book_append_sheet(p,b,"Candidature");const $=`Spese_Funding_${new Date().toISOString().split("T")[0]}.xlsx`;e.writeFile(p,$)}catch(o){console.error("Failed to export global report:",o)}finally{R.value=!1}}return ut(u,()=>{v.value=1},{deep:!0}),pt(()=>{et()}),(o,e)=>{var p,h,i,m,M;return l(),n(C,null,[c(_t,{title:G.value,subtitle:H.value},{actions:z(()=>[_.value?y("",!0):(l(),Q(N,{key:0,onClick:e[0]||(e[0]=s=>O.value=!O.value),variant:"secondary",icon:"adjustments-horizontal"},{default:z(()=>e[9]||(e[9]=[E(" Filtri ")])),_:1,__:[9]})),_.value?(l(),Q(N,{key:1,onClick:it,variant:"secondary",icon:"document-arrow-down",disabled:R.value},{default:z(()=>e[10]||(e[10]=[E(" Esporta Report ")])),_:1,__:[10]},8,["disabled"])):y("",!0),_.value?y("",!0):(l(),Q(N,{key:2,onClick:lt,variant:"secondary",icon:"document-text"},{default:z(()=>e[11]||(e[11]=[E(" Esporta Spese ")])),_:1,__:[11]})),c(N,{onClick:Y,variant:"primary",icon:"plus"},{default:z(()=>e[12]||(e[12]=[E(" Nuova Spesa ")])),_:1,__:[12]})]),_:1},8,["title","subtitle"]),O.value?(l(),n("div",ht,[t("div",bt,[t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Candidatura",-1)),c(B,{modelValue:u.value.applicationId,"onUpdate:modelValue":e[1]||(e[1]=s=>u.value.applicationId=s),type:"select"},{default:z(()=>[e[13]||(e[13]=t("option",{value:""},"Tutte le Candidature",-1)),(l(!0),n(C,null,A(F.value,s=>(l(),n("option",{key:s.id,value:s.id},a(s.project_title),9,wt))),128))]),_:1,__:[13]},8,["modelValue"])]),t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),c(B,{modelValue:u.value.status,"onUpdate:modelValue":e[2]||(e[2]=s=>u.value.status=s),type:"select"},{default:z(()=>e[15]||(e[15]=[t("option",{value:""},"Tutti gli Stati",-1),t("option",{value:"pending"},"In Attesa",-1),t("option",{value:"approved"},"Approvata",-1),t("option",{value:"rejected"},"Rifiutata",-1)])),_:1,__:[15]},8,["modelValue"])]),t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Categoria",-1)),c(B,{modelValue:u.value.category,"onUpdate:modelValue":e[3]||(e[3]=s=>u.value.category=s),type:"select"},{default:z(()=>e[17]||(e[17]=[t("option",{value:""},"Tutte le Categorie",-1),t("option",{value:"personnel"},"Personale",-1),t("option",{value:"equipment"},"Attrezzature",-1),t("option",{value:"travel"},"Viaggi",-1),t("option",{value:"external"},"Servizi Esterni",-1),t("option",{value:"other"},"Altro",-1)])),_:1,__:[17]},8,["modelValue"])]),t("div",null,[e[20]||(e[20]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Periodo",-1)),c(B,{modelValue:u.value.dateRange,"onUpdate:modelValue":e[4]||(e[4]=s=>u.value.dateRange=s),type:"select"},{default:z(()=>e[19]||(e[19]=[t("option",{value:""},"Tutto il Periodo",-1),t("option",{value:"thisMonth"},"Questo Mese",-1),t("option",{value:"lastMonth"},"Mese Scorso",-1),t("option",{value:"thisQuarter"},"Questo Trimestre",-1),t("option",{value:"thisYear"},"Quest'Anno",-1)])),_:1,__:[19]},8,["modelValue"])])])])):y("",!0),_.value&&r.value?(l(),n("div",kt,[e[25]||(e[25]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," 🎯 Riepilogo Finanziario Progetto ",-1)),t("div",Dt,[t("div",St,[t("div",zt,[c(w,{name:"currency-euro",class:"text-primary-600 dark:text-primary-400",size:"sm"}),t("div",Rt,[e[21]||(e[21]=t("p",{class:"text-sm font-medium text-primary-600 dark:text-primary-400"},"Budget Totale",-1)),t("p",jt,a(x(g)(r.value.financial_summary.total_project_budget)),1)])])]),t("div",It,[t("div",Ct,[c(w,{name:"users",class:"text-green-600",size:"sm"}),t("div",Et,[e[22]||(e[22]=t("p",{class:"text-sm font-medium text-green-600"},"Costi Personale",-1)),t("p",At,a(x(g)(r.value.financial_summary.personnel_costs)),1),t("p",Ft,a(r.value.project_costs.total_hours)+"h totali ",1)])])]),t("div",Tt,[t("div",Pt,[c(w,{name:"receipt-percent",class:"text-orange-600",size:"sm"}),t("div",Vt,[e[23]||(e[23]=t("p",{class:"text-sm font-medium text-orange-600"},"Spese Dirette",-1)),t("p",Mt,a(x(g)(r.value.financial_summary.direct_expenses)),1)])])]),t("div",$t,[t("div",Nt,[c(w,{name:"chart-pie",class:"text-purple-600",size:"sm"}),t("div",Bt,[e[24]||(e[24]=t("p",{class:"text-sm font-medium text-purple-600"},"Utilizzo Budget",-1)),t("p",Lt,a(r.value.financial_summary.funding_utilization_percentage.toFixed(1))+"% ",1),t("p",Ot,a(x(g)(r.value.financial_summary.remaining_budget))+" residuo ",1)])])])])])):y("",!0),_.value&&((h=(p=r.value)==null?void 0:p.timesheet_entries)==null?void 0:h.length)>0?(l(),n("div",Qt,[e[30]||(e[30]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"}," ⏱️ Consuntivazione Ore del Progetto ",-1)),r.value.project_costs.personnel_breakdown?(l(),n("div",Ut,[e[26]||(e[26]=t("h4",{class:"text-md font-medium text-gray-700 mb-3"},"Riepilogo per Risorsa",-1)),t("div",Yt,[(l(!0),n(C,null,A(r.value.project_costs.personnel_breakdown,(s,b)=>(l(),n("div",{key:b,class:"bg-gray-50 rounded-lg p-4"},[t("div",qt,[t("div",null,[t("p",Gt,a(b),1),t("p",Ht,a(s.role),1)]),t("div",Xt,[t("p",Wt,a(s.hours)+"h",1),t("p",Jt,a(x(g)(s.cost)),1)])])]))),128))])])):y("",!0),t("div",Kt,[t("table",Zt,[e[27]||(e[27]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Data"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Risorsa"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Ore"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Tariffa"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Costo"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Attività")])],-1)),t("tbody",te,[(l(!0),n(C,null,A(r.value.timesheet_entries.slice(0,20),s=>(l(),n("tr",{key:s.id},[t("td",ee,a(x(U)(s.date)),1),t("td",se,a(s.user_name),1),t("td",ae,a(s.hours)+"h ",1),t("td",oe,a(x(g)(s.hourly_rate))+"/h ",1),t("td",ie,a(x(g)(s.cost)),1),t("td",le,[t("div",null,[t("p",ne,a(s.task_name||"Attività generica"),1),t("p",re,a(s.description),1)])])]))),128))])]),r.value.timesheet_entries.length>20?(l(),n("div",de,[t("p",ue,[E(" Mostrando prime 20 righe di "+a(r.value.timesheet_entries.length)+" totali. ",1),e[28]||(e[28]=t("br",null,null,-1)),e[29]||(e[29]=t("span",{class:"font-medium"},'Usa "Esporta Report" per il dettaglio completo.',-1))])])):y("",!0)])])):y("",!0),t("div",pe,[t("div",ce,[t("div",me,[t("div",xe,[t("div",ge,[c(w,{name:"currency-euro",size:"sm",class:"text-blue-600"})])]),t("div",ve,[t("p",_e,a(_.value?"Spese Dirette":"Spese Totali"),1),t("p",ye,a(x(g)(S.value.totalExpenses)),1),_.value&&r.value?(l(),n("p",fe," + "+a(x(g)(r.value.financial_summary.personnel_costs))+" costi personale ",1)):y("",!0)])])]),t("div",he,[t("div",be,[t("div",we,[t("div",ke,[c(w,{name:"check-circle",size:"sm",class:"text-green-600"})])]),t("div",De,[e[31]||(e[31]=t("p",{class:"text-sm font-medium text-gray-600"},"Approvate",-1)),t("p",Se,a(x(g)(S.value.approvedExpenses)),1)])])]),t("div",ze,[t("div",Re,[t("div",je,[t("div",Ie,[c(w,{name:"clock",size:"sm",class:"text-yellow-600"})])]),t("div",Ce,[e[32]||(e[32]=t("p",{class:"text-sm font-medium text-gray-600"},"In Attesa",-1)),t("p",Ee,a(x(g)(S.value.pendingExpenses)),1)])])]),t("div",Ae,[t("div",Fe,[t("div",Te,[t("div",Pe,[c(w,{name:"exclamation-triangle",size:"sm",class:"text-red-600"})])]),t("div",Ve,[e[33]||(e[33]=t("p",{class:"text-sm font-medium text-gray-600"},"Rifiutate",-1)),t("p",Me,a(x(g)(S.value.rejectedExpenses)),1)])])])]),t("div",$e,[e[44]||(e[44]=t("div",{class:"px-6 py-4 border-b border-gray-200"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Spese di Finanziamento")],-1)),R.value?(l(),n("div",Ne,[c(rt)])):f.value.length===0?(l(),n("div",Be,[c(w,{name:"presentation-chart-bar",size:"xl",class:"text-gray-400 mx-auto mb-4"}),e[35]||(e[35]=t("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Nessuna Spesa Trovata",-1)),t("p",Le,a(X.value?"Nessuna spesa corrisponde ai filtri attuali.":"Inizia aggiungendo la tua prima spesa di finanziamento."),1),t("button",{onClick:Y,class:"btn-primary"},[c(w,{name:"plus",size:"sm"}),e[34]||(e[34]=E(" Aggiungi Prima Spesa "))])])):(l(),n("div",Oe,[t("table",Qe,[e[36]||(e[36]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Descrizione "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Candidatura "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Categoria "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Importo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Data "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Stato "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," Azioni ")])],-1)),t("tbody",Ue,[(l(!0),n(C,null,A(K.value,s=>{var b,$,d;return l(),n("tr",{key:s.id,class:"hover:bg-gray-50"},[t("td",Ye,[t("div",null,[t("div",qe,a(s.description),1),s.notes?(l(),n("div",Ge,a(s.notes),1)):y("",!0)])]),t("td",He,[t("div",Xe,a(((b=s.application)==null?void 0:b.project_title)||"N/A"),1),t("div",We,a(((d=($=s.application)==null?void 0:$.opportunity)==null?void 0:d.source_entity)||""),1)]),t("td",Je,[t("span",Ke,a(s.category||"other"),1)]),t("td",Ze,a(x(g)(s.amount)),1),t("td",ts,a(x(U)(s.expense_date)),1),t("td",es,[c(yt,{status:s.approval_status},null,8,["status"])]),t("td",ss,[c(ft,{"show-delete":!1,"show-edit":s.approval_status==="pending",onView:I=>at(s),onEdit:I=>ot(s)},null,8,["show-edit","onView","onEdit"])])])}),128))])])])),V.value>1?(l(),n("div",as,[t("div",os,[t("div",is,[t("button",{onClick:e[5]||(e[5]=s=>v.value--),disabled:v.value===1,class:"btn-pagination"}," Previous ",8,ls),t("button",{onClick:e[6]||(e[6]=s=>v.value++),disabled:v.value===V.value,class:"btn-pagination"}," Next ",8,ns)]),t("div",rs,[t("div",null,[t("p",ds," Showing "+a(W.value)+" to "+a(J.value)+" of "+a(f.value.length)+" results ",1)]),t("div",null,[t("nav",us,[t("button",{onClick:e[7]||(e[7]=s=>v.value--),disabled:v.value===1,class:"btn-pagination-nav"}," Previous ",8,ps),(l(!0),n(C,null,A(Z.value,s=>(l(),n("button",{key:s,onClick:b=>v.value=s,class:xt(s===v.value?"btn-pagination-active":"btn-pagination-nav")},a(s),11,cs))),128)),t("button",{onClick:e[8]||(e[8]=s=>v.value++),disabled:v.value===V.value,class:"btn-pagination-nav"}," Next ",8,ms)])])])])])):y("",!0),_.value&&((m=(i=r.value)==null?void 0:i.project_costs)!=null&&m.total_hours)?(l(),n("div",xs,[t("div",gs,[e[37]||(e[37]=t("h3",{class:"text-lg font-medium text-gray-900"}," 📊 Costi Aggregati Progetto Collegato ",-1)),t("p",vs,' Riepilogo costi del personale e risorse dal progetto "'+a(((M=r.value.project)==null?void 0:M.name)||"Collegato")+'" ',1)]),t("div",_s,[t("div",ys,[t("div",fs,[t("div",hs,a(r.value.project_costs.total_hours)+"h ",1),e[38]||(e[38]=t("div",{class:"text-sm text-blue-700"},"Ore Totali",-1))]),t("div",bs,[t("div",ws,a(x(g)(r.value.project_costs.total_personnel_cost)),1),e[39]||(e[39]=t("div",{class:"text-sm text-green-700"},"Costo Personale",-1))]),t("div",ks,[t("div",Ds,a(x(g)(r.value.project_costs.average_hourly_rate))+"/h ",1),e[40]||(e[40]=t("div",{class:"text-sm text-purple-700"},"Tariffa Media",-1))])]),Object.keys(r.value.project_costs.personnel_breakdown).length>0?(l(),n("div",Ss,[e[42]||(e[42]=t("h4",{class:"text-md font-medium text-gray-700 mb-3"},"Dettaglio per Risorsa",-1)),t("div",zs,[t("table",Rs,[e[41]||(e[41]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Risorsa "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Ruolo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Ore "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Costo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Tariffa Media ")])],-1)),t("tbody",js,[(l(!0),n(C,null,A(r.value.project_costs.personnel_breakdown,(s,b)=>(l(),n("tr",{key:b},[t("td",Is,a(b),1),t("td",Cs,a(s.role),1),t("td",Es,a(s.hours)+"h ",1),t("td",As,a(x(g)(s.cost)),1),t("td",Fs,a(x(g)(s.hours>0?s.cost/s.hours:0))+"/h ",1)]))),128))])])])])):y("",!0),t("div",Ts,[t("div",Ps,[c(w,{name:"information-circle",size:"sm",class:"text-amber-400 mr-2 mt-0.5"}),e[43]||(e[43]=t("div",{class:"text-sm text-amber-700"},[t("p",null,[t("strong",null,"Nota:"),E(" Questi costi sono calcolati automaticamente dal timesheet del progetto collegato.")]),t("p",{class:"mt-1"},'Per il report completo utilizzare il pulsante "Esporta Report" che include anche:'),t("ul",{class:"list-disc list-inside mt-1 space-y-1"},[t("li",null,"Dettaglio ore per data e attività"),t("li",null,"Spese dirette della candidatura"),t("li",null,"Riepilogo finanziario completo")])],-1))])])])])):y("",!0)])],64)}}},Xs=nt(Vs,[["__scopeId","data-v-633a9634"]]);export{Xs as default};
