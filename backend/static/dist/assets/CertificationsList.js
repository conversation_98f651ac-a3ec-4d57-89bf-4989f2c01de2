import{u as A}from"./certifications.js";import{_ as H,H as N}from"./app.js";import{A as T}from"./ActionButtonGroup.js";import{S as j}from"./StandardButton.js";import{S as F}from"./StandardInput.js";import{b as d,j as e,l as w,e as n,k as u,f as b,t as l,F as L,p as G,c as C,r as h,x as R,o as c,s as p,n as v,E as U}from"./vendor.js";/* empty css                                                             */const M={name:"CertificationsList",components:{HeroIcon:N,ActionButtonGroup:T,StandardButton:j,StandardInput:F},setup(){const r=A(),t=C(()=>r.certifications),S=C(()=>r.loading),i=C(()=>r.error),k=h({status:"",category:"",expiring_soon:"",responsible_person_id:""}),y=h(!1),s=h(null),m=h(!1),g=async()=>{try{await r.fetchCertifications(k.value)}catch(o){console.error("Errore nel caricamento certificazioni:",o)}},x=()=>{g()},_=o=>o?new Date(o).toLocaleDateString("it-IT"):"N/A",a=o=>({active:"bg-green-100 text-green-800",expired:"bg-red-100 text-red-800",in_renewal:"bg-yellow-100 text-yellow-800",suspended:"bg-gray-100 text-gray-800"})[o]||"bg-gray-100 text-gray-800",f=o=>({active:"Attiva",expired:"Scaduta",in_renewal:"In Rinnovo",suspended:"Sospesa"})[o]||o,z=o=>o>=80?"text-green-600 dark:text-green-400":o>=60?"text-yellow-600 dark:text-yellow-400":"text-red-600 dark:text-red-400",V=o=>o>=80?"bg-green-500 dark:bg-green-400":o>=60?"bg-yellow-500 dark:bg-yellow-400":"bg-red-500 dark:bg-red-400",D=o=>{s.value=o,y.value=!0},B=()=>{y.value=!1,s.value=null},E=async()=>{if(s.value)try{m.value=!0,await r.deleteCertification(s.value.id),y.value=!1,s.value=null}catch(o){console.error("Errore nell'eliminazione:",o)}finally{m.value=!1}};return R(()=>{g()}),{certifications:t,loading:S,error:i,filters:k,showDeleteModal:y,certificationToDelete:s,deleting:m,loadCertifications:g,applyFilters:x,formatDate:_,getStatusBadgeClass:a,getStatusLabel:f,getHealthScoreColor:z,getHealthScoreBarColor:V,confirmDelete:D,cancelDelete:B,deleteCertification:E}}},Q={class:"certifications-list"},q={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6"},P={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6"},J={class:"mt-4 lg:mt-0"},K={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},O={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"},W={key:0,class:"flex justify-center py-12"},X={key:1,class:"p-6 text-center text-red-600 dark:text-red-400"},Y={key:2,class:"p-12 text-center text-gray-500 dark:text-gray-400"},Z={key:3},$={class:"divide-y divide-gray-200 dark:divide-gray-700"},ee={class:"hidden md:grid md:grid-cols-6 gap-4 items-center"},te={class:"font-semibold text-gray-900 dark:text-white"},ae={class:"text-sm text-gray-600 dark:text-gray-400"},ie={class:"text-gray-900 dark:text-white"},oe={key:0,class:"flex items-center mt-1 text-orange-600 dark:text-orange-400"},ne={class:"text-gray-900 dark:text-white"},le={key:0,class:"text-sm text-gray-600 dark:text-gray-400"},se={class:"text-center"},re={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1"},de={class:"md:hidden"},ce={class:"flex justify-between items-start mb-3"},ge={class:"flex-1"},ue={class:"font-semibold text-gray-900"},me={class:"text-sm text-gray-600"},fe={class:"grid grid-cols-2 gap-4 text-sm"},ye={class:"block font-medium"},pe={class:"flex justify-end mt-4"},ve={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},xe={class:"bg-white dark:bg-gray-800 rounded-lg max-w-md w-full mx-4 p-6"},_e={class:"flex items-center mb-4"},be={class:"text-gray-600 dark:text-gray-400 mb-6"},he={class:"flex justify-end space-x-3"};function ke(r,t,S,i,k,y){var _;const s=b("StandardButton"),m=b("StandardInput"),g=b("HeroIcon"),x=b("ActionButtonGroup");return c(),d("div",Q,[e("div",q,[e("div",P,[t[5]||(t[5]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Certificazioni"),e("p",{class:"text-gray-600 dark:text-gray-400 mt-1"},"Gestisci le certificazioni aziendali")],-1)),e("div",J,[n(s,{variant:"primary",icon:"plus",to:"/app/certifications/create"},{default:u(()=>t[4]||(t[4]=[p(" Nuova Certificazione ")])),_:1,__:[4]})])]),e("div",K,[e("div",null,[n(m,{modelValue:i.filters.status,"onUpdate:modelValue":t[0]||(t[0]=a=>i.filters.status=a),type:"select",label:"Stato",onChange:i.applyFilters},{default:u(()=>t[6]||(t[6]=[e("option",{value:""},"Tutti",-1),e("option",{value:"active"},"Attive",-1),e("option",{value:"expired"},"Scadute",-1),e("option",{value:"in_renewal"},"In Rinnovo",-1),e("option",{value:"suspended"},"Sospese",-1)])),_:1,__:[6]},8,["modelValue","onChange"])]),e("div",null,[n(m,{modelValue:i.filters.category,"onUpdate:modelValue":t[1]||(t[1]=a=>i.filters.category=a),type:"select",label:"Categoria",onChange:i.applyFilters},{default:u(()=>t[7]||(t[7]=[e("option",{value:""},"Tutte",-1),e("option",{value:"quality"},"Qualità",-1),e("option",{value:"security"},"Sicurezza",-1),e("option",{value:"environmental"},"Ambientale",-1),e("option",{value:"privacy"},"Privacy",-1)])),_:1,__:[7]},8,["modelValue","onChange"])]),e("div",null,[n(m,{modelValue:i.filters.expiring_soon,"onUpdate:modelValue":t[2]||(t[2]=a=>i.filters.expiring_soon=a),type:"select",label:"In scadenza",onChange:i.applyFilters},{default:u(()=>t[8]||(t[8]=[e("option",{value:""},"Tutte",-1),e("option",{value:"true"},"Solo in scadenza",-1),e("option",{value:"false"},"Non in scadenza",-1)])),_:1,__:[8]},8,["modelValue","onChange"])]),e("div",null,[n(m,{modelValue:i.filters.responsible_person_id,"onUpdate:modelValue":t[3]||(t[3]=a=>i.filters.responsible_person_id=a),type:"select",label:"Responsabile",onChange:i.applyFilters},{default:u(()=>t[9]||(t[9]=[e("option",{value:""},"Tutti",-1)])),_:1,__:[9]},8,["modelValue","onChange"])])])]),e("div",O,[i.loading?(c(),d("div",W,[n(g,{name:"arrow-path",class:"h-8 w-8 text-primary-600 dark:text-primary-400 animate-spin"})])):i.error?(c(),d("div",X,[n(g,{name:"exclamation-triangle",class:"h-8 w-8 mx-auto mb-2"}),e("p",null,l(i.error),1),n(s,{onClick:i.loadCertifications,variant:"primary",class:"mt-3"},{default:u(()=>t[10]||(t[10]=[p(" Riprova ")])),_:1,__:[10]},8,["onClick"])])):i.certifications.length===0?(c(),d("div",Y,[n(g,{name:"security",class:"h-12 w-12 mx-auto mb-4 text-gray-300 dark:text-gray-600"}),t[12]||(t[12]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessuna certificazione trovata",-1)),t[13]||(t[13]=e("p",{class:"text-gray-600 dark:text-gray-400 mb-6"},"Inizia aggiungendo la prima certificazione aziendale",-1)),n(s,{variant:"primary",icon:"plus",to:"/app/certifications/create"},{default:u(()=>t[11]||(t[11]=[p(" Nuova Certificazione ")])),_:1,__:[11]})])):(c(),d("div",Z,[t[17]||(t[17]=e("div",{class:"hidden md:grid md:grid-cols-6 gap-4 px-6 py-4 bg-gray-50 dark:bg-gray-700/50 border-b border-gray-200 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300"},[e("div",null,"Certificazione"),e("div",null,"Ente Certificatore"),e("div",null,"Stato"),e("div",null,"Scadenza"),e("div",null,"Health Score"),e("div",null,"Azioni")],-1)),e("div",$,[(c(!0),d(L,null,G(i.certifications,a=>(c(),d("div",{key:a.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"},[e("div",ee,[e("div",null,[e("div",te,l(a.standard_name),1),e("div",ae,l(a.certificate_number||"N/A"),1)]),e("div",ie,l(a.certifying_body),1),e("div",null,[e("span",{class:v([i.getStatusBadgeClass(a.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},l(i.getStatusLabel(a.status)),3),a.is_expiring_soon?(c(),d("div",oe,[n(g,{name:"exclamation-triangle",class:"h-4 w-4 mr-1"}),t[14]||(t[14]=e("span",{class:"text-xs"},"In scadenza",-1))])):w("",!0)]),e("div",null,[e("div",ne,l(i.formatDate(a.expiry_date)),1),a.days_to_expiry!==null?(c(),d("div",le,l(a.days_to_expiry)+" giorni ",1)):w("",!0)]),e("div",se,[e("div",{class:v(["text-lg font-semibold",i.getHealthScoreColor(a.health_score)])},l(a.health_score)+"% ",3),e("div",re,[e("div",{class:v(["h-2 rounded-full transition-all duration-300",i.getHealthScoreBarColor(a.health_score)]),style:U({width:a.health_score+"%"})},null,6)])]),n(x,{onView:f=>r.$router.push(`/app/certifications/${a.id}`),onEdit:f=>r.$router.push(`/app/certifications/${a.id}/edit`),onDelete:f=>i.confirmDelete(a),"delete-message":`Sei sicuro di voler eliminare la certificazione '${a.title}'?`},null,8,["onView","onEdit","onDelete","delete-message"])]),e("div",de,[e("div",ce,[e("div",ge,[e("h3",ue,l(a.standard_name),1),e("p",me,l(a.certifying_body),1)]),e("span",{class:v([i.getStatusBadgeClass(a.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},l(i.getStatusLabel(a.status)),3)]),e("div",fe,[e("div",null,[t[15]||(t[15]=e("span",{class:"text-gray-500"},"Scadenza:",-1)),e("span",ye,l(i.formatDate(a.expiry_date)),1)]),e("div",null,[t[16]||(t[16]=e("span",{class:"text-gray-500"},"Health Score:",-1)),e("span",{class:v(["block font-medium",i.getHealthScoreColor(a.health_score)])},l(a.health_score)+"% ",3)])]),e("div",pe,[n(x,{onView:f=>r.$router.push(`/app/certifications/${a.id}`),onEdit:f=>r.$router.push(`/app/certifications/${a.id}/edit`),onDelete:f=>i.confirmDelete(a),"delete-message":`Sei sicuro di voler eliminare la certificazione '${a.title}'?`},null,8,["onView","onEdit","onDelete","delete-message"])])])]))),128))])]))]),i.showDeleteModal?(c(),d("div",ve,[e("div",xe,[e("div",_e,[n(g,{name:"exclamation-triangle",class:"h-8 w-8 text-red-500 mr-3"}),t[18]||(t[18]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Conferma Eliminazione",-1))]),e("p",be,' Sei sicuro di voler eliminare la certificazione "'+l((_=i.certificationToDelete)==null?void 0:_.standard_name)+'"? Questa azione non può essere annullata. ',1),e("div",he,[n(s,{onClick:i.cancelDelete,variant:"secondary"},{default:u(()=>t[19]||(t[19]=[p(" Annulla ")])),_:1,__:[19]},8,["onClick"]),n(s,{onClick:i.deleteCertification,variant:"danger",disabled:i.deleting,loading:i.deleting},{default:u(()=>[p(l(i.deleting?"Eliminazione...":"Elimina"),1)]),_:1},8,["onClick","disabled","loading"])])])])):w("",!0)])}const Ee=H(M,[["render",ke]]);export{Ee as default};
