import{u as x}from"./useFormatters.js";import{H as a}from"./app.js";import{r as _,b as d,j as e,I as f,t as o,e as r,F as b,p as y,o as l,n as h,l as c,v as k}from"./vendor.js";import"./formatters.js";const v={class:"project-files-example"},z={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},w={class:"flex items-center justify-between mb-6"},j={class:"text-sm text-gray-500 dark:text-gray-400"},P={class:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 mb-6 transition-colors hover:border-gray-400"},D={class:"text-center"},C={class:"space-y-3"},T={class:"flex items-center space-x-4"},E={class:"flex-shrink-0"},F={class:"flex-1 min-w-0"},G={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},M={class:"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400 mt-1"},B={key:0},I={key:1,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200"},q={key:0,class:"text-xs text-gray-600 dark:text-gray-400 mt-1"},L={class:"flex items-center space-x-2"},S={class:"text-gray-400 hover:text-brand-primary-600 dark:hover:text-brand-primary-400 transition-colors",title:"Scarica"},V={class:"text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",title:"Modifica"},N={class:"text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors",title:"Elimina"},X={class:"mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},U={class:"space-y-2 text-sm text-gray-600 dark:text-gray-400"},Z={class:"flex items-start"},O={class:"flex items-start"},R={class:"flex items-start"},A={class:"flex items-start"},H={class:"flex items-start"},J={class:"flex items-start"},K={class:"flex items-start"},te={__name:"ProjectFilesExample",setup(Q){const{formatDate:m}=x(),n=_([{id:1,original_filename:"requirements.pdf",file_path:"/uploads/projects/1/requirements.pdf",file_size:2456789,formatted_size:"2.3 MB",file_extension:".pdf",category:"documentation",description:"Documento dei requisiti di progetto",is_public:!0,is_image:!1,is_document:!0,uploader_name:"Marco Rossi",uploaded_at:"2024-01-15T10:30:00Z",project_id:1},{id:2,original_filename:"mockup-design.png",file_path:"/uploads/projects/1/mockup-design.png",file_size:1234567,formatted_size:"1.2 MB",file_extension:".png",category:"media",description:"Mockup interfaccia utente",is_public:!1,is_image:!0,is_document:!1,uploader_name:"Laura Bianchi",uploaded_at:"2024-01-14T14:15:00Z",project_id:1},{id:3,original_filename:"final-deliverable.zip",file_path:"/uploads/projects/1/final-deliverable.zip",file_size:8765432,formatted_size:"8.4 MB",file_extension:".zip",category:"deliverable",description:"Pacchetto finale del progetto",is_public:!0,is_image:!1,is_document:!1,uploader_name:"Giuseppe Verdi",uploaded_at:"2024-01-13T16:45:00Z",project_id:1}]),g=i=>i.is_image?"photo":i.is_document?"document-text":i.file_extension===".zip"||i.file_extension===".rar"||i.file_extension===".7z"?"archive-box":i.file_extension===".pdf"?"document-text":"document",p=i=>i.is_image?"text-green-500":i.is_document?"text-blue-500":i.file_extension===".pdf"?"text-red-500":"text-gray-500",u=i=>({general:"Generale",documentation:"Documentazione",media:"Media",deliverable:"Deliverable"})[i]||i;return(i,t)=>(l(),d("div",v,[t[10]||(t[10]=e("div",{class:"mb-6"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-3"}," ProjectFiles Component Example "),e("p",{class:"text-gray-600 dark:text-gray-400 mb-4"}," Sistema completo di gestione file per progetti con upload drag-and-drop, categorizzazione e controllo accessi. ")],-1)),e("div",z,[e("div",w,[t[0]||(t[0]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"},"File del Progetto",-1)),e("div",j,o(n.value.length)+" file"+o(n.value.length!==1?"s":""),1)]),e("div",P,[e("div",D,[r(a,{name:"cloud-arrow-up",size:"xl",class:"text-gray-400 mx-auto mb-4"}),t[1]||(t[1]=e("div",{class:"mt-4"},[e("span",{class:"mt-2 block text-sm font-medium text-gray-900 dark:text-white"}," Trascina file qui o clicca per selezionare "),e("p",{class:"mt-2 text-xs text-gray-500 dark:text-gray-400"}," PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, PNG, JPG, JPEG, GIF, ZIP, RAR, 7Z, CSV fino a 10MB ")],-1))])]),e("div",C,[(l(!0),d(b,null,y(n.value,s=>(l(),d("div",{key:s.id,class:"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[e("div",T,[e("div",E,[r(a,{name:g(s),size:"lg",class:h(p(s))},null,8,["name","class"])]),e("div",F,[e("h5",G,o(s.original_filename),1),e("div",M,[e("span",null,o(s.formatted_size),1),e("span",null,o(k(m)(s.uploaded_at)),1),s.uploader_name?(l(),d("span",B,"Caricato da "+o(s.uploader_name),1)):c("",!0),s.category&&s.category!=="general"?(l(),d("span",I,o(u(s.category)),1)):c("",!0)]),s.description?(l(),d("p",q,o(s.description),1)):c("",!0)])]),e("div",L,[e("button",S,[r(a,{name:"arrow-down-tray",size:"md"})]),e("button",V,[r(a,{name:"pencil",size:"md"})]),e("button",N,[r(a,{name:"trash",size:"md"})])])]))),128))])]),e("div",X,[t[9]||(t[9]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"}," Caratteristiche del Componente ",-1)),e("ul",U,[e("li",Z,[r(a,{name:"check-circle",size:"sm",class:"text-green-500 mr-2 mt-0.5"}),t[2]||(t[2]=e("span",null,"Upload drag-and-drop con progress bar in tempo reale",-1))]),e("li",O,[r(a,{name:"check-circle",size:"sm",class:"text-green-500 mr-2 mt-0.5"}),t[3]||(t[3]=e("span",null,"Validazione file: dimensione massima 10MB, tipi supportati",-1))]),e("li",R,[r(a,{name:"check-circle",size:"sm",class:"text-green-500 mr-2 mt-0.5"}),t[4]||(t[4]=e("span",null,"Categorizzazione file: Generale, Documentazione, Media, Deliverable",-1))]),e("li",A,[r(a,{name:"check-circle",size:"sm",class:"text-green-500 mr-2 mt-0.5"}),t[5]||(t[5]=e("span",null,"Gestione metadati: descrizione, visibilità clienti",-1))]),e("li",H,[r(a,{name:"check-circle",size:"sm",class:"text-green-500 mr-2 mt-0.5"}),t[6]||(t[6]=e("span",null,"Operazioni CRUD: download, modifica, eliminazione",-1))]),e("li",J,[r(a,{name:"check-circle",size:"sm",class:"text-green-500 mr-2 mt-0.5"}),t[7]||(t[7]=e("span",null,"Icone dinamiche basate su tipo di file",-1))]),e("li",K,[r(a,{name:"check-circle",size:"sm",class:"text-green-500 mr-2 mt-0.5"}),t[8]||(t[8]=e("span",null,"Gestione errori e feedback utente",-1))])])]),t[11]||(t[11]=f(`<div class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"><h4 class="text-md font-medium text-gray-900 dark:text-white mb-4"> Integrazione API </h4><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4"><h5 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Endpoints Backend</h5><ul class="text-xs text-gray-600 dark:text-gray-400 space-y-1"><li><code>GET /api/projects/{id}/files</code> - Lista file</li><li><code>POST /api/projects/{id}/files/upload</code> - Upload file</li><li><code>PUT /api/projects/{id}/files/{file_id}</code> - Aggiorna file</li><li><code>DELETE /api/projects/{id}/files/{file_id}</code> - Elimina file</li><li><code>GET /api/projects/{id}/files/{file_id}/download</code> - Download file</li></ul></div><div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4"><h5 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Modello Database</h5><ul class="text-xs text-gray-600 dark:text-gray-400 space-y-1"><li><code>ProjectFile</code> - Tabella principale</li><li><code>original_filename</code> - Nome originale</li><li><code>file_path</code> - Percorso storage</li><li><code>file_size</code> - Dimensione in bytes</li><li><code>category</code> - Categoria del file</li><li><code>is_public</code> - Visibilità clienti</li></ul></div></div></div><div class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"><h4 class="text-md font-medium text-gray-900 dark:text-white mb-4"> Esempio di Utilizzo </h4><div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 text-sm"><pre class="text-gray-800 dark:text-gray-200"><code>&lt;ProjectFiles
  :project=&quot;selectedProject&quot;
  :loading=&quot;isLoading&quot;
/&gt;</code></pre></div></div>`,2))]))}};export{te as default};
