import{r as b,c as k,x as Z,b as o,h as F,l as i,j as t,e as u,t as n,k as ee,f as te,s as x,n as m,F as R,p as U,u as ae,q as re,o as s,v as se}from"./vendor.js";import{e as oe,a as ne,H as g}from"./app.js";import{u as le}from"./useFormatters.js";import{E as ie}from"./EventEditModal.js";import{C as de}from"./ConfirmationModal.js";import"./formatters.js";const ue={key:0,class:"flex justify-center items-center h-64"},ge={key:1,class:"text-center py-12"},ce={class:"text-gray-500 dark:text-gray-400"},me={key:2,class:"text-center py-12"},be={key:3,class:"space-y-6"},ye={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},xe={class:"flex items-center justify-between mb-6"},ve={class:"flex items-center space-x-4"},pe={class:"text-2xl font-bold text-gray-900 dark:text-white"},ke={class:"flex space-x-3"},fe=["disabled"],_e=["disabled"],he={class:"flex items-center space-x-4 mb-6"},we={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 space-y-6"},Ee={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ce={class:"md:col-span-2"},ze={class:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},Me={class:"text-gray-900 dark:text-white"},Re={class:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},Ne={class:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},Pe={class:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},Ae={class:"text-gray-900 dark:text-white whitespace-pre-wrap"},Ie={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Se={class:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},De={class:"text-gray-900 dark:text-white"},Oe={key:0},Le={class:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},Te={class:"text-gray-900 dark:text-white"},Fe={key:0},Ue={class:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},Be={class:"text-gray-900 dark:text-white"},je={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ve={key:0},$e={class:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},qe={class:"text-gray-900 dark:text-white"},Ge={key:1},He={class:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},We={class:"text-gray-900 dark:text-white"},Je={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Ke={class:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},Qe={key:0},Xe={class:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},Ye={class:"text-gray-900 dark:text-white"},Ze={key:0,class:"text-gray-500 dark:text-gray-400"},et={key:1},tt={class:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},at={class:"text-gray-900 dark:text-white"},rt={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},st={class:"flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},ot={class:"flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},nt={class:"flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},lt={class:"flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},it={key:1},dt={class:"flex flex-wrap gap-2"},ut={class:"grid grid-cols-1 md:grid-cols-2 gap-6 pt-4 border-t dark:border-gray-600"},gt={key:0},ct={class:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},mt={class:"text-gray-900 dark:text-white text-sm"},bt={key:1},yt={class:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border dark:border-gray-600"},xt={class:"text-gray-900 dark:text-white text-sm"},vt={key:1,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},pt={class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},kt={key:0,class:"flex justify-center py-8"},ft={key:1,class:"space-y-3"},_t={class:"flex items-center space-x-3"},ht={class:"w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center"},wt={class:"text-sm font-medium text-gray-900 dark:text-white"},Et={class:"text-xs text-gray-500 dark:text-gray-400"},Ct={class:"text-xs text-gray-500 dark:text-gray-400"},zt={key:2,class:"text-center py-8"},Dt={__name:"EventView",setup(Mt){const B=ae(),j=re(),y=oe(),v=ne(),{formatDate:V}=le(),C=b(!0),f=b(null),a=b(null),z=b([]),M=b(!1),_=b(!1),h=b(!1),w=b(!1),E=b(!1),$=k(()=>{var r;return v.hasPermission("PERMISSION_MANAGE_COMMUNICATION")||a.value&&a.value.created_by_id===((r=v.user)==null?void 0:r.id)}),q=k(()=>{var r;return v.hasPermission("PERMISSION_MANAGE_COMMUNICATION")||a.value&&a.value.created_by_id===((r=v.user)==null?void 0:r.id)}),N=k(()=>v.hasPermission("PERMISSION_MANAGE_COMMUNICATION")),P=k(()=>{var r,e;return((r=a.value)==null?void 0:r.user_registered)||y.userRegistrations[(e=a.value)==null?void 0:e.id]}),G=k(()=>!(!a.value||!a.value.requires_registration||a.value.status!=="published"||a.value.registration_deadline&&new Date(a.value.registration_deadline)<new Date||a.value.max_participants&&(a.value.participants_count||0)>=a.value.max_participants)),A=async()=>{var r,e;try{C.value=!0,f.value=null;const d=B.params.id,l=y.events.find(c=>c.id==d);l?a.value=l:a.value=await y.fetchEvent(d),N.value&&a.value.participants_count>0&&await H()}catch(d){console.error("Error loading event:",d),f.value=((e=(r=d.response)==null?void 0:r.data)==null?void 0:e.message)||"Errore nel caricamento dell'evento"}finally{C.value=!1}},H=async()=>{try{M.value=!0,z.value=await y.fetchEventParticipants(a.value.id)}catch(r){console.error("Error loading participants:",r)}finally{M.value=!1}},W=async()=>{var r,e,d;try{_.value=!0,await y.registerForEvent(a.value.id),a.value.user_registered=!0,a.value.participants_count!==void 0&&(a.value.participants_count+=1),alert("Registrazione completata con successo!")}catch(l){console.error("Error registering for event:",l);let c="Errore nella registrazione all'evento";((r=l.response)==null?void 0:r.status)===400?c=((e=l.response.data)==null?void 0:e.message)||"Registrazione non valida":((d=l.response)==null?void 0:d.status)===500?c="Errore del server. Riprova più tardi.":navigator.onLine||(c="Connessione internet non disponibile"),alert(c)}finally{_.value=!1}},J=async()=>{var r,e,d;try{h.value=!0,await y.unregisterFromEvent(a.value.id),a.value.user_registered=!1,a.value.participants_count!==void 0&&(a.value.participants_count=Math.max(0,a.value.participants_count-1)),alert("Registrazione annullata con successo!")}catch(l){console.error("Error unregistering from event:",l);let c="Errore nell'annullamento della registrazione";((r=l.response)==null?void 0:r.status)===400?c=((e=l.response.data)==null?void 0:e.message)||"Annullamento non valido":((d=l.response)==null?void 0:d.status)===500?c="Errore del server. Riprova più tardi.":navigator.onLine||(c="Connessione internet non disponibile"),alert(c)}finally{h.value=!1}},K=()=>{w.value=!0},Q=()=>{E.value=!0},X=r=>{a.value=r,w.value=!1},Y=async()=>{try{await y.deleteEvent(a.value.id),E.value=!1,j.push("/app/communications/events")}catch(r){console.error("Error deleting event:",r)}},p=r=>r?new Date(r).toLocaleString("it-IT",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"-",I=r=>{const e={draft:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",published:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",cancelled:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"};return e[r]||e.draft},S=r=>({draft:"Bozza",published:"Pubblicato",cancelled:"Annullato",completed:"Completato"})[r]||"Bozza",D=r=>{const e={meeting:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",training:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",social:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",conference:"bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",workshop:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",other:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"};return e[r]||e.other},O=r=>({meeting:"Riunione",training:"Formazione",social:"Evento sociale",conference:"Conferenza",workshop:"Workshop",other:"Altro"})[r]||"Altro",L=r=>{const e={low:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",medium:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",high:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",urgent:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"};return e[r]||e.medium},T=r=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[r]||"Media";return Z(()=>{A()}),(r,e)=>{const d=te("router-link");return s(),o(R,null,[C.value?(s(),o("div",ue,e[3]||(e[3]=[t("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):f.value?(s(),o("div",ge,[u(g,{name:"exclamation-triangle",size:"2xl",class:"mx-auto text-red-400 mb-4"}),e[4]||(e[4]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Errore nel caricamento",-1)),t("p",ce,n(f.value),1),t("button",{onClick:A,class:"mt-4 btn-primary"}," Riprova ")])):a.value?(s(),o("div",be,[t("div",ye,[t("div",xe,[t("div",ve,[t("button",{onClick:e[0]||(e[0]=l=>r.$router.back()),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[u(g,{name:"arrow-left",size:"sm",class:"mr-2"}),e[8]||(e[8]=x(" Indietro "))]),t("h1",pe,n(a.value.title),1)]),t("div",ke,[G.value&&!P.value?(s(),o("button",{key:0,onClick:W,disabled:_.value,class:"btn-primary"},[u(g,{name:"plus",size:"sm",class:"mr-2"}),x(" "+n(_.value?"Registrazione...":"Registrati"),1)],8,fe)):i("",!0),P.value?(s(),o("button",{key:1,onClick:J,disabled:h.value,class:"btn-secondary"},[u(g,{name:"minus",size:"sm",class:"mr-2"}),x(" "+n(h.value?"Annullamento...":"Annulla Registrazione"),1)],8,_e)):i("",!0),$.value?(s(),o("button",{key:2,onClick:K,class:"btn-secondary"},[u(g,{name:"pencil",size:"sm",class:"mr-2"}),e[9]||(e[9]=x(" Modifica "))])):i("",!0),q.value?(s(),o("button",{key:3,onClick:Q,class:"btn-danger"},[u(g,{name:"trash",size:"sm",class:"mr-2"}),e[10]||(e[10]=x(" Elimina "))])):i("",!0)])]),t("div",he,[t("span",{class:m(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",I(a.value.status)])},n(S(a.value.status)),3),t("span",{class:m(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",D(a.value.category)])},n(O(a.value.category)),3),t("span",{class:m(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",L(a.value.priority)])},n(T(a.value.priority)),3)])]),a.value?(s(),o("div",we,[t("div",Ee,[t("div",Ce,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Titolo ",-1)),t("div",ze,[t("p",Me,n(a.value.title),1)])]),t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Categoria ",-1)),t("div",Re,[t("span",{class:m(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",D(a.value.category)])},n(O(a.value.category)),3)])]),t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Priorità ",-1)),t("div",Ne,[t("span",{class:m(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",L(a.value.priority)])},n(T(a.value.priority)),3)])])]),t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Descrizione ",-1)),t("div",Pe,[t("p",Ae,n(a.value.description),1)])]),t("div",Ie,[t("div",null,[e[15]||(e[15]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data e ora inizio ",-1)),t("div",Se,[t("p",De,n(p(a.value.start_date)),1)])]),a.value.end_date?(s(),o("div",Oe,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data e ora fine ",-1)),t("div",Le,[t("p",Te,n(p(a.value.end_date)),1)])])):i("",!0)]),a.value.location?(s(),o("div",Fe,[e[17]||(e[17]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Luogo ",-1)),t("div",Ue,[t("p",Be,n(a.value.location),1)])])):i("",!0),t("div",je,[a.value.max_participants?(s(),o("div",Ve,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Numero massimo partecipanti ",-1)),t("div",$e,[t("p",qe,n(a.value.max_participants),1)])])):i("",!0),a.value.registration_deadline?(s(),o("div",Ge,[e[19]||(e[19]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Scadenza registrazione ",-1)),t("div",He,[t("p",We,n(p(a.value.registration_deadline)),1)])])):i("",!0)]),t("div",Je,[t("div",null,[e[20]||(e[20]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Stato ",-1)),t("div",Ke,[t("span",{class:m(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",I(a.value.status)])},n(S(a.value.status)),3)])]),a.value.participants_count!==void 0?(s(),o("div",Qe,[e[21]||(e[21]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Partecipanti registrati ",-1)),t("div",Xe,[t("p",Ye,[x(n(a.value.participants_count)+" ",1),a.value.max_participants?(s(),o("span",Ze," / "+n(a.value.max_participants),1)):i("",!0)])])])):i("",!0),a.value.created_by?(s(),o("div",et,[e[22]||(e[22]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Creato da ",-1)),t("div",tt,[t("p",at,n(a.value.created_by.name),1)])])):i("",!0)]),t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"}," Opzioni evento ",-1)),t("div",rt,[t("div",st,[u(g,{name:a.value.requires_registration?"check":"x-mark",class:m([a.value.requires_registration?"text-green-500":"text-red-500","w-4 h-4 mr-2"])},null,8,["name","class"]),e[23]||(e[23]=t("span",{class:"text-sm text-gray-700 dark:text-gray-300"},"Registrazione",-1))]),t("div",ot,[u(g,{name:a.value.is_public?"check":"x-mark",class:m([a.value.is_public?"text-green-500":"text-red-500","w-4 h-4 mr-2"])},null,8,["name","class"]),e[24]||(e[24]=t("span",{class:"text-sm text-gray-700 dark:text-gray-300"},"Pubblico",-1))]),t("div",nt,[u(g,{name:a.value.send_reminders?"check":"x-mark",class:m([a.value.send_reminders?"text-green-500":"text-red-500","w-4 h-4 mr-2"])},null,8,["name","class"]),e[25]||(e[25]=t("span",{class:"text-sm text-gray-700 dark:text-gray-300"},"Promemoria",-1))]),t("div",lt,[u(g,{name:a.value.allow_comments?"check":"x-mark",class:m([a.value.allow_comments?"text-green-500":"text-red-500","w-4 h-4 mr-2"])},null,8,["name","class"]),e[26]||(e[26]=t("span",{class:"text-sm text-gray-700 dark:text-gray-300"},"Commenti",-1))])])]),a.value.tags&&a.value.tags.length>0?(s(),o("div",it,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tag ",-1)),t("div",dt,[(s(!0),o(R,null,U(a.value.tags,l=>(s(),o("span",{key:l,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},n(l),1))),128))])])):i("",!0),t("div",ut,[a.value.created_at?(s(),o("div",gt,[e[29]||(e[29]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Creato il ",-1)),t("div",ct,[t("p",mt,n(p(a.value.created_at)),1)])])):i("",!0),a.value.updated_at?(s(),o("div",bt,[e[30]||(e[30]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Ultima modifica ",-1)),t("div",yt,[t("p",xt,n(p(a.value.updated_at)),1)])])):i("",!0)])])):i("",!0),N.value&&a.value.participants_count>0?(s(),o("div",vt,[t("h3",pt," Partecipanti Registrati ("+n(a.value.participants_count)+") ",1),M.value?(s(),o("div",kt,e[31]||(e[31]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)]))):z.value.length>0?(s(),o("div",ft,[(s(!0),o(R,null,U(z.value,l=>(s(),o("div",{key:l.id,class:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md"},[t("div",_t,[t("div",ht,[u(g,{name:"user",size:"sm",class:"text-gray-500"})]),t("div",null,[t("p",wt,n(l.name),1),t("p",Et,n(l.email),1)])]),t("span",Ct,n(se(V)(l.registered_at)),1)]))),128))])):(s(),o("div",zt,e[32]||(e[32]=[t("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun partecipante registrato ancora.",-1)])))])):i("",!0)])):(s(),o("div",me,[u(g,{name:"calendar",size:"2xl",class:"mx-auto text-gray-400 mb-4"}),e[6]||(e[6]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Evento non trovato",-1)),e[7]||(e[7]=t("p",{class:"text-gray-500 dark:text-gray-400"},"L'evento richiesto non esiste o non è più disponibile.",-1)),u(d,{to:"/app/communications/events",class:"mt-4 btn-primary inline-block"},{default:ee(()=>e[5]||(e[5]=[x(" Torna agli Eventi ")])),_:1,__:[5]})])),w.value?(s(),F(ie,{key:4,event:a.value,onClose:e[1]||(e[1]=l=>w.value=!1),onUpdated:X},null,8,["event"])):i("",!0),E.value?(s(),F(de,{key:5,title:"Elimina Evento",message:`Sei sicuro di voler eliminare l'evento '${a.value.title}'?`,"confirm-text":"Elimina","confirm-class":"bg-red-600 hover:bg-red-700",onConfirm:Y,onCancel:e[2]||(e[2]=l=>E.value=!1)},null,8,["message"])):i("",!0)],64)}}};export{Dt as default};
