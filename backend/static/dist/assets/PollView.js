import{r as v,c as _,x as q,b as o,l as c,h as O,j as e,e as n,t as r,v as z,s as C,F as b,p as k,u as G,q as L,o as l,B as $,Q,V as J,E as K,n as W}from"./vendor.js";import{_ as X,e as Y,a as Z,H as u}from"./app.js";import{u as ee}from"./useFormatters.js";import{S as te}from"./StatusBadge.js";import{E as se}from"./EditPollModal.js";import{C as ae}from"./ConfirmationModal.js";import"./formatters.js";const le={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},oe={key:0,class:"flex justify-center items-center h-64"},ie={key:1,class:"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4"},re={class:"flex"},ne={class:"ml-3"},de={class:"text-sm text-red-800 dark:text-red-200"},ue={key:2,class:"space-y-6"},ce={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ve={class:"flex items-start justify-between"},me={class:"flex-1"},xe={class:"text-2xl font-bold text-gray-900 dark:text-white mb-2"},_e={class:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},pe={class:"flex items-center space-x-1"},fe={class:"flex items-center space-x-1"},ge={class:"flex items-center space-x-1"},he={class:"flex items-center space-x-2"},ye={key:0,class:"mt-4"},be={class:"text-gray-700 dark:text-gray-300"},ke={class:"mt-4 flex flex-wrap gap-4 text-sm text-gray-500 dark:text-gray-400"},we={key:0,class:"flex items-center space-x-1"},Ve={key:1,class:"flex items-center space-x-1"},Se={class:"flex items-center space-x-1"},Me={class:"flex items-center space-x-1"},ze={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ce={key:0,class:"space-y-3"},Ee={key:0},Pe=["id","value"],Ne=["for"],Ie={key:1},Oe=["id","value"],$e=["for"],Ae={class:"mt-4"},De=["disabled"],Ue={key:1,class:"space-y-4"},je={class:"flex justify-between items-center mb-1"},Be={class:"text-sm font-medium text-gray-700 dark:text-gray-300"},Fe={class:"text-sm text-gray-500 dark:text-gray-400"},Re={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},He={key:0,class:"mt-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-md"},Te={class:"flex"},qe={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ge={class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"},Le={class:"space-y-2"},Qe={class:"text-gray-700 dark:text-gray-300"},Je={class:"text-gray-500 dark:text-gray-400"},Ke={__name:"PollView",setup(We){const A=G(),D=L(),m=Y(),p=Z(),{formatDate:w}=ee(),V=v(!0),f=v(""),t=v(null),g=v(null),h=v([]),d=v(null),y=v(!1),S=v(!1),E=_(()=>!t.value||!t.value.is_active?"draft":t.value.expires_at&&new Date(t.value.expires_at)<new Date?"closed":"active"),P=_(()=>!d.value||d.value===null||d.value===void 0||typeof d.value=="object"&&Object.keys(d.value).length===0?!1:!!(d.value.option_id||d.value.option_ids&&d.value.option_ids.length>0)),U=_(()=>{var i;return(i=t.value)!=null&&i.allows_multiple_choices?h.value.length>0:g.value!==null}),j=_(()=>!t.value||!p.user?!1:t.value.author_id===p.user.id||p.hasPermission("PERMISSION_MANAGE_COMMUNICATION")),B=_(()=>p.hasPermission("PERMISSION_MANAGE_COMMUNICATION")),N=async()=>{V.value=!0,f.value="";try{const i=A.params.id;t.value=await m.fetchPoll(i),d.value=await m.fetchUserVoteForPoll(i)}catch(i){f.value="Errore nel caricamento del sondaggio",console.error("Error loading poll:",i)}finally{V.value=!1}},I=i=>!t.value||!t.value.total_votes||t.value.total_votes===0?0:Math.round((i.votes||0)/t.value.total_votes*100),F=i=>{var s,x;return d.value?(s=t.value)!=null&&s.allows_multiple_choices?(x=d.value.option_ids)==null?void 0:x.includes(i.id):d.value.option_id===i.id:!1},R=async()=>{var i;try{(i=t.value)!=null&&i.allows_multiple_choices?await m.submitPollVote({poll_id:t.value.id,option_ids:h.value}):await m.submitPollVote({poll_id:t.value.id,option_id:g.value}),await N()}catch(s){console.error("Errore nel voto:",s)}},H=i=>{t.value=i,y.value=!1},T=async()=>{try{await m.deletePoll(t.value.id),D.push("/app/communications/polls")}catch(i){console.error("Errore nell'eliminazione del sondaggio:",i)}};return q(()=>{N()}),(i,s)=>{var x;return l(),o("div",le,[V.value?(l(),o("div",oe,s[6]||(s[6]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):f.value?(l(),o("div",ie,[e("div",re,[n(u,{name:"exclamation-triangle",size:"md",class:"text-red-400"}),e("div",ne,[e("p",de,r(f.value),1)])])])):t.value?(l(),o("div",ue,[e("div",ce,[e("div",ve,[e("div",me,[e("h1",xe,r(t.value.title),1),e("div",_e,[e("div",pe,[n(u,{name:"user",size:"xs"}),e("span",null,r(t.value.author_name||"Anonimo"),1)]),e("div",fe,[n(u,{name:"calendar",size:"xs"}),e("span",null,r(z(w)(t.value.created_at)),1)]),e("div",ge,[n(u,{name:"users",size:"xs"}),e("span",null,r(t.value.total_votes||0)+" voti",1)]),n(te,{status:E.value,size:"sm"},null,8,["status"])])]),e("div",he,[j.value?(l(),o("button",{key:0,onClick:s[0]||(s[0]=a=>y.value=!0),class:"inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[n(u,{name:"pencil",size:"xs",class:"mr-1"}),s[7]||(s[7]=C(" Modifica "))])):c("",!0),B.value?(l(),o("button",{key:1,onClick:s[1]||(s[1]=a=>S.value=!0),class:"inline-flex items-center px-3 py-1 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},[n(u,{name:"trash",size:"xs",class:"mr-1"}),s[8]||(s[8]=C(" Elimina "))])):c("",!0)])]),t.value.description?(l(),o("div",ye,[e("p",be,r(t.value.description),1)])):c("",!0),e("div",ke,[t.value.expires_at?(l(),o("div",we,[n(u,{name:"clock",size:"xs"}),e("span",null,"Scade: "+r(z(w)(t.value.expires_at)),1)])):c("",!0),t.value.max_participants?(l(),o("div",Ve,[n(u,{name:"user-group",size:"xs"}),e("span",null,"Max partecipanti: "+r(t.value.max_participants),1)])):c("",!0),e("div",Se,[n(u,{name:"check-circle",size:"xs"}),e("span",null,r(t.value.allows_multiple_choices?"Scelta multipla":"Scelta singola"),1)]),e("div",Me,[n(u,{name:"eye",size:"xs"}),e("span",null,r(t.value.is_anonymous?"Voto anonimo":"Voto pubblico"),1)])])]),e("div",ze,[s[11]||(s[11]=e("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"}," Opzioni di voto ",-1)),!P.value&&E.value==="active"?(l(),o("div",Ce,[t.value.allows_multiple_choices?(l(),o("div",Ee,[(l(!0),o(b,null,k(t.value.options,a=>(l(),o("div",{key:a.id,class:"flex items-center"},[$(e("input",{id:`option-${a.id}`,"onUpdate:modelValue":s[2]||(s[2]=M=>h.value=M),value:a.id,type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,8,Pe),[[Q,h.value]]),e("label",{for:`option-${a.id}`,class:"ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300"},r(a.text),9,Ne)]))),128))])):(l(),o("div",Ie,[(l(!0),o(b,null,k(t.value.options,a=>(l(),o("div",{key:a.id,class:"flex items-center"},[$(e("input",{id:`option-${a.id}`,"onUpdate:modelValue":s[3]||(s[3]=M=>g.value=M),value:a.id,type:"radio",name:"poll-option",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"},null,8,Oe),[[J,g.value]]),e("label",{for:`option-${a.id}`,class:"ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300"},r(a.text),9,$e)]))),128))])),e("div",Ae,[e("button",{onClick:R,disabled:!U.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[n(u,{name:"hand-raised",size:"sm",class:"mr-2"}),s[9]||(s[9]=C(" Vota "))],8,De)])])):(l(),o("div",Ue,[(l(!0),o(b,null,k(t.value.options,a=>(l(),o("div",{key:a.id,class:"relative"},[e("div",je,[e("span",Be,r(a.text),1),e("span",Fe,r(a.votes||0)+" voti ("+r(I(a))+"%) ",1)]),e("div",Re,[e("div",{class:W(["h-2 rounded-full transition-all duration-300",[F(a)?"bg-green-500":"bg-blue-500"]]),style:K({width:`${I(a)}%`})},null,6)])]))),128)),P.value?(l(),o("div",He,[e("div",Te,[n(u,{name:"check-circle",size:"md",class:"text-green-400"}),s[10]||(s[10]=e("div",{class:"ml-3"},[e("p",{class:"text-sm text-green-800 dark:text-green-200"}," Hai già votato per questo sondaggio ")],-1))])])):c("",!0)]))]),!t.value.is_anonymous&&t.value.votes&&t.value.votes.length>0?(l(),o("div",qe,[e("h2",Ge," Partecipanti ("+r(t.value.votes.length)+") ",1),e("div",Le,[(l(!0),o(b,null,k(t.value.votes,a=>(l(),o("div",{key:a.id,class:"flex items-center justify-between text-sm"},[e("span",Qe,r(a.user_name||"Utente"),1),e("span",Je,r(z(w)(a.voted_at)),1)]))),128))])])):c("",!0)])):c("",!0),y.value?(l(),O(se,{key:3,poll:t.value,onClose:s[4]||(s[4]=a=>y.value=!1),onUpdated:H},null,8,["poll"])):c("",!0),S.value?(l(),O(ae,{key:4,title:"Elimina Sondaggio",message:`Sei sicuro di voler eliminare il sondaggio '${(x=t.value)==null?void 0:x.title}'?`,"confirm-text":"Elimina","confirm-class":"bg-red-600 hover:bg-red-700",onConfirm:T,onCancel:s[5]||(s[5]=a=>S.value=!1)},null,8,["message"])):c("",!0)])}}},lt=X(Ke,[["__scopeId","data-v-a85e1d11"]]);export{lt as default};
