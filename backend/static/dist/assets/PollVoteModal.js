import{_ as b,i as x,H as y,e as v}from"./app.js";import{u as f}from"./useFormatters.js";import{b as n,o as a,j as e,e as u,f as h,l as d,t as l,s as g,F as k,p as S,E as M,h as O}from"./vendor.js";const z={name:"PollVoteModal",components:{HeroIcon:y,LoadingSpinner:x},props:{poll:{type:Object,required:!0}},emits:["close","voted"],data(){return{selectedOptions:[],isSubmitting:!1,errorMessage:"",successMessage:""}},computed:{hasSelectedOptions(){return this.selectedOptions.length>0}},methods:{toggleOption(s){if(this.poll.multiple_choice){const o=this.selectedOptions.indexOf(s);o>-1?this.selectedOptions.splice(o,1):this.selectedOptions.push(s)}else this.selectedOptions=[s];this.errorMessage="",this.successMessage=""},isOptionSelected(s){return this.selectedOptions.includes(s)},getOptionPercentage(s){return!this.poll.total_votes||this.poll.total_votes===0?0:Math.round((s.votes||0)/this.poll.total_votes*100)},formatDate(s){const{formatDate:o}=f();return o(s)},async submitVote(){if(!(!this.hasSelectedOptions||this.isSubmitting)){this.isSubmitting=!0,this.errorMessage="",this.successMessage="";try{const s=v(),o={poll_id:this.poll.id,option_ids:this.selectedOptions};await s.submitPollVote(o),this.successMessage="Voto inviato con successo!",this.$emit("voted",{pollId:this.poll.id,optionIds:this.selectedOptions}),setTimeout(()=>{this.closeModal()},1500)}catch(s){console.error("Errore durante l'invio del voto:",s),this.errorMessage=s.message||"Errore durante l'invio del voto. Riprova."}finally{this.isSubmitting=!1}}},closeModal(){this.$emit("close")}}},V={class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},w={class:"card w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto"},C={class:"flex items-center justify-between p-6 border-b dark:border-gray-600"},P={class:"p-6 space-y-6"},j={class:"mb-6"},D={class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},I={key:0,class:"text-gray-600 dark:text-gray-400 text-sm mb-3"},B={class:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},E={key:0,class:"flex items-center"},H={class:"flex items-center"},L={class:"space-y-3"},N={class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},F={class:"space-y-2"},R=["onClick"],T={class:"flex items-center"},q=["id","type","name","value","checked","onChange"],A=["for"],G={key:0,class:"ml-auto text-right"},J={class:"text-sm text-gray-500 dark:text-gray-400"},K={class:"w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1"},Q={key:0,class:"p-3 bg-red-50 border border-red-200 rounded-lg"},U={class:"flex items-center"},W={class:"text-sm text-red-700"},X={key:1,class:"p-3 bg-green-50 border border-green-200 rounded-lg"},Y={class:"flex items-center"},Z={class:"text-sm text-green-700"},$={class:"flex justify-end space-x-3 pt-4 border-t dark:border-gray-600"},ee=["disabled"];function te(s,o,r,se,c,i){const m=h("HeroIcon"),_=h("LoadingSpinner");return a(),n("div",V,[e("div",w,[e("div",C,[o[3]||(o[3]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," Vota Sondaggio ",-1)),e("button",{onClick:o[0]||(o[0]=(...t)=>i.closeModal&&i.closeModal(...t)),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"},[u(m,{name:"x-mark",size:"md"})])]),e("div",P,[e("div",j,[e("h4",D,l(r.poll.title),1),r.poll.description?(a(),n("p",I,l(r.poll.description),1)):d("",!0),e("div",B,[r.poll.expires_at?(a(),n("span",E,[u(m,{name:"clock",size:"sm",class:"mr-1"}),g(" Scade: "+l(i.formatDate(r.poll.expires_at)),1)])):d("",!0),e("span",H,[u(m,{name:"users",size:"sm",class:"mr-1"}),g(" "+l(r.poll.total_votes||0)+" voti ",1)])])]),e("div",L,[e("label",N,l(r.poll.multiple_choice?"Seleziona una o più opzioni:":"Seleziona un'opzione:"),1),e("div",F,[(a(!0),n(k,null,S(r.poll.options,t=>(a(),n("div",{key:t.id,class:"flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer",onClick:p=>i.toggleOption(t.id)},[e("div",T,[e("input",{id:`option-${t.id}`,type:r.poll.multiple_choice?"checkbox":"radio",name:r.poll.multiple_choice?null:"poll-option",value:t.id,checked:i.isOptionSelected(t.id),onChange:p=>i.toggleOption(t.id),class:"h-4 w-4 text-brand-primary-600 focus:ring-brand-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700"},null,40,q),e("label",{for:`option-${t.id}`,class:"ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer"},l(t.text),9,A)]),r.poll.show_results?(a(),n("div",G,[e("span",J,l(t.votes||0)+" voti ",1),e("div",K,[e("div",{class:"bg-brand-primary-500 h-2 rounded-full transition-all duration-300",style:M({width:`${i.getOptionPercentage(t)}%`})},null,4)])])):d("",!0)],8,R))),128))])]),c.errorMessage?(a(),n("div",Q,[e("div",U,[u(m,{name:"exclamation-triangle",size:"sm",class:"text-red-500 mr-2"}),e("p",W,l(c.errorMessage),1)])])):d("",!0),c.successMessage?(a(),n("div",X,[e("div",Y,[u(m,{name:"check-circle",size:"sm",class:"text-green-500 mr-2"}),e("p",Z,l(c.successMessage),1)])])):d("",!0),e("div",$,[e("button",{type:"button",onClick:o[1]||(o[1]=(...t)=>i.closeModal&&i.closeModal(...t)),class:"btn-secondary","data-testid":"cancel-vote"}," Annulla "),e("button",{type:"button",onClick:o[2]||(o[2]=(...t)=>i.submitVote&&i.submitVote(...t)),disabled:c.isSubmitting||!i.hasSelectedOptions,class:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center","data-testid":"submit-vote"},[c.isSubmitting?(a(),O(_,{key:0,class:"mr-2",size:"sm"})):d("",!0),g(" "+l(c.isSubmitting?"Registrazione voto...":"Invia Voto"),1)],8,ee)])])])])}const le=b(z,[["render",te],["__scopeId","data-v-7995da4c"]]);export{le as P};
