import{D as ee}from"./DashboardTemplate.js";import{c as A,_ as te,H as S}from"./app.js";import{r as d,c as H,w as se,x as O,y as re,h as ae,k as p,v as g,o as F,j as e,t as l,e as z,s as h,n as D,E as J,b as oe,l as ie}from"./vendor.js";function ne(Q={}){const{apiEndpoint:a="/api/dashboard",autoLoad:b=!0,refreshInterval:T=0,defaultPeriod:V="7"}=Q,f=d(!1),k=d(""),m=d(V),u=d([]),x=d([]),v=d([]),w=d([]),_=d([]),E=d({}),M=H(()=>u.value.length>0||x.value.length>0||v.value.length>0||w.value.length>0||_.value.length>0),B=H(()=>f.value&&M.value),y=async(t=m.value)=>{var s,n;if(!a){console.log("Dashboard: API endpoint not configured, skipping data fetch");return}try{f.value=!0,k.value="";const c=new URLSearchParams;t&&c.append("period",t);const N=await A.get(`${a}/stats?${c}`);if((s=N.data)!=null&&s.success)E.value=N.data.data,U(N.data.data);else throw new Error(((n=N.data)==null?void 0:n.message)||"Errore nel caricamento dei dati")}catch(c){console.error("Dashboard data fetch error:",c),k.value=c.message||"Errore nel caricamento della dashboard"}finally{f.value=!1}},K=async(t=m.value)=>{var s;if(a)try{const n=new URLSearchParams;t&&n.append("period",t);const c=await A.get(`${a}/stats?${n}`);(s=c.data)!=null&&s.success&&(u.value=j(c.data.data))}catch(n){console.error("Stats fetch error:",n)}},Z=async(t=10)=>{var s;if(a)try{const n=await A.get(`${a}/activities?limit=${t}`);(s=n.data)!=null&&s.success&&(w.value=L(n.data.data.activities||[]))}catch(n){console.error("Activities fetch error:",n)}},G=async(t=5)=>{var s;if(a)try{const n=await A.get(`${a}/kpis?limit=${t}`);(s=n.data)!=null&&s.success&&(_.value=P(n.data.data.kpis||[]))}catch(n){console.error("KPIs fetch error:",n)}},R=async(t="tasks",s=5)=>{var n;if(a)try{const c=await A.get(`${a}/recent-${t}?limit=${s}`);(n=c.data)!=null&&n.success&&(v.value=I(c.data.data[t]||[]))}catch(c){console.error("Recent items fetch error:",c)}},U=t=>{t.stats&&(u.value=j(t.stats)),t.activities&&(w.value=L(t.activities)),t.kpis&&(_.value=P(t.kpis)),(t.recent_items||t.tasks||t.projects)&&(v.value=I(t.recent_items||t.tasks||t.projects||[])),t.charts&&(x.value=i(t.charts))},j=t=>{if(!t||typeof t!="object")return[];const s=[];return t.projects&&s.push({id:"projects",title:"Progetti Attivi",value:t.projects.active||0,subtitle:`di ${t.projects.total||0} totali`,icon:"folder",color:"primary",link:"/app/projects?status=active"}),t.tasks&&s.push({id:"tasks",title:"Task Pendenti",value:t.tasks.pending||0,subtitle:t.tasks.overdue?`${t.tasks.overdue} in ritardo`:"",icon:"clipboard-document-list",color:t.tasks.overdue>0?"red":"yellow",link:"/app/tasks?status=pending"}),t.team&&s.push({id:"team",title:"Team Members",value:t.team.users||0,subtitle:`${t.team.departments||0} dipartimenti`,icon:"users",color:"blue",link:"/app/personnel"}),t.clients&&s.push({id:"clients",title:"Clienti",value:t.clients||0,icon:"building-office",color:"secondary",link:"/app/crm/clients"}),s},L=t=>Array.isArray(t)?t.map(s=>({id:s.id,title:s.title||s.name,description:s.description,type:s.type||"system",icon:r(s.type),timestamp:s.timestamp||s.created_at,user:s.user,metadata:s.metadata})):[],P=t=>Array.isArray(t)?t.map(s=>({id:s.id,name:s.name,description:s.description,current_value:s.current_value,target_value:s.target_value,unit:s.unit||"",performance_percentage:s.performance_percentage||0,trend:s.trend})):[],I=t=>Array.isArray(t)?t.map(s=>({id:s.id,title:s.title||s.name,description:s.description||s.project_name,status:s.status,date:s.due_date||s.created_at||s.date,priority:s.priority,type:s.type,link:s.link})):[],i=t=>Array.isArray(t)?t.map(s=>({id:s.id,title:s.title,type:s.type,data:s.data,options:s.options,actions:s.actions})):[],r=t=>({project:"folder",task:"clipboard-document-list",user:"user",system:"cog-6-tooth",timesheet:"clock",document:"document",notification:"bell"})[t]||"information-circle",o=async()=>{await y(m.value)},C=async t=>{m.value=t,await y(t)},W=()=>{k.value=""},q=t=>{u.value.push(t)},X=(t,s)=>{const n=u.value.findIndex(c=>c.id===t);n!==-1&&(u.value[n]={...u.value[n],...s})},Y=t=>{x.value.push(t)};se(m,t=>{b&&y(t)});let $=null;return T>0&&($=setInterval(()=>{f.value||o()},T)),O(()=>{b&&y()}),{loading:f,error:k,selectedPeriod:m,stats:u,charts:x,recentItems:v,activities:w,kpis:_,rawData:E,hasData:M,isRefreshing:B,fetchDashboardData:y,fetchStats:K,fetchActivities:Z,fetchKpis:G,fetchRecentItems:R,refreshData:o,changePeriod:C,clearError:W,addCustomStat:q,updateStat:X,addCustomChart:Y,cleanup:()=>{$&&(clearInterval($),$=null)},processStats:j,processActivities:L,processKpis:P,processRecentItems:I,processCharts:i}}const le={class:"bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white"},ce={class:"flex items-center justify-between"},de={class:"text-right"},ue={class:"text-2xl font-bold"},pe={class:"mt-4 bg-white/20 rounded-full h-2"},me={class:"flex justify-between items-start"},ge={class:"flex-1"},fe={class:"text-sm font-medium text-gray-900 dark:text-white flex items-center"},xe={class:"text-xs text-gray-500 dark:text-gray-400"},ve={class:"flex flex-col items-end space-y-1"},ye={class:"text-xs text-gray-500 dark:text-gray-400"},he={class:"flex-1 min-w-0"},be={class:"text-sm font-medium text-gray-900 dark:text-white"},ke={class:"text-xs text-gray-500 dark:text-gray-400"},we={key:0,class:"font-medium"},_e={class:"text-xs text-gray-400 dark:text-gray-500"},Ce={class:"flex justify-between items-start"},Te={class:"flex-1"},je={class:"text-sm font-medium text-gray-900 dark:text-white flex items-center"},Le={class:"text-xs text-gray-500 dark:text-gray-400"},Pe={class:"text-right"},Ie={class:"text-sm font-bold text-gray-900 dark:text-white"},Ae={class:"text-xs text-gray-500"},Se={class:"mt-2"},ze={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Ee={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},Me={class:"space-y-4"},Re={class:"space-y-2"},$e={class:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4"},Ne={class:"flex items-center justify-between"},De={class:"text-sm text-gray-600 dark:text-gray-400"},Ve={__name:"DashboardExample",setup(Q){const a=ne({apiEndpoint:null,autoLoad:!1,refreshInterval:0}),b=d(75),T=d(new Date().toLocaleString("it-IT")),V=[{id:"projects",title:"Progetti Attivi",value:12,subtitle:"di 16 totali",icon:"folder",color:"primary",link:"/app/projects?status=active"},{id:"tasks",title:"Task Pendenti",value:28,subtitle:"3 in ritardo",icon:"clipboard-document-list",color:"red",link:"/app/tasks?status=pending"},{id:"team",title:"Team Members",value:15,subtitle:"4 dipartimenti",icon:"users",color:"blue",link:"/app/personnel"},{id:"clients",title:"Clienti",value:8,icon:"building-office",color:"secondary",link:"/app/crm/clients"}],f=[{id:"projects",title:"Stato Progetti",type:"donut"},{id:"tasks",title:"Trend Task Completati",type:"line"}],k=[{id:1,title:"Implementare autenticazione JWT",description:"Progetto DatPortal",status:"pending",priority:"high",date:"2024-01-15T10:00:00Z"},{id:2,title:"Review codice frontend",description:"Progetto E-commerce",status:"active",priority:"medium",date:"2024-01-16T14:30:00Z"},{id:3,title:"Deploy ambiente staging",description:"Progetto CRM",status:"pending",priority:"low",date:"2024-01-17T09:15:00Z"}],m=[{id:1,title:"Nuovo progetto creato",description:"Sistema di gestione inventario",type:"project",icon:"folder",timestamp:"2024-01-15T08:30:00Z",user:{name:"Mario Rossi"}},{id:2,title:"Task completato",description:"Setup database PostgreSQL",type:"task",icon:"check-circle",timestamp:"2024-01-15T07:45:00Z",user:{name:"Laura Bianchi"}},{id:3,title:"Nuovo membro del team",description:"Giulia Verdi si è unita al team",type:"user",icon:"user-plus",timestamp:"2024-01-14T16:20:00Z",user:{name:"Admin"}}],u=[{id:1,name:"Velocità Sviluppo",description:"Story points per sprint",current_value:42,target_value:50,unit:" SP",performance_percentage:84,trend:"up"},{id:2,name:"Qualità Codice",description:"Coverage test automatici",current_value:87,target_value:90,unit:"%",performance_percentage:97,trend:"stable"},{id:3,name:"Soddisfazione Cliente",description:"Rating medio feedback",current_value:4.2,target_value:4.5,unit:"/5",performance_percentage:93,trend:"down"}],x=()=>{a.stats.value=V,a.charts.value=f,a.recentItems.value=k,a.activities.value=m,a.kpis.value=u,T.value=new Date().toLocaleString("it-IT")},v=()=>{a.loading.value=!0,setTimeout(()=>{x(),a.loading.value=!1,b.value=Math.floor(Math.random()*20)+70},1e3)},w=i=>{a.selectedPeriod.value=i,v()},_=()=>{a.clearError()},E=(i,r)=>{console.log("Chart action:",i,r)},M=i=>({high:"exclamation-triangle",medium:"clock",low:"information-circle"})[i]||"clipboard-document-list",B=i=>{const r={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return r[i]||r.medium},y=i=>{const r={up:"bg-green-500",down:"bg-red-500",stable:"bg-yellow-500"};return r[i]||r.stable},K=i=>{const r={up:"text-green-600 dark:text-green-400",down:"text-red-600 dark:text-red-400",stable:"text-yellow-600 dark:text-yellow-400"};return r[i]||r.stable},Z=i=>{const r={up:"↗ In crescita",down:"↘ In calo",stable:"→ Stabile"};return r[i]||r.stable},G=i=>i>=90?"bg-green-500":i>=70?"bg-yellow-500":"bg-red-500",R=i=>i?new Date(i).toLocaleDateString("it-IT",{month:"short",day:"numeric"}):"",U=i=>{if(!i)return"";const r=new Date(i),C=Math.floor((new Date-r)/(1e3*60*60));return C<1?"Ora":C<24?`${C}h fa`:C<48?"Ieri":R(i)},j=()=>{console.log("Exporting dashboard data...")},L=()=>{console.log("Creating new project...")},P=()=>{console.log("Creating new task...")},I=()=>{console.log("Viewing reports...")};return O(()=>{x()}),re(()=>{a.cleanup()}),(i,r)=>(F(),ae(ee,{title:"Dashboard Esempio",subtitle:"Esempio di utilizzo del DashboardTemplate con dati mock",loading:g(a).loading.value,error:g(a).error.value,stats:g(a).stats.value,charts:g(a).charts.value,"recent-items":g(a).recentItems.value,activities:g(a).activities.value,kpis:g(a).kpis.value,"selected-period":g(a).selectedPeriod.value,"recent-items-title":"Task in Scadenza","recent-items-empty-message":"Nessun task in scadenza","recent-items-link":"/app/tasks","recent-items-link-text":"Vedi tutti i task","activities-title":"Attività Recenti","activities-empty-message":"Nessuna attività recente","kpis-title":"KPIs del Progetto",onRefresh:v,onPeriodChange:w,onClearError:_,onChartAction:E},{"header-actions":p(()=>[e("button",{onClick:j,class:"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},[z(S,{name:"arrow-down-tray",size:"sm",class:"mr-2"}),r[0]||(r[0]=h(" Esporta "))])]),widget:p(()=>[e("div",le,[e("div",ce,[r[2]||(r[2]=e("div",null,[e("h3",{class:"text-lg font-semibold"},"Benvenuto nel Design System!"),e("p",{class:"text-blue-100 mt-1"},"Questo è un esempio di widget personalizzato")],-1)),e("div",de,[e("div",ue,l(b.value)+"%",1),r[1]||(r[1]=e("div",{class:"text-blue-100 text-sm"},"Completamento",-1))])]),e("div",pe,[e("div",{class:"bg-white rounded-full h-2 transition-all duration-500",style:J({width:b.value+"%"})},null,4)])])]),"chart-projects":p(()=>r[3]||(r[3]=[e("div",{class:"h-64 flex items-center justify-center"},[e("div",{class:"text-center"},[e("div",{class:"w-32 h-32 mx-auto mb-4 relative"},[e("svg",{class:"w-32 h-32 transform -rotate-90",viewBox:"0 0 36 36"},[e("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"#e5e7eb","stroke-width":"3"}),e("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"#3b82f6","stroke-width":"3","stroke-dasharray":"75, 100","stroke-linecap":"round"})]),e("div",{class:"absolute inset-0 flex items-center justify-center"},[e("div",{class:"text-center"},[e("div",{class:"text-xl font-bold text-gray-900 dark:text-white"},"75%"),e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"Attivi")])])]),e("div",{class:"space-y-2"},[e("div",{class:"flex items-center justify-between text-sm"},[e("div",{class:"flex items-center"},[e("div",{class:"w-3 h-3 bg-blue-500 rounded-full mr-2"}),e("span",null,"Attivi")]),e("span",{class:"font-medium"},"12")]),e("div",{class:"flex items-center justify-between text-sm"},[e("div",{class:"flex items-center"},[e("div",{class:"w-3 h-3 bg-gray-300 rounded-full mr-2"}),e("span",null,"Completati")]),e("span",{class:"font-medium"},"4")])])])],-1)])),"chart-tasks":p(()=>r[4]||(r[4]=[e("div",{class:"h-64 flex items-center justify-center"},[e("div",{class:"w-full"},[e("svg",{class:"w-full h-48",viewBox:"0 0 400 200"},[e("defs",null,[e("linearGradient",{id:"taskGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%"},[e("stop",{offset:"0%",style:{"stop-color":"#10b981","stop-opacity":"0.3"}}),e("stop",{offset:"100%",style:{"stop-color":"#10b981","stop-opacity":"0"}})])]),e("g",{stroke:"#e5e7eb","stroke-width":"1",opacity:"0.5"},[e("line",{x1:"0",y1:"40",x2:"400",y2:"40"}),e("line",{x1:"0",y1:"80",x2:"400",y2:"80"}),e("line",{x1:"0",y1:"120",x2:"400",y2:"120"}),e("line",{x1:"0",y1:"160",x2:"400",y2:"160"})]),e("path",{d:"M 0 160 L 50 140 L 100 120 L 150 100 L 200 80 L 250 90 L 300 70 L 350 60 L 400 50 L 400 200 L 0 200 Z",fill:"url(#taskGradient)"}),e("path",{d:"M 0 160 L 50 140 L 100 120 L 150 100 L 200 80 L 250 90 L 300 70 L 350 60 L 400 50",fill:"none",stroke:"#10b981","stroke-width":"3","stroke-linecap":"round","stroke-linejoin":"round"}),e("g",{fill:"#10b981"},[e("circle",{cx:"0",cy:"160",r:"4"}),e("circle",{cx:"50",cy:"140",r:"4"}),e("circle",{cx:"100",cy:"120",r:"4"}),e("circle",{cx:"150",cy:"100",r:"4"}),e("circle",{cx:"200",cy:"80",r:"4"}),e("circle",{cx:"250",cy:"90",r:"4"}),e("circle",{cx:"300",cy:"70",r:"4"}),e("circle",{cx:"350",cy:"60",r:"4"}),e("circle",{cx:"400",cy:"50",r:"4"})])]),e("div",{class:"flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-2"},[e("span",null,"Gen"),e("span",null,"Feb"),e("span",null,"Mar"),e("span",null,"Apr"),e("span",null,"Mag"),e("span",null,"Giu"),e("span",null,"Lug"),e("span",null,"Ago"),e("span",null,"Set")])])],-1)])),"recent-item":p(({item:o})=>[e("div",me,[e("div",ge,[e("h3",fe,[z(S,{name:M(o.priority),size:"sm",class:"mr-2"},null,8,["name"]),h(" "+l(o.title),1)]),e("p",xe,l(o.description),1)]),e("div",ve,[e("span",{class:D(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",B(o.priority)])},l(o.priority),3),e("span",ye,l(R(o.date)),1)])])]),"activity-item":p(({activity:o})=>[e("div",he,[e("p",be,l(o.title),1),e("p",ke,[h(l(o.description)+" ",1),o.user?(F(),oe("span",we," • "+l(o.user.name),1)):ie("",!0)]),e("p",_e,l(U(o.timestamp)),1)])]),"kpi-item":p(({kpi:o})=>[e("div",Ce,[e("div",Te,[e("h3",je,[e("div",{class:D(["w-2 h-2 rounded-full mr-2",y(o.trend)])},null,2),h(" "+l(o.name),1)]),e("p",Le,l(o.description),1)]),e("div",Pe,[e("p",Ie,l(o.current_value)+l(o.unit),1),e("p",Ae," Target: "+l(o.target_value)+l(o.unit),1),e("p",{class:D(["text-xs",K(o.trend)])},l(Z(o.trend)),3)])]),e("div",Se,[e("div",ze,[e("div",{class:D(["h-2 rounded-full transition-all duration-500",G(o.performance_percentage)]),style:J({width:Math.min(o.performance_percentage,100)+"%"})},null,6)]),e("p",Ee,l(Math.round(o.performance_percentage))+"% del target ",1)])]),sidebar:p(()=>[e("div",Me,[r[8]||(r[8]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Quick Actions",-1)),e("div",Re,[e("button",{onClick:L,class:"w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"},[z(S,{name:"plus",size:"sm",class:"inline mr-2"}),r[5]||(r[5]=h(" Nuovo Progetto "))]),e("button",{onClick:P,class:"w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"},[z(S,{name:"clipboard-document-list",size:"sm",class:"inline mr-2"}),r[6]||(r[6]=h(" Nuovo Task "))]),e("button",{onClick:I,class:"w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"},[z(S,{name:"chart-bar",size:"sm",class:"inline mr-2"}),r[7]||(r[7]=h(" Visualizza Report "))])])])]),footer:p(()=>[e("div",$e,[e("div",Ne,[e("div",De," Ultimo aggiornamento: "+l(T.value),1),r[9]||(r[9]=e("div",{class:"flex space-x-4 text-sm"},[e("a",{href:"#",class:"text-brand-primary-600 hover:text-brand-primary-500"}," Documentazione "),e("a",{href:"#",class:"text-brand-primary-600 hover:text-brand-primary-500"}," Supporto ")],-1))])])]),_:1},8,["loading","error","stats","charts","recent-items","activities","kpis","selected-period"]))}},Ue=te(Ve,[["__scopeId","data-v-51c99046"]]);export{Ue as default};
