import{d as Ce,r as p,c as v}from"./vendor.js";import{c as i}from"./app.js";const Ae=Ce("recruiting",()=>{const f=p([]),m=p([]),d=p([]),y=p([]),w=p(null),h=p(null),g=p(null),s=p(!1),n=p(null),E=p({search:"",status:"",department_id:"",project_id:"",is_public:null,sort_by:"created_at",sort_order:"desc"}),b=p({search:"",status:"",source:"",skills:"",sort_by:"created_at",sort_order:"desc"}),u=p({page:1,per_page:20,total:0,pages:0}),q=v(()=>f.value.filter(a=>a.status==="active")),I=v(()=>f.value.filter(a=>a.status==="draft")),x=v(()=>f.value.filter(a=>a.is_public===!0)),F=v(()=>{const a={};return m.value.forEach(e=>{a[e.status]||(a[e.status]=[]),a[e.status].push(e)}),a}),z=v(()=>y.value.filter(a=>a.status==="scheduled"&&new Date(a.scheduled_date)>=new Date)),B=v(()=>w.value?[{label:"Posizioni Attive",value:w.value.stats.active_positions,icon:"briefcase",iconClass:"text-blue-600",change:"+12%"},{label:"Candidati Totali",value:w.value.stats.total_candidates,icon:"users",iconClass:"text-green-600"},{label:"Candidature Mese",value:w.value.stats.applications_month,icon:"document-text",iconClass:"text-purple-600",change:"+23%"},{label:"Colloqui Settimana",value:w.value.stats.interviews_week,icon:"calendar",iconClass:"text-orange-600"}]:null),O=v(()=>{const a={};return d.value.forEach(e=>{a[e.status]||(a[e.status]=[]),a[e.status].push(e)}),a}),T=v(()=>{const a={};return d.value.forEach(e=>{a[e.current_step]||(a[e.current_step]=[]),a[e.current_step].push(e)}),a}),M=v(()=>d.value.filter(a=>a.status==="pending")),V=v(()=>d.value.filter(a=>a.status==="in_progress")),N=v(()=>d.value.filter(a=>a.status==="completed")),W=async()=>{var a,e;s.value=!0,n.value=null;try{const r=await i.get("/api/recruiting/overview");if(r.data.success)return w.value=r.data.data,r.data.data;throw new Error(r.data.message||"Errore nel caricamento dashboard recruiting")}catch(r){throw n.value=((e=(a=r.response)==null?void 0:a.data)==null?void 0:e.message)||r.message||"Errore nel caricamento dashboard recruiting",console.error("Error fetching recruiting dashboard:",r),r}finally{s.value=!1}},$=async(a={})=>{var e,r;s.value=!0,n.value=null;try{const t=new URLSearchParams({page:a.page||u.value.page,per_page:a.per_page||u.value.per_page,...E.value,...a});for(let[c,l]of t.entries())(l===""||l===null||l===void 0)&&t.delete(c);const o=await i.get(`/api/recruiting/job-postings?${t}`);if(o.data.success)return f.value=o.data.data.job_postings||[],o.data.data.pagination&&(u.value=o.data.data.pagination),o.data.data;throw new Error(o.data.message||"Errore nel caricamento posizioni")}catch(t){throw n.value=((r=(e=t.response)==null?void 0:e.data)==null?void 0:r.message)||t.message||"Errore nel caricamento posizioni",console.error("Error fetching job postings:",t),t}finally{s.value=!1}},R=async a=>{var e,r;s.value=!0,n.value=null;try{const t=await i.get(`/api/recruiting/job-postings/${a}`);if(t.data.success)return h.value=t.data.data,t.data.data;throw new Error(t.data.message||"Errore nel caricamento posizione")}catch(t){throw n.value=((r=(e=t.response)==null?void 0:e.data)==null?void 0:r.message)||t.message||"Errore nel caricamento posizione",console.error("Error fetching job posting:",t),t}finally{s.value=!1}},G=async a=>{s.value=!0,n.value=null;try{const e=await i.post("/api/recruiting/job-postings",a);if(e.data.success)return await $(),e.data.data}catch(e){throw n.value=e.message,console.error("Error creating job posting:",e),e}finally{s.value=!1}},H=async(a,e)=>{s.value=!0,n.value=null;try{const r=await i.put(`/api/recruiting/job-postings/${a}`,e);if(r.data.success)return h.value&&h.value.id===a&&await R(a),await $(),r.data}catch(r){throw n.value=r.message,console.error("Error updating job posting:",r),r}finally{s.value=!1}},K=async a=>{s.value=!0,n.value=null;try{const e=await i.delete(`/api/recruiting/job-postings/${a}`);if(e.data.success)return f.value=f.value.filter(r=>r.id!==a),h.value&&h.value.id===a&&(h.value=null),e.data}catch(e){throw n.value=e.message,console.error("Error deleting job posting:",e),e}finally{s.value=!1}},P=async(a={})=>{var e,r;s.value=!0,n.value=null;try{const t=new URLSearchParams({page:a.page||u.value.page,per_page:a.per_page||u.value.per_page,...b.value,...a});for(let[c,l]of t.entries())(l===""||l===null||l===void 0)&&t.delete(c);const o=await i.get(`/api/recruiting/candidates?${t}`);if(o.data.success)return m.value=o.data.data.candidates||[],o.data.data.pagination&&(u.value=o.data.data.pagination),o.data.data;throw new Error(o.data.message||"Errore nel caricamento candidati")}catch(t){throw n.value=((r=(e=t.response)==null?void 0:e.data)==null?void 0:r.message)||t.message||"Errore nel caricamento candidati",console.error("Error fetching candidates:",t),t}finally{s.value=!1}},j=async a=>{var e,r;s.value=!0,n.value=null;try{const t=await i.get(`/api/recruiting/candidates/${a}`);if(t.data.success)return g.value=t.data.data,t.data.data;throw new Error(t.data.message||"Errore nel caricamento candidato")}catch(t){throw n.value=((r=(e=t.response)==null?void 0:e.data)==null?void 0:r.message)||t.message||"Errore nel caricamento candidato",console.error("Error fetching candidate:",t),t}finally{s.value=!1}},Q=async a=>{s.value=!0,n.value=null;try{const e=await i.post("/api/recruiting/candidates",a);if(e.data.success)return await P(),e.data.data}catch(e){throw n.value=e.message,console.error("Error creating candidate:",e),e}finally{s.value=!1}},X=async(a,e)=>{s.value=!0,n.value=null;try{const r=await i.put(`/api/recruiting/candidates/${a}`,e);if(r.data.success)return g.value&&g.value.id===a&&await j(a),await P(),r.data.data}catch(r){throw n.value=r.message,console.error("Error updating candidate:",r),r}finally{s.value=!1}},Y=async a=>{s.value=!0,n.value=null;try{const e=await i.delete(`/api/recruiting/candidates/${a}`);if(e.data.success)return m.value=m.value.filter(r=>r.id!==a),g.value&&g.value.id===a&&(g.value=null),e.data}catch(e){throw n.value=e.message,console.error("Error deleting candidate:",e),e}finally{s.value=!1}},Z=async(a,e)=>{s.value=!0,n.value=null;try{const r=new FormData;r.append("cv",e);const t=await i.post(`/api/recruiting/candidates/${a}/cv`,r,{headers:{"Content-Type":"multipart/form-data"}});if(t.data.success)return g.value&&g.value.id===a&&await j(a),t.data.data}catch(r){throw n.value=r.message,console.error("Error uploading CV:",r),r}finally{s.value=!1}},ee=async()=>{try{const a=await i.get("/api/recruiting/departments");return a.data.success?a.data.data:[]}catch(a){return console.error("Error fetching departments:",a),[]}},ae=async()=>{try{const a=await i.get("/api/recruiting/projects");return a.data.success?a.data.data:[]}catch(a){return console.error("Error fetching projects:",a),[]}},L=async(a={})=>{var e,r;s.value=!0,n.value=null;try{const t=new URLSearchParams({page:a.page||u.value.page,per_page:a.per_page||u.value.per_page,...a});for(let[c,l]of t.entries())(l===""||l===null||l===void 0)&&t.delete(c);const o=await i.get(`/api/recruiting/applications?${t}`);if(o.data.success)return d.value=o.data.data.applications||[],o.data.data.pagination&&(u.value=o.data.data.pagination),o.data.data;throw new Error(o.data.message||"Errore nel caricamento candidature")}catch(t){throw n.value=((r=(e=t.response)==null?void 0:e.data)==null?void 0:r.message)||t.message||"Errore nel caricamento candidature",console.error("Error fetching applications:",t),t}finally{s.value=!1}},S=async a=>{var e,r;s.value=!0,n.value=null;try{const t=await i.get(`/api/recruiting/applications/${a}`);if(t.data.success)return t.data.data;throw new Error(t.data.message||"Errore nel caricamento candidatura")}catch(t){throw n.value=((r=(e=t.response)==null?void 0:e.data)==null?void 0:r.message)||t.message||"Errore nel caricamento candidatura",console.error("Error fetching application:",t),t}finally{s.value=!1}},U=async a=>{s.value=!0,n.value=null;try{const e=await i.post("/api/recruiting/applications",a);if(e.data.success)return await L(),e.data.data}catch(e){throw n.value=e.message,console.error("Error creating application:",e),e}finally{s.value=!1}},C=async(a,e)=>{s.value=!0,n.value=null;try{const r=await i.put(`/api/recruiting/applications/${a}`,e);if(r.data.success){const t=d.value.findIndex(o=>o.id===a);if(t!==-1){const o=await S(a);o&&(d.value[t]=o)}return r.data}}catch(r){throw n.value=r.message,console.error("Error updating application:",r),r}finally{s.value=!1}},te=async a=>{s.value=!0,n.value=null;try{const e=await i.delete(`/api/recruiting/applications/${a}`);if(e.data.success)return d.value=d.value.filter(r=>r.id!==a),e.data}catch(e){throw n.value=e.message,console.error("Error deleting application:",e),e}finally{s.value=!1}},re=async a=>{var e,r;s.value=!0,n.value=null;try{const t=await i.get(`/api/recruiting/job-postings/${a}/applications`);if(t.data.success)return t.data.data;throw new Error(t.data.message||"Errore nel caricamento candidature posizione")}catch(t){throw n.value=((r=(e=t.response)==null?void 0:e.data)==null?void 0:r.message)||t.message||"Errore nel caricamento candidature posizione",console.error("Error fetching job posting applications:",t),t}finally{s.value=!1}},se=async a=>{var e,r;s.value=!0,n.value=null;try{const t=await i.get(`/api/recruiting/candidates/${a}/applications`);if(t.data.success)return t.data.data;throw new Error(t.data.message||"Errore nel caricamento candidature candidato")}catch(t){throw n.value=((r=(e=t.response)==null?void 0:e.data)==null?void 0:r.message)||t.message||"Errore nel caricamento candidature candidato",console.error("Error fetching candidate applications:",t),t}finally{s.value=!1}},ne=async(a,e,r="")=>await U({candidate_id:a,job_posting_id:e,cover_letter:r}),oe=async(a,e,r=null)=>{const t={status:e};return r&&(t.current_step=r),await C(a,t)},ie=async(a,e)=>await C(a,{interview_notes:e}),le=async(a,e)=>await C(a,{overall_score:e}),A=async(a={})=>{var e,r;s.value=!0,n.value=null;try{const t=new URLSearchParams({page:a.page||u.value.page,per_page:a.per_page||u.value.per_page,...a});for(let[c,l]of t.entries())(l===""||l===null||l===void 0)&&t.delete(c);const o=await i.get(`/api/recruiting/interviews?${t}`);if(o.data.success)return y.value=o.data.data.interviews||[],o.data.data.pagination&&(u.value=o.data.data.pagination),o.data.data;throw new Error(o.data.message||"Errore nel caricamento colloqui")}catch(t){throw n.value=((r=(e=t.response)==null?void 0:e.data)==null?void 0:r.message)||t.message||"Errore nel caricamento colloqui",console.error("Error fetching interviews:",t),t}finally{s.value=!1}},ce=async a=>{var e,r;s.value=!0,n.value=null;try{const t=await i.get(`/api/recruiting/interviews/${a}`);if(t.data.success)return t.data.data;throw new Error(t.data.message||"Errore nel caricamento colloquio")}catch(t){throw n.value=((r=(e=t.response)==null?void 0:e.data)==null?void 0:r.message)||t.message||"Errore nel caricamento colloquio",console.error("Error fetching interview:",t),t}finally{s.value=!1}},k=async a=>{s.value=!0,n.value=null;try{const e=await i.post("/api/recruiting/interviews",a);if(e.data.success)return await A(),e.data.data}catch(e){throw n.value=e.message,console.error("Error creating interview:",e),e}finally{s.value=!1}},ue=async(a,e)=>{s.value=!0,n.value=null;try{const r=await i.put(`/api/recruiting/interviews/${a}`,e);if(r.data.success)return await A(),r.data.data}catch(r){throw n.value=r.message,console.error("Error updating interview:",r),r}finally{s.value=!1}},de=async a=>{s.value=!0,n.value=null;try{const e=await i.delete(`/api/recruiting/interviews/${a}`);if(e.data.success)return y.value=y.value.filter(r=>r.id!==a),e.data}catch(e){throw n.value=e.message,console.error("Error deleting interview:",e),e}finally{s.value=!1}},pe=async()=>{try{const a=await i.get("/api/recruiting/interviewers");return a.data.success?a.data.data:[]}catch(a){return console.error("Error fetching interviewers:",a),[]}},ve=async(a,e,r)=>await k({application_id:a,candidate_id:e,...r}),ge=async(a,e="confirmation")=>{s.value=!0,n.value=null;try{const r=await i.post(`/api/recruiting/interviews/${a}/send-email`,{type:e});if(r.data.success)return r.data;throw new Error(r.data.message||"Errore nell'invio email")}catch(r){throw n.value=r.message,console.error("Error sending interview email:",r),r}finally{s.value=!1}},fe=async(a,e,r)=>{s.value=!0,n.value=null;try{const t=await i.post(`/api/recruiting/applications/${a}/send-status-email`,{old_status:e,new_status:r});if(t.data.success)return t.data;throw new Error(t.data.message||"Errore nell'invio email")}catch(t){throw n.value=t.message,console.error("Error sending status email:",t),t}finally{s.value=!1}},we=async a=>{s.value=!0,n.value=null;try{const e=await i.post(`/api/recruiting/candidates/${a}/send-welcome-email`);if(e.data.success)return e.data;throw new Error(e.data.message||"Errore nell'invio email")}catch(e){throw n.value=e.message,console.error("Error sending welcome email:",e),e}finally{s.value=!1}},he=async a=>{s.value=!0,n.value=null;try{const e=await i.get(`/api/recruiting/interviews/${a}/calendar`,{responseType:"blob"}),r=window.URL.createObjectURL(new Blob([e.data])),t=document.createElement("a");t.href=r;const o=e.headers["content-disposition"];let c=`colloquio_${a}.ics`;if(o){const l=o.match(/filename="(.+)"/);l&&(c=l[1])}return t.setAttribute("download",c),document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(r),{success:!0,message:"Calendario scaricato con successo"}}catch(e){throw n.value=e.message,console.error("Error downloading interview calendar:",e),e}finally{s.value=!1}},me=async(a={})=>{s.value=!0,n.value=null;try{const e=new URLSearchParams;a.status&&e.append("status",a.status),a.date_from&&e.append("date_from",a.date_from),a.date_to&&e.append("date_to",a.date_to),a.interviewer_id&&e.append("interviewer_id",a.interviewer_id);const r=await i.get(`/api/recruiting/interviews/calendar?${e.toString()}`,{responseType:"blob"}),t=window.URL.createObjectURL(new Blob([r.data])),o=document.createElement("a");o.href=t;const c=r.headers["content-disposition"];let l="colloqui_batch.ics";if(c){const _=c.match(/filename="(.+)"/);_&&(l=_[1])}return o.setAttribute("download",l),document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(t),{success:!0,message:"Calendario batch scaricato con successo"}}catch(e){throw n.value=e.message,console.error("Error downloading batch calendar:",e),e}finally{s.value=!1}},ye=async(a,e)=>{s.value=!0,n.value=null;try{const r=await i.get(`/api/recruiting/calendar/month/${a}/${e}`,{responseType:"blob"}),t=window.URL.createObjectURL(new Blob([r.data])),o=document.createElement("a");o.href=t;const c=r.headers["content-disposition"];let l=`calendario_${a}_${e}.ics`;if(c){const _=c.match(/filename="(.+)"/);_&&(l=_[1])}return o.setAttribute("download",l),document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(t),{success:!0,message:"Calendario mensile scaricato con successo"}}catch(r){throw n.value=r.message,console.error("Error downloading monthly calendar:",r),r}finally{s.value=!1}},Ee=async(a={})=>{var e,r;s.value=!0,n.value=null;try{const t=new URLSearchParams;a.start_date&&t.append("start_date",a.start_date),a.end_date&&t.append("end_date",a.end_date);const o=await i.get(`/api/recruiting/reports?${t.toString()}`);if(o.data.success)return o.data.data;throw new Error(o.data.message||"Errore nel caricamento dei report")}catch(t){throw n.value=((r=(e=t.response)==null?void 0:e.data)==null?void 0:r.message)||t.message||"Errore nel caricamento dei report",console.error("Error fetching reports data:",t),t}finally{s.value=!1}},be=a=>{E.value={...E.value,...a}},_e=a=>{b.value={...b.value,...a}},D=()=>{E.value={search:"",status:"",department_id:"",project_id:"",is_public:null,sort_by:"created_at",sort_order:"desc"}},J=()=>{b.value={search:"",status:"",source:"",skills:"",sort_by:"created_at",sort_order:"desc"}};return{jobPostings:f,candidates:m,applications:d,interviews:y,dashboardData:w,currentJobPosting:h,currentCandidate:g,loading:s,error:n,jobPostingFilters:E,candidateFilters:b,pagination:u,activeJobPostings:q,draftJobPostings:I,publicJobPostings:x,candidatesByStatus:F,upcomingInterviews:z,dashboardStats:B,applicationsByStatus:O,applicationsByStep:T,pendingApplications:M,inProgressApplications:V,completedApplications:N,fetchDashboardData:W,fetchJobPostings:$,fetchJobPosting:R,createJobPosting:G,updateJobPosting:H,deleteJobPosting:K,fetchCandidates:P,fetchCandidate:j,createCandidate:Q,updateCandidate:X,deleteCandidate:Y,uploadCandidateCV:Z,fetchDepartments:ee,fetchProjects:ae,fetchApplications:L,fetchApplication:S,createApplication:U,updateApplication:C,deleteApplication:te,fetchJobPostingApplications:re,fetchCandidateApplications:se,applyCandidateToJobPosting:ne,updateApplicationStatus:oe,addApplicationNotes:ie,scoreApplication:le,fetchInterviews:A,fetchInterview:ce,createInterview:k,updateInterview:ue,deleteInterview:de,fetchInterviewers:pe,scheduleInterviewFromApplication:ve,sendInterviewEmail:ge,sendApplicationStatusEmail:fe,sendCandidateWelcomeEmail:we,downloadInterviewCalendar:he,downloadInterviewsBatchCalendar:me,downloadMonthlyCalendar:ye,fetchReportsData:Ee,updateJobPostingFilters:be,updateCandidateFilters:_e,clearJobPostingFilters:D,clearCandidateFilters:J,resetState:()=>{f.value=[],m.value=[],d.value=[],y.value=[],w.value=null,h.value=null,g.value=null,s.value=!1,n.value=null,D(),J(),u.value={page:1,per_page:20,total:0,pages:0}}}});export{Ae as u};
