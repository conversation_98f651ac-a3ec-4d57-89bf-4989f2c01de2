import{r as S,c as D,w as ee,x as J,y as ce,b as d,o as l,j as e,e as x,h as B,l as M,B as te,W as ue,n as H,k as U,t as c,F as W,p as se,K as ge,X as me,z as F,s as A,A as fe,v as R,H as ve}from"./vendor.js";import{H as _,i as ye,_ as q,e as X,a as G}from"./app.js";import{u as Q}from"./useFormatters.js";import{_ as xe}from"./ListPageTemplate.js";import{P as be}from"./Pagination.js";import{A as pe}from"./ActionButtonGroup.js";import{u as he}from"./useDebounce.js";import{S as Y}from"./StandardButton.js";import{S as Z}from"./StandardInput.js";import{C as ke}from"./ConfirmationModal.js";import"./formatters.js";/* empty css                                                             */const _e={class:"relative"},we=["type","placeholder","disabled"],$e={key:0,class:"absolute z-[9999] mt-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto"},Me={key:0,class:"p-4 text-center"},Se={class:"ml-2 text-sm text-gray-500 dark:text-gray-400"},Ce={key:1,class:"p-4 text-center text-sm text-gray-500 dark:text-gray-400"},je={key:2,class:"divide-y divide-gray-200 dark:divide-gray-600"},Ie=["onClick","onMouseenter"],ze={class:"flex items-center space-x-3"},Ne={key:0,class:"h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},Ee=["src","alt"],De={key:1,class:"text-xs font-medium text-gray-700 dark:text-gray-300"},Re={class:"flex-1 min-w-0"},Ae={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},Ve={key:0,class:"text-xs text-gray-500 dark:text-gray-400 truncate"},Ue={__name:"AutocompleteInput",props:{modelValue:{type:[String,Number,Object],default:""},options:{type:Array,default:()=>[]},labelKey:{type:String,default:"label"},valueKey:{type:String,default:"value"},descriptionKey:{type:String,default:"description"},placeholder:{type:String,default:"Cerca..."},icon:{type:String,default:null},type:{type:String,default:"text"},disabled:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},loading:{type:Boolean,default:!1},loadingText:{type:String,default:"Caricamento..."},noResultsText:{type:String,default:"Nessun risultato trovato"},minSearchLength:{type:Number,default:0},filterOnClient:{type:Boolean,default:!0}},emits:["update:modelValue","search","select","focus","blur"],setup(o,{expose:k,emit:p}){const h=o,w=p,g=S(""),f=S(!1),r=S(-1),m=S(null),y=S(null),i=D(()=>{if(!h.filterOnClient||!g.value||g.value.length<h.minSearchLength)return h.options;const n=g.value.toLowerCase();return h.options.filter($=>{var C,P;const a=((C=$[h.labelKey])==null?void 0:C.toLowerCase())||"",b=((P=$[h.descriptionKey])==null?void 0:P.toLowerCase())||"";return a.includes(n)||b.includes(n)})}),I=n=>n[h.valueKey]||n.id||JSON.stringify(n),V=n=>n?n.split(" ").map($=>$.charAt(0)).join("").toUpperCase().slice(0,2):"",u=n=>{const $=n.target.value;g.value=$,r.value=-1,$.length>=h.minSearchLength?(f.value=!0,w("search",$)):f.value=!1,w("update:modelValue",$)},v=()=>{(i.value.length>0||h.loading)&&(f.value=!0),w("focus")},E=()=>{setTimeout(()=>{f.value=!1,r.value=-1},150),w("blur")},K=n=>{var $;if(f.value)switch(n.key){case"ArrowDown":n.preventDefault(),r.value=Math.min(r.value+1,i.value.length-1);break;case"ArrowUp":n.preventDefault(),r.value=Math.max(r.value-1,-1);break;case"Enter":n.preventDefault(),r.value>=0&&r.value<i.value.length&&j(i.value[r.value]);break;case"Escape":f.value=!1,r.value=-1,($=m.value)==null||$.blur();break}},j=n=>{g.value=n[h.labelKey],f.value=!1,r.value=-1,w("update:modelValue",n[h.valueKey]||n),w("select",n),F(()=>{var $;($=m.value)==null||$.blur()})},O=()=>{g.value="",f.value=!1,r.value=-1,w("update:modelValue",""),w("search",""),F(()=>{var n;(n=m.value)==null||n.focus()})},T=n=>{y.value&&!y.value.contains(n.target)&&(f.value=!1,r.value=-1)};return ee(()=>h.modelValue,n=>{typeof n=="string"?g.value=n:n&&typeof n=="object"&&(g.value=n[h.labelKey]||"")},{immediate:!0}),J(()=>{document.addEventListener("click",T)}),ce(()=>{document.removeEventListener("click",T)}),k({focus:()=>{var n;return(n=m.value)==null?void 0:n.focus()},blur:()=>{var n;return(n=m.value)==null?void 0:n.blur()},clear:O}),(n,$)=>(l(),d("div",{class:"relative",ref_key:"autocompleteContainer",ref:y},[e("div",_e,[o.icon?(l(),B(_,{key:0,name:o.icon,size:"sm",class:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"},null,8,["name"])):M("",!0),te(e("input",{ref_key:"inputRef",ref:m,"onUpdate:modelValue":$[0]||($[0]=a=>g.value=a),type:o.type,placeholder:o.placeholder,disabled:o.disabled,class:H(["block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500",o.icon?"pl-10":"",o.disabled?"opacity-50 cursor-not-allowed":""]),onInput:u,onFocus:v,onBlur:E,onKeydown:K,autocomplete:"off"},null,42,we),[[ue,g.value]]),g.value&&o.clearable&&!o.disabled?(l(),d("button",{key:1,onClick:O,type:"button",class:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[x(_,{name:"x-mark",size:"sm"})])):M("",!0)]),x(me,{"enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:U(()=>[f.value&&(i.value.length>0||o.loading||o.noResultsText)?(l(),d("div",$e,[o.loading?(l(),d("div",Me,[x(ye,{size:"sm"}),e("span",Se,c(o.loadingText),1)])):i.value.length===0&&o.noResultsText?(l(),d("div",Ce,c(o.noResultsText),1)):(l(),d("div",je,[(l(!0),d(W,null,se(i.value,(a,b)=>(l(),d("div",{key:I(a),class:H(["p-3 cursor-pointer transition-colors duration-150",b===r.value?"bg-primary-50 dark:bg-primary-900/30":"hover:bg-gray-50 dark:hover:bg-gray-700"]),onClick:C=>j(a),onMouseenter:C=>r.value=b},[ge(n.$slots,"option",{option:a,index:b},()=>[e("div",ze,[a.avatar?(l(),d("div",Ne,[a.avatar?(l(),d("img",{key:0,src:a.avatar,alt:a[o.labelKey],class:"h-8 w-8 rounded-full"},null,8,Ee)):(l(),d("span",De,c(V(a[o.labelKey])),1))])):M("",!0),e("div",Re,[e("p",Ae,c(a[o.labelKey]),1),a[o.descriptionKey]?(l(),d("p",Ve,c(a[o.descriptionKey]),1)):M("",!0)])])])],42,Ie))),128))]))])):M("",!0)]),_:3})],512))}},Ke={class:"message-composer"},Oe={key:0},Te={class:"relative"},Be={class:"absolute bottom-2 right-2 text-xs text-gray-400"},Pe={class:"flex items-center justify-between"},Le={class:"flex items-center space-x-2"},Fe={class:"flex items-center space-x-2"},qe={__name:"MessageComposer",props:{recipientId:{type:[String,Number],required:!0},recipientName:{type:String,required:!0},conversationId:{type:[String,Number],default:null},initialSubject:{type:String,default:""},placeholder:{type:String,default:"Scrivi il tuo messaggio..."}},emits:["message-sent","error"],setup(o,{emit:k}){const p=o,h=k,w=X(),g=S(p.initialSubject),f=S(""),r=S(!!p.initialSubject),m=S(!1),y=S(null),i=async()=>{if(!(!f.value.trim()||m.value)){m.value=!0;try{const u={recipient_id:p.recipientId,content:f.value.trim(),subject:g.value.trim()||null,conversation_id:p.conversationId||null},v=await w.sendMessage(u);I(),h("message-sent",v),await F(),y.value&&y.value.focus()}catch(u){console.error("Errore nell'invio del messaggio:",u),h("error",u)}finally{m.value=!1}}},I=()=>{f.value="",g.value=p.initialSubject,r.value=!!p.initialSubject},V=u=>{(u.ctrlKey||u.metaKey)&&u.key==="Enter"&&(u.preventDefault(),i())};return F(()=>{y.value&&y.value.focus()}),(u,v)=>(l(),d("div",Ke,[e("form",{onSubmit:fe(i,["prevent"]),class:"space-y-4"},[r.value||g.value?(l(),d("div",Oe,[v[3]||(v[3]=e("label",{for:"subject",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Oggetto (opzionale) ",-1)),x(Z,{id:"subject",modelValue:g.value,"onUpdate:modelValue":v[0]||(v[0]=E=>g.value=E),type:"text",placeholder:"Inserisci l'oggetto del messaggio...",maxlength:200},null,8,["modelValue"])])):M("",!0),e("div",null,[v[4]||(v[4]=e("label",{for:"content",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Messaggio ",-1)),e("div",Te,[x(Z,{id:"content",modelValue:f.value,"onUpdate:modelValue":v[1]||(v[1]=E=>f.value=E),ref_key:"contentTextarea",ref:y,type:"textarea",placeholder:"Scrivi il tuo messaggio...",rows:3,maxlength:2e3,class:"resize-none",onKeydown:V,required:""},null,8,["modelValue"]),e("div",Be,c(f.value.length)+"/2000 ",1)])]),e("div",Pe,[e("div",Le,[e("button",{type:"button",onClick:v[2]||(v[2]=E=>r.value=!r.value),class:"text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"},[x(_,{name:"tag",size:"sm",class:"mr-1"}),A(" "+c(r.value?"Nascondi oggetto":"Aggiungi oggetto"),1)])]),e("div",Fe,[f.value||g.value?(l(),B(Y,{key:0,variant:"secondary",size:"sm",type:"button",onClick:I},{default:U(()=>v[5]||(v[5]=[A(" Cancella ")])),_:1,__:[5]})):M("",!0),x(Y,{variant:"primary",size:"sm",type:"submit",disabled:!f.value.trim()||m.value,loading:m.value,icon:"paper-airplane"},{default:U(()=>v[6]||(v[6]=[A(" Invia ")])),_:1,__:[6]},8,["disabled","loading"])])])],32)]))}},ae=q(qe,[["__scopeId","data-v-0f5dfabb"]]),Ge={class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},He={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},We={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-visible shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},Je={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Xe={class:"flex items-center justify-between mb-4"},Qe={key:0,class:"space-y-4"},Ye={class:"flex items-center space-x-3"},Ze={class:"h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},et={class:"text-xs font-medium text-gray-700 dark:text-gray-300"},tt={class:"flex-1 min-w-0"},st={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},at={class:"text-xs text-gray-500 dark:text-gray-400 truncate"},rt={key:0,class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-md p-3"},ot={class:"flex items-center space-x-3"},nt={class:"h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center"},lt={class:"text-xs font-medium text-blue-700 dark:text-blue-300"},it={class:"flex-1"},dt={class:"text-sm font-medium text-blue-900 dark:text-blue-100"},ct={class:"text-xs text-blue-700 dark:text-blue-300"},ut={key:1,class:"space-y-4"},gt={class:"bg-gray-50 dark:bg-gray-700 rounded-md p-3"},mt={class:"flex items-center space-x-3"},ft={class:"h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},vt={class:"text-xs font-medium text-gray-700 dark:text-gray-300"},yt={class:"text-sm font-medium text-gray-900 dark:text-white"},xt={class:"text-xs text-gray-500 dark:text-gray-400"},bt={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},pt=["disabled"],ht={__name:"NewMessageModal",emits:["close","message-sent"],setup(o,{emit:k}){const p=k,h=X(),w=G(),g=S(1),f=S(""),r=S(null),m=S(!1),y=S([]),i=D(()=>{var a;return(a=w.user)==null?void 0:a.id}),I=D(()=>y.value.map(a=>({...a,roleAndDepartment:`${a.role} - ${a.department}`}))),V=he(a=>{a&&a.length>=2&&E(a)},300),u=a=>a.split(" ").map(b=>b.charAt(0)).join("").toUpperCase().slice(0,2),v=async()=>{m.value=!0;try{const a=await h.fetchUsers();y.value=a.filter(b=>b.id!==i.value)}catch(a){console.error("Errore nel caricamento degli utenti:",a)}finally{m.value=!1}},E=async(a=f.value)=>{if(!(!a||a.length<2)){m.value=!0;try{const b=await h.searchUsers({query:a,exclude_self:!0,current_user_id:i.value});y.value=b}catch(b){console.error("Errore nella ricerca degli utenti:",b)}finally{m.value=!1}}},K=a=>{V(a)},j=a=>{r.value=a,f.value=a.name},O=()=>{r.value&&(g.value=2)},T=()=>{g.value=1},n=a=>{p("message-sent",a.conversation_id)},$=a=>{console.error("Errore nell'invio del messaggio:",a)};return J(()=>{v()}),(a,b)=>(l(),d("div",Ge,[e("div",He,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:b[0]||(b[0]=C=>a.$emit("close"))}),e("div",We,[e("div",Je,[e("div",Xe,[b[5]||(b[5]=e("h3",{class:"text-lg leading-6 font-medium text-gray-900 dark:text-white",id:"modal-title"}," Nuovo Messaggio ",-1)),e("button",{onClick:b[1]||(b[1]=C=>a.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[x(_,{name:"x-mark",size:"md"})])]),g.value===1?(l(),d("div",Qe,[e("div",null,[b[6]||(b[6]=e("label",{for:"recipient-search",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Destinatario ",-1)),x(Ue,{modelValue:f.value,"onUpdate:modelValue":b[2]||(b[2]=C=>f.value=C),options:I.value,"label-key":"name","value-key":"id","description-key":"roleAndDepartment",placeholder:"Cerca un collega...",icon:"magnifying-glass",loading:m.value,"loading-text":"Caricamento utenti...","no-results-text":"Nessun utente trovato","min-search-length":2,"filter-on-client":!1,onSearch:K,onSelect:j},{option:U(({option:C})=>[e("div",Ye,[e("div",Ze,[e("span",et,c(u(C.name)),1)]),e("div",tt,[e("p",st,c(C.name),1),e("p",at,c(C.role)+" - "+c(C.department),1)])])]),_:1},8,["modelValue","options","loading"])]),r.value?(l(),d("div",rt,[e("div",ot,[e("div",nt,[e("span",lt,c(u(r.value.name)),1)]),e("div",it,[e("p",dt,c(r.value.name),1),e("p",ct,c(r.value.role)+" - "+c(r.value.department),1)]),e("button",{onClick:b[3]||(b[3]=C=>r.value=null),class:"text-blue-400 hover:text-blue-600 dark:text-blue-300 dark:hover:text-blue-100"},[x(_,{name:"x-mark",size:"sm"})])])])):M("",!0)])):M("",!0),g.value===2?(l(),d("div",ut,[e("div",gt,[e("div",mt,[e("div",ft,[e("span",vt,c(u(r.value.name)),1)]),e("div",null,[e("p",yt," A: "+c(r.value.name),1),e("p",xt,c(r.value.role)+" - "+c(r.value.department),1)])])]),x(ae,{"recipient-id":r.value.id,"recipient-name":r.value.name,onMessageSent:n,onError:$},null,8,["recipient-id","recipient-name"])])):M("",!0)]),e("div",bt,[g.value===1?(l(),d("button",{key:0,onClick:O,disabled:!r.value,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"}," Continua ",8,pt)):M("",!0),g.value===2?(l(),d("button",{key:1,onClick:T,class:"w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-gray-300 dark:border-gray-500 dark:hover:bg-gray-500"}," Indietro ")):M("",!0),e("button",{onClick:b[4]||(b[4]=C=>a.$emit("close")),class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-gray-300 dark:border-gray-500 dark:hover:bg-gray-500"}," Chiudi ")])])])]))}},kt={class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},_t={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},wt={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full"},$t={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 border-b border-gray-200 dark:border-gray-700"},Mt={class:"flex items-center justify-between"},St={class:"flex items-center space-x-3"},Ct={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},jt={class:"text-lg leading-6 font-medium text-gray-900 dark:text-white",id:"modal-title"},It={class:"text-sm text-gray-500 dark:text-gray-400"},zt={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600"},Nt={class:"bg-white dark:bg-gray-800 rounded p-3 text-sm"},Et={class:"flex items-center justify-between mb-2"},Dt={class:"font-medium text-gray-900 dark:text-white"},Rt={class:"text-xs text-gray-500 dark:text-gray-400"},At={key:0,class:"font-medium text-gray-800 dark:text-gray-200 mb-2"},Vt={class:"text-gray-600 dark:text-gray-400 line-clamp-3"},Ut={class:"bg-white dark:bg-gray-800 px-4 py-5 sm:p-6"},Kt={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},Ot={__name:"ReplyMessageModal",props:{originalMessage:{type:Object,required:!0}},emits:["close","sent"],setup(o,{emit:k}){const p=o,h=k,w=G(),{formatDate:g}=Q(),f=D(()=>{var u;return(u=w.user)==null?void 0:u.id}),r=()=>p.originalMessage.sender_id===f.value,m=()=>{var u,v;return r()?p.originalMessage.recipient_name||((u=p.originalMessage.recipient)==null?void 0:u.full_name)||"Destinatario sconosciuto":p.originalMessage.sender_name||((v=p.originalMessage.sender)==null?void 0:v.full_name)||"Mittente sconosciuto"},y=()=>r()?p.originalMessage.recipient_id:p.originalMessage.sender_id,i=()=>{const u=p.originalMessage.subject||"";return u.toLowerCase().startsWith("re:")?u:u?`Re: ${u}`:"Re: "},I=u=>{h("sent",u)},V=u=>{console.error("Errore nell'invio della risposta:",u)};return(u,v)=>(l(),d("div",kt,[e("div",_t,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:v[0]||(v[0]=E=>u.$emit("close"))}),e("div",wt,[e("div",$t,[e("div",Mt,[e("div",St,[e("div",Ct,[x(_,{name:"arrow-uturn-left",size:"md",class:"text-gray-500"})]),e("div",null,[e("h3",jt," Rispondi a "+c(m()),1),e("p",It," Re: "+c(o.originalMessage.subject||"Nessun oggetto"),1)])]),e("button",{onClick:v[1]||(v[1]=E=>u.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[x(_,{name:"x-mark",size:"md"})])])]),e("div",zt,[v[3]||(v[3]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400 mb-2"}," Messaggio originale: ",-1)),e("div",Nt,[e("div",Et,[e("span",Dt,c(m()),1),e("span",Rt,c(R(g)(o.originalMessage.sent_at)),1)]),o.originalMessage.subject?(l(),d("div",At,c(o.originalMessage.subject),1)):M("",!0),e("div",Vt,c(o.originalMessage.body||o.originalMessage.content),1)])]),e("div",Ut,[x(ae,{"recipient-id":y(),"recipient-name":m(),"conversation-id":o.originalMessage.conversation_id,"initial-subject":i(),placeholder:"Scrivi la tua risposta...",onMessageSent:I,onError:V},null,8,["recipient-id","recipient-name","conversation-id","initial-subject"])]),e("div",Kt,[e("button",{onClick:v[2]||(v[2]=E=>u.$emit("close")),class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-gray-300 dark:border-gray-500 dark:hover:bg-gray-500"}," Annulla ")])])])]))}},Tt=q(Ot,[["__scopeId","data-v-63223329"]]),Bt={class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},Pt={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Lt={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full"},Ft={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 border-b border-gray-200 dark:border-gray-700"},qt={class:"flex items-center justify-between"},Gt={class:"flex items-center space-x-3"},Ht={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},Wt={class:"text-lg leading-6 font-medium text-gray-900 dark:text-white",id:"modal-title"},Jt={class:"text-sm text-gray-500 dark:text-gray-400"},Xt={class:"bg-white dark:bg-gray-800 px-4 py-5 sm:p-6"},Qt={key:0,class:"mb-4"},Yt={class:"text-lg font-medium text-gray-900 dark:text-white"},Zt={class:"prose prose-sm max-w-none dark:prose-invert"},es={class:"text-gray-700 dark:text-gray-300 whitespace-pre-wrap"},ts={class:"mt-6 pt-4 border-t border-gray-200 dark:border-gray-700"},ss={class:"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400"},as={class:"flex items-center space-x-4"},rs={class:"flex items-center"},os={key:0,class:"flex items-center"},ns={key:1,class:"flex items-center"},ls={key:2,class:"flex items-center"},is={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},ds={__name:"ViewMessageModal",props:{message:{type:Object,required:!0}},emits:["close","reply","delete"],setup(o,{emit:k}){const p=o,h=G(),{formatDate:w}=Q(),g=D(()=>{var y;return(y=h.user)==null?void 0:y.id}),f=D(()=>h.hasPermission("PERMISSION_MANAGE_COMMUNICATION")),r=()=>p.message.sender_id===g.value,m=()=>{var y,i;return r()?p.message.recipient_name||((y=p.message.recipient)==null?void 0:y.full_name)||"Destinatario sconosciuto":p.message.sender_name||((i=p.message.sender)==null?void 0:i.full_name)||"Mittente sconosciuto"};return(y,i)=>(l(),d("div",Bt,[e("div",Pt,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:i[0]||(i[0]=I=>y.$emit("close"))}),e("div",Lt,[e("div",Ft,[e("div",qt,[e("div",Gt,[e("div",Ht,[x(_,{name:"user",size:"md",class:"text-gray-500"})]),e("div",null,[e("h3",Wt,c(m()),1),e("p",Jt,c(R(w)(o.message.sent_at)),1)])]),e("button",{onClick:i[1]||(i[1]=I=>y.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[x(_,{name:"x-mark",size:"md"})])])]),e("div",Xt,[o.message.subject?(l(),d("div",Qt,[e("h4",Yt,c(o.message.subject),1)])):M("",!0),e("div",Zt,[e("div",es,c(o.message.body||o.message.content),1)]),e("div",ts,[e("div",ss,[e("div",as,[e("span",rs,[x(_,{name:"clock",size:"xs",class:"mr-1"}),A(" "+c(R(w)(o.message.sent_at)),1)]),r()?(l(),d("span",os,[x(_,{name:"paper-airplane",size:"xs",class:"mr-1"}),i[5]||(i[5]=A(" Inviato da te "))])):o.message.is_read?(l(),d("span",ns,[x(_,{name:"check",size:"xs",class:"mr-1"}),i[6]||(i[6]=A(" Letto "))])):(l(),d("span",ls,[x(_,{name:"envelope",size:"xs",class:"mr-1"}),i[7]||(i[7]=A(" Non letto "))]))])])])]),e("div",is,[r()?M("",!0):(l(),d("button",{key:0,onClick:i[2]||(i[2]=I=>y.$emit("reply",o.message)),class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"},[x(_,{name:"arrow-uturn-left",size:"sm",class:"mr-2"}),i[8]||(i[8]=A(" Rispondi "))])),f.value?(l(),d("button",{key:1,onClick:i[3]||(i[3]=I=>y.$emit("delete",o.message)),class:"mt-3 w-full inline-flex justify-center rounded-md border border-red-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-red-700 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-red-400 dark:border-red-500 dark:hover:bg-gray-500"},[x(_,{name:"trash",size:"sm",class:"mr-2"}),i[9]||(i[9]=A(" Elimina "))])):M("",!0),e("button",{onClick:i[4]||(i[4]=I=>y.$emit("close")),class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-gray-300 dark:border-gray-500 dark:hover:bg-gray-500"}," Chiudi ")])])])]))}},cs=q(ds,[["__scopeId","data-v-c779d278"]]),us={class:"grid grid-cols-1 gap-5 sm:grid-cols-4"},gs={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},ms={class:"p-5"},fs={class:"flex items-center"},vs={class:"flex-shrink-0"},ys={class:"ml-5 w-0 flex-1"},xs={class:"text-lg font-medium text-gray-900 dark:text-white"},bs={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},ps={class:"p-5"},hs={class:"flex items-center"},ks={class:"flex-shrink-0"},_s={class:"ml-5 w-0 flex-1"},ws={class:"text-lg font-medium text-gray-900 dark:text-white"},$s={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Ms={class:"p-5"},Ss={class:"flex items-center"},Cs={class:"flex-shrink-0"},js={class:"ml-5 w-0 flex-1"},Is={class:"text-lg font-medium text-gray-900 dark:text-white"},zs={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Ns={class:"p-5"},Es={class:"flex items-center"},Ds={class:"flex-shrink-0"},Rs={class:"ml-5 w-0 flex-1"},As={class:"text-lg font-medium text-gray-900 dark:text-white"},Vs={class:"flex space-x-4"},Us={key:0,class:"flex justify-center items-center h-64"},Ks={key:1,class:"text-center py-12"},Os={key:2,class:"p-6"},Ts={class:"space-y-4"},Bs=["onClick"],Ps={class:"flex items-start justify-between"},Ls={class:"flex items-start space-x-4 flex-1 min-w-0"},Fs={class:"flex-shrink-0"},qs={class:"w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center"},Gs={class:"flex-1 min-w-0"},Hs={class:"flex items-center justify-between mb-2"},Ws={class:"flex items-center space-x-2"},Js={class:"text-sm font-semibold text-gray-900 dark:text-white"},Xs={key:0,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"},Qs={key:1,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800"},Ys={class:"text-xs text-gray-500 dark:text-gray-400"},Zs={class:"text-base font-medium text-gray-900 dark:text-white mb-2"},ea={class:"text-gray-600 dark:text-gray-400 text-sm line-clamp-2"},ta={class:"ml-4"},sa={key:0,class:"mt-8 flex justify-center"},aa={__name:"MessagesIndex",setup(o){const k=X(),p=G(),{formatDate:h}=Q(),w=S(!1),g=S(!1),f=S(!1),r=S(!1),m=S(null),y=S(""),i=D(()=>p.hasPermission("PERMISSION_VIEW_COMMUNICATION")),I=D(()=>p.hasPermission("PERMISSION_MANAGE_COMMUNICATION")),V=D(()=>{var t;return(t=p.user)==null?void 0:t.id}),u=D(()=>k.messages.filter(t=>!t.is_read&&!j(t)).length),v=D(()=>k.messages.filter(t=>j(t)).length),E=D(()=>k.messages.filter(t=>!j(t)).length),K=D(()=>{let t=k.messages;switch(y.value){case"unread":t=t.filter(s=>!s.is_read&&!j(s));break;case"sent":t=t.filter(s=>j(s));break;case"received":t=t.filter(s=>!j(s));break}return t.sort((s,z)=>new Date(z.sent_at)-new Date(s.sent_at))}),j=t=>t.sender_id===V.value,O=t=>{var s,z;return j(t)?t.recipient_name||((s=t.recipient)==null?void 0:s.full_name)||"Destinatario sconosciuto":t.sender_name||((z=t.sender)==null?void 0:z.full_name)||"Mittente sconosciuto"},T=t=>{m.value=t,f.value=!0,!t.is_read&&!j(t)&&n(t)},n=async t=>{try{console.log("Message object:",t),console.log("Message ID:",t.id),await k.markMessageAsRead(t.id)}catch(s){console.error("Errore nel segnare il messaggio come letto:",s)}},$=t=>{m.value=t,g.value=!0},a=t=>{m.value=t,r.value=!0},b=t=>{w.value=!1},C=t=>{g.value=!1,m.value=null},P=t=>{f.value=!1,m.value=t,g.value=!0},re=t=>{f.value=!1,m.value=t,r.value=!0},oe=t=>{const s=[];return!t.is_read&&!j(t)&&s.push({key:"mark-read",label:"Segna come letto",icon:"check",danger:!1}),s},ne=(t,s)=>{switch(s){case"mark-read":n(t);break;default:console.log("Azione non riconosciuta:",s)}},le=async()=>{try{await k.deleteMessage(m.value.id),r.value=!1,m.value=null}catch(t){console.error("Errore nell'eliminazione del messaggio:",t)}},ie=async t=>{try{await k.fetchMessages({page:t,per_page:20,type:y.value||void 0})}catch(s){console.error("Errore nel caricamento della pagina:",s)}};return ee(y,async(t,s)=>{if(s!==void 0)try{console.log("🔄 Filter changed from",s,"to",t),await k.fetchMessages({page:1,per_page:20,type:t||void 0})}catch(z){console.error("Errore nel caricamento dei messaggi con filtro:",z)}}),J(async()=>{try{console.log("🚀 MessagesIndex mounted - loading with per_page: 20"),await k.fetchMessages({page:1,per_page:20,type:"all"})}catch(t){console.error("Errore nel caricamento dei messaggi:",t)}}),(t,s)=>(l(),d(W,null,[x(xe,{title:"Messaggi Privati",subtitle:"Comunicazioni dirette tra colleghi",data:R(k).messages,loading:R(k).loading.messages,"can-create":i.value,"show-pagination":!1,"create-label":"Nuovo Messaggio","search-placeholder":"Cerca messaggi...","empty-message":"Nessun messaggio","results-label":"messaggi",onCreate:s[1]||(s[1]=z=>w.value=!0)},{stats:U(()=>[e("div",us,[e("div",gs,[e("div",ms,[e("div",fs,[e("div",vs,[x(_,{name:"envelope",size:"lg",class:"text-blue-500"})]),e("div",ys,[e("dl",null,[s[6]||(s[6]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Totali",-1)),e("dd",xs,c(R(k).messages.length),1)])])])])]),e("div",bs,[e("div",ps,[e("div",hs,[e("div",ks,[x(_,{name:"envelope-open",size:"lg",class:"text-green-500"})]),e("div",_s,[e("dl",null,[s[7]||(s[7]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Non Letti",-1)),e("dd",ws,c(u.value),1)])])])])]),e("div",$s,[e("div",Ms,[e("div",Ss,[e("div",Cs,[x(_,{name:"paper-airplane",size:"lg",class:"text-purple-500"})]),e("div",js,[e("dl",null,[s[8]||(s[8]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Inviati",-1)),e("dd",Is,c(v.value),1)])])])])]),e("div",zs,[e("div",Ns,[e("div",Es,[e("div",Ds,[x(_,{name:"inbox-arrow-down",size:"lg",class:"text-yellow-500"})]),e("div",Rs,[e("dl",null,[s[9]||(s[9]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Ricevuti",-1)),e("dd",As,c(E.value),1)])])])])])])]),filters:U(()=>[e("div",Vs,[te(e("select",{"onUpdate:modelValue":s[0]||(s[0]=z=>y.value=z),class:"block w-40 pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},s[10]||(s[10]=[e("option",{value:""},"Tutti i messaggi",-1),e("option",{value:"unread"},"Non letti",-1),e("option",{value:"sent"},"Inviati",-1),e("option",{value:"received"},"Ricevuti",-1)]),512),[[ve,y.value]])])]),content:U(({data:z,loading:de})=>[de?(l(),d("div",Us,s[11]||(s[11]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):K.value.length===0?(l(),d("div",Ks,[x(_,{name:"envelope",size:"2xl",class:"mx-auto text-gray-400 mb-4"}),s[12]||(s[12]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Nessun messaggio trovato",-1)),s[13]||(s[13]=e("p",{class:"text-gray-500 dark:text-gray-400"},"Non ci sono messaggi da visualizzare con i filtri selezionati.",-1))])):(l(),d("div",Os,[e("div",Ts,[(l(!0),d(W,null,se(K.value,N=>(l(),d("div",{key:N.id,class:H(["bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer",{"border-l-4 border-blue-500":!N.is_read&&!j(N)}]),onClick:L=>T(N)},[e("div",Ps,[e("div",Ls,[e("div",Fs,[e("div",qs,[x(_,{name:"user",size:"md",class:"text-gray-500"})])]),e("div",Gs,[e("div",Hs,[e("div",Ws,[e("h3",Js,c(O(N)),1),j(N)?(l(),d("span",Xs,[x(_,{name:"paper-airplane",size:"xs",class:"mr-1"}),s[14]||(s[14]=A(" Inviato "))])):N.is_read?M("",!0):(l(),d("span",Qs,[x(_,{name:"envelope",size:"xs",class:"mr-1"}),s[15]||(s[15]=A(" Nuovo "))]))]),e("span",Ys,c(R(h)(N.sent_at)),1)]),e("h4",Zs,c(N.subject||"Nessun oggetto"),1),e("p",ea,c(N.body),1)])]),e("div",ta,[x(pe,{"show-view":!1,"show-edit":!0,"show-delete":I.value,"edit-icon":"arrow-uturn-left","edit-label":"Rispondi","edit-title":"Rispondi al messaggio","delete-title":"Elimina messaggio","delete-message":"Sei sicuro di voler eliminare questo messaggio?","more-actions":oe(N),onEdit:L=>$(N),onDelete:L=>a(N),onAction:L=>ne(N,L)},null,8,["show-delete","more-actions","onEdit","onDelete","onAction"])])])],10,Bs))),128))]),R(k).pagination.messages.totalPages>1?(l(),d("div",sa,[x(be,{"current-page":R(k).pagination.messages.page,"total-pages":R(k).pagination.messages.totalPages,total:R(k).pagination.messages.total,"per-page":R(k).pagination.messages.per_page||20,"results-label":"messaggi",onPageChange:ie},null,8,["current-page","total-pages","total","per-page"])])):M("",!0)]))]),_:1},8,["data","loading","can-create"]),w.value?(l(),B(ht,{key:0,onClose:s[2]||(s[2]=z=>w.value=!1),onSent:b})):M("",!0),g.value?(l(),B(Tt,{key:1,"original-message":m.value,onClose:s[3]||(s[3]=z=>g.value=!1),onSent:C},null,8,["original-message"])):M("",!0),f.value?(l(),B(cs,{key:2,message:m.value,onClose:s[4]||(s[4]=z=>f.value=!1),onReply:P,onDelete:re},null,8,["message"])):M("",!0),r.value?(l(),B(ke,{key:3,title:"Elimina Messaggio",message:"Sei sicuro di voler eliminare questo messaggio?","confirm-text":"Elimina","confirm-class":"bg-red-600 hover:bg-red-700",onConfirm:le,onCancel:s[5]||(s[5]=z=>r.value=!1)})):M("",!0)],64))}},ya=q(aa,[["__scopeId","data-v-08758b1c"]]);export{ya as default};
