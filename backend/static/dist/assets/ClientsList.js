import{r as b,c as g,x as E,h as F,k as p,q as H,f as q,o,j as e,e as x,s as G,b as u,F as v,p as f,l as M,t as l,v as k,n as U,B as w,H as h}from"./vendor.js";import{u as j}from"./crm.js";import{d as O,H as C}from"./app.js";import{g as R,a as J}from"./industries.js";import{_ as K}from"./ListPageTemplate.js";import{A as Q}from"./ActionButtonGroup.js";import"./Pagination.js";import"./StandardButton.js";const W={class:"flex space-x-4"},X=["value"],Y={class:"overflow-x-auto"},Z={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ee={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},te={class:"px-6 py-4 whitespace-nowrap"},ae={class:"text-sm font-medium text-gray-900 dark:text-white"},se={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},re=["href"],le={class:"px-6 py-4 whitespace-nowrap"},oe={class:"text-sm text-gray-900 dark:text-white"},ie={class:"px-6 py-4 whitespace-nowrap"},ne={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},de={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},ue={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},ce={class:"text-center py-12"},we={__name:"ClientsList",setup(ge){const _=H(),c=j(),{showToast:y}=O(),i=b(""),n=b(""),S=g(()=>c.loading),d=g(()=>c.clients),z=g(()=>[{label:"Totale Clienti",value:d.value.length,icon:"users",iconClass:"text-blue-500"},{label:"Lead Attivi",value:d.value.filter(a=>a.status==="lead").length,icon:"arrow-trending-up",iconClass:"text-yellow-500"},{label:"Prospect",value:d.value.filter(a=>a.status==="prospect").length,icon:"eye",iconClass:"text-blue-500"},{label:"Clienti Attivi",value:d.value.filter(a=>a.status==="client").length,icon:"check-circle",iconClass:"text-green-500"}]),D=[{key:"name",label:"Cliente"},{key:"industry",label:"Settore"},{key:"status",label:"Stato"},{key:"contacts_count",label:"Contatti"},{key:"created_at",label:"Data Creazione"},{key:"actions",label:"Azioni"}],I=g(()=>{let a=d.value;return i.value&&(a=a.filter(t=>t.industry===i.value)),n.value&&(a=a.filter(t=>t.status===n.value)),a}),$=async()=>{await c.fetchClients()},L=()=>{_.push("/app/crm/clients/new")},N=async a=>{if(confirm("Sei sicuro di voler eliminare questo cliente?"))try{await c.deleteClient(a),y("Cliente eliminato con successo","success")}catch{y("Errore nell'eliminazione del cliente","error")}},V=()=>{i.value="",n.value=""},A=a=>({lead:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",prospect:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",client:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",inactive:"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"})[a]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",T=a=>({lead:"Lead",prospect:"Prospect",client:"Cliente",inactive:"Inattivo"})[a]||a,B=a=>a?new Date(a).toLocaleDateString("it-IT"):"N/A";return E(()=>{$()}),(a,t)=>{const P=q("router-link");return o(),F(K,{title:"Clienti",subtitle:"Gestione clienti e informazioni aziendali",data:I.value,columns:D,stats:z.value,loading:S.value,"can-create":!0,"create-label":"Nuovo Cliente","search-placeholder":"Nome cliente...","empty-message":"Inizia creando il tuo primo cliente","results-label":"clienti",onCreate:L},{filters:p(()=>[e("div",W,[e("div",null,[t[3]||(t[3]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Settore",-1)),w(e("select",{"onUpdate:modelValue":t[0]||(t[0]=r=>i.value=r),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[2]||(t[2]=e("option",{value:""},"Tutti i settori",-1)),(o(!0),u(v,null,f(k(J)(),r=>(o(),u("option",{key:r.value,value:r.value},l(r.label),9,X))),128))],512),[[h,i.value]])]),e("div",null,[t[5]||(t[5]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),w(e("select",{"onUpdate:modelValue":t[1]||(t[1]=r=>n.value=r),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},t[4]||(t[4]=[e("option",{value:""},"Tutti gli stati",-1),e("option",{value:"lead"},"Lead",-1),e("option",{value:"prospect"},"Prospect",-1),e("option",{value:"client"},"Cliente",-1),e("option",{value:"inactive"},"Inattivo",-1)]),512),[[h,n.value]])]),e("div",{class:"flex items-end"},[e("button",{onClick:V,class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md"}," Pulisci Filtri ")])])]),content:p(({data:r})=>[e("div",Y,[e("table",Z,[t[6]||(t[6]=e("thead",{class:"bg-gray-50 dark:bg-gray-800"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Cliente "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Settore "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Stato "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Contatti "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Data Creazione "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",ee,[(o(!0),u(v,null,f(r,s=>(o(),u("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",te,[e("div",null,[e("div",ae,l(s.name),1),s.website?(o(),u("div",se,[e("a",{href:s.website,target:"_blank",class:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"},l(s.website),9,re)])):M("",!0)])]),e("td",le,[e("span",oe,l(k(R)(s.industry)),1)]),e("td",ie,[e("span",{class:U([A(s.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},l(T(s.status)),3)]),e("td",ne,l(s.contacts_count||0)+" contatti ",1),e("td",de,l(B(s.created_at)),1),e("td",ue,[x(Q,{onView:m=>a.$router.push(`/app/crm/clients/${s.id}`),onEdit:m=>a.$router.push(`/app/crm/clients/${s.id}/edit`),onDelete:m=>N(s.id),"delete-message":`Sei sicuro di voler eliminare il cliente '${s.name}'?`},null,8,["onView","onEdit","onDelete","delete-message"])])]))),128))])])])]),"empty-state":p(()=>[e("div",ce,[x(C,{name:"users",class:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600"}),t[8]||(t[8]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun cliente trovato",-1)),t[9]||(t[9]=e("p",{class:"text-gray-500 dark:text-gray-400 mb-4"},"Inizia creando il tuo primo cliente",-1)),x(P,{to:"/app/crm/clients/new",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},{default:p(()=>[x(C,{name:"plus",class:"w-4 h-4 mr-2"}),t[7]||(t[7]=G(" Crea Primo Cliente "))]),_:1,__:[7]})])]),_:1},8,["data","stats","loading"])}}};export{we as default};
