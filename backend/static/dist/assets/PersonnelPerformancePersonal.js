import{c as R,b as r,o as t,j as e,l as v,t as a,e as b,k as P,s as N,F as Y,p as Q,n as ae,E as ue,r as z,w as le,x as oe,h as L,A as te,B as $,C as I,H as J,I as be,Q as re,u as Pe,q as Ue}from"./vendor.js";import{_ as se,H as T,c as H,h as je,a as De}from"./app.js";import{_ as Me}from"./PageHeader.js";import{T as Ne}from"./TabNavigation.js";import{S as E}from"./StandardButton.js";import{S as de}from"./StatusBadge.js";import{C as Ee}from"./ConfirmationModal.js";import{_ as me}from"./BaseModal.js";/* empty css                                                             */const Ge={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Oe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ie={class:"flex items-center space-x-4"},qe={class:"h-16 w-16 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center"},Re={class:"text-xl font-medium text-primary-700 dark:text-primary-300"},Be={class:"text-lg font-medium text-gray-900 dark:text-white"},Le={class:"text-sm text-gray-600 dark:text-gray-400"},Ye={key:0,class:"text-sm text-gray-600 dark:text-gray-400"},Fe={class:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-600"},He={class:"flex items-center justify-between text-sm"},Qe={class:"font-medium text-gray-900 dark:text-white"},Je={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ke={class:"flex items-center justify-between mb-4"},We={class:"text-lg font-medium text-gray-900 dark:text-white"},Xe={key:0,class:"space-y-4"},Ze={class:"flex items-center justify-center"},et={class:"relative h-24 w-24"},tt={class:"h-24 w-24 transform -rotate-90",viewBox:"0 0 100 100"},at=["stroke-dasharray","stroke-dashoffset"],st={class:"absolute inset-0 flex items-center justify-center"},rt={class:"text-lg font-semibold text-gray-900 dark:text-white"},lt={class:"grid grid-cols-2 gap-4 text-center"},ot={class:"text-2xl font-semibold text-gray-900 dark:text-white"},it={class:"text-2xl font-semibold text-green-600"},nt={class:"pt-4 border-t border-gray-200 dark:border-gray-600 space-y-2"},dt={class:"flex justify-between text-sm"},ut={class:"font-medium text-blue-600"},mt={class:"flex justify-between text-sm"},gt={class:"font-medium text-gray-900 dark:text-white"},ct={key:0,class:"flex justify-between text-sm"},yt={class:"flex items-center space-x-1"},vt={class:"font-medium text-yellow-600"},pt={key:1,class:"text-center py-8"},xt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},bt={class:"flex items-center justify-between mb-4"},ft={class:"text-lg font-medium text-gray-900 dark:text-white"},_t={key:0,class:"space-y-4"},kt={class:"grid grid-cols-2 gap-4 text-center"},wt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},ht={class:"text-2xl font-semibold text-green-600"},$t={key:0,class:"text-center"},Ct={class:"text-3xl font-semibold text-yellow-600 mb-1"},zt={class:"flex items-center justify-center space-x-1"},Vt={class:"pt-4 border-t border-gray-200 dark:border-gray-600 space-y-2"},St={key:0,class:"flex justify-between text-sm"},Tt={class:"font-medium text-blue-600"},At={key:1,class:"flex justify-between text-sm"},Pt={class:"font-medium text-yellow-600"},Ut={key:1,class:"text-center py-8"},jt={key:0,class:"mt-6"},Dt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Mt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Nt={class:"flex items-center justify-between mb-2"},Et={class:"font-medium text-gray-900 dark:text-white text-sm"},Gt={class:"text-xs text-gray-500 dark:text-gray-400"},Ot={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mb-2"},It={class:"flex justify-between text-xs text-gray-600 dark:text-gray-400"},qt={key:0,class:"flex items-center space-x-1"},Rt={__name:"PerformanceYearSummary",props:{summary:{type:Object,default:null},employee:{type:Object,required:!0},year:{type:Number,required:!0}},emits:["view-goals","view-reviews"],setup(u){const M=u,f=R(()=>{var S,k;if(!M.employee)return"U";const l=((S=M.employee.full_name)==null?void 0:S.split(" ")[0])||"",i=((k=M.employee.full_name)==null?void 0:k.split(" ")[1])||"";return(l.charAt(0)+i.charAt(0)).toUpperCase()||"U"}),q=R(()=>{var l,i;return(i=(l=M.summary)==null?void 0:l.goals_stats)!=null&&i.total?M.summary.goals_stats.completed/M.summary.goals_stats.total*100:0}),U=R(()=>2*Math.PI*40),n=R(()=>{const l=q.value;return U.value-l/100*U.value}),A=l=>({technical:"Tecniche",soft_skills:"Soft Skills",business:"Business",career_development:"Sviluppo Carriera"})[l]||l;return(l,i)=>{var S,k,s,j,p,B;return t(),r(Y,null,[e("div",Ge,[e("div",Oe,[e("div",Ie,[e("div",qe,[e("span",Re,a(f.value),1)]),e("div",null,[e("h3",Be,a((S=u.employee)==null?void 0:S.full_name),1),e("p",Le,a((k=u.employee)==null?void 0:k.email),1),(s=u.employee)!=null&&s.department?(t(),r("p",Ye,a(u.employee.department),1)):v("",!0)])]),e("div",Fe,[e("div",He,[i[2]||(i[2]=e("span",{class:"text-gray-600 dark:text-gray-400"},"Anno di Riferimento",-1)),e("span",Qe,a(u.year),1)])])]),e("div",Je,[e("div",Ke,[e("h3",We," Obiettivi "+a(u.year),1),b(E,{variant:"ghost",size:"sm",icon:"eye",onClick:i[0]||(i[0]=y=>l.$emit("view-goals"))},{default:P(()=>i[3]||(i[3]=[N(" Visualizza ")])),_:1,__:[3]})]),(j=u.summary)!=null&&j.goals_stats?(t(),r("div",Xe,[e("div",Ze,[e("div",et,[(t(),r("svg",tt,[i[4]||(i[4]=e("circle",{cx:"50",cy:"50",r:"40","stroke-width":"8",stroke:"currentColor",fill:"transparent",class:"text-gray-200 dark:text-gray-600"},null,-1)),e("circle",{cx:"50",cy:"50",r:"40","stroke-width":"8",stroke:"currentColor",fill:"transparent","stroke-dasharray":U.value,"stroke-dashoffset":n.value,class:"text-green-500 transition-all duration-500","stroke-linecap":"round"},null,8,at)])),e("div",st,[e("span",rt,a(Math.round(q.value))+"% ",1)])])]),e("div",lt,[e("div",null,[e("div",ot,a(u.summary.goals_stats.total),1),i[5]||(i[5]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Totali",-1))]),e("div",null,[e("div",it,a(u.summary.goals_stats.completed),1),i[6]||(i[6]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Completati",-1))])]),e("div",nt,[e("div",dt,[i[7]||(i[7]=e("span",{class:"text-gray-600 dark:text-gray-400"},"In Corso",-1)),e("span",ut,a(u.summary.goals_stats.in_progress),1)]),e("div",mt,[i[8]||(i[8]=e("span",{class:"text-gray-600 dark:text-gray-400"},"Progresso Medio",-1)),e("span",gt,a(u.summary.goals_stats.avg_progress)+"%",1)]),u.summary.goals_stats.avg_rating?(t(),r("div",ct,[i[9]||(i[9]=e("span",{class:"text-gray-600 dark:text-gray-400"},"Rating Medio",-1)),e("div",yt,[e("span",vt,a(u.summary.goals_stats.avg_rating),1),b(T,{name:"star",size:"sm",class:"text-yellow-500"})])])):v("",!0)])])):(t(),r("div",pt,[b(T,{name:"target",size:"xl",class:"text-gray-300 dark:text-gray-600 mx-auto mb-2"}),i[10]||(i[10]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400"},"Nessun obiettivo definito",-1))]))]),e("div",xt,[e("div",bt,[e("h3",ft," Valutazioni "+a(u.year),1),b(E,{variant:"ghost",size:"sm",icon:"eye",onClick:i[1]||(i[1]=y=>l.$emit("view-reviews"))},{default:P(()=>i[11]||(i[11]=[N(" Visualizza ")])),_:1,__:[11]})]),(p=u.summary)!=null&&p.reviews_stats?(t(),r("div",_t,[e("div",kt,[e("div",null,[e("div",wt,a(u.summary.reviews_stats.total),1),i[12]||(i[12]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Totali",-1))]),e("div",null,[e("div",ht,a(u.summary.reviews_stats.completed),1),i[13]||(i[13]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Completate",-1))])]),u.summary.reviews_stats.avg_overall_rating?(t(),r("div",$t,[e("div",Ct,a(u.summary.reviews_stats.avg_overall_rating),1),e("div",zt,[(t(),r(Y,null,Q(5,y=>b(T,{key:y,name:"star",size:"sm",class:ae(y<=u.summary.reviews_stats.avg_overall_rating?"text-yellow-500":"text-gray-300 dark:text-gray-600")},null,8,["class"])),64))]),i[14]||(i[14]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400 mt-1"},"Rating Complessivo",-1))])):v("",!0),e("div",Vt,[u.summary.reviews_stats.in_progress>0?(t(),r("div",St,[i[15]||(i[15]=e("span",{class:"text-gray-600 dark:text-gray-400"},"In Corso",-1)),e("span",Tt,a(u.summary.reviews_stats.in_progress),1)])):v("",!0),u.summary.reviews_stats.pending>0?(t(),r("div",At,[i[16]||(i[16]=e("span",{class:"text-gray-600 dark:text-gray-400"},"In Attesa",-1)),e("span",Pt,a(u.summary.reviews_stats.pending),1)])):v("",!0)])])):(t(),r("div",Ut,[b(T,{name:"star",size:"xl",class:"text-gray-300 dark:text-gray-600 mx-auto mb-2"}),i[17]||(i[17]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400"},"Nessuna valutazione disponibile",-1))]))])]),(B=u.summary)!=null&&B.categories_stats&&Object.keys(u.summary.categories_stats).length>0?(t(),r("div",jt,[e("div",Dt,[i[18]||(i[18]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Obiettivi per Categoria ",-1)),e("div",Mt,[(t(!0),r(Y,null,Q(u.summary.categories_stats,(y,o)=>(t(),r("div",{key:o,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},[e("div",Nt,[e("h4",Et,a(A(o)),1),e("div",Gt,a(y.completed)+"/"+a(y.total),1)]),e("div",Ot,[e("div",{class:"bg-primary-600 h-2 rounded-full transition-all duration-300",style:ue({width:`${y.completed/y.total*100}%`})},null,4)]),e("div",It,[e("span",null,a(Math.round(y.completed/y.total*100))+"%",1),y.avg_rating?(t(),r("span",qt,[b(T,{name:"star",size:"xs",class:"text-yellow-500"}),e("span",null,a(y.avg_rating),1)])):v("",!0)])]))),128))])])])):v("",!0)],64)}}},Bt=se(Rt,[["__scopeId","data-v-38155c60"]]),Lt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Yt={class:"md:col-span-2"},Ft={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Ht=["disabled"],Qt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Jt={class:"relative"},Kt={key:0},Wt={class:"flex items-center space-x-4"},Xt={class:"flex space-x-1"},Zt={class:"space-y-4"},ea={key:0},ta={key:1,class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"},aa={class:"flex items-center space-x-2 mb-2"},sa={class:"text-sm text-blue-700 dark:text-blue-300"},ra={key:0,class:"text-xs text-blue-600 dark:text-blue-400 mt-1"},la={class:"flex justify-between items-center"},oa={key:0,class:"text-sm text-gray-600 dark:text-gray-400"},ia={key:1},na={class:"flex space-x-3"},da={__name:"GoalEditModal",props:{goal:{type:Object,default:null},employee:{type:Object,required:!0},year:{type:Number,required:!0},templates:{type:Array,default:()=>[]}},emits:["close","goal-saved"],setup(u,{emit:M}){const f=u,q=M,U=z(!1),n=z({title:"",description:"",category:"",priority:"medium",weight:null,visibility:"private",start_date:"",target_date:"",completion_date:"",success_criteria:"",measurable_outcomes:"",status:"active",progress_percentage:0,achievement_rating:null,employee_self_assessment:"",manager_assessment:"",completion_notes:""}),A=R(()=>!!f.goal),l=()=>{f.goal?n.value={title:f.goal.title||"",description:f.goal.description||"",category:f.goal.category||"",priority:f.goal.priority||"medium",weight:f.goal.weight||null,visibility:f.goal.visibility||"private",start_date:f.goal.start_date||"",target_date:f.goal.target_date||"",completion_date:f.goal.completion_date||"",success_criteria:f.goal.success_criteria||"",measurable_outcomes:f.goal.measurable_outcomes||"",status:f.goal.status||"active",progress_percentage:f.goal.progress_percentage||0,achievement_rating:f.goal.achievement_rating||null,employee_self_assessment:f.goal.employee_self_assessment||"",manager_assessment:f.goal.manager_assessment||"",completion_notes:f.goal.completion_notes||""}:n.value={title:"",description:"",category:"",priority:"medium",weight:null,visibility:"private",start_date:"",target_date:"",completion_date:"",success_criteria:"",measurable_outcomes:"",status:"active",progress_percentage:0,achievement_rating:null,employee_self_assessment:"",manager_assessment:"",completion_notes:""}},i=async()=>{if(n.value.title){U.value=!0;try{let k;if(A.value)k=await H.put(`/api/performance/goals/${f.goal.id}`,n.value);else{const s={...n.value,year:f.year};k=await H.post(`/api/performance/employees/${f.employee.id}/goals`,s)}if(k.data.success)q("goal-saved",k.data.data.goal||k.data.data);else throw new Error(k.data.message||"Errore nel salvataggio")}catch(k){console.error("Error saving goal:",k)}finally{U.value=!1}}},S=k=>k?new Date(k).toLocaleString("it-IT"):"";return le(()=>n.value.status,k=>{k==="completed"?(n.value.completion_date||(n.value.completion_date=new Date().toISOString().split("T")[0]),n.value.progress_percentage<100&&(n.value.progress_percentage=100)):(n.value.completion_date="",n.value.achievement_rating=null,n.value.completion_notes="")}),le(()=>n.value.progress_percentage,k=>{k>=100&&n.value.status}),oe(()=>{l()}),(k,s)=>(t(),L(me,{show:!0,onClose:s[18]||(s[18]=j=>k.$emit("close")),title:A.value?"Modifica Obiettivo":"Nuovo Obiettivo",size:"xl"},{footer:P(()=>{var j;return[e("div",la,[A.value?(t(),r("div",oa," Ultima modifica: "+a(S((j=u.goal)==null?void 0:j.updated_at)),1)):(t(),r("div",ia)),e("div",na,[b(E,{variant:"secondary",onClick:s[17]||(s[17]=p=>k.$emit("close"))},{default:P(()=>s[42]||(s[42]=[N(" Annulla ")])),_:1,__:[42]}),b(E,{variant:"primary",disabled:!n.value.title||U.value,loading:U.value,onClick:i},{default:P(()=>[N(a(U.value?"Salvataggio...":A.value?"Aggiorna Obiettivo":"Crea Obiettivo"),1)]),_:1},8,["disabled","loading"])])])]}),default:P(()=>{var j;return[e("form",{onSubmit:te(i,["prevent"]),class:"space-y-6"},[e("div",Lt,[e("div",Yt,[s[19]||(s[19]=e("label",{for:"title",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Titolo Obiettivo * ",-1)),$(e("input",{id:"title","onUpdate:modelValue":s[0]||(s[0]=p=>n.value.title=p),type:"text",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"Inserisci il titolo dell'obiettivo"},null,512),[[I,n.value.title]])]),e("div",null,[s[21]||(s[21]=e("label",{for:"category",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Categoria ",-1)),$(e("select",{id:"category","onUpdate:modelValue":s[1]||(s[1]=p=>n.value.category=p),class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},s[20]||(s[20]=[e("option",{value:""},"Seleziona categoria",-1),e("option",{value:"technical"},"Competenze Tecniche",-1),e("option",{value:"soft_skills"},"Soft Skills",-1),e("option",{value:"business"},"Business",-1),e("option",{value:"career_development"},"Sviluppo Carriera",-1)]),512),[[J,n.value.category]])]),e("div",null,[s[23]||(s[23]=e("label",{for:"priority",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Priorità ",-1)),$(e("select",{id:"priority","onUpdate:modelValue":s[2]||(s[2]=p=>n.value.priority=p),class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},s[22]||(s[22]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1)]),512),[[J,n.value.priority]])]),e("div",null,[s[24]||(s[24]=e("label",{for:"weight",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Peso (0.0 - 1.0) ",-1)),$(e("input",{id:"weight","onUpdate:modelValue":s[3]||(s[3]=p=>n.value.weight=p),type:"number",step:"0.1",min:"0",max:"1",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white",placeholder:"0.3"},null,512),[[I,n.value.weight,void 0,{number:!0}]])]),e("div",null,[s[26]||(s[26]=e("label",{for:"visibility",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Visibilità ",-1)),$(e("select",{id:"visibility","onUpdate:modelValue":s[4]||(s[4]=p=>n.value.visibility=p),class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},s[25]||(s[25]=[e("option",{value:"private"},"Privato",-1),e("option",{value:"public"},"Pubblico",-1),e("option",{value:"team"},"Team",-1)]),512),[[J,n.value.visibility]])])]),e("div",null,[s[27]||(s[27]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Descrizione ",-1)),$(e("textarea",{id:"description","onUpdate:modelValue":s[5]||(s[5]=p=>n.value.description=p),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white",placeholder:"Descrizione dettagliata dell'obiettivo"},null,512),[[I,n.value.description]])]),e("div",Ft,[e("div",null,[s[28]||(s[28]=e("label",{for:"start_date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data Inizio ",-1)),$(e("input",{id:"start_date","onUpdate:modelValue":s[6]||(s[6]=p=>n.value.start_date=p),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},null,512),[[I,n.value.start_date]])]),e("div",null,[s[29]||(s[29]=e("label",{for:"target_date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data Target ",-1)),$(e("input",{id:"target_date","onUpdate:modelValue":s[7]||(s[7]=p=>n.value.target_date=p),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},null,512),[[I,n.value.target_date]])]),e("div",null,[s[30]||(s[30]=e("label",{for:"completion_date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data Completamento ",-1)),$(e("input",{id:"completion_date","onUpdate:modelValue":s[8]||(s[8]=p=>n.value.completion_date=p),type:"date",disabled:n.value.status!=="completed",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white disabled:opacity-50"},null,8,Ht),[[I,n.value.completion_date]])])]),e("div",null,[s[31]||(s[31]=e("label",{for:"success_criteria",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Criteri di Successo ",-1)),$(e("textarea",{id:"success_criteria","onUpdate:modelValue":s[9]||(s[9]=p=>n.value.success_criteria=p),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white",placeholder:"Definisci i criteri per considerare l'obiettivo completato"},null,512),[[I,n.value.success_criteria]])]),e("div",null,[s[32]||(s[32]=e("label",{for:"measurable_outcomes",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Risultati Misurabili ",-1)),$(e("textarea",{id:"measurable_outcomes","onUpdate:modelValue":s[10]||(s[10]=p=>n.value.measurable_outcomes=p),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white",placeholder:"Definisci i risultati misurabili e le metriche di valutazione"},null,512),[[I,n.value.measurable_outcomes]])]),e("div",Qt,[e("div",null,[s[34]||(s[34]=e("label",{for:"status",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Stato ",-1)),$(e("select",{id:"status","onUpdate:modelValue":s[11]||(s[11]=p=>n.value.status=p),class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},s[33]||(s[33]=[e("option",{value:"active"},"Attivo",-1),e("option",{value:"completed"},"Completato",-1),e("option",{value:"cancelled"},"Annullato",-1),e("option",{value:"deferred"},"Rinviato",-1)]),512),[[J,n.value.status]])]),e("div",null,[s[36]||(s[36]=e("label",{for:"progress_percentage",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Progresso (%) ",-1)),e("div",Jt,[$(e("input",{id:"progress_percentage","onUpdate:modelValue":s[12]||(s[12]=p=>n.value.progress_percentage=p),type:"number",min:"0",max:"100",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white pr-8"},null,512),[[I,n.value.progress_percentage,void 0,{number:!0}]]),s[35]||(s[35]=e("span",{class:"absolute right-3 top-2 text-gray-500 dark:text-gray-400"},"%",-1))])])]),n.value.status==="completed"?(t(),r("div",Kt,[s[37]||(s[37]=e("label",{for:"achievement_rating",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Rating Raggiungimento (1-5) ",-1)),e("div",Wt,[$(e("input",{id:"achievement_rating","onUpdate:modelValue":s[13]||(s[13]=p=>n.value.achievement_rating=p),type:"number",min:"1",max:"5",step:"0.1",class:"w-32 rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},null,512),[[I,n.value.achievement_rating,void 0,{number:!0}]]),e("div",Xt,[(t(),r(Y,null,Q(5,p=>b(T,{key:p,name:"star",size:"md",class:ae(p<=(n.value.achievement_rating||0)?"text-yellow-500":"text-gray-300 dark:text-gray-600")},null,8,["class"])),64))])])])):v("",!0),e("div",Zt,[e("div",null,[s[38]||(s[38]=e("label",{for:"employee_self_assessment",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Auto-valutazione Dipendente ",-1)),$(e("textarea",{id:"employee_self_assessment","onUpdate:modelValue":s[14]||(s[14]=p=>n.value.employee_self_assessment=p),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white",placeholder:"Valutazione del dipendente sui progressi dell'obiettivo"},null,512),[[I,n.value.employee_self_assessment]])]),e("div",null,[s[39]||(s[39]=e("label",{for:"manager_assessment",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Valutazione Manager ",-1)),$(e("textarea",{id:"manager_assessment","onUpdate:modelValue":s[15]||(s[15]=p=>n.value.manager_assessment=p),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white",placeholder:"Valutazione del manager sui progressi dell'obiettivo"},null,512),[[I,n.value.manager_assessment]])]),n.value.status==="completed"?(t(),r("div",ea,[s[40]||(s[40]=e("label",{for:"completion_notes",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Note di Completamento ",-1)),$(e("textarea",{id:"completion_notes","onUpdate:modelValue":s[16]||(s[16]=p=>n.value.completion_notes=p),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white",placeholder:"Note finali sul completamento dell'obiettivo"},null,512),[[I,n.value.completion_notes]])])):v("",!0)]),(j=u.goal)!=null&&j.template_source?(t(),r("div",ta,[e("div",aa,[b(T,{name:"document-text",size:"md",class:"text-blue-600"}),s[41]||(s[41]=e("h4",{class:"text-sm font-medium text-blue-900 dark:text-blue-100"}," Creato da Template ",-1))]),e("p",sa,' Questo obiettivo è stato creato dal template "'+a(u.goal.template_source.title)+'" ',1),u.goal.assigned_by?(t(),r("div",ra," Assegnato da: "+a(u.goal.assigned_by.full_name),1)):v("",!0)])):v("",!0)],32)]}),_:1},8,["title"]))}},fe=se(da,[["__scopeId","data-v-8eeab8c1"]]),ua={class:"space-y-6"},ma={class:"flex items-center justify-between"},ga={class:"text-lg font-medium text-gray-900 dark:text-white"},ca={class:"text-sm text-gray-600 dark:text-gray-400 mt-1"},ya={key:0,class:"flex space-x-2"},va={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},pa={class:"flex flex-wrap gap-4 items-center"},xa={class:"flex-1 min-w-64"},ba={class:"relative"},fa={class:"flex rounded-md shadow-sm"},_a={key:0,class:"flex justify-center items-center h-32"},ka={key:1},wa={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ha={class:"divide-y divide-gray-200 dark:divide-gray-600"},$a=["onClick"],Ca={class:"flex items-start justify-between"},za={class:"flex-1"},Va={class:"flex items-center space-x-3 mb-2"},Sa={class:"text-lg font-medium text-gray-900 dark:text-white"},Ta={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},Aa={key:0,class:"text-gray-600 dark:text-gray-400 text-sm mb-3"},Pa={class:"flex items-center space-x-6 text-sm text-gray-600 dark:text-gray-400"},Ua={key:0,class:"flex items-center space-x-1"},ja={key:1,class:"flex items-center space-x-1"},Da={key:2,class:"flex items-center space-x-1"},Ma={key:3,class:"flex items-center space-x-1"},Na={class:"ml-6 text-right"},Ea={class:"mb-2"},Ga={class:"text-2xl font-semibold text-gray-900 dark:text-white"},Oa={key:0,class:"mb-2"},Ia={class:"flex items-center space-x-1"},qa={class:"text-lg font-semibold text-yellow-600"},Ra={class:"flex space-x-2 mt-3"},Ba={class:"mt-4"},La={class:"flex justify-between text-sm mb-1"},Ya={class:"text-gray-900 dark:text-white font-medium"},Fa={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},Ha={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Qa=["onClick"],Ja={class:"flex items-start justify-between mb-4"},Ka={class:"flex-1"},Wa={class:"text-lg font-medium text-gray-900 dark:text-white mb-1 line-clamp-2"},Xa={class:"flex items-center space-x-2"},Za={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},es={class:"relative h-16 w-16 ml-4"},ts={class:"h-16 w-16 transform -rotate-90",viewBox:"0 0 100 100"},as=["stroke-dasharray","stroke-dashoffset"],ss={class:"absolute inset-0 flex items-center justify-center"},rs={class:"text-sm font-semibold text-gray-900 dark:text-white"},ls={key:0,class:"text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3"},os={class:"space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-4"},is={key:0,class:"flex items-center space-x-2"},ns={key:1,class:"flex items-center space-x-2"},ds={key:2,class:"flex items-center space-x-2"},us={class:"flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-600"},ms={class:"flex space-x-2"},gs={key:2,class:"text-center py-12"},cs={class:"text-gray-600 dark:text-gray-400 mb-6"},ys={__name:"GoalManagementPanel",props:{goals:{type:Array,default:()=>[]},templates:{type:Array,default:()=>[]},employee:{type:Object,required:!0},year:{type:Number,required:!0},canEdit:{type:Boolean,default:!1},loading:{type:Boolean,default:!1}},emits:["goal-updated","goal-created","goal-deleted","assign-templates"],setup(u,{emit:M}){const f=u,q=M,U=z(""),n=z(""),A=z(""),l=z("list"),i=z(!1),S=z(!1),k=z(!1),s=z(null),j=z(null),p=R(()=>{let C=f.goals;if(U.value){const m=U.value.toLowerCase();C=C.filter(X=>{var Z;return X.title.toLowerCase().includes(m)||((Z=X.description)==null?void 0:Z.toLowerCase().includes(m))})}return n.value&&(C=C.filter(m=>m.category===n.value)),A.value&&(C=C.filter(m=>m.status===A.value)),C}),B=R(()=>2*Math.PI*35),y=C=>B.value-C/100*B.value,o=C=>{s.value=C,S.value=!0},G=C=>{s.value=C,S.value=!0},O=C=>{j.value=C,k.value=!0},_=async()=>{try{await H.delete(`/api/performance/goals/${j.value.id}`),q("goal-deleted",j.value),k.value=!1,j.value=null}catch(C){console.error("Error deleting goal:",C)}},w=()=>{i.value=!1,S.value=!1,s.value=null},x=C=>{s.value?q("goal-updated",C):q("goal-created",C),w()},D=C=>C?new Date(C).toLocaleDateString("it-IT"):"",F=C=>({technical:"Tecniche",soft_skills:"Soft Skills",business:"Business",career_development:"Sviluppo Carriera"})[C]||C,V=C=>({low:"Bassa",medium:"Media",high:"Alta"})[C]||C,K=C=>{const m={low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"};return m[C]||m.medium};return(C,m)=>{var X,Z;return t(),r("div",ua,[e("div",ma,[e("div",null,[e("h3",ga," Obiettivi "+a(u.year),1),e("p",ca," Gestisci gli obiettivi di performance per "+a((X=u.employee)==null?void 0:X.full_name),1)]),u.canEdit?(t(),r("div",ya,[b(E,{variant:"secondary",icon:"document-plus",onClick:m[0]||(m[0]=c=>C.$emit("assign-templates"))},{default:P(()=>m[9]||(m[9]=[N(" Assegna Template ")])),_:1,__:[9]}),b(E,{variant:"primary",icon:"plus",onClick:m[1]||(m[1]=c=>i.value=!0)},{default:P(()=>m[10]||(m[10]=[N(" Nuovo Obiettivo ")])),_:1,__:[10]})])):v("",!0)]),e("div",va,[e("div",pa,[e("div",xa,[e("div",ba,[b(T,{name:"magnifying-glass",size:"md",class:"absolute left-3 top-3 text-gray-400"}),$(e("input",{"onUpdate:modelValue":m[2]||(m[2]=c=>U.value=c),type:"text",placeholder:"Cerca obiettivi...",class:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[I,U.value]])])]),$(e("select",{"onUpdate:modelValue":m[3]||(m[3]=c=>n.value=c),class:"rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},m[11]||(m[11]=[be('<option value="" data-v-0cdd51e3>Tutte le categorie</option><option value="technical" data-v-0cdd51e3>Tecniche</option><option value="soft_skills" data-v-0cdd51e3>Soft Skills</option><option value="business" data-v-0cdd51e3>Business</option><option value="career_development" data-v-0cdd51e3>Sviluppo Carriera</option>',5)]),512),[[J,n.value]]),$(e("select",{"onUpdate:modelValue":m[4]||(m[4]=c=>A.value=c),class:"rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},m[12]||(m[12]=[be('<option value="" data-v-0cdd51e3>Tutti gli stati</option><option value="active" data-v-0cdd51e3>Attivo</option><option value="completed" data-v-0cdd51e3>Completato</option><option value="cancelled" data-v-0cdd51e3>Annullato</option><option value="deferred" data-v-0cdd51e3>Rinviato</option>',5)]),512),[[J,A.value]]),e("div",fa,[e("button",{onClick:m[5]||(m[5]=c=>l.value="list"),class:ae(["px-3 py-2 text-sm font-medium rounded-l-md border",l.value==="list"?"bg-primary-600 text-white border-primary-600":"bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"])},[b(T,{name:"list-bullet",size:"sm"})],2),e("button",{onClick:m[6]||(m[6]=c=>l.value="grid"),class:ae(["px-3 py-2 text-sm font-medium rounded-r-md border-t border-r border-b",l.value==="grid"?"bg-primary-600 text-white border-primary-600":"bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"])},[b(T,{name:"squares-2x2",size:"sm"})],2)])])]),u.loading?(t(),r("div",_a,m[13]||(m[13]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):(t(),r("div",ka,[l.value==="list"?(t(),r("div",wa,[e("div",ha,[(t(!0),r(Y,null,Q(p.value,c=>(t(),r("div",{key:c.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer",onClick:W=>o(c)},[e("div",Ca,[e("div",za,[e("div",Va,[e("h4",Sa,a(c.title),1),b(de,{status:c.status,type:"goal"},null,8,["status"]),c.template_source?(t(),r("span",Ta," Template ")):v("",!0)]),c.description?(t(),r("p",Aa,a(c.description),1)):v("",!0),e("div",Pa,[c.category?(t(),r("div",Ua,[b(T,{name:"tag",size:"sm"}),e("span",null,a(F(c.category)),1)])):v("",!0),c.priority?(t(),r("div",ja,[b(T,{name:"flag",size:"sm"}),e("span",null,a(V(c.priority)),1)])):v("",!0),c.target_date?(t(),r("div",Da,[b(T,{name:"calendar-days",size:"sm"}),e("span",null,a(D(c.target_date)),1)])):v("",!0),c.assigned_by?(t(),r("div",Ma,[b(T,{name:"user",size:"sm"}),e("span",null,a(c.assigned_by.full_name),1)])):v("",!0)])]),e("div",Na,[e("div",Ea,[e("div",Ga,a(c.progress_percentage||0)+"% ",1),m[14]||(m[14]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Progresso",-1))]),c.achievement_rating?(t(),r("div",Oa,[e("div",Ia,[e("span",qa,a(c.achievement_rating),1),b(T,{name:"star",size:"sm",class:"text-yellow-500"})]),m[15]||(m[15]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Rating",-1))])):v("",!0),e("div",Ra,[b(E,{variant:"ghost",size:"sm",icon:"pencil",onClick:te(W=>G(c),["stop"])},{default:P(()=>m[16]||(m[16]=[N(" Modifica ")])),_:2,__:[16]},1032,["onClick"]),u.canEdit?(t(),L(E,{key:0,variant:"ghost",size:"sm",icon:"trash",onClick:te(W=>O(c),["stop"])},{default:P(()=>m[17]||(m[17]=[N(" Elimina ")])),_:2,__:[17]},1032,["onClick"])):v("",!0)])])]),e("div",Ba,[e("div",La,[m[18]||(m[18]=e("span",{class:"text-gray-600 dark:text-gray-400"},"Progresso",-1)),e("span",Ya,a(c.progress_percentage||0)+"%",1)]),e("div",Fa,[e("div",{class:"bg-primary-600 h-2 rounded-full transition-all duration-300",style:ue({width:`${c.progress_percentage||0}%`})},null,4)])])],8,$a))),128))])])):(t(),r("div",Ha,[(t(!0),r(Y,null,Q(p.value,c=>(t(),r("div",{key:c.id,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer",onClick:W=>o(c)},[e("div",Ja,[e("div",Ka,[e("h4",Wa,a(c.title),1),e("div",Xa,[b(de,{status:c.status,type:"goal",size:"sm"},null,8,["status"]),c.template_source?(t(),r("span",Za," Template ")):v("",!0)])]),e("div",es,[(t(),r("svg",ts,[m[19]||(m[19]=e("circle",{cx:"50",cy:"50",r:"35","stroke-width":"8",stroke:"currentColor",fill:"transparent",class:"text-gray-200 dark:text-gray-600"},null,-1)),e("circle",{cx:"50",cy:"50",r:"35","stroke-width":"8",stroke:"currentColor",fill:"transparent","stroke-dasharray":B.value,"stroke-dashoffset":y(c.progress_percentage||0),class:"text-primary-500 transition-all duration-500","stroke-linecap":"round"},null,8,as)])),e("div",ss,[e("span",rs,a(c.progress_percentage||0)+"% ",1)])])]),c.description?(t(),r("p",ls,a(c.description),1)):v("",!0),e("div",os,[c.category?(t(),r("div",is,[b(T,{name:"tag",size:"sm"}),e("span",null,a(F(c.category)),1)])):v("",!0),c.target_date?(t(),r("div",ns,[b(T,{name:"calendar-days",size:"sm"}),e("span",null,a(D(c.target_date)),1)])):v("",!0),c.achievement_rating?(t(),r("div",ds,[b(T,{name:"star",size:"sm",class:"text-yellow-500"}),e("span",null,a(c.achievement_rating)+"/5",1)])):v("",!0)]),e("div",us,[e("div",ms,[b(E,{variant:"ghost",size:"sm",icon:"pencil",onClick:te(W=>G(c),["stop"])},{default:P(()=>m[20]||(m[20]=[N(" Modifica ")])),_:2,__:[20]},1032,["onClick"]),u.canEdit?(t(),L(E,{key:0,variant:"ghost",size:"sm",icon:"trash",onClick:te(W=>O(c),["stop"])},{default:P(()=>m[21]||(m[21]=[N(" Elimina ")])),_:2,__:[21]},1032,["onClick"])):v("",!0)]),c.priority?(t(),r("div",{key:0,class:ae(["text-xs px-2 py-1 rounded-full",K(c.priority)])},a(V(c.priority)),3)):v("",!0)])],8,Qa))),128))])),p.value.length===0?(t(),r("div",gs,[b(T,{name:"target",size:"2xl",class:"text-gray-300 dark:text-gray-600 mx-auto mb-4"}),m[23]||(m[23]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"}," Nessun obiettivo trovato ",-1)),e("p",cs,a(U.value||n.value||A.value?"Nessun obiettivo corrisponde ai filtri selezionati":"Non sono stati ancora definiti obiettivi per questo anno"),1),u.canEdit&&!U.value&&!n.value&&!A.value?(t(),L(E,{key:0,variant:"primary",icon:"plus",onClick:m[7]||(m[7]=c=>i.value=!0)},{default:P(()=>m[22]||(m[22]=[N(" Crea Primo Obiettivo ")])),_:1,__:[22]})):v("",!0)])):v("",!0)])),i.value||S.value?(t(),L(fe,{key:2,goal:s.value,employee:u.employee,year:u.year,templates:u.templates,onClose:w,onGoalSaved:x},null,8,["goal","employee","year","templates"])):v("",!0),k.value?(t(),L(Ee,{key:3,title:"Elimina Obiettivo",message:`Sei sicuro di voler eliminare l'obiettivo '${(Z=j.value)==null?void 0:Z.title}'?`,"confirm-text":"Elimina","confirm-variant":"danger",onConfirm:_,onCancel:m[8]||(m[8]=c=>k.value=!1)},null,8,["message"])):v("",!0)])}}},vs=se(ys,[["__scopeId","data-v-0cdd51e3"]]),ps={class:"space-y-6"},xs={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},bs={class:"mt-2 flex items-center space-x-3"},fs={class:"h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center"},_s={class:"text-sm font-medium text-primary-700 dark:text-primary-300"},ks={class:"text-sm font-medium text-gray-900 dark:text-white"},ws={class:"text-xs text-gray-600 dark:text-gray-400"},hs={class:"space-y-4"},$s={class:"relative"},Cs={class:"flex space-x-4"},zs={class:"space-y-4 max-h-96 overflow-y-auto"},Vs={key:0,class:"flex justify-center items-center h-32"},Ss={key:1,class:"text-center py-8"},Ts={class:"text-gray-600 dark:text-gray-400"},As={key:2,class:"space-y-3"},Ps={class:"flex items-start space-x-3"},Us=["id","value","disabled"],js={class:"flex-1"},Ds=["for"],Ms={class:"flex items-center justify-between mb-2"},Ns={class:"text-sm font-medium text-gray-900 dark:text-white"},Es={class:"flex items-center space-x-2"},Gs={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},Os={class:"text-xs text-gray-500 dark:text-gray-400"},Is={key:0,class:"text-sm text-gray-600 dark:text-gray-400 mb-2"},qs={class:"space-y-1 text-xs text-gray-600 dark:text-gray-400"},Rs={key:0,class:"flex items-start space-x-2"},Bs={key:1,class:"flex items-start space-x-2"},Ls={key:2,class:"flex items-center space-x-2"},Ys={key:1,class:"mt-2 flex items-center space-x-2 text-amber-600"},Fs={key:0,class:"bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-4"},Hs={class:"flex items-center space-x-2 mb-2"},Qs={class:"text-sm text-primary-700 dark:text-primary-300"},Js={class:"mt-3 space-y-1"},Ks={class:"flex justify-between items-center"},Ws={class:"text-sm text-gray-600 dark:text-gray-400"},Xs={class:"flex space-x-3"},Zs={__name:"GoalTemplateAssignment",props:{employee:{type:Object,required:!0},year:{type:Number,required:!0},templates:{type:Array,default:()=>[]}},emits:["close","templates-assigned"],setup(u,{emit:M}){const f=u,q=M,U=z(!1),n=z(!1),A=z(""),l=z(""),i=z(""),S=z([]),k=z([]),s=R(()=>{var D,F;if(!f.employee)return"U";const w=((D=f.employee.full_name)==null?void 0:D.split(" ")[0])||"",x=((F=f.employee.full_name)==null?void 0:F.split(" ")[1])||"";return(w.charAt(0)+x.charAt(0)).toUpperCase()||"U"}),j=R(()=>{let w=f.templates;if(A.value){const x=A.value.toLowerCase();w=w.filter(D=>{var F;return D.title.toLowerCase().includes(x)||((F=D.description)==null?void 0:F.toLowerCase().includes(x))})}return l.value&&(w=w.filter(x=>x.category===l.value)),i.value&&(w=w.filter(x=>x.visibility===i.value)),w.sort((x,D)=>x.usage_count!==D.usage_count?(D.usage_count||0)-(x.usage_count||0):x.title.localeCompare(D.title))}),p=async()=>{try{const w=await H.get(`/api/performance/employees/${f.employee.id}/goals/${f.year}`);w.data.success&&(k.value=w.data.data.goals||[])}catch(w){console.error("Error loading assigned goals:",w)}},B=w=>k.value.some(x=>x.template_id===w),y=w=>f.templates.find(x=>x.id===w),o=async()=>{if(S.value.length!==0){n.value=!0;try{const w=await H.post(`/api/performance/employees/${f.employee.id}/goals/assign`,{template_ids:S.value,year:f.year});if(w.data.success)q("templates-assigned",{assigned_goals:w.data.data.assigned_goals,count:w.data.data.count});else throw new Error(w.data.message||"Errore nell'assegnazione template")}catch(w){console.error("Error assigning templates:",w)}finally{n.value=!1}}},G=w=>({technical:"Tecniche",soft_skills:"Soft Skills",business:"Business",career_development:"Sviluppo Carriera"})[w]||w,O=w=>({low:"Bassa",medium:"Media",high:"Alta"})[w]||w,_=w=>{const x={low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"};return x[w]||x.medium};return oe(()=>{p()}),(w,x)=>(t(),L(me,{show:!0,onClose:x[5]||(x[5]=D=>w.$emit("close")),title:"Assegna Template Obiettivi",size:"lg"},{footer:P(()=>[e("div",Ks,[e("div",Ws,a(S.value.length)+" template selezionati ",1),e("div",Xs,[b(E,{variant:"secondary",onClick:x[4]||(x[4]=D=>w.$emit("close"))},{default:P(()=>x[13]||(x[13]=[N(" Annulla ")])),_:1,__:[13]}),b(E,{variant:"primary",disabled:S.value.length===0||n.value,loading:n.value,onClick:o},{default:P(()=>[N(a(n.value?"Assegnazione...":`Assegna ${S.value.length} Template`),1)]),_:1},8,["disabled","loading"])])])]),default:P(()=>{var D,F;return[e("div",ps,[e("div",xs,[x[6]||(x[6]=e("h3",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Assegnazione a:",-1)),e("div",bs,[e("div",fs,[e("span",_s,a(s.value),1)]),e("div",null,[e("p",ks,a((D=u.employee)==null?void 0:D.full_name),1),e("p",ws,"Anno: "+a(u.year),1)])])]),e("div",hs,[e("div",$s,[b(T,{name:"magnifying-glass",size:"md",class:"absolute left-3 top-3 text-gray-400"}),$(e("input",{"onUpdate:modelValue":x[0]||(x[0]=V=>A.value=V),type:"text",placeholder:"Cerca template...",class:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[I,A.value]])]),e("div",Cs,[$(e("select",{"onUpdate:modelValue":x[1]||(x[1]=V=>l.value=V),class:"rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},x[7]||(x[7]=[e("option",{value:""},"Tutte le categorie",-1),e("option",{value:"technical"},"Tecniche",-1),e("option",{value:"soft_skills"},"Soft Skills",-1),e("option",{value:"business"},"Business",-1),e("option",{value:"career_development"},"Sviluppo Carriera",-1)]),512),[[J,l.value]]),$(e("select",{"onUpdate:modelValue":x[2]||(x[2]=V=>i.value=V),class:"rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},x[8]||(x[8]=[e("option",{value:""},"Tutte le visibilità",-1),e("option",{value:"public"},"Pubblico",-1),e("option",{value:"private"},"Privato",-1),e("option",{value:"team"},"Team",-1)]),512),[[J,i.value]])])]),e("div",zs,[U.value?(t(),r("div",Vs,x[9]||(x[9]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):j.value.length===0?(t(),r("div",Ss,[b(T,{name:"document-text",size:"xl",class:"text-gray-300 dark:text-gray-600 mx-auto mb-4"}),x[10]||(x[10]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"}," Nessun template trovato ",-1)),e("p",Ts,a(A.value||l.value||i.value?"Nessun template corrisponde ai filtri selezionati":"Non ci sono template disponibili"),1)])):(t(),r("div",As,[(t(!0),r(Y,null,Q(j.value,V=>(t(),r("div",{key:V.id,class:"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[e("div",Ps,[$(e("input",{id:`template-${V.id}`,"onUpdate:modelValue":x[3]||(x[3]=K=>S.value=K),value:V.id,type:"checkbox",disabled:B(V.id),class:"mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded disabled:opacity-50"},null,8,Us),[[re,S.value]]),e("div",js,[e("label",{for:`template-${V.id}`,class:"cursor-pointer"},[e("div",Ms,[e("h4",Ns,a(V.title),1),e("div",Es,[V.category?(t(),r("span",Gs,a(G(V.category)),1)):v("",!0),V.priority?(t(),r("span",{key:1,class:ae(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",_(V.priority)])},a(O(V.priority)),3)):v("",!0),e("span",Os," Usato "+a(V.usage_count||0)+" volte ",1)])]),V.description?(t(),r("p",Is,a(V.description),1)):v("",!0),e("div",qs,[V.success_criteria?(t(),r("div",Rs,[b(T,{name:"check-circle",size:"sm",class:"text-green-500 mt-0.5 flex-shrink-0"}),e("span",null,a(V.success_criteria),1)])):v("",!0),V.measurable_outcomes?(t(),r("div",Bs,[b(T,{name:"chart-bar",size:"sm",class:"text-blue-500 mt-0.5 flex-shrink-0"}),e("span",null,a(V.measurable_outcomes),1)])):v("",!0),V.weight?(t(),r("div",Ls,[b(T,{name:"chart-bar",size:"sm",class:"text-yellow-500"}),e("span",null,"Peso: "+a(V.weight),1)])):v("",!0)]),B(V.id)?(t(),r("div",Ys,[b(T,{name:"exclamation-triangle",size:"sm"}),x[11]||(x[11]=e("span",{class:"text-xs"},"Già assegnato per questo anno",-1))])):v("",!0)],8,Ds)])])]))),128))]))]),S.value.length>0?(t(),r("div",Fs,[e("div",Hs,[b(T,{name:"information-circle",size:"md",class:"text-primary-600"}),x[12]||(x[12]=e("h4",{class:"text-sm font-medium text-primary-900 dark:text-primary-100"}," Template Selezionati ",-1))]),e("p",Qs," Hai selezionato "+a(S.value.length)+" template che verranno assegnati a "+a((F=u.employee)==null?void 0:F.full_name)+" per l'anno "+a(u.year)+". ",1),e("div",Js,[(t(!0),r(Y,null,Q(S.value,V=>{var K;return t(),r("div",{key:V,class:"text-xs text-primary-600 dark:text-primary-400"}," • "+a((K=y(V))==null?void 0:K.title),1)}),128))])])):v("",!0)])]}),_:1}))}},er=se(Zs,[["__scopeId","data-v-1ff8cdb6"]]),tr={__name:"CreateGoalModal",props:{employee:{type:Object,required:!0},year:{type:Number,required:!0},templates:{type:Array,default:()=>[]}},emits:["close","goal-created"],setup(u){return(M,f)=>(t(),L(fe,{goal:null,employee:u.employee,year:u.year,templates:u.templates,onClose:f[0]||(f[0]=q=>M.$emit("close")),onGoalSaved:f[1]||(f[1]=q=>M.$emit("goal-created",q))},null,8,["employee","year","templates"]))}},ar={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},sr={class:"flex items-center space-x-3"},rr={class:"h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center"},lr={class:"text-sm font-medium text-primary-700 dark:text-primary-300"},or={class:"text-sm font-medium text-gray-900 dark:text-white"},ir={class:"text-xs text-gray-600 dark:text-gray-400"},nr={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},dr=["value"],ur=["value"],mr=["value"],gr={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},cr={key:0},yr=["value"],vr={key:1,class:"space-y-4"},pr={class:"border-t border-gray-200 dark:border-gray-600 pt-6"},xr={class:"bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4 mb-4"},br={class:"flex items-center space-x-2 mb-3"},fr={class:"text-sm font-medium text-gray-900 dark:text-white"},_r={key:0,class:"space-y-2"},kr=["id","value"],wr=["for"],hr={class:"text-xs text-gray-500 ml-2"},$r={key:1,class:"text-sm text-gray-600 dark:text-gray-400"},Cr={class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"},zr={class:"flex items-center justify-between mb-3"},Vr={class:"flex items-center space-x-2"},Sr={class:"text-sm font-medium text-gray-900 dark:text-white"},Tr={class:"flex items-center space-x-2"},Ar={class:"text-sm text-blue-700 dark:text-blue-300"},Pr={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ur={class:"space-y-2"},jr={class:"flex items-center"},Dr={class:"flex items-center"},Mr={class:"flex justify-between items-center"},Nr={class:"text-sm text-gray-600 dark:text-gray-400"},Er={class:"flex space-x-3"},Gr={__name:"CreateReviewModal",props:{employee:{type:Object,required:!0},year:{type:Number,required:!0}},emits:["close","review-created"],setup(u,{emit:M}){const f=u,q=M,U=z(!1),n=z([]),A=z([]),l=z({review_type:"annual",review_year:f.year,review_period_start:"",review_period_end:"",due_date:"",template_id:"",initial_comments:"",status:"draft",notify_employee:!0,auto_link_goals:!0,create_next_year_goals:!0,previous_year_goals:[]}),i=R(()=>{var G,O;if(!f.employee)return"U";const y=((G=f.employee.full_name)==null?void 0:G.split(" ")[0])||"",o=((O=f.employee.full_name)==null?void 0:O.split(" ")[1])||"";return(y.charAt(0)+o.charAt(0)).toUpperCase()||"U"}),S=R(()=>l.value.review_type&&l.value.review_year&&l.value.review_period_start&&l.value.review_period_end),k=async()=>{try{const y=await H.get("/api/performance/templates");y.data.success&&(n.value=y.data.data.templates||[])}catch(y){console.error("Error loading templates:",y)}},s=async()=>{if(l.value.review_type==="annual")try{const y=l.value.review_year-1,o=await H.get(`/api/performance/employees/${f.employee.id}/goals/${y}`);o.data.success&&(A.value=o.data.data.goals||[])}catch(y){console.error("Error loading previous year goals:",y),A.value=[]}},j=async()=>{if(S.value){U.value=!0;try{const y={employee_id:f.employee.id,review_type:l.value.review_type,review_year:l.value.review_year,review_period_start:l.value.review_period_start,review_period_end:l.value.review_period_end,due_date:l.value.due_date||null,template_id:l.value.template_id||null,status:l.value.status,reviewer_comments:l.value.initial_comments||null,notify_employee:l.value.notify_employee,auto_link_goals:l.value.auto_link_goals,create_next_year_goals:l.value.create_next_year_goals,previous_year_goals:l.value.previous_year_goals},o=await H.post("/api/performance/reviews",y);if(o.data.success)q("review-created",o.data.data.review);else throw new Error(o.data.message||"Errore nella creazione della valutazione")}catch(y){console.error("Error creating review:",y)}finally{U.value=!1}}},p=y=>({draft:"Bozza",in_progress:"In Corso",completed:"Completata",approved:"Approvata"})[y]||y,B=()=>{const y=l.value.review_year;if(l.value.review_type==="annual")l.value.review_period_start=`${y}-01-01`,l.value.review_period_end=`${y}-12-31`,l.value.due_date=`${y+1}-01-31`;else if(l.value.review_type==="quarterly"){const G=Math.floor(new Date().getMonth()/3)+1,O=(G-1)*3+1,_=G*3;l.value.review_period_start=`${y}-${O.toString().padStart(2,"0")}-01`,l.value.review_period_end=`${y}-${_.toString().padStart(2,"0")}-${new Date(y,_,0).getDate()}`,l.value.due_date=`${y}-${(_+1).toString().padStart(2,"0")}-15`}};return oe(async()=>{B(),await k(),await s()}),(y,o)=>(t(),L(me,{show:!0,onClose:o[13]||(o[13]=G=>y.$emit("close")),title:"Nuova Valutazione Performance",size:"xl"},{footer:P(()=>[e("div",Mr,[e("div",Nr," La valutazione verrà creata in stato "+a(p(l.value.status)),1),e("div",Er,[b(E,{variant:"secondary",onClick:o[12]||(o[12]=G=>y.$emit("close"))},{default:P(()=>o[31]||(o[31]=[N(" Annulla ")])),_:1,__:[31]}),b(E,{variant:"primary",disabled:!S.value||U.value,loading:U.value,onClick:j},{default:P(()=>[N(a(U.value?"Creazione...":"Crea Valutazione"),1)]),_:1},8,["disabled","loading"])])])]),default:P(()=>{var G,O;return[e("form",{onSubmit:te(j,["prevent"]),class:"space-y-6"},[e("div",ar,[o[14]||(o[14]=e("h3",{class:"text-sm font-medium text-gray-900 dark:text-white mb-2"},"Valutazione per:",-1)),e("div",sr,[e("div",rr,[e("span",lr,a(i.value),1)]),e("div",null,[e("p",or,a((G=u.employee)==null?void 0:G.full_name),1),e("p",ir,a((O=u.employee)==null?void 0:O.email),1)])])]),e("div",nr,[e("div",null,[o[16]||(o[16]=e("label",{for:"review_type",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tipo Valutazione * ",-1)),$(e("select",{id:"review_type","onUpdate:modelValue":o[0]||(o[0]=_=>l.value.review_type=_),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},o[15]||(o[15]=[e("option",{value:""},"Seleziona tipo",-1),e("option",{value:"annual"},"Valutazione Annuale",-1),e("option",{value:"quarterly"},"Valutazione Trimestrale",-1),e("option",{value:"probation"},"Valutazione Fine Prova",-1),e("option",{value:"project"},"Valutazione Progetto",-1)]),512),[[J,l.value.review_type]])]),e("div",null,[o[17]||(o[17]=e("label",{for:"review_year",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Anno di Riferimento * ",-1)),$(e("select",{id:"review_year","onUpdate:modelValue":o[1]||(o[1]=_=>l.value.review_year=_),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},[e("option",{value:u.year},a(u.year),9,dr),e("option",{value:u.year-1},a(u.year-1),9,ur),e("option",{value:u.year+1},a(u.year+1),9,mr)],512),[[J,l.value.review_year]])])]),e("div",gr,[e("div",null,[o[18]||(o[18]=e("label",{for:"review_period_start",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Inizio Periodo Valutazione * ",-1)),$(e("input",{id:"review_period_start","onUpdate:modelValue":o[2]||(o[2]=_=>l.value.review_period_start=_),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},null,512),[[I,l.value.review_period_start]])]),e("div",null,[o[19]||(o[19]=e("label",{for:"review_period_end",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Fine Periodo Valutazione * ",-1)),$(e("input",{id:"review_period_end","onUpdate:modelValue":o[3]||(o[3]=_=>l.value.review_period_end=_),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},null,512),[[I,l.value.review_period_end]])])]),e("div",null,[o[20]||(o[20]=e("label",{for:"due_date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data Scadenza Completamento ",-1)),$(e("input",{id:"due_date","onUpdate:modelValue":o[4]||(o[4]=_=>l.value.due_date=_),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},null,512),[[I,l.value.due_date]])]),n.value.length>0?(t(),r("div",cr,[o[22]||(o[22]=e("label",{for:"template_id",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Template Valutazione ",-1)),$(e("select",{id:"template_id","onUpdate:modelValue":o[5]||(o[5]=_=>l.value.template_id=_),class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},[o[21]||(o[21]=e("option",{value:""},"Nessun template (valutazione personalizzata)",-1)),(t(!0),r(Y,null,Q(n.value,_=>(t(),r("option",{key:_.id,value:_.id},a(_.name)+" - "+a(_.display_type),9,yr))),128))],512),[[J,l.value.template_id]])])):v("",!0),e("div",null,[o[23]||(o[23]=e("label",{for:"initial_comments",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Commenti Iniziali ",-1)),$(e("textarea",{id:"initial_comments","onUpdate:modelValue":o[6]||(o[6]=_=>l.value.initial_comments=_),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white",placeholder:"Inserisci eventuali commenti o note per questa valutazione"},null,512),[[I,l.value.initial_comments]])]),l.value.review_type==="annual"?(t(),r("div",vr,[e("div",pr,[o[25]||(o[25]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Associazione Obiettivi ",-1)),e("div",xr,[e("div",br,[b(T,{name:"flag",size:"md",class:"text-amber-500"}),e("h4",fr," Obiettivi "+a(l.value.review_year-1)+" da Valutare ",1)]),A.value.length>0?(t(),r("div",_r,[(t(!0),r(Y,null,Q(A.value,_=>(t(),r("div",{key:_.id,class:"flex items-center space-x-3 p-2 bg-white dark:bg-gray-800 rounded border"},[$(e("input",{id:`prev-goal-${_.id}`,"onUpdate:modelValue":o[7]||(o[7]=w=>l.value.previous_year_goals=w),value:_.id,type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,8,kr),[[re,l.value.previous_year_goals]]),e("label",{for:`prev-goal-${_.id}`,class:"flex-1 text-sm text-gray-900 dark:text-white"},[N(a(_.title)+" ",1),e("span",hr,"("+a(_.status)+")",1)],8,wr)]))),128))])):(t(),r("div",$r," Nessun obiettivo trovato per l'anno "+a(l.value.review_year-1),1))]),e("div",Cr,[e("div",zr,[e("div",Vr,[b(T,{name:"target",size:"md",class:"text-blue-500"}),e("h4",Sr," Pianificazione Obiettivi "+a(l.value.review_year+1),1)]),e("div",Tr,[$(e("input",{id:"create_next_year_goals","onUpdate:modelValue":o[8]||(o[8]=_=>l.value.create_next_year_goals=_),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[re,l.value.create_next_year_goals]]),o[24]||(o[24]=e("label",{for:"create_next_year_goals",class:"text-sm text-gray-700 dark:text-gray-300"}," Pianifica obiettivi per l'anno prossimo ",-1))])]),e("p",Ar," Questa valutazione includerà una sezione per definire gli obiettivi per l'anno "+a(l.value.review_year+1),1)])])])):v("",!0),e("div",Pr,[e("div",null,[o[27]||(o[27]=e("label",{for:"status",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Stato Iniziale ",-1)),$(e("select",{id:"status","onUpdate:modelValue":o[9]||(o[9]=_=>l.value.status=_),class:"w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"},o[26]||(o[26]=[e("option",{value:"draft"},"Bozza",-1),e("option",{value:"in_progress"},"In Corso",-1)]),512),[[J,l.value.status]])]),e("div",null,[o[30]||(o[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Azioni Post-Creazione ",-1)),e("div",Ur,[e("label",jr,[$(e("input",{"onUpdate:modelValue":o[10]||(o[10]=_=>l.value.notify_employee=_),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[re,l.value.notify_employee]]),o[28]||(o[28]=e("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"}," Notifica al dipendente ",-1))]),e("label",Dr,[$(e("input",{"onUpdate:modelValue":o[11]||(o[11]=_=>l.value.auto_link_goals=_),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[re,l.value.auto_link_goals]]),o[29]||(o[29]=e("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"}," Collega automaticamente obiettivi dell'anno ",-1))])])])])],32)]}),_:1}))}},Or=se(Gr,[["__scopeId","data-v-410e0627"]]),Ir={class:"space-y-6"},qr={key:0,class:"flex justify-center items-center h-64"},Rr={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},Br={class:"flex"},Lr={class:"text-sm text-red-700 dark:text-red-300 mt-1"},Yr={key:2,class:"space-y-6"},Fr={class:"flex items-center space-x-2"},Hr=["value"],Qr={key:0,class:"flex space-x-2"},Jr={class:"mt-6"},Kr={key:0},Wr={key:1},Xr={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Zr={class:"flex items-center justify-between mb-6"},el={class:"text-lg font-medium text-gray-900 dark:text-white"},tl={key:0,class:"space-y-4"},al=["onClick"],sl={class:"flex items-center justify-between"},rl={class:"font-medium text-gray-900 dark:text-white"},ll={class:"text-sm text-gray-600 dark:text-gray-400 mt-1"},ol={class:"flex items-center space-x-4 mt-2"},il={key:0,class:"text-sm text-gray-600 dark:text-gray-400"},nl={key:1,class:"text-sm text-gray-600 dark:text-gray-400"},dl={key:1,class:"text-center py-8"},ul={class:"text-gray-600 dark:text-gray-400 mb-4"},ml={key:2},gl={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},cl={class:"text-lg font-medium text-gray-900 dark:text-white mb-6"},yl={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},vl={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},pl={key:0},xl={class:"space-y-3"},bl={class:"flex justify-between items-center"},fl={class:"font-medium"},_l={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},kl={class:"flex justify-between text-sm text-gray-600 dark:text-gray-400"},wl={key:0},hl={class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},$l={key:0,class:"space-y-3"},Cl={class:"text-sm text-gray-600 dark:text-gray-400 capitalize"},zl={class:"flex items-center space-x-2"},Vl={class:"text-sm font-medium"},Sl={key:0,class:"text-xs text-gray-500"},Tl={key:3},Al={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Pl={class:"space-y-6"},Ul={class:"flex items-center justify-between mb-3"},jl={class:"font-medium text-gray-900 dark:text-white"},Dl={key:0,class:"grid grid-cols-2 md:grid-cols-4 gap-4"},Ml={class:"text-center"},Nl={class:"text-lg font-semibold text-gray-900 dark:text-white"},El={class:"text-center"},Gl={class:"text-lg font-semibold text-green-600"},Ol={class:"text-center"},Il={class:"text-lg font-semibold text-gray-900 dark:text-white"},ql={class:"text-center"},Rl={class:"text-lg font-semibold text-yellow-600"},Bl={key:1,class:"text-sm text-gray-600 dark:text-gray-400"},Ll={key:2,class:"text-sm text-gray-500 dark:text-gray-500"},Yl={key:0,class:"text-center py-8"},Fl={__name:"PersonnelPerformancePersonal",setup(u){const M=Pe(),f=Ue(),{hasPermission:q}=je(),U=De(),n=z(!1),A=z(null),l=z("goals"),i=z(new Date().getFullYear()),S=z(null),k=z(null),s=z([]),j=z([]),p=z([]),B=z(!1),y=z({}),o=z(!1),G=z(!1),O=z(!1),_=R(()=>parseInt(M.params.id)),w=R(()=>parseInt(M.params.year)||new Date().getFullYear()),x=R(()=>U.user&&_.value===U.user.id),D=R(()=>q.value("manage_personnel")||q.value("admin_access")),F=R(()=>{const g=new Date().getFullYear();return[g-2,g-1,g,g+1]}),V=R(()=>[{id:"goals",name:"Obiettivi",count:s.value.length,icon:"target"},{id:"reviews",name:"Valutazioni",count:j.value.length,icon:"star"},{id:"analytics",name:"Analytics",icon:"chart-bar"},{id:"history",name:"Storico",icon:"clock"}]),K=async()=>{n.value=!0,A.value=null;try{await Promise.all([C(),m(),X(),Z()])}catch(g){console.error("Error loading performance data:",g),A.value=g.message||"Errore nel caricamento dei dati"}finally{n.value=!1}},C=async()=>{B.value=!0;try{const g=await H.get(`/api/performance/employees/${_.value}/goals/${i.value}`);g.data.success&&(s.value=g.data.data.goals||[],S.value=g.data.data.employee)}catch(g){console.error("Error loading goals:",g)}finally{B.value=!1}},m=async()=>{try{const g=await H.get(`/api/performance/employees/${_.value}/summary/${i.value}`);g.data.success&&(k.value=g.data.data)}catch(g){console.error("Error loading summary:",g)}},X=async()=>{try{const g=await H.get(`/api/performance/reviews?employee_id=${_.value}&year=${i.value}`);g.data.success&&(j.value=g.data.data.reviews||[])}catch(g){console.error("Error loading reviews:",g),j.value=[]}},Z=async()=>{if(D.value)try{const g=await H.get("/api/performance/templates/goals");g.data.success&&(p.value=g.data.data.templates||[])}catch(g){console.error("Error loading templates:",g),p.value=[]}},c=async()=>{p.value.length===0&&await Z(),G.value=!0},W=async()=>{await C(),await m()},_e=()=>{l.value="goals"},ke=()=>{l.value="reviews"},we=g=>{f.push(`/app/personnel/performance/reviews/${g.id}`)},he=g=>{i.value=g,f.push(`/app/personnel/${_.value}/performance/${g}`)},ie=()=>{const g=new Date().getFullYear();return[g-2,g-1,g].filter(d=>d!==i.value)},$e=async g=>{if(!y.value[g])try{const d=await H.get(`/api/performance/employees/${_.value}/summary/${g}`);d.data.success&&(y.value[g]=d.data.data)}catch(d){console.error(`Error loading history for year ${g}:`,d),y.value[g]=null}},Ce=async()=>{const g=ie();await Promise.all(g.map(d=>$e(d)))},ze=g=>{o.value=!1,W()},Ve=g=>{G.value=!1,W()},Se=g=>{O.value=!1,X()},ne=g=>g?new Date(g).toLocaleDateString("it-IT"):"",Te=g=>({annual:"Annuale",quarterly:"Trimestrale",probation:"Fine Prova",project:"Progetto"})[g]||g,Ae=g=>({technical:"Tecniche",soft_skills:"Soft Skills",business:"Business",career_development:"Sviluppo Carriera"})[g]||g;return oe(()=>{i.value=w.value,K()}),le(()=>M.params,()=>{i.value=w.value,K()}),le(i,()=>{M.params.year!==i.value.toString()&&f.push(`/app/personnel/${_.value}/performance/${i.value}`),K()}),le(l,g=>{g==="history"&&Ce()}),(g,d)=>{var ge,ce,ye;return t(),r("div",Ir,[n.value?(t(),r("div",qr,d[9]||(d[9]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"},null,-1)]))):A.value?(t(),r("div",Rr,[e("div",Br,[b(T,{name:"x-circle",size:"md",class:"text-red-400 mr-2 mt-0.5"}),e("div",null,[d[10]||(d[10]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento",-1)),e("p",Lr,a(A.value),1)])])])):(t(),r("div",Yr,[b(Me,{title:`Performance ${((ge=S.value)==null?void 0:ge.full_name)||"Dipendente"}`,subtitle:`Gestione obiettivi e valutazioni per l'anno ${i.value}`,icon:"chart-line"},{actions:P(()=>[e("div",Fr,[d[11]||(d[11]=e("label",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Anno:",-1)),$(e("select",{"onUpdate:modelValue":d[0]||(d[0]=h=>i.value=h),onChange:K,class:"rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm"},[(t(!0),r(Y,null,Q(F.value,h=>(t(),r("option",{key:h,value:h},a(h),9,Hr))),128))],544),[[J,i.value]])]),D.value?(t(),r("div",Qr,[b(E,{variant:"secondary",icon:"plus",onClick:d[1]||(d[1]=h=>o.value=!0)},{default:P(()=>d[12]||(d[12]=[N(" Nuovo Obiettivo ")])),_:1,__:[12]}),b(E,{variant:"secondary",icon:"document-plus",onClick:c},{default:P(()=>d[13]||(d[13]=[N(" Assegna Template ")])),_:1,__:[13]}),b(E,{variant:"primary",icon:"star",onClick:d[2]||(d[2]=h=>O.value=!0)},{default:P(()=>d[14]||(d[14]=[N(" Nuova Valutazione ")])),_:1,__:[14]})])):v("",!0)]),_:1},8,["title","subtitle"]),k.value?(t(),L(Bt,{key:0,summary:k.value,employee:S.value,year:i.value,onViewGoals:_e,onViewReviews:ke},null,8,["summary","employee","year"])):v("",!0),b(Ne,{tabs:V.value,"active-tab":l.value,onTabChange:d[3]||(d[3]=h=>l.value=h)},null,8,["tabs","active-tab"]),e("div",Jr,[l.value==="goals"?(t(),r("div",Kr,[b(vs,{goals:s.value,templates:p.value,employee:S.value,year:i.value,"can-edit":D.value||x.value,loading:B.value,onGoalUpdated:W,onGoalCreated:W,onGoalDeleted:W,onAssignTemplates:c},null,8,["goals","templates","employee","year","can-edit","loading"])])):l.value==="reviews"?(t(),r("div",Wr,[e("div",Xr,[e("div",Zr,[e("h3",el," Valutazioni "+a(i.value),1),D.value?(t(),L(E,{key:0,variant:"primary",icon:"plus",size:"sm",onClick:d[4]||(d[4]=h=>O.value=!0)},{default:P(()=>d[15]||(d[15]=[N(" Nuova Valutazione ")])),_:1,__:[15]})):v("",!0)]),j.value.length>0?(t(),r("div",tl,[(t(!0),r(Y,null,Q(j.value,h=>(t(),r("div",{key:h.id,class:"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors",onClick:ee=>we(h)},[e("div",sl,[e("div",null,[e("h4",rl," Valutazione "+a(h.review_year)+" - "+a(Te(h.review_type)),1),e("p",ll," Periodo: "+a(ne(h.review_period_start))+" - "+a(ne(h.review_period_end)),1),e("div",ol,[b(de,{status:h.status,type:"review"},null,8,["status"]),h.overall_rating?(t(),r("span",il," Rating: "+a(h.overall_rating)+"/5 ",1)):v("",!0),h.due_date?(t(),r("span",nl," Scadenza: "+a(ne(h.due_date)),1)):v("",!0)])]),b(T,{name:"chevron-right",size:"md",class:"text-gray-400"})])],8,al))),128))])):(t(),r("div",dl,[b(T,{name:"document-text",size:"xl",class:"text-gray-400 mx-auto mb-4"}),d[17]||(d[17]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"}," Nessuna valutazione trovata ",-1)),e("p",ul," Non ci sono valutazioni per l'anno "+a(i.value),1),D.value?(t(),L(E,{key:0,variant:"primary",icon:"plus",onClick:d[5]||(d[5]=h=>O.value=!0)},{default:P(()=>d[16]||(d[16]=[N(" Crea Prima Valutazione ")])),_:1,__:[16]})):v("",!0)]))])])):l.value==="analytics"?(t(),r("div",ml,[e("div",gl,[e("h3",cl," Analytics Performance "+a(i.value),1),e("div",yl,[e("div",vl,[d[19]||(d[19]=e("h4",{class:"font-medium text-gray-900 dark:text-white mb-4"},"Progresso Obiettivi",-1)),(ce=k.value)!=null&&ce.goals_stats?(t(),r("div",pl,[e("div",xl,[e("div",bl,[d[18]||(d[18]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Completati",-1)),e("span",fl,a(k.value.goals_stats.completed)+"/"+a(k.value.goals_stats.total),1)]),e("div",_l,[e("div",{class:"bg-green-600 h-2 rounded-full",style:ue({width:`${k.value.goals_stats.completed/k.value.goals_stats.total*100}%`})},null,4)]),e("div",kl,[e("span",null,"Progresso medio: "+a(k.value.goals_stats.avg_progress)+"%",1),k.value.goals_stats.avg_rating?(t(),r("span",wl," Rating medio: "+a(k.value.goals_stats.avg_rating)+"/5 ",1)):v("",!0)])])])):v("",!0)]),e("div",hl,[d[20]||(d[20]=e("h4",{class:"font-medium text-gray-900 dark:text-white mb-4"},"Obiettivi per Categoria",-1)),(ye=k.value)!=null&&ye.categories_stats?(t(),r("div",$l,[(t(!0),r(Y,null,Q(k.value.categories_stats,(h,ee)=>(t(),r("div",{key:ee,class:"flex justify-between items-center"},[e("span",Cl,a(Ae(ee)),1),e("div",zl,[e("span",Vl,a(h.completed)+"/"+a(h.total),1),h.avg_rating?(t(),r("span",Sl," ("+a(h.avg_rating)+"/5) ",1)):v("",!0)])]))),128))])):v("",!0)])])])])):l.value==="history"?(t(),r("div",Tl,[e("div",Al,[d[28]||(d[28]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-6"}," Storico Performance ",-1)),e("div",Pl,[(t(!0),r(Y,null,Q(ie(),h=>{var ee,ve,pe,xe;return t(),r("div",{key:h,class:"border-b border-gray-200 dark:border-gray-600 pb-4 last:border-b-0"},[e("div",Ul,[e("h4",jl,"Anno "+a(h),1),b(E,{variant:"ghost",size:"sm",onClick:Hl=>he(h)},{default:P(()=>d[21]||(d[21]=[N(" Visualizza Dettaglio ")])),_:2,__:[21]},1032,["onClick"])]),y.value[h]?(t(),r("div",Dl,[e("div",Ml,[e("div",Nl,a(((ee=y.value[h].goals_stats)==null?void 0:ee.total)||0),1),d[22]||(d[22]=e("div",{class:"text-xs text-gray-600 dark:text-gray-400"},"Obiettivi Totali",-1))]),e("div",El,[e("div",Gl,a(((ve=y.value[h].goals_stats)==null?void 0:ve.completed)||0),1),d[23]||(d[23]=e("div",{class:"text-xs text-gray-600 dark:text-gray-400"},"Completati",-1))]),e("div",Ol,[e("div",Il,a(((pe=y.value[h].reviews_stats)==null?void 0:pe.total)||0),1),d[24]||(d[24]=e("div",{class:"text-xs text-gray-600 dark:text-gray-400"},"Valutazioni",-1))]),e("div",ql,[e("div",Rl,a(((xe=y.value[h].reviews_stats)==null?void 0:xe.avg_overall_rating)||"N/A"),1),d[25]||(d[25]=e("div",{class:"text-xs text-gray-600 dark:text-gray-400"},"Rating Medio",-1))])])):y.value[h]===void 0?(t(),r("div",Bl," Caricamento dati storici per "+a(h)+"... ",1)):(t(),r("div",Ll," Nessun dato disponibile per "+a(h),1))])}),128)),ie().length===0?(t(),r("div",Yl,[b(T,{name:"clock",size:"xl",class:"text-gray-300 dark:text-gray-600 mx-auto mb-4"}),d[26]||(d[26]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"}," Nessuno storico disponibile ",-1)),d[27]||(d[27]=e("p",{class:"text-gray-600 dark:text-gray-400"}," Lo storico performance sarà disponibile per gli anni precedenti. ",-1))])):v("",!0)])])])):v("",!0)])])),o.value?(t(),L(tr,{key:3,employee:S.value,year:i.value,onClose:d[6]||(d[6]=h=>o.value=!1),onGoalCreated:ze},null,8,["employee","year"])):v("",!0),G.value?(t(),L(er,{key:4,employee:S.value,year:i.value,templates:p.value,onClose:d[7]||(d[7]=h=>G.value=!1),onTemplatesAssigned:Ve},null,8,["employee","year","templates"])):v("",!0),O.value?(t(),L(Or,{key:5,employee:S.value,year:i.value,onClose:d[8]||(d[8]=h=>O.value=!1),onReviewCreated:Se},null,8,["employee","year"])):v("",!0)])}}},so=se(Fl,[["__scopeId","data-v-af54501c"]]);export{so as default};
