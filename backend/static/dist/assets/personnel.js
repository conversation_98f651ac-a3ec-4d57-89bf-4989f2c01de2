import{d as C,r as i,c as w}from"./vendor.js";import{c as f}from"./app.js";const j=C("personnel",()=>{const c=i([]),m=i([]),g=i([]),d=i(null),t=i(!1),l=i(null),o=i({search:"",department:null,skill:null,role:null,location:null,sort:"name"}),p=i({page:1,per_page:20,total:0,pages:0}),E=w(()=>{let a=c.value;if(o.value.search){const s=o.value.search.toLowerCase();a=a.filter(e=>{var r,n,u;return((r=e.full_name)==null?void 0:r.toLowerCase().includes(s))||((n=e.email)==null?void 0:n.toLowerCase().includes(s))||((u=e.position)==null?void 0:u.toLowerCase().includes(s))})}return o.value.department&&(a=a.filter(s=>s.department_id===o.value.department)),o.value.skill&&(a=a.filter(s=>{var e;return(e=s.skills)==null?void 0:e.some(r=>r.id===o.value.skill)})),o.value.role&&(a=a.filter(s=>s.role===o.value.role)),a}),y=w(()=>{const a=(s=null)=>m.value.filter(e=>e.parent_id===s).map(e=>({...e,children:a(e.id)}));return a()}),U=async(a={})=>{var s,e;t.value=!0,l.value=null;try{const r=new URLSearchParams({page:a.page||p.value.page,per_page:a.per_page||p.value.per_page,...a}),n=await f.get("/api/personnel/users",{params:a});if(!((s=n.data)!=null&&s.success))throw new Error(((e=n.data)==null?void 0:e.message)||"Errore nel caricamento utenti");const u=n.data;if(u.success){c.value=u.data.users||[];const v=u.data.pagination||{};p.value={page:v.page||1,per_page:v.per_page||20,total:v.total||0,pages:v.pages||0}}else throw new Error(u.message||"Errore nel caricamento utenti")}catch(r){l.value=r.message,console.error("Errore fetchUsers:",r)}finally{t.value=!1}},k=async a=>{var s,e;t.value=!0,l.value=null;try{const r=await f.get(`/api/personnel/users/${a}`);if(r.data.success)return d.value=r.data.data.user,r.data.data.user;throw new Error(r.data.message||"Errore nel caricamento utente")}catch(r){throw l.value=((e=(s=r.response)==null?void 0:s.data)==null?void 0:e.message)||r.message||"Errore nel caricamento utente",console.error("Errore fetchUser:",r),r}finally{t.value=!1}},_=async(a,s)=>{var e;t.value=!0,l.value=null;try{const r=await fetch(`/api/personnel/users/${a}`,{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(s)});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const n=await r.json();if(n.success){const u=c.value.findIndex(v=>v.id===a);return u!==-1&&(c.value[u]={...c.value[u],...n.data.user}),((e=d.value)==null?void 0:e.id)===a&&(d.value={...d.value,...n.data.user}),n.data.user}else throw new Error(n.message||"Errore nell'aggiornamento utente")}catch(r){throw l.value=r.message,console.error("Errore updateUser:",r),r}finally{t.value=!1}},P=async()=>{var a,s;t.value=!0,l.value=null;try{const e=await f.get("/api/personnel/departments");if(e.data.success)m.value=e.data.data.departments||[];else throw new Error(e.data.message||"Errore nel caricamento dipartimenti")}catch(e){l.value=((s=(a=e.response)==null?void 0:a.data)==null?void 0:s.message)||e.message||"Errore nel caricamento dipartimenti",console.error("Errore fetchDepartments:",e)}finally{t.value=!1}},T=async()=>{var a,s;t.value=!0,l.value=null;try{const e=await f.get("/api/personnel/skills");if(e.data.success)g.value=e.data.data.skills||[];else throw new Error(e.data.message||"Errore nel caricamento competenze")}catch(e){l.value=((s=(a=e.response)==null?void 0:a.data)==null?void 0:s.message)||e.message||"Errore nel caricamento competenze",console.error("Errore fetchSkills:",e)}finally{t.value=!1}},S=async()=>{var a,s,e;if(!((a=c.value)!=null&&a.length)){t.value=!0,l.value=null;try{const r=await f.get("/api/personnel/users?per_page=null");if(r.data.success)c.value=r.data.data.users||[];else throw new Error(r.data.message||"Errore nel caricamento utenti")}catch(r){l.value=((e=(s=r.response)==null?void 0:s.data)==null?void 0:e.message)||r.message||"Errore nel caricamento utenti",console.error("Errore loadAllUsers:",r)}finally{t.value=!1}}},$=(a,s)=>{o.value[a]=s},h=()=>{o.value={search:"",department:null,skill:null,role:null,location:null,sort:"name"}};return{users:c,departments:m,skills:g,currentUser:d,loading:t,error:l,filters:o,pagination:p,filteredUsers:E,departmentTree:y,fetchUsers:U,fetchUser:k,updateUser:_,fetchDepartments:P,fetchSkills:T,loadAllUsers:S,setFilter:$,clearFilters:h,setPagination:(a,s=null)=>{p.value.page=a,s&&(p.value.per_page=s)},$reset:()=>{c.value=[],m.value=[],g.value=[],d.value=null,t.value=!1,l.value=null,h(),p.value={page:1,per_page:20,total:0,pages:0}}}});export{j as u};
