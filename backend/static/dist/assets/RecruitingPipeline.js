import{r as g,c as oe,x as ne,b as o,j as e,l as B,e as l,F as m,p as v,B as y,C as U,v as N,H as S,I as ie,A,q as le,o as n,n as $,t as i}from"./vendor.js";import{u as de}from"./recruiting.js";import{u as ce}from"./useDebounce.js";import{_ as ue,H as k,d as ge}from"./app.js";import{S as p}from"./StandardButton.js";import{A as pe}from"./ActionButtonGroup.js";const be={class:"recruiting-pipeline"},xe={class:"mb-6"},me={class:"flex items-center justify-between"},ve={class:"flex items-center space-x-3"},ye={class:"grid grid-cols-1 md:grid-cols-5 gap-4 mb-6"},fe={class:"flex items-center"},_e={class:"flex-shrink-0"},ke={class:"ml-3"},he={class:"text-sm font-medium text-gray-500 dark:text-gray-400"},we={class:"text-lg font-semibold text-gray-900 dark:text-white"},Ce={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-6"},Se={class:"flex flex-wrap items-center gap-4"},Ae={class:"flex-1 min-w-64"},je=["value"],ze={key:0,class:"flex justify-center py-12"},Ie={key:1,class:"grid grid-cols-1 lg:grid-cols-5 gap-6"},Ne={class:"flex items-center justify-between mb-6 p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm"},$e={class:"flex items-center space-x-3"},Ve={class:"text-sm font-semibold text-gray-900 dark:text-white"},Pe={class:"text-xs text-gray-500 dark:text-gray-400"},De={class:"text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-1 rounded-full font-medium"},qe={class:"space-y-4",style:{"min-height":"300px"}},Ee=["onClick"],Me={class:"flex items-start justify-between mb-3"},Re={class:"flex items-center space-x-3"},Be={class:"flex-shrink-0 h-12 w-12 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center shadow-md"},Ue={class:"text-sm font-bold text-white"},Te={class:"flex-1 min-w-0"},Fe={class:"text-base font-semibold text-gray-900 dark:text-white truncate"},Le={class:"text-sm text-gray-600 dark:text-gray-400 truncate"},He={class:"mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"},Oe={class:"flex items-center space-x-2"},Ge={class:"text-sm font-medium text-gray-900 dark:text-white"},Je={class:"flex items-center space-x-2 mt-1"},Ke={class:"text-xs text-gray-500 dark:text-gray-400"},Qe={class:"grid grid-cols-2 gap-3 mb-3"},We={class:"text-center p-2 bg-blue-50 dark:bg-blue-900/20 rounded-md"},Xe={class:"text-sm font-semibold text-gray-900 dark:text-white"},Ye={key:0,class:"text-center p-2 bg-green-50 dark:bg-green-900/20 rounded-md"},Ze={class:"text-sm font-semibold text-gray-900 dark:text-white"},et={key:1,class:"text-center p-2 bg-gray-50 dark:bg-gray-700 rounded-md"},tt={class:"border-t border-gray-200 dark:border-gray-700 pt-3 space-y-3"},st={class:"flex items-center justify-center space-x-2"},at={class:"flex items-center justify-center"},rt={key:0,class:"text-center py-8"},ot={class:"mt-3"},nt={class:"space-y-4"},it=["value"],lt=["value"],dt={class:"flex justify-end space-x-3 mt-6"},ct={__name:"RecruitingPipeline",setup(ut){const j=le(),b=de(),V=ge(),h=g(!1),u=g([]),P=g([]),z=g([]),f=g(!1),d=g({search:"",job_posting_id:"",status:""}),c=g({candidate_id:"",job_posting_id:"",cover_letter:""}),x=[{id:"application_received",label:"Ricevute",icon:"inbox-arrow-down",iconClass:"text-info-600"},{id:"screening",label:"Screening",icon:"document-magnifying-glass",iconClass:"text-warning-600"},{id:"interview_1",label:"Primo Colloquio",icon:"users",iconClass:"text-brand-secondary-600"},{id:"interview_2",label:"Secondo Colloquio",icon:"chat-bubble-left-ellipsis",iconClass:"text-brand-primary-600"},{id:"offer",label:"Offerta",icon:"check-circle",iconClass:"text-success-600"}],T=oe(()=>[{label:"Totale Candidature",value:(u.value||[]).length,icon:"document-text",iconClass:"text-info-500"},{label:"In Attesa",value:(u.value||[]).filter(s=>s.status==="pending").length,icon:"clock",iconClass:"text-warning-500"},{label:"In Corso",value:(u.value||[]).filter(s=>s.status==="in_progress").length,icon:"arrow-path",iconClass:"text-brand-secondary-500"},{label:"Completate",value:(u.value||[]).filter(s=>s.status==="completed").length,icon:"check-circle",iconClass:"text-success-500"},{label:"Score Medio",value:O(),icon:"star",iconClass:"text-warning-500"}]),w=async()=>{h.value=!0;try{await Promise.all([_(),F(),L()])}finally{h.value=!1}},_=async()=>{const s={...d.value};Object.keys(s).forEach(a=>{s[a]||delete s[a]});const t=await b.fetchApplications(s);u.value=(t==null?void 0:t.applications)||[]},F=async()=>{const s=await b.fetchCandidates();P.value=(s==null?void 0:s.candidates)||[]},L=async()=>{const s=await b.fetchJobPostings({status:"active"});z.value=(s==null?void 0:s.job_postings)||[]},I=s=>(u.value||[]).filter(t=>t.current_step===s),H=s=>I(s).length,O=()=>{const s=(u.value||[]).filter(a=>a.overall_score);return s.length===0?"N/A":(s.reduce((a,r)=>a+r.overall_score,0)/s.length).toFixed(1)},G=async s=>{const t=x.findIndex(a=>a.id===s.current_step);if(t<x.length-1){const a=x[t+1];try{await b.updateApplicationStatus(s.id,null,a.id),await _()}catch(r){console.error("Error advancing step:",r),V.error("Errore nell'avanzamento dello step")}}},J=s=>{j.push(`/app/recruiting/interviews/new?application_id=${s.id}&candidate_id=${s.candidate_id}`)},K=s=>{j.push(`/app/recruiting/applications/${s.id}`)},Q=s=>{j.push(`/app/recruiting/applications/${s.id}`)},W=async()=>{try{await b.createApplication(c.value),f.value=!1,c.value={candidate_id:"",job_posting_id:"",cover_letter:""},await _()}catch(s){console.error("Error creating application:",s),V.error("Errore nella creazione della candidatura")}},X=()=>{d.value={search:"",job_posting_id:"",status:""},_()},D=ce(()=>{_()},300),Y=s=>{if(!s)return"N/A";const t=s.first_name||"",a=s.last_name||"";return`${t.charAt(0)}${a.charAt(0)}`.toUpperCase()},Z=s=>({pending:"In Attesa",in_progress:"In Corso",completed:"Completata",rejected:"Rifiutata"})[s]||s,ee=s=>({pending:"bg-warning-100 text-warning-800 dark:bg-warning-900/20 dark:text-warning-400",in_progress:"bg-brand-secondary-100 text-brand-secondary-800 dark:bg-brand-secondary-900/20 dark:text-brand-secondary-400",completed:"bg-success-100 text-success-800 dark:bg-success-900/20 dark:text-success-400",rejected:"bg-danger-100 text-danger-800 dark:bg-danger-900/20 dark:text-danger-400"})[s]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",te=s=>s?new Date(s).toLocaleDateString("it-IT",{day:"numeric",month:"short"}):"",se=s=>{const t=x[x.length-1].id;return s===t},ae=s=>({application_received:"bg-blue-500",screening:"bg-amber-500",interview_1:"bg-purple-500",interview_2:"bg-indigo-500",offer:"bg-green-500"})[s]||"bg-gray-500",re=s=>({application_received:"Candidature appena ricevute",screening:"Valutazione iniziale CV",interview_1:"Primo colloquio tecnico",interview_2:"Colloquio finale",offer:"Offerte di lavoro"})[s]||"";return ne(()=>{w()}),(s,t)=>(n(),o("div",be,[e("div",xe,[e("div",me,[t[11]||(t[11]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"}," Pipeline Candidature "),e("p",{class:"text-sm text-gray-600 dark:text-gray-400"}," Gestione visuale del flusso di candidature attraverso i vari step del processo ")],-1)),e("div",ve,[l(p,{variant:"outline-primary",icon:"arrow-path",text:"Aggiorna",onClick:w,loading:h.value,size:"sm"},null,8,["loading"]),l(p,{variant:"primary",icon:"plus",text:"Nuova Candidatura",onClick:t[0]||(t[0]=a=>f.value=!0)})])])]),e("div",ye,[(n(!0),o(m,null,v(T.value,(a,r)=>(n(),o("div",{key:r,class:"bg-white dark:bg-gray-800 rounded-lg shadow p-4"},[e("div",fe,[e("div",_e,[l(k,{name:a.icon,size:"md",class:$(a.iconClass)},null,8,["name","class"])]),e("div",ke,[e("p",he,i(a.label),1),e("p",we,i(a.value),1)])])]))),128))]),e("div",Ce,[e("div",Se,[e("div",Ae,[y(e("input",{"onUpdate:modelValue":t[1]||(t[1]=a=>d.value.search=a),type:"text",placeholder:"Cerca candidati...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",onInput:t[2]||(t[2]=(...a)=>N(D)&&N(D)(...a))},null,544),[[U,d.value.search]])]),y(e("select",{"onUpdate:modelValue":t[3]||(t[3]=a=>d.value.job_posting_id=a),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",onChange:w},[t[12]||(t[12]=e("option",{value:""},"Tutte le posizioni",-1)),(n(!0),o(m,null,v(z.value,a=>(n(),o("option",{key:a.id,value:a.id},i(a.title),9,je))),128))],544),[[S,d.value.job_posting_id]]),y(e("select",{"onUpdate:modelValue":t[4]||(t[4]=a=>d.value.status=a),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",onChange:w},t[13]||(t[13]=[ie('<option value="" data-v-fb686089>Tutti gli stati</option><option value="pending" data-v-fb686089>In Attesa</option><option value="in_progress" data-v-fb686089>In Corso</option><option value="completed" data-v-fb686089>Completate</option><option value="rejected" data-v-fb686089>Rifiutate</option>',5)]),544),[[S,d.value.status]]),l(p,{variant:"outline-secondary",icon:"funnel",text:"Reset Filtri",size:"sm",onClick:X})])]),h.value?(n(),o("div",ze,t[14]||(t[14]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary-600"},null,-1)]))):(n(),o("div",Ie,[(n(),o(m,null,v(x,a=>e("div",{key:a.id,class:"bg-gray-50 dark:bg-gray-900 rounded-xl p-4 border border-gray-200 dark:border-gray-700"},[e("div",Ne,[e("div",$e,[e("div",{class:$(["p-2 rounded-lg",ae(a.id)])},[l(k,{name:a.icon,size:"sm",class:"text-white"},null,8,["name"])],2),e("div",null,[e("h3",Ve,i(a.label),1),e("p",Pe,i(re(a.id)),1)])]),e("span",De,i(H(a.id)),1)]),e("div",qe,[(n(!0),o(m,null,v(I(a.id),r=>{var q,E,M,R;return n(),o("div",{key:r.id,class:"bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 cursor-pointer hover:shadow-lg hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-200 transform hover:-translate-y-1",onClick:C=>Q(r)},[e("div",Me,[e("div",Re,[e("div",Be,[e("span",Ue,i(Y(r.candidate)),1)]),e("div",Te,[e("p",Fe,i(((q=r.candidate)==null?void 0:q.full_name)||"N/A"),1),e("p",Le,i(((E=r.candidate)==null?void 0:E.email)||"N/A"),1)])]),e("span",{class:$(["inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium whitespace-nowrap",ee(r.status)])},i(Z(r.status)),3)]),e("div",He,[e("div",Oe,[l(k,{name:"briefcase",size:"sm",class:"text-gray-500 dark:text-gray-400"}),e("span",Ge,i(((M=r.job_posting)==null?void 0:M.title)||"N/A"),1)]),e("div",Je,[l(k,{name:"map-pin",size:"xs",class:"text-gray-400"}),e("span",Ke,i(((R=r.job_posting)==null?void 0:R.location)||"N/A"),1)])]),e("div",Qe,[e("div",We,[t[15]||(t[15]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"Data",-1)),e("p",Xe,i(te(r.applied_at)),1)]),r.overall_score?(n(),o("div",Ye,[t[16]||(t[16]=e("p",{class:"text-xs text-green-600 dark:text-green-400 font-medium"},"Score",-1)),e("p",Ze,i(r.overall_score)+"/10 ",1)])):(n(),o("div",et,t[17]||(t[17]=[e("p",{class:"text-xs text-gray-500 dark:text-gray-400 font-medium"},"Score",-1),e("p",{class:"text-sm font-semibold text-gray-500 dark:text-gray-400"},"N/A",-1)])))]),e("div",tt,[e("div",st,[l(p,{variant:"outline-primary",icon:"arrow-right",size:"sm",text:"Avanza",onClick:A(C=>G(r),["stop"]),disabled:se(r.current_step)},null,8,["onClick","disabled"]),l(p,{variant:"outline-secondary",icon:"calendar",size:"sm",text:"Colloquio",onClick:A(C=>J(r),["stop"])},null,8,["onClick"])]),e("div",at,[l(pe,{"show-delete":!1,"show-labels":!1,onView:C=>s.$router.push(`/app/recruiting/applications/${r.id}`),onEdit:C=>K(r)},null,8,["onView","onEdit"])])])],8,Ee)}),128)),I(a.id).length===0?(n(),o("div",rt,[l(k,{name:"inbox",size:"lg",class:"mx-auto text-gray-400 mb-2"}),t[18]||(t[18]=e("p",{class:"text-sm text-gray-500 dark:text-gray-400"},"Nessuna candidatura",-1))])):B("",!0)])])),64))])),f.value?(n(),o("div",{key:2,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:t[10]||(t[10]=a=>f.value=!1)},[e("div",{class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[9]||(t[9]=A(()=>{},["stop"]))},[e("div",ot,[t[24]||(t[24]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Nuova Candidatura ",-1)),e("form",{onSubmit:A(W,["prevent"])},[e("div",nt,[e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Candidato * ",-1)),y(e("select",{"onUpdate:modelValue":t[5]||(t[5]=a=>c.value.candidate_id=a),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[19]||(t[19]=e("option",{value:""},"Seleziona candidato",-1)),(n(!0),o(m,null,v(P.value,a=>(n(),o("option",{key:a.id,value:a.id},i(a.full_name)+" - "+i(a.email),9,it))),128))],512),[[S,c.value.candidate_id]])]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Posizione * ",-1)),y(e("select",{"onUpdate:modelValue":t[6]||(t[6]=a=>c.value.job_posting_id=a),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[21]||(t[21]=e("option",{value:""},"Seleziona posizione",-1)),(n(!0),o(m,null,v(z.value,a=>(n(),o("option",{key:a.id,value:a.id},i(a.title)+" - "+i(a.location),9,lt))),128))],512),[[S,c.value.job_posting_id]])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Lettera di Motivazione ",-1)),y(e("textarea",{"onUpdate:modelValue":t[7]||(t[7]=a=>c.value.cover_letter=a),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Motivazione del candidato..."},null,512),[[U,c.value.cover_letter]])])]),e("div",dt,[l(p,{variant:"secondary",text:"Annulla",onClick:t[8]||(t[8]=a=>f.value=!1)}),l(p,{variant:"primary",text:"Crea Candidatura",loading:N(b).loading,type:"submit"},null,8,["loading"])])],32)])])])):B("",!0)]))}},yt=ue(ct,[["__scopeId","data-v-fb686089"]]);export{yt as default};
