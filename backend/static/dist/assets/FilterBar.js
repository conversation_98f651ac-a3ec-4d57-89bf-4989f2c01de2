import{H as y}from"./app.js";import{c as v,b as a,o as l,K as k,l as g,F as o,p as u,j as d,t as i,s as m,n as b,e as p}from"./vendor.js";const F={class:"flex flex-wrap items-center gap-4"},f=["value","onChange"],x={value:""},C=["value"],$={key:0},B=["checked","onChange"],w=["onClick"],_={__name:"FilterBar",props:{selectFilters:{type:Array,default:()=>[]},checkboxFilters:{type:Array,default:()=>[]},toggleFilters:{type:Array,default:()=>[]},showClearButton:{type:Boolean,default:!0}},emits:["filter-change","clear-filters"],setup(n){const c=n,h=v(()=>{const s=c.selectFilters.some(e=>e.value),r=c.checkboxFilters.some(e=>e.value),t=c.toggleFilters.some(e=>e.value!==e.defaultValue);return s||r||t});return(s,r)=>(l(),a("div",F,[(l(!0),a(o,null,u(n.selectFilters,t=>(l(),a("select",{key:t.id,value:t.value,onChange:e=>s.$emit("filter-change",t.id,e.target.value),class:"input-field min-w-[150px]"},[d("option",x,i(t.placeholder||`Tutti ${t.label}`),1),(l(!0),a(o,null,u(t.options,e=>(l(),a("option",{key:e.value,value:e.value},[m(i(e.label)+" ",1),e.count!==void 0?(l(),a("span",$,"("+i(e.count)+")",1)):g("",!0)],8,C))),128))],40,f))),128)),(l(!0),a(o,null,u(n.checkboxFilters,t=>(l(),a("label",{key:t.id,class:"flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300"},[d("input",{type:"checkbox",checked:t.value,onChange:e=>s.$emit("filter-change",t.id,e.target.checked),class:"rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"},null,40,B),d("span",null,i(t.label),1)]))),128)),(l(!0),a(o,null,u(n.toggleFilters,t=>(l(),a("div",{key:t.id,class:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1"},[(l(!0),a(o,null,u(t.options,e=>(l(),a("button",{key:e.value,onClick:V=>s.$emit("filter-change",t.id,e.value),class:b(["px-3 py-1 text-sm rounded-md transition-colors",t.value===e.value?"bg-white dark:bg-gray-600 shadow":"hover:bg-gray-200 dark:hover:bg-gray-600"])},i(e.label),11,w))),128))]))),128)),k(s.$slots,"additional-filters"),n.showClearButton&&h.value?(l(),a("button",{key:0,onClick:r[0]||(r[0]=t=>s.$emit("clear-filters")),class:"btn-secondary text-sm"},[p(y,{name:"x-mark",size:"sm",class:"mr-1"}),r[1]||(r[1]=m(" Pulisci Filtri "))])):g("",!0)]))}};export{_};
