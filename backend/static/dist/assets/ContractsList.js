import{r as g,c as _,w as G,x as O,h as N,k as c,q as J,o as i,j as e,e as x,l as z,t as n,s as k,b as l,F as A,p as $,n as K,v as Q,B as h,H as w}from"./vendor.js";import{u as W}from"./crm.js";import{d as X,H as Y}from"./app.js";import{S as Z}from"./StatusBadge.js";import{S as B}from"./StandardButton.js";import{A as tt}from"./ActionButtonGroup.js";import{g as et,a as at}from"./contractTypes.js";import{_ as rt}from"./ListPageTemplate.js";import"./Pagination.js";const ot={class:"flex space-x-4"},st=["value"],it={class:"flex items-end"},nt={class:"overflow-x-auto"},lt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},dt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ut={class:"px-6 py-4 whitespace-nowrap"},ct={class:"text-sm font-medium text-gray-900 dark:text-white"},pt={class:"text-sm text-gray-500 dark:text-gray-400"},gt={class:"px-6 py-4 whitespace-nowrap"},xt={class:"text-sm text-gray-900 dark:text-white"},yt={class:"px-6 py-4 whitespace-nowrap"},mt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},vt={key:0},bt={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},ft={key:1},_t={key:2,class:"text-gray-400 dark:text-gray-500"},kt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},ht={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},wt={key:1,class:"text-xs text-gray-500 dark:text-gray-400"},Ct={class:"px-6 py-4 whitespace-nowrap"},Tt={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},St={class:"text-center py-12"},Et={class:"text-gray-500 dark:text-gray-400 mb-4"},Mt={__name:"ContractsList",setup(Vt){const y=J(),m=W(),{showToast:v}=X(),b=g(!1),d=g([]),C=g([]),r=g({client_id:"",contract_type:"",status:""}),D=[{key:"contract_number",label:"Contratto"},{key:"client",label:"Cliente"},{key:"contract_type",label:"Tipo"},{key:"budget_amount",label:"Valore"},{key:"start_date",label:"Periodo"},{key:"status",label:"Stato"},{key:"actions",label:"Azioni"}],F=_(()=>{const a=d.value.reduce((t,o)=>o.contract_type==="hourly"&&o.budget_hours?t+o.hourly_rate*o.budget_hours:t+(o.budget_amount||0),0);return[{label:"Totale Contratti",value:d.value.length,icon:"document-text",iconClass:"text-blue-500"},{label:"Contratti Attivi",value:d.value.filter(t=>t.status==="active").length,icon:"check-circle",iconClass:"text-green-500"},{label:"Valore Totale",value:`€${f(a)}`,icon:"currency-euro",iconClass:"text-purple-500"},{label:"Completati",value:d.value.filter(t=>t.status==="completed").length,icon:"archive-box",iconClass:"text-gray-500"}]}),I=_(()=>{let a=d.value;return r.value.client_id&&(a=a.filter(t=>t.client_id===parseInt(r.value.client_id))),r.value.contract_type&&(a=a.filter(t=>t.contract_type===r.value.contract_type)),r.value.status&&(a=a.filter(t=>t.status===r.value.status)),a}),T=_(()=>r.value.client_id||r.value.contract_type||r.value.status),S=async()=>{var a;try{b.value=!0;const t=new URLSearchParams;r.value.client_id&&t.append("client_id",r.value.client_id),r.value.contract_type&&t.append("type",r.value.contract_type),r.value.status&&t.append("status",r.value.status);const o=t.toString(),s=`/api/contracts/${o?"?"+o:""}`,u=await fetch(s);if(u.ok){const p=await u.json();d.value=(((a=p.data)==null?void 0:a.contracts)||[]).map(V=>({...V,budget_amount:V.total_budget}))}else throw new Error("Errore nel caricamento contratti")}catch(t){console.error("Error loading contracts:",t),v("Errore nel caricamento dei contratti","error")}finally{b.value=!1}},L=async()=>{m.clients.length===0&&await m.fetchClients(),C.value=m.clients},M=()=>{y.push("/app/crm/contracts/new")},P=a=>{y.push(`/app/crm/contracts/${a}`)},q=a=>{y.push(`/app/crm/contracts/${a}/edit`)},R=()=>{r.value={client_id:"",contract_type:"",status:""}},U=async a=>{if(confirm("Sei sicuro di voler eliminare questo contratto?"))try{if((await fetch(`/api/contracts/${a}`,{method:"DELETE"})).ok)d.value=d.value.filter(o=>o.id!==a),v("Contratto eliminato con successo","success");else throw new Error("Errore nella eliminazione")}catch(t){console.error("Error deleting contract:",t),v("Errore nell'eliminazione del contratto","error")}},f=a=>new Intl.NumberFormat("it-IT").format(a||0),E=a=>a?new Date(a).toLocaleDateString("it-IT"):"N/A",H=et,j=a=>{const t=at(a);return{"bg-blue-100 text-blue-800":"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400","bg-green-100 text-green-800":"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400","bg-purple-100 text-purple-800":"bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400","bg-orange-100 text-orange-800":"bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400","bg-indigo-100 text-indigo-800":"bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400","bg-gray-100 text-gray-800":"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"}[t]||t};return G(()=>[r.value.client_id,r.value.contract_type,r.value.status],()=>{S()},{deep:!0}),O(async()=>{await Promise.all([S(),L()])}),(a,t)=>(i(),N(rt,{title:"Contratti",subtitle:"Gestione contratti e accordi commerciali",data:I.value,columns:D,stats:F.value,loading:b.value,"can-create":!0,"create-label":"Nuovo Contratto","search-placeholder":"Titolo, numero contratto...","empty-message":"Inizia creando il tuo primo contratto","results-label":"contratti",onCreate:M},{filters:c(()=>[e("div",ot,[e("div",null,[t[4]||(t[4]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Cliente",-1)),h(e("select",{"onUpdate:modelValue":t[0]||(t[0]=o=>r.value.client_id=o),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[3]||(t[3]=e("option",{value:""},"Tutti i clienti",-1)),(i(!0),l(A,null,$(C.value,o=>(i(),l("option",{key:o.id,value:o.id},n(o.name),9,st))),128))],512),[[w,r.value.client_id]])]),e("div",null,[t[6]||(t[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Tipo",-1)),h(e("select",{"onUpdate:modelValue":t[1]||(t[1]=o=>r.value.contract_type=o),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},t[5]||(t[5]=[e("option",{value:""},"Tutti i tipi",-1),e("option",{value:"hourly"},"Orario",-1),e("option",{value:"fixed"},"Fisso",-1),e("option",{value:"retainer"},"Retainer",-1)]),512),[[w,r.value.contract_type]])]),e("div",null,[t[8]||(t[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),h(e("select",{"onUpdate:modelValue":t[2]||(t[2]=o=>r.value.status=o),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},t[7]||(t[7]=[e("option",{value:""},"Tutti gli stati",-1),e("option",{value:"active"},"Attivo",-1),e("option",{value:"completed"},"Completato",-1),e("option",{value:"cancelled"},"Cancellato",-1)]),512),[[w,r.value.status]])]),e("div",it,[x(B,{variant:"secondary",size:"sm",onClick:R},{default:c(()=>t[9]||(t[9]=[k(" Reset Filtri ")])),_:1,__:[9]})])])]),content:c(({data:o})=>[e("div",nt,[e("table",lt,[t[10]||(t[10]=e("thead",{class:"bg-gray-50 dark:bg-gray-800"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Contratto "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Cliente "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Tipo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Valore "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Periodo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Stato "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",dt,[(i(!0),l(A,null,$(o,s=>{var u;return i(),l("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",ut,[e("div",null,[e("div",ct,n(s.contract_number),1),e("div",pt,n(s.title),1)])]),e("td",gt,[e("div",xt,n(((u=s.client)==null?void 0:u.name)||"N/A"),1)]),e("td",yt,[e("span",{class:K(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",j(s.contract_type)])},n(Q(H)(s.contract_type)),3)]),e("td",mt,[s.contract_type==="hourly"?(i(),l("div",vt,[k(" €"+n(f(s.hourly_rate))+"/ora ",1),s.budget_hours?(i(),l("div",bt," Max: "+n(s.budget_hours)+"h ",1)):z("",!0)])):s.budget_amount?(i(),l("div",ft," €"+n(f(s.budget_amount)),1)):(i(),l("div",_t,"N/A"))]),e("td",kt,[e("div",null,n(E(s.start_date)),1),s.end_date?(i(),l("div",ht," al "+n(E(s.end_date)),1)):(i(),l("div",wt," Indeterminato "))]),e("td",Ct,[x(Z,{status:s.status,type:"contract"},null,8,["status"])]),e("td",Tt,[x(tt,{"show-labels":!1,size:"sm",onView:p=>P(s.id),onEdit:p=>q(s.id),onDelete:p=>U(s.id),"delete-message":"Sei sicuro di voler eliminare questo contratto?"},null,8,["onView","onEdit","onDelete"])])])}),128))])])])]),"empty-state":c(()=>[e("div",St,[x(Y,{name:"document-text",class:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600"}),t[12]||(t[12]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun contratto trovato",-1)),e("p",Et,n(T.value?"Prova a modificare i filtri di ricerca":"Inizia creando il tuo primo contratto"),1),T.value?z("",!0):(i(),N(B,{key:0,variant:"primary",icon:"plus",to:"/app/crm/contracts/new"},{default:c(()=>t[11]||(t[11]=[k(" Crea Primo Contratto ")])),_:1,__:[11]}))])]),_:1},8,["data","stats","loading"]))}};export{Mt as default};
