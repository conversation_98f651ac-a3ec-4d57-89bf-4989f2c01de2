import{r as b,c as q,w as X,x as Z,b as n,o as s,l as c,j as t,e as p,B as A,H as D,I as U,F as R,p as G,t as d,C as O,s as y,k as w,h as H,A as Y,u as ee,q as te}from"./vendor.js";import{u as oe}from"./recruiting.js";import{_ as K,d as W,H as x,c as Q}from"./app.js";import{F as ae}from"./FormBuilder.js";import{S as k}from"./StandardButton.js";import"./AlertsSection.js";/* empty css                                                           */const re={class:"job-posting-ai-generator"},ie={class:"flex justify-between items-center mb-6"},se={class:"flex items-center space-x-3"},le={class:"flex-shrink-0"},ne={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},ue={class:"space-y-4"},de=["value"],pe=["value"],ce={key:0,class:"text-xs text-gray-500 mt-1"},me={class:"flex items-center justify-between pt-4"},ve={class:"text-xs text-gray-500"},fe={key:1},be={class:"bg-gray-50 rounded-lg p-4 h-full"},ge={key:0,class:"text-center text-gray-500 py-16"},xe={key:1,class:"text-center py-16"},ye={key:2,class:"space-y-4"},_e={class:"flex justify-between items-center"},he={class:"font-medium text-gray-900 flex items-center"},we={class:"flex space-x-2"},ke={class:"max-h-96 overflow-y-auto space-y-4"},ze={key:0},Ie={class:"bg-white p-3 rounded border"},Ce={class:"text-sm font-semibold text-gray-900"},je={key:1},Ae={class:"bg-white p-3 rounded border max-h-32 overflow-y-auto"},qe={class:"text-sm text-gray-700 whitespace-pre-wrap"},Ee={key:2},Se={class:"bg-white p-3 rounded border max-h-32 overflow-y-auto"},Te={class:"text-sm text-gray-700 whitespace-pre-wrap"},$e={key:3},Pe={class:"bg-white p-3 rounded border max-h-32 overflow-y-auto"},De={class:"text-sm text-gray-700 whitespace-pre-wrap"},Re={key:4},Ge={class:"bg-white p-3 rounded border max-h-32 overflow-y-auto"},Me={class:"text-sm text-gray-700 whitespace-pre-wrap"},Ve={key:5,class:"border-t pt-3"},Be={class:"flex items-center space-x-4 text-xs text-gray-500"},Le={key:3,class:"text-center py-16"},Fe={class:"text-sm text-red-600 mb-4"},Je={class:"flex justify-between items-center mt-6 pt-6 border-t"},Ne={class:"flex space-x-3"},Ue={__name:"JobPostingAIGenerator",props:{show:{type:Boolean,default:!1},departments:{type:Array,default:()=>[]}},emits:["content-generated","content-applied","close"],setup(M,{emit:E}){const I=E,h=M,{showToast:_}=W(),l=b(!1),v=b(!1),m=b(null),i=b(null),C=b([]),u=b({role_type:"",experience_level:"",department_id:"",reference_job_id:"",company_context:"",custom_prompt:""}),$=q(()=>u.value.role_type&&u.value.experience_level),f=q(()=>u.value.reference_job_id?C.value.find(a=>a.id===parseInt(u.value.reference_job_id)):null),S=async()=>{var a,e;if(!$.value){_("Seleziona almeno tipo di ruolo e livello di esperienza","warning");return}v.value=!0,m.value=null;try{const r=await Q.post("/api/recruiting/ai/job-posting/generate",u.value);if(r.data.success)i.value=r.data.data.generated_content,I("content-generated",r.data.data),_("Contenuto generato con successo!","success");else throw new Error(r.data.message||"Errore nella generazione")}catch(r){m.value=((e=(a=r.response)==null?void 0:a.data)==null?void 0:e.message)||r.message||"Errore nella generazione AI",console.error("AI Generation Error:",r),_("Errore nella generazione AI","error")}finally{v.value=!1}},P=()=>{i.value&&(I("content-applied",i.value),_("Contenuto applicato con successo!","success"),z())},V=async()=>{if(!i.value)return;const a=`
TITOLO: ${i.value.title||""}

DESCRIZIONE:
${i.value.description||""}

REQUISITI:
${i.value.requirements||""}

RESPONSABILITÀ:
${i.value.responsibilities||""}

BENEFIT SUGGERITI:
${i.value.suggested_benefits||""}
  `.trim();try{await navigator.clipboard.writeText(a),_("Contenuto copiato negli appunti","success")}catch(e){console.error("Copy failed:",e),_("Errore nella copia","error")}},B=async()=>{try{const a=await Q.get("/api/recruiting/ai/job-posting/templates");a.data.success&&(C.value=a.data.data.templates||[])}catch(a){console.error("Error loading job templates:",a)}},L=()=>{},F=()=>{m.value=null},z=()=>{l.value=!1,I("close")},J=()=>{u.value={role_type:"",experience_level:"",department_id:"",reference_job_id:"",company_context:"",custom_prompt:""},i.value=null,m.value=null},N=a=>a?new Date(a).toLocaleDateString("it-IT"):"",o=a=>a?new Date(a).toLocaleString("it-IT"):"";return X(()=>h.show,a=>{l.value=a,a&&J()}),Z(()=>{B()}),(a,e)=>(s(),n("div",re,[l.value?(s(),n("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:z},[t("div",{class:"relative top-10 mx-auto p-6 border max-w-5xl shadow-lg rounded-md bg-white",onClick:e[6]||(e[6]=Y(()=>{},["stop"]))},[t("div",ie,[t("div",se,[t("div",le,[p(x,{name:"light-bulb",class:"w-6 h-6 text-purple-600"})]),e[7]||(e[7]=t("div",null,[t("h3",{class:"text-xl font-semibold text-gray-900"}," ✨ Generatore AI Job Posting "),t("p",{class:"text-sm text-gray-600"}," Crea contenuti professionali per posizioni lavorative con l'intelligenza artificiale ")],-1))]),t("button",{onClick:z,class:"text-gray-400 hover:text-gray-600"},[p(x,{name:"x-mark",class:"w-6 h-6"})])]),t("div",ne,[t("div",ue,[t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Tipo di Ruolo * ",-1)),A(t("select",{"onUpdate:modelValue":e[0]||(e[0]=r=>u.value.role_type=r),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500",required:""},e[8]||(e[8]=[U('<option value="" data-v-9963ff72>Seleziona tipo di ruolo</option><option value="Technical" data-v-9963ff72>Tecnico</option><option value="Management" data-v-9963ff72>Management</option><option value="Sales" data-v-9963ff72>Vendite</option><option value="Marketing" data-v-9963ff72>Marketing</option><option value="Operations" data-v-9963ff72>Operazioni</option><option value="HR" data-v-9963ff72>Risorse Umane</option><option value="Finance" data-v-9963ff72>Finanza</option><option value="Generico" data-v-9963ff72>Generico</option>',9)]),512),[[D,u.value.role_type]])]),t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Livello di Esperienza * ",-1)),A(t("select",{"onUpdate:modelValue":e[1]||(e[1]=r=>u.value.experience_level=r),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500",required:""},e[10]||(e[10]=[U('<option value="" data-v-9963ff72>Seleziona livello</option><option value="Intern" data-v-9963ff72>Stage</option><option value="Junior" data-v-9963ff72>Junior (0-2 anni)</option><option value="Mid" data-v-9963ff72>Mid (2-5 anni)</option><option value="Senior" data-v-9963ff72>Senior (5+ anni)</option><option value="Executive" data-v-9963ff72>Executive/C-Level</option>',6)]),512),[[D,u.value.experience_level]])]),t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Dipartimento ",-1)),A(t("select",{"onUpdate:modelValue":e[2]||(e[2]=r=>u.value.department_id=r),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"},[e[12]||(e[12]=t("option",{value:""},"Nessun dipartimento specifico",-1)),(s(!0),n(R,null,G(M.departments,r=>(s(),n("option",{key:r.id,value:r.id},d(r.name),9,de))),128))],512),[[D,u.value.department_id]])]),t("div",null,[e[15]||(e[15]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Template di Riferimento ",-1)),A(t("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>u.value.reference_job_id=r),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500",onChange:L},[e[14]||(e[14]=t("option",{value:""},"Nessun template",-1)),(s(!0),n(R,null,G(C.value,r=>(s(),n("option",{key:r.id,value:r.id},d(r.title)+" "+d(r.department?`(${r.department})`:""),9,pe))),128))],544),[[D,u.value.reference_job_id]]),f.value?(s(),n("p",ce,d(f.value.employment_type)+" • "+d(N(f.value.created_at)),1)):c("",!0)]),t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Contesto Aziendale ",-1)),A(t("textarea",{"onUpdate:modelValue":e[4]||(e[4]=r=>u.value.company_context=r),rows:"2",placeholder:"Es: Startup tecnologica in crescita, focus su innovazione...",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"},null,512),[[O,u.value.company_context]])]),t("div",null,[e[17]||(e[17]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Richieste Specifiche ",-1)),A(t("textarea",{"onUpdate:modelValue":e[5]||(e[5]=r=>u.value.custom_prompt=r),rows:"3",placeholder:"Es: Enfatizza remote work, competenze in Vue.js, crescita rapida...",class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-purple-500"},null,512),[[O,u.value.custom_prompt]])]),t("div",me,[t("div",ve,[p(x,{name:"information-circle",class:"w-4 h-4 inline mr-1"}),e[18]||(e[18]=y(" La generazione richiede 10-30 secondi "))]),p(k,{onClick:S,disabled:!$.value||v.value,variant:"primary",class:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"},{default:w(()=>[v.value?(s(),H(x,{key:0,name:"arrow-path",class:"animate-spin h-4 w-4 mr-2"})):(s(),n("span",fe,"✨")),y(" "+d(v.value?"Generazione...":"Genera con AI"),1)]),_:1},8,["disabled"])])]),t("div",be,[!i.value&&!v.value?(s(),n("div",ge,[p(x,{name:"light-bulb",class:"mx-auto h-16 w-16 text-gray-400 mb-4"}),e[19]||(e[19]=t("h4",{class:"text-lg font-medium text-gray-900 mb-2"}," Contenuto AI ",-1)),e[20]||(e[20]=t("p",{class:"text-sm"}," Il job posting generato dall'AI apparirà qui ",-1)),e[21]||(e[21]=t("div",{class:"mt-4 text-xs text-gray-400"},[t("div",{class:"flex items-center justify-center space-x-4"},[t("span",null,"📝 Descrizione"),t("span",null,"📋 Requisiti"),t("span",null,"🎯 Responsabilità")])],-1))])):c("",!0),v.value?(s(),n("div",xe,e[22]||(e[22]=[t("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"},null,-1),t("h4",{class:"text-lg font-medium text-gray-900 mb-2"}," Generazione in corso... ",-1),t("p",{class:"text-sm text-gray-600"}," L'AI sta creando il contenuto personalizzato ",-1)]))):c("",!0),i.value&&!v.value?(s(),n("div",ye,[t("div",_e,[t("h4",he,[p(x,{name:"sparkles",class:"w-4 h-4 text-purple-600 mr-2"}),e[23]||(e[23]=y(" Contenuto Generato "))]),t("div",we,[t("button",{onClick:V,class:"text-purple-600 hover:text-purple-800 text-sm flex items-center"},[p(x,{name:"clipboard",class:"w-4 h-4 mr-1"}),e[24]||(e[24]=y(" Copia "))]),t("button",{onClick:P,class:"text-green-600 hover:text-green-800 text-sm flex items-center"},[p(x,{name:"check",class:"w-4 h-4 mr-1"}),e[25]||(e[25]=y(" Applica "))])])]),t("div",ke,[i.value.title?(s(),n("div",ze,[e[26]||(e[26]=t("h5",{class:"text-sm font-medium text-purple-900 mb-2"},"Titolo:",-1)),t("div",Ie,[t("p",Ce,d(i.value.title),1)])])):c("",!0),i.value.description?(s(),n("div",je,[e[27]||(e[27]=t("h5",{class:"text-sm font-medium text-purple-900 mb-2"},"Descrizione:",-1)),t("div",Ae,[t("p",qe,d(i.value.description),1)])])):c("",!0),i.value.requirements?(s(),n("div",Ee,[e[28]||(e[28]=t("h5",{class:"text-sm font-medium text-purple-900 mb-2"},"Requisiti:",-1)),t("div",Se,[t("p",Te,d(i.value.requirements),1)])])):c("",!0),i.value.responsibilities?(s(),n("div",$e,[e[29]||(e[29]=t("h5",{class:"text-sm font-medium text-purple-900 mb-2"},"Responsabilità:",-1)),t("div",Pe,[t("p",De,d(i.value.responsibilities),1)])])):c("",!0),i.value.suggested_benefits?(s(),n("div",Re,[e[30]||(e[30]=t("h5",{class:"text-sm font-medium text-purple-900 mb-2"},"Benefit Suggeriti:",-1)),t("div",Ge,[t("p",Me,d(i.value.suggested_benefits),1)])])):c("",!0),i.value.ai_metadata?(s(),n("div",Ve,[t("div",Be,[t("span",null,"Generato: "+d(o(i.value.ai_metadata.generated_at)),1),t("span",null,"Confidenza: "+d(Math.round((i.value.ai_metadata.confidence_score||.95)*100))+"%",1)])])):c("",!0)])])):c("",!0),m.value?(s(),n("div",Le,[p(x,{name:"exclamation-triangle",class:"mx-auto h-12 w-12 text-red-500 mb-4"}),e[32]||(e[32]=t("h4",{class:"text-lg font-medium text-gray-900 mb-2"}," Errore nella generazione ",-1)),t("p",Fe,d(m.value),1),p(k,{onClick:F,variant:"secondary",size:"sm"},{default:w(()=>e[31]||(e[31]=[y(" Riprova ")])),_:1,__:[31]})])):c("",!0)])]),t("div",Je,[e[35]||(e[35]=t("div",{class:"text-xs text-gray-500"}," Powered by OpenAI GPT-4 ",-1)),t("div",Ne,[p(k,{onClick:z,variant:"secondary"},{default:w(()=>e[33]||(e[33]=[y(" Chiudi ")])),_:1,__:[33]}),i.value?(s(),H(k,{key:0,onClick:P,variant:"primary",class:"bg-green-600 hover:bg-green-700"},{default:w(()=>[p(x,{name:"check",class:"w-4 h-4 mr-2"}),e[34]||(e[34]=y(" Applica e Salva "))]),_:1,__:[34]})):c("",!0)])])])])):c("",!0)]))}},Oe=K(Ue,[["__scopeId","data-v-9963ff72"]]),He={class:"job-posting-form"},Qe={class:"mb-6"},Ze={class:"flex items-center space-x-3 mb-2"},Ke={class:"text-2xl font-bold text-gray-900 dark:text-white"},We={class:"text-sm text-gray-600 dark:text-gray-400"},Xe={class:"mb-6 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700"},Ye={class:"flex items-center justify-between"},et={class:"flex items-center space-x-3"},tt={class:"flex-shrink-0"},ot=["for"],at={key:0,class:"text-red-500"},rt=["id","value","onChange","required"],it=["value"],st={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},lt=["for"],nt=["id","value","onChange"],ut=["value"],dt={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},pt={class:"flex justify-end space-x-3"},ct={__name:"JobPostingForm",setup(M){const E=ee(),I=te(),h=oe(),{showToast:_}=W(),l=b({title:"",description:"",requirements:"",location:"",employment_type:"full_time",salary_min:null,salary_max:null,department_id:"",project_id:"",status:"draft",is_public:!1}),v=b({}),m=b(null),i=b([]),C=b([]),u=b(!1),$=q(()=>h.loading),f=q(()=>!!E.params.id),S=q(()=>"/app/recruiting/job-postings"),P=q(()=>[{id:"title",type:"text",label:"Titolo Posizione",placeholder:"es. Sviluppatore Frontend Senior",required:!0},{id:"location",type:"text",label:"Località",placeholder:"es. Milano, Roma, Remoto",required:!0},{id:"employment_type",type:"select",label:"Tipo Contratto",required:!0,options:[{value:"full_time",label:"Tempo Pieno"},{value:"part_time",label:"Part-time"},{value:"contract",label:"Contratto"},{value:"intern",label:"Stage"}]},{id:"department_id",type:"custom",label:"Dipartimento",required:!0},{id:"project_id",type:"custom",label:"Progetto (Opzionale)"},{id:"salary_min",type:"number",label:"Stipendio Minimo (€)",placeholder:"30000",min:0},{id:"salary_max",type:"number",label:"Stipendio Massimo (€)",placeholder:"50000",min:0},{id:"description",type:"textarea",label:"Descrizione",placeholder:"Descrivi la posizione, responsabilità e quello che offrite...",rows:4,required:!0},{id:"requirements",type:"textarea",label:"Requisiti",placeholder:"Elenca i requisiti tecnici e di esperienza richiesti...",rows:4,required:!0},{id:"status",type:"select",label:"Stato",required:!0,options:[{value:"draft",label:"Bozza"},{value:"active",label:"Attiva"},{value:"paused",label:"In pausa"},{value:"closed",label:"Chiusa"}]}]),V=async()=>{if(f.value)try{const o=await h.fetchJobPosting(parseInt(E.params.id));o&&(l.value={title:o.title||"",description:o.description||"",requirements:o.requirements||"",location:o.location||"",employment_type:o.employment_type||"full_time",salary_min:o.salary_min,salary_max:o.salary_max,department_id:o.department_id||"",project_id:o.project_id||"",status:o.status||"draft",is_public:o.is_public||!1})}catch(o){console.error("Error loading job posting:",o),m.value="Errore nel caricamento della posizione"}},B=async()=>{try{const[o,a]=await Promise.all([h.fetchDepartments(),h.fetchProjects()]);i.value=o||[],C.value=a||[]}catch(o){console.error("Error loading dropdown data:",o)}},L=()=>{var a,e,r,j;const o={};return(a=l.value.title)!=null&&a.trim()||(o.title="Il titolo è obbligatorio"),(e=l.value.location)!=null&&e.trim()||(o.location="La località è obbligatoria"),(r=l.value.description)!=null&&r.trim()||(o.description="La descrizione è obbligatoria"),(j=l.value.requirements)!=null&&j.trim()||(o.requirements="I requisiti sono obbligatori"),l.value.department_id||(o.department_id="Il dipartimento è obbligatorio"),l.value.employment_type||(o.employment_type="Il tipo di contratto è obbligatorio"),l.value.status||(o.status="Lo stato è obbligatorio"),l.value.salary_min&&l.value.salary_max&&l.value.salary_min>l.value.salary_max&&(o.salary_max="Lo stipendio massimo deve essere maggiore del minimo"),v.value=o,Object.keys(o).length===0},F=async()=>{if(m.value=null,!L()){m.value="Controlla i campi del modulo e riprova";return}try{const o={...l.value};o.salary_min===""&&(o.salary_min=null),o.salary_max===""&&(o.salary_max=null),o.project_id===""&&(o.project_id=null),f.value?await h.updateJobPosting(parseInt(E.params.id),o):await h.createJobPosting(o),I.push(S.value)}catch(o){console.error("Error saving job posting:",o),m.value=f.value?"Errore nell'aggiornamento della posizione":"Errore nella creazione della posizione"}},z=()=>{I.push(S.value)},J=o=>{console.log("AI content generated:",o),_('Contenuto generato con successo! Clicca "Applica" per utilizzarlo.',"success")},N=o=>{o.title&&(l.value.title=o.title),o.description&&(l.value.description=o.description),o.requirements&&(l.value.requirements=o.requirements),o.responsibilities&&l.value.description&&o.responsibilities&&(l.value.description+=`

**Responsabilità:**
`+o.responsibilities),o.suggested_benefits&&l.value.description&&o.suggested_benefits&&(l.value.description+=`

**Benefit:**
`+o.suggested_benefits),_("Contenuto AI applicato al modulo!","success"),u.value=!1,v.value={},m.value=null};return Z(async()=>{await Promise.all([B(),V()])}),(o,a)=>(s(),n("div",He,[t("div",Qe,[t("div",Ze,[p(k,{variant:"ghost",icon:"arrow-left",onClick:z,size:"sm"}),t("h1",Ke,d(f.value?"Modifica Posizione":"Nuova Posizione"),1)]),t("p",We,d(f.value?"Aggiorna i dettagli della posizione lavorativa":"Crea una nuova posizione lavorativa per il recruiting"),1)]),t("div",Xe,[t("div",Ye,[t("div",et,[t("div",tt,[p(x,{name:"light-bulb",class:"w-6 h-6 text-purple-600"})]),a[3]||(a[3]=t("div",null,[t("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," ✨ Generazione AI "),t("p",{class:"text-sm text-gray-600 dark:text-gray-400"}," Crea contenuti professionali per la tua posizione lavorativa utilizzando l'intelligenza artificiale ")],-1))]),p(k,{onClick:a[0]||(a[0]=e=>u.value=!0),variant:"primary",class:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700",icon:"sparkles"},{default:w(()=>a[4]||(a[4]=[y(" Genera con AI ")])),_:1,__:[4]})])]),p(ae,{fields:P.value,modelValue:l.value,"onUpdate:modelValue":a[1]||(a[1]=e=>l.value=e),errors:v.value,"global-error":m.value,loading:$.value,"submit-label":f.value?"Aggiorna Posizione":"Crea Posizione","loading-label":f.value?"Aggiornamento...":"Creazione...","cancel-label":"Annulla","cancel-route":S.value,onSubmit:F},{"field-department_id":w(({field:e,value:r,update:j,error:T})=>[t("div",null,[t("label",{for:e.id,class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},[y(d(e.label)+" ",1),e.required?(s(),n("span",at,"*")):c("",!0)],8,ot),t("select",{id:e.id,value:r,onChange:g=>j(g.target.value),required:e.required,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},[a[5]||(a[5]=t("option",{value:""},"Seleziona dipartimento",-1)),(s(!0),n(R,null,G(i.value,g=>(s(),n("option",{key:g.id,value:g.id},d(g.name),9,it))),128))],40,rt),T?(s(),n("p",st,d(T),1)):c("",!0)])]),"field-project_id":w(({field:e,value:r,update:j,error:T})=>[t("div",null,[t("label",{for:e.id,class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},d(e.label),9,lt),t("select",{id:e.id,value:r,onChange:g=>j(g.target.value),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},[a[6]||(a[6]=t("option",{value:""},"Nessun progetto specifico",-1)),(s(!0),n(R,null,G(C.value,g=>(s(),n("option",{key:g.id,value:g.id},d(g.name),9,ut))),128))],40,nt),T?(s(),n("p",dt,d(T),1)):c("",!0)])]),actions:w(({loading:e,submit:r})=>[t("div",pt,[p(k,{variant:"secondary",text:"Annulla",onClick:z}),p(k,{variant:"primary",text:f.value?"Aggiorna Posizione":"Crea Posizione",loading:e,onClick:r,icon:"check"},null,8,["text","loading","onClick"])])]),_:1},8,["fields","modelValue","errors","global-error","loading","submit-label","loading-label","cancel-route"]),p(Oe,{show:u.value,departments:i.value,onContentGenerated:J,onContentApplied:N,onClose:a[2]||(a[2]=e=>u.value=!1)},null,8,["show","departments"])]))}},_t=K(ct,[["__scopeId","data-v-bd75e36a"]]);export{_t as default};
