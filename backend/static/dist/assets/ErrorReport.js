import{r as i,b as n,j as e,e as a,k as x,f as w,I as z,l as g,t as y,q as E,o as d,s as S}from"./vendor.js";import{l as I,H as o,o as V,d as C}from"./app.js";const N={class:"max-w-4xl mx-auto"},T={class:"mb-8"},A={class:"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-4"},D={class:"flex items-center space-x-4"},H={class:"p-3 bg-red-100 dark:bg-red-900/20 rounded-lg"},B={class:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 mb-8"},R={class:"flex items-start space-x-3"},q={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},F={class:"mt-8 flex items-center justify-between"},$={class:"flex items-center space-x-4"},j={key:0,class:"flex items-center space-x-2"},U={key:1,class:"flex items-center space-x-2"},L={class:"text-sm text-green-600 dark:text-green-400"},M={key:2,class:"flex items-center space-x-2"},O={class:"text-sm text-red-600 dark:text-red-400"},Q={class:"flex items-center space-x-3"},G=["disabled"],J={key:0,class:"flex items-center space-x-2"},K={key:1,class:"flex items-center space-x-2"},P={class:"mt-8 bg-gray-50 dark:bg-gray-900/50 rounded-lg p-6"},W={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ee={__name:"ErrorReport",setup(X){E();const k=I(),{showToast:b}=C(),s=i({}),m=i(!1),l=i(!1),p=i(!1),u=i(""),v=i(""),h=r=>{s.value=r,m.value=r.isValid};async function _(){if(!(!m.value||l.value)){l.value=!0,u.value="",p.value=!1;try{const r={type:s.value.errorType,message:s.value.description,severity:s.value.priority,error_context:{steps_to_reproduce:s.value.stepsToReproduce.filter(c=>c.trim()),expected_behavior:s.value.expectedBehavior,actual_behavior:s.value.actualBehavior,additional_notes:s.value.additionalNotes,system_info:s.value.systemInfo},automatic:!1,manual_report:!0,user_reported:!0};s.value.screenshot&&(r.error_context.screenshot_filename=s.value.screenshot.name,r.error_context.screenshot_size=s.value.screenshot.size);const t=await k.submitManualError(r);t&&(p.value=!0,v.value=t.tracking_id||t.id||"N/A",b("Errore segnalato con successo! Il nostro team lo analizzerà presto.","success"),window.scrollTo({top:0,behavior:"smooth"}))}catch(r){console.error("Error submitting manual error:",r),u.value=r.message||"Errore durante l'invio della segnalazione",b("Errore durante l'invio della segnalazione. Riprova più tardi.","error")}finally{l.value=!1}}}return(r,t)=>{const c=w("router-link");return d(),n("div",N,[e("div",T,[e("nav",A,[a(c,{to:"/app/help/dashboard",class:"hover:text-gray-700 dark:hover:text-gray-300"},{default:x(()=>t[2]||(t[2]=[S(" Help Center ")])),_:1,__:[2]}),a(o,{name:"chevron-right",size:"sm"}),t[3]||(t[3]=e("span",{class:"text-gray-900 dark:text-white"},"Segnala Errore",-1))]),e("div",D,[e("div",H,[a(o,{name:"exclamation-triangle",size:"xl",class:"text-red-600 dark:text-red-400"})]),t[4]||(t[4]=e("div",null,[e("h1",{class:"text-3xl font-bold text-gray-900 dark:text-white"}," Segnala un Errore "),e("p",{class:"text-lg text-gray-600 dark:text-gray-400 mt-2"}," Aiutaci a migliorare il sistema segnalando errori o problemi che hai riscontrato ")],-1))])]),e("div",B,[e("div",R,[a(o,{name:"information-circle",size:"md",class:"text-blue-600 dark:text-blue-400 mt-1"}),t[5]||(t[5]=z('<div><h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2"> Come Segnalare un Errore </h3><ul class="text-blue-800 dark:text-blue-200 space-y-2"><li class="flex items-start space-x-2"><span class="text-blue-600 dark:text-blue-400 mt-1">•</span><span>Descrivi dettagliatamente l&#39;errore che hai riscontrato</span></li><li class="flex items-start space-x-2"><span class="text-blue-600 dark:text-blue-400 mt-1">•</span><span>Includi i passaggi per riprodurre il problema</span></li><li class="flex items-start space-x-2"><span class="text-blue-600 dark:text-blue-400 mt-1">•</span><span>Aggiungi screenshot se possibile</span></li><li class="flex items-start space-x-2"><span class="text-blue-600 dark:text-blue-400 mt-1">•</span><span>Le informazioni del sistema vengono raccolte automaticamente</span></li></ul></div>',1))])]),e("div",q,[a(V,{modelValue:s.value,"onUpdate:modelValue":[t[0]||(t[0]=f=>s.value=f),h]},null,8,["modelValue"]),e("div",F,[e("div",$,[l.value?(d(),n("div",j,[a(o,{name:"arrow-path",size:"sm",class:"text-primary-600 dark:text-primary-400 animate-spin"}),t[6]||(t[6]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Invio in corso...",-1))])):g("",!0),p.value?(d(),n("div",U,[a(o,{name:"check-circle",size:"sm",class:"text-green-600 dark:text-green-400"}),e("span",L," Errore inviato con successo! ID: "+y(v.value),1)])):g("",!0),u.value?(d(),n("div",M,[a(o,{name:"exclamation-circle",size:"sm",class:"text-red-600 dark:text-red-400"}),e("span",O,y(u.value),1)])):g("",!0)]),e("div",Q,[e("button",{onClick:t[1]||(t[1]=f=>r.$router.push("/app/help/dashboard")),type:"button",class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"}," Torna all'Help Center "),e("button",{onClick:_,disabled:!m.value||l.value,type:"button",class:"px-6 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"},[l.value?(d(),n("span",J,[a(o,{name:"arrow-path",size:"sm",class:"animate-spin"}),t[7]||(t[7]=e("span",null,"Invio...",-1))])):(d(),n("span",K,[a(o,{name:"paper-airplane",size:"sm"}),t[8]||(t[8]=e("span",null,"Invia Segnalazione",-1))]))],8,G)])])]),e("div",P,[t[11]||(t[11]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"}," Altre Opzioni di Supporto ",-1)),e("div",W,[a(c,{to:"/app/help/faq",class:"flex items-center space-x-3 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},{default:x(()=>[a(o,{name:"question-mark-circle",size:"md",class:"text-blue-600 dark:text-blue-400"}),t[9]||(t[9]=e("div",null,[e("div",{class:"font-medium text-gray-900 dark:text-white"},"FAQ"),e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Domande frequenti")],-1))]),_:1,__:[9]}),a(c,{to:"/app/help/contact",class:"flex items-center space-x-3 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},{default:x(()=>[a(o,{name:"envelope",size:"md",class:"text-green-600 dark:text-green-400"}),t[10]||(t[10]=e("div",null,[e("div",{class:"font-medium text-gray-900 dark:text-white"},"Contatta Supporto"),e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Assistenza diretta")],-1))]),_:1,__:[10]})])])])}}};export{ee as default};
