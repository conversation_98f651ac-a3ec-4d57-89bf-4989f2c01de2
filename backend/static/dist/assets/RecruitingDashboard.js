import{u as N}from"./recruiting.js";import{S as T}from"./StandardButton.js";import{H as g,_ as V}from"./app.js";import{c as p,b as r,o as s,F as $,p as D,j as t,l as k,e as l,n as v,t as c,x as q,h as P,k as y,f as B,s as b}from"./vendor.js";const M={class:"flex items-center"},R={key:0,class:"mr-3"},A={class:"flex-1"},F={class:"text-sm font-medium text-gray-500 dark:text-gray-400"},L={key:0,class:"text-xs text-gray-400 dark:text-gray-500"},O={key:2,class:"ml-2"},W={__name:"StatsCardGrid",props:{stats:{type:Array,required:!0,default:()=>[]},columns:{type:[Number,Object],default:()=>({sm:1,md:2,lg:3,xl:4})},loading:{type:Boolean,default:!1},size:{type:String,default:"md",validator:h=>["sm","md","lg"].includes(h)}},setup(h){const d=h,f=p(()=>{const u="grid gap-6 mb-6";if(typeof d.columns=="number"){const n={1:"grid-cols-1",2:"grid-cols-1 md:grid-cols-2",3:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3",4:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",5:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5",6:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6"};return`${u} ${n[d.columns]||n[4]}`}const m=[];return d.columns.sm&&m.push(`grid-cols-${d.columns.sm}`),d.columns.md&&m.push(`md:grid-cols-${d.columns.md}`),d.columns.lg&&m.push(`lg:grid-cols-${d.columns.lg}`),d.columns.xl&&m.push(`xl:grid-cols-${d.columns.xl}`),`${u} ${m.join(" ")}`}),o=p(()=>({sm:"text-lg font-semibold text-gray-900 dark:text-white",md:"text-2xl font-semibold text-gray-900 dark:text-white",lg:"text-3xl font-semibold text-gray-900 dark:text-white"})[d.size]),w=p(()=>({sm:"sm",md:"md",lg:"lg"})[d.size]),z=(u,m)=>{if(d.loading)return"-";switch(m){case"currency":return new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(u||0);case"percentage":return`${u||0}%`;case"number":return new Intl.NumberFormat("it-IT").format(u||0);default:return u||0}};return(u,m)=>(s(),r("div",{class:v(f.value)},[(s(!0),r($,null,D(h.stats,n=>(s(),r("div",{key:n.key,class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},[t("div",M,[n.icon?(s(),r("div",R,[l(g,{name:n.icon,size:w.value,class:v(n.iconClass||"text-gray-500 dark:text-gray-400")},null,8,["name","size","class"])])):n.color?(s(),r("div",{key:1,class:v(["w-3 h-3 rounded-full mr-3",n.color])},null,2)):k("",!0),t("div",A,[t("p",F,c(n.label),1),t("p",{class:v(o.value)},c(z(n.value,n.format)),3),n.subValue?(s(),r("p",L,c(n.subValue),1)):k("",!0)]),n.trend?(s(),r("div",O,[l(g,{name:n.trend>0?"arrow-trending-up":"arrow-trending-down",size:"sm",class:v(n.trend>0?"text-green-500":"text-red-500")},null,8,["name","class"])])):k("",!0)])]))),128))],2))}},E={class:"recruiting-dashboard"},G={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8"},H={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},U={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},J={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between"},K={class:"p-6"},Q={key:0,class:"flex justify-center py-8"},X={key:1,class:"space-y-4"},Y=["onClick"],Z={class:"flex-1"},ee={class:"font-medium text-gray-900 dark:text-white"},te={class:"text-sm text-gray-500 dark:text-gray-400"},se={class:"flex items-center mt-1 text-sm text-gray-500 dark:text-gray-400"},ae={key:0,class:"ml-2 flex items-center"},re={key:2,class:"text-center py-8"},oe={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},ie={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between"},le={class:"p-6"},ne={key:0,class:"flex justify-center py-8"},de={key:1,class:"space-y-4"},ce={class:"flex items-start justify-between"},ue={class:"flex-1"},ge={class:"font-medium text-gray-900 dark:text-white"},me={class:"text-sm text-gray-500 dark:text-gray-400"},xe={class:"flex items-center mt-2 text-sm text-gray-500 dark:text-gray-400"},pe={class:"flex items-center mt-1 text-sm text-gray-500 dark:text-gray-400"},ye={key:2,class:"text-center py-8"},be={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},ve={class:"p-6"},_e={key:0,class:"grid grid-cols-2 md:grid-cols-4 gap-6"},he={class:"text-sm text-gray-600 dark:text-gray-400 mt-1"},fe={key:1,class:"flex justify-center py-8"},ke={__name:"RecruitingDashboard",setup(h){const d=N(),f=p(()=>d.loading),o=p(()=>d.dashboardData);p(()=>d.dashboardStats);const w=p(()=>{var i;return((i=o.value)==null?void 0:i.active_positions)||[]}),z=p(()=>{var i;return((i=o.value)==null?void 0:i.upcoming_interviews)||[]}),u=p(()=>{var e;if(!((e=o.value)!=null&&e.stats))return[];const i=o.value.stats;return[{key:"open_positions",label:"Posizioni Aperte",value:i.open_positions||0,icon:"briefcase",iconClass:"text-blue-600",trend:o.value.positions_change?o.value.positions_change.startsWith("+")?1:-1:null,subValue:o.value.positions_change},{key:"active_candidates",label:"Candidati Attivi",value:i.active_candidates||0,icon:"user-group",iconClass:"text-green-600",trend:o.value.candidates_change?o.value.candidates_change.startsWith("+")?1:-1:null,subValue:o.value.candidates_change},{key:"interviews_week",label:"Colloqui Settimana",value:i.interviews_this_week||0,icon:"calendar",iconClass:"text-purple-600",trend:o.value.interviews_change?o.value.interviews_change.startsWith("+")?1:-1:null,subValue:o.value.interviews_change},{key:"offers_sent",label:"Offerte Inviate",value:i.offers_sent||0,icon:"document-text",iconClass:"text-orange-600",trend:o.value.offers_change?o.value.offers_change.startsWith("+")?1:-1:null,subValue:o.value.offers_change}]}),m=[{key:"new",label:"Nuove",color:"text-gray-600 dark:text-gray-400"},{key:"screening",label:"Screening",color:"text-blue-600 dark:text-blue-400"},{key:"interviewing",label:"Colloqui",color:"text-purple-600 dark:text-purple-400"},{key:"offer",label:"Offerta",color:"text-green-600 dark:text-green-400"}];function n(i){return{phone_screening:"Telefono",video_technical:"Video Tech",onsite_cultural:"In Sede",final_executive:"Finale"}[i]||i}function j(i){return{phone_screening:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",video_technical:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",onsite_cultural:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",final_executive:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"}[i]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}function I(i){if(!i)return"";const e=new Date(i),x=new Date,_=new Date(x);_.setDate(_.getDate()+1);const a=e.toDateString()===x.toDateString(),C=e.toDateString()===_.toDateString(),S=e.toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit"});return a?`Oggi ${S}`:C?`Domani ${S}`:e.toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"})}return q(async()=>{await d.fetchDashboardData()}),(i,e)=>{var _;const x=B("router-link");return s(),r("div",E,[e[18]||(e[18]=t("div",{class:"mb-6"},[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Recruiting Dashboard"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Gestione processo di selezione e assunzione candidati ")],-1)),u.value.length?(s(),P(W,{key:0,stats:u.value,columns:4,loading:f.value,class:"mb-8"},null,8,["stats","loading"])):k("",!0),t("div",G,[l(x,{to:"/app/recruiting/job-postings/new",class:"p-4 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md transition-shadow flex items-center space-x-3 group"},{default:y(()=>[l(g,{name:"plus-circle",size:"lg",class:"text-blue-600 group-hover:text-blue-700"}),e[0]||(e[0]=t("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Nuova Posizione",-1))]),_:1,__:[0]}),l(x,{to:"/app/recruiting/candidates",class:"p-4 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md transition-shadow flex items-center space-x-3 group"},{default:y(()=>[l(g,{name:"user-group",size:"lg",class:"text-green-600 group-hover:text-green-700"}),e[1]||(e[1]=t("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Candidati",-1))]),_:1,__:[1]}),l(x,{to:"/app/recruiting/interviews",class:"p-4 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md transition-shadow flex items-center space-x-3 group"},{default:y(()=>[l(g,{name:"calendar",size:"lg",class:"text-purple-600 group-hover:text-purple-700"}),e[2]||(e[2]=t("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Colloqui",-1))]),_:1,__:[2]}),l(x,{to:"/app/recruiting/pipeline",class:"p-4 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-md transition-shadow flex items-center space-x-3 group"},{default:y(()=>[l(g,{name:"view-columns",size:"lg",class:"text-orange-600 group-hover:text-orange-700"}),e[3]||(e[3]=t("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Pipeline",-1))]),_:1,__:[3]})]),t("div",H,[t("div",U,[t("div",J,[e[5]||(e[5]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Posizioni Attive",-1)),l(x,{to:"/app/recruiting/job-postings",class:"text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"},{default:y(()=>e[4]||(e[4]=[b(" Vedi tutte ")])),_:1,__:[4]})]),t("div",K,[f.value?(s(),r("div",Q,e[6]||(e[6]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)]))):w.value.length?(s(),r("div",X,[(s(!0),r($,null,D(w.value.slice(0,5),a=>{var C;return s(),r("div",{key:a.id,class:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer",onClick:S=>i.$router.push(`/app/recruiting/job-postings/${a.id}`)},[t("div",Z,[t("h4",ee,c(a.title),1),t("p",te,c(((C=a.department)==null?void 0:C.name)||"N/A")+" • "+c(a.applications_count)+" candidature ",1),t("div",se,[l(g,{name:"map-pin",size:"xs",class:"mr-1"}),b(" "+c(a.location)+" ",1),a.remote_allowed?(s(),r("span",ae,[l(g,{name:"globe-alt",size:"xs",class:"mr-1"}),e[7]||(e[7]=b(" Remoto "))])):k("",!0)])]),l(g,{name:"chevron-right",size:"sm",class:"text-gray-400"})],8,Y)}),128))])):(s(),r("div",re,[l(g,{name:"briefcase",size:"lg",class:"mx-auto text-gray-400 mb-4"}),e[9]||(e[9]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessuna posizione attiva",-1)),e[10]||(e[10]=t("p",{class:"text-gray-500 dark:text-gray-400 mb-4"},"Crea la tua prima posizione lavorativa",-1)),l(T,{variant:"primary",icon:"plus",to:"/app/recruiting/job-postings/new"},{default:y(()=>e[8]||(e[8]=[b(" Nuova Posizione ")])),_:1,__:[8]})]))])]),t("div",oe,[t("div",ie,[e[12]||(e[12]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Prossimi Colloqui",-1)),l(x,{to:"/app/recruiting/interviews",class:"text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"},{default:y(()=>e[11]||(e[11]=[b(" Calendario completo ")])),_:1,__:[11]})]),t("div",le,[f.value?(s(),r("div",ne,e[13]||(e[13]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)]))):z.value.length?(s(),r("div",de,[(s(!0),r($,null,D(z.value.slice(0,5),a=>(s(),r("div",{key:a.id,class:"p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},[t("div",ce,[t("div",ue,[t("h4",ge,c(a.candidate_name),1),t("p",me,c(a.position_title),1),t("div",xe,[l(g,{name:"clock",size:"xs",class:"mr-1"}),b(" "+c(I(a.scheduled_date)),1)]),t("div",pe,[l(g,{name:"user",size:"xs",class:"mr-1"}),b(" "+c(a.interviewer.name),1)])]),t("span",{class:v(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",j(a.interview_type)])},c(n(a.interview_type)),3)])]))),128))])):(s(),r("div",ye,[l(g,{name:"calendar",size:"lg",class:"mx-auto text-gray-400 mb-4"}),e[14]||(e[14]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun colloquio programmato",-1)),e[15]||(e[15]=t("p",{class:"text-gray-500 dark:text-gray-400"},"I prossimi colloqui appariranno qui",-1))]))])])]),t("div",be,[e[17]||(e[17]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Pipeline Candidature")],-1)),t("div",ve,[(_=o.value)!=null&&_.pipeline_stats?(s(),r("div",_e,[(s(),r($,null,D(m,a=>t("div",{key:a.key,class:"text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},[t("div",{class:v(["text-3xl font-bold",a.color])},c(o.value.pipeline_stats[a.key]||0),3),t("div",he,c(a.label),1)])),64))])):(s(),r("div",fe,e[16]||(e[16]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)])))])])])}}},De=V(ke,[["__scopeId","data-v-ae3dcfc9"]]);export{De as default};
