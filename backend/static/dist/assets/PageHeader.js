import{H as n}from"./app.js";import{b as s,j as o,l as a,h as i,s as c,n as m,t as r,K as d,o as t}from"./vendor.js";const u={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},x={class:"text-2xl font-bold text-gray-900 dark:text-white flex items-center"},f={key:0,class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},y={key:0,class:"mt-4 sm:mt-0 flex items-center space-x-3"},p={__name:"PageHeader",props:{title:{type:String,required:!0},subtitle:{type:String,default:""},icon:{type:String,default:""},iconColor:{type:String,default:"text-purple-600"}},setup(e){return(l,g)=>(t(),s("div",u,[o("div",null,[o("h1",x,[e.icon?(t(),i(n,{key:0,name:e.icon,size:"lg",class:m(["mr-3",e.iconColor||"text-purple-600"])},null,8,["name","class"])):a("",!0),c(" "+r(e.title),1)]),e.subtitle?(t(),s("p",f,r(e.subtitle),1)):a("",!0)]),l.$slots.actions?(t(),s("div",y,[d(l.$slots,"actions")])):a("",!0)]))}};export{p as _};
