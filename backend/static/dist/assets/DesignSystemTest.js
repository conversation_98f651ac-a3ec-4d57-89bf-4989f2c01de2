import{r as o,c as m,h as y,k as l,o as C,j as a,e as d,n as D,t as r,B as S,H as T}from"./vendor.js";import{_ as z}from"./ListPageTemplate.js";import{H as u}from"./app.js";import"./Pagination.js";const A={class:"flex items-center"},I={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center mr-3"},M={class:"text-xs font-medium text-primary-800"},w={class:"font-medium"},B={class:"flex items-center space-x-2"},E=["onClick"],H=["onClick"],U={__name:"DesignSystemTest",setup(R){const p=o(!1),n=o(""),i=o([{id:1,name:"<PERSON>",email:"<EMAIL>",department:"IT",status:"active"},{id:2,name:"<PERSON><PERSON><PERSON>",email:"<EMAIL>",department:"HR",status:"active"},{id:3,name:"<PERSON>",email:"<EMAIL>",department:"Finance",status:"inactive"},{id:4,name:"Sara Neri",email:"<EMAIL>",department:"Marketing",status:"active"},{id:5,name:"Luca Blu",email:"<EMAIL>",department:"Sales",status:"active"},{id:6,name:"Anna Rosa",email:"<EMAIL>",department:"IT",status:"inactive"},{id:7,name:"Marco Gialli",email:"<EMAIL>",department:"HR",status:"active"},{id:8,name:"Elena Viola",email:"<EMAIL>",department:"Finance",status:"active"},{id:9,name:"Roberto Arancio",email:"<EMAIL>",department:"Marketing",status:"inactive"},{id:10,name:"Chiara Celeste",email:"<EMAIL>",department:"Sales",status:"active"},{id:11,name:"Diego Marrone",email:"<EMAIL>",department:"IT",status:"active"},{id:12,name:"Francesca Oro",email:"<EMAIL>",department:"HR",status:"active"}]),v=[{key:"name",label:"Nome"},{key:"email",label:"Email"},{key:"department",label:"Dipartimento"},{key:"status",label:"Status"},{key:"actions",label:"Azioni"}],c=m(()=>n.value?i.value.filter(e=>e.status===n.value):i.value),x=m(()=>[{label:"Totale Utenti",value:i.value.length,icon:"users",iconClass:"text-blue-500"},{label:"Utenti Mostrati",value:c.value.length,icon:"eye",iconClass:"text-purple-500"},{label:"Utenti Attivi",value:i.value.filter(e=>e.status==="active").length,icon:"check-circle",iconClass:"text-green-500"},{label:"Dipartimenti",value:new Set(i.value.map(e=>e.department)).size,icon:"building-office",iconClass:"text-orange-500"}]),g=e=>e==="active"?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",b=e=>e==="active"?"Attivo":"Inattivo",f=()=>{console.log("Create clicked"),alert("Funzione Create!")},h=e=>{console.log("Edit:",e),alert(`Edit: ${e.name}`)},k=e=>{if(console.log("Delete:",e),confirm(`Eliminare ${e.name}?`)){const s=i.value.findIndex(t=>t.id===e.id);s>-1&&i.value.splice(s,1)}};return(e,s)=>(C(),y(z,{title:"Test Design System",subtitle:"Prototipo ListPageTemplate con dati fittizi",data:c.value,columns:v,stats:x.value,loading:p.value,"can-create":!0,"create-label":"Crea Test","search-placeholder":"Cerca nei test...","results-label":"elementi test",onCreate:f},{filters:l(()=>[S(a("select",{"onUpdate:modelValue":s[0]||(s[0]=t=>n.value=t),class:"ml-3 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},s[1]||(s[1]=[a("option",{value:""},"Tutti gli status",-1),a("option",{value:"active"},"Attivo",-1),a("option",{value:"inactive"},"Inattivo",-1)]),512),[[T,n.value]])]),"column-name":l(({item:t})=>[a("div",A,[a("div",I,[a("span",M,r(t.name.charAt(0)),1)]),a("span",w,r(t.name),1)])]),"column-status":l(({item:t})=>[a("span",{class:D(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",g(t.status)])},r(b(t.status)),3)]),"column-actions":l(({item:t})=>[a("div",B,[a("button",{onClick:_=>h(t),class:"text-primary-600 hover:text-primary-900"},[d(u,{name:"pencil",size:"sm"})],8,E),a("button",{onClick:_=>k(t),class:"text-red-600 hover:text-red-900"},[d(u,{name:"trash",size:"sm"})],8,H)])]),_:1},8,["data","stats","loading"]))}};export{U as default};
