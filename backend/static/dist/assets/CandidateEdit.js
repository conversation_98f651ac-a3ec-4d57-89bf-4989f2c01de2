import{r as v,c as f,u as R,x as j,b as s,j as t,e as r,l as V,t as x,k as F,F as N,p as B,q as D,o as d}from"./vendor.js";import{u as M}from"./recruiting.js";import{F as U}from"./FormBuilder.js";import{S as c}from"./StandardButton.js";import{_ as $,H as E}from"./app.js";import"./AlertsSection.js";/* empty css                                                           */const O={class:"candidate-edit"},H={class:"mb-6"},T={class:"flex items-center space-x-3 mb-2"},W={class:"text-2xl font-bold text-gray-900 dark:text-white"},G={class:"text-sm text-gray-600 dark:text-gray-400"},J={class:"flex justify-end space-x-3"},K={key:0,class:"mt-6"},P={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},Q={class:"flex items-center justify-between mb-4"},X={key:0,class:"space-y-4"},Y={class:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},Z={class:"flex items-center space-x-3"},ee={class:"text-xs text-gray-500 dark:text-gray-400"},ae={class:"flex items-center space-x-2"},te={key:0,class:"mt-4"},ie={class:"flex flex-wrap gap-2"},le={key:1,class:"text-center py-8"},oe={__name:"CandidateEdit",setup(re){const u=R(),b=D(),m=M(),i=v({first_name:"",last_name:"",email:"",phone:"",location:"",source:"website",linkedin_url:"",status:"new"}),_=v({}),p=v(null),n=v(null),h=v(null),z=f(()=>m.loading),o=f(()=>!!u.params.id),C=f(()=>o.value?`/app/recruiting/candidates/${u.params.id}`:"/app/recruiting/candidates"),I=f(()=>[{id:"first_name",type:"text",label:"Nome",placeholder:"es. Mario",required:!0},{id:"last_name",type:"text",label:"Cognome",placeholder:"es. Rossi",required:!0},{id:"email",type:"email",label:"Email",placeholder:"<EMAIL>",required:!0},{id:"phone",type:"text",label:"Telefono",placeholder:"+39 ************"},{id:"location",type:"text",label:"Località",placeholder:"es. Milano, Roma"},{id:"source",type:"select",label:"Fonte di Acquisizione",required:!0,options:[{value:"website",label:"Sito Web"},{value:"linkedin",label:"LinkedIn"},{value:"referral",label:"Referral"},{value:"agency",label:"Agenzia"}]},{id:"linkedin_url",type:"text",label:"URL LinkedIn",placeholder:"https://linkedin.com/in/username"},{id:"status",type:"select",label:"Stato",required:!0,options:[{value:"new",label:"Nuovo"},{value:"screening",label:"Screening"},{value:"interviewing",label:"In colloquio"},{value:"offered",label:"Offerta fatta"},{value:"hired",label:"Assunto"},{value:"rejected",label:"Scartato"}]}]),k=async()=>{if(o.value)try{const e=await m.fetchCandidate(parseInt(u.params.id));e&&(n.value=e,i.value={first_name:e.first_name||"",last_name:e.last_name||"",email:e.email||"",phone:e.phone||"",location:e.location||"",source:e.source||"website",linkedin_url:e.linkedin_url||"",status:e.status||"new"})}catch(e){console.error("Error loading candidate:",e),p.value="Errore nel caricamento del candidato"}},S=()=>{var a,l,g;const e={};return(a=i.value.first_name)!=null&&a.trim()||(e.first_name="Il nome è obbligatorio"),(l=i.value.last_name)!=null&&l.trim()||(e.last_name="Il cognome è obbligatorio"),(g=i.value.email)!=null&&g.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(i.value.email)||(e.email="Inserisci un'email valida"):e.email="L'email è obbligatoria",i.value.source||(e.source="La fonte di acquisizione è obbligatoria"),i.value.status||(e.status="Lo stato è obbligatorio"),i.value.linkedin_url&&!i.value.linkedin_url.startsWith("http")&&(e.linkedin_url="Inserisci un URL LinkedIn valido (iniziare con http/https)"),_.value=e,Object.keys(e).length===0},A=async()=>{if(p.value=null,!S()){p.value="Controlla i campi del modulo e riprova";return}try{const e={...i.value};if(Object.keys(e).forEach(a=>{e[a]===""&&(e[a]=null)}),o.value)await m.updateCandidate(parseInt(u.params.id),e),b.push(`/app/recruiting/candidates/${u.params.id}`);else{const a=await m.createCandidate(e);b.push(`/app/recruiting/candidates/${a.id}`)}}catch(e){console.error("Error saving candidate:",e),p.value=o.value?"Errore nell'aggiornamento del candidato":"Errore nella creazione del candidato"}},y=()=>{h.value.click()},L=async e=>{const a=e.target.files[0];if(!(!a||!n.value))try{await m.uploadCandidateCV(n.value.id,a),await k()}catch(l){console.error("Error uploading CV:",l),alert("Errore nel caricamento del CV")}finally{e.target.value=""}},w=()=>{b.push(C.value)},q=e=>e?new Date(e).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"";return j(async()=>{await k()}),(e,a)=>(d(),s("div",O,[t("div",H,[t("div",T,[r(c,{variant:"ghost",icon:"arrow-left",onClick:w,size:"sm"}),t("h1",W,x(o.value?"Modifica Candidato":"Nuovo Candidato"),1)]),t("p",G,x(o.value?"Aggiorna le informazioni del candidato":"Aggiungi un nuovo candidato al database recruiting"),1)]),r(U,{fields:I.value,modelValue:i.value,"onUpdate:modelValue":a[0]||(a[0]=l=>i.value=l),errors:_.value,"global-error":p.value,loading:z.value,"submit-label":o.value?"Aggiorna Candidato":"Crea Candidato","loading-label":o.value?"Aggiornamento...":"Creazione...","cancel-label":"Annulla","cancel-route":C.value,onSubmit:A},{actions:F(({loading:l,submit:g})=>[t("div",J,[r(c,{variant:"secondary",text:"Annulla",onClick:w}),r(c,{variant:"primary",text:o.value?"Aggiorna Candidato":"Crea Candidato",loading:l,onClick:g,icon:"check"},null,8,["text","loading","onClick"])])]),_:1},8,["fields","modelValue","errors","global-error","loading","submit-label","loading-label","cancel-route"]),o.value&&n.value?(d(),s("div",K,[t("div",P,[t("div",Q,[a[1]||(a[1]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Curriculum Vitae ",-1)),r(c,{variant:"outline-primary",icon:"document-arrow-up",text:"Carica CV",size:"sm",onClick:y})]),n.value.cv_path?(d(),s("div",X,[t("div",Y,[t("div",Z,[r(E,{name:"document-text",size:"md",class:"text-primary-600"}),t("div",null,[a[2]||(a[2]=t("p",{class:"text-sm font-medium text-gray-900 dark:text-white"},"CV Caricato",-1)),t("p",ee," Aggiornato: "+x(q(n.value.updated_at)),1)])]),t("div",ae,[r(c,{variant:"ghost",icon:"document-arrow-down",size:"sm",title:"Scarica CV"}),r(c,{variant:"ghost",icon:"arrow-path",size:"sm",title:"Sostituisci CV",onClick:y})])]),n.value.skills&&n.value.skills.length?(d(),s("div",te,[a[3]||(a[3]=t("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-2"},"Competenze Estratte",-1)),t("div",ie,[(d(!0),s(N,null,B(n.value.skills,l=>(d(),s("span",{key:l.id,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200"},x(l.name),1))),128))])])):V("",!0)])):(d(),s("div",le,[r(E,{name:"document-text",size:"lg",class:"mx-auto text-gray-400 mb-4"}),a[4]||(a[4]=t("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-2"}," Nessun CV Caricato ",-1)),a[5]||(a[5]=t("p",{class:"text-sm text-gray-500 dark:text-gray-400 mb-4"}," Carica il CV del candidato per l'analisi automatica delle competenze ",-1)),r(c,{variant:"primary",icon:"document-arrow-up",text:"Carica CV",onClick:y})]))])])):V("",!0),t("input",{ref_key:"cvInput",ref:h,type:"file",accept:".pdf,.doc,.docx,.txt",style:{display:"none"},onChange:L},null,544)]))}},ve=$(oe,[["__scopeId","data-v-edf28da2"]]);export{ve as default};
