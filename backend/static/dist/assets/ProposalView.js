import{r as k,c as S,u as U,x as Q,b as o,e as u,j as e,l,k as M,t as c,n as W,s as g,f as G,A as J,B as h,H as K,I as X,C as P,q as Y,o as s}from"./vendor.js";import{u as Z}from"./crm.js";import{H as p,d as tt}from"./app.js";import{S as et}from"./StatusBadge.js";import{_ as at}from"./PageHeader.js";import{_ as rt}from"./StatsGrid.js";const ot={class:"min-h-screen bg-gray-50 dark:bg-gray-900"},st={key:0,class:"flex space-x-4"},nt={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},lt={key:0,class:"bg-white dark:bg-gray-800 rounded-lg shadow p-8"},it={key:1,class:"space-y-6"},dt={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},ut={class:"p-6"},ct={class:"flex items-center justify-between mb-6"},gt={class:"text-right"},pt={key:0,class:"text-2xl font-bold text-gray-900 dark:text-white"},mt={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},vt={class:"text-lg font-medium text-gray-900 dark:text-white"},yt={key:0},bt={class:"text-lg font-medium text-gray-900 dark:text-white"},xt={key:1},ft={key:0,class:"text-sm"},kt={key:1,class:"text-sm text-yellow-600 dark:text-yellow-400"},ht={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},wt={class:"lg:col-span-2 space-y-6"},_t={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},Ct={class:"p-6"},Dt={class:"grid grid-cols-1 gap-6"},zt={class:"mt-1 text-lg text-gray-900 dark:text-white"},St={key:0},Mt={class:"mt-1 text-gray-900 dark:text-gray-300 whitespace-pre-line"},Pt={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},It={class:"p-6"},jt={class:"flow-root"},Tt={class:"-mb-8"},Vt={class:"relative pb-8"},$t={class:"relative flex space-x-3"},Et={class:"min-w-0 flex-1"},At={class:"text-sm text-gray-500 dark:text-gray-400"},Bt={class:"font-medium text-gray-900 dark:text-white"},Nt={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},Lt={key:0,class:"relative pb-8"},Rt={class:"relative flex space-x-3"},Ft={class:"min-w-0 flex-1"},Ht={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},qt={class:"space-y-6"},Ot={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},Ut={class:"p-6"},Qt={key:0},Wt={class:"flex items-center space-x-3 mb-4"},Gt={class:"font-medium text-gray-900 dark:text-white"},Jt={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Kt={class:"space-y-2 text-sm"},Xt={key:0,class:"flex items-center"},Yt=["href"],Zt={key:1,class:"flex items-start"},te={class:"text-gray-600 dark:text-gray-400"},ee={class:"mt-4"},ae={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},re={class:"p-6 space-y-3"},oe={key:2,class:"bg-white dark:bg-gray-800 rounded-lg shadow p-8"},se={class:"text-center"},ne={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 dark:bg-gray-900 dark:bg-opacity-70 overflow-y-auto h-full w-full z-50"},le={class:"relative top-20 mx-auto p-6 border max-w-lg shadow-lg rounded-md bg-white dark:bg-gray-800 dark:border-gray-600"},ie={class:"mt-3"},de={class:"space-y-4"},ue={key:0},ce=["placeholder"],ge={class:"mt-1 text-xs text-gray-500 dark:text-gray-400"},pe={key:1},me={class:"flex justify-end space-x-3 mt-6"},ve=["disabled"],ye={key:0,class:"flex items-center"},be={key:1},De={__name:"ProposalView",setup(xe){const A=U(),I=Y(),w=Z(),{showToast:m}=tt(),_=k(!1),a=k(null),C=k(!1),D=k(!1),n=k({contract_type:"",start_date:new Date().toISOString().split("T")[0],end_date:"",budget_amount:"",hourly_rate:""}),B=S(()=>parseInt(A.params.id)),T=S(()=>{var i;return(i=a.value)!=null&&i.expiry_date?new Date(a.value.expiry_date)<new Date:!1}),N=S(()=>{var y;if(!((y=a.value)!=null&&y.expiry_date)||a.value.status==="accepted"||a.value.status==="rejected")return!1;const i=new Date(a.value.expiry_date)-new Date,v=Math.ceil(i/(1e3*60*60*24));return v<=7&&v>=0}),L=S(()=>{if(!a.value)return[];const r=[];a.value.value&&r.push({name:"Valore",value:`€${j(a.value.value)}`,subtitle:"valore totale",icon:"banknotes"});const t={draft:{name:"Bozza",icon:"document",subtitle:"in preparazione"},sent:{name:"Inviata",icon:"paper-airplane",subtitle:"in attesa risposta"},negotiating:{name:"Negoziazione",icon:"chat-bubble-left-right",subtitle:"in corso"},accepted:{name:"Accettata",icon:"check-circle",subtitle:"da convertire"},rejected:{name:"Rifiutata",icon:"x-circle",subtitle:"chiusa"}},i=t[a.value.status]||t.draft;if(r.push({name:"Stato",value:i.name,subtitle:i.subtitle,icon:i.icon}),a.value.expiry_date){const f=new Date(a.value.expiry_date)-new Date,b=Math.ceil(f/(1e3*60*60*24));r.push({name:"Scadenza",value:b>0?`${b} giorni`:"Scaduta",subtitle:z(a.value.expiry_date),icon:"calendar"})}if(a.value.created_at){const v=new Date(a.value.created_at),f=new Date-v,b=Math.ceil(f/(1e3*60*60*24));r.push({name:"Età",value:`${b} giorni`,subtitle:"dalla creazione",icon:"clock"})}return r}),R=async()=>{try{if(_.value=!0,a.value=await w.getProposal(B.value),!a.value)throw new Error("Proposta non trovata")}catch(r){console.error("Error loading proposal:",r),m("Errore nel caricamento della proposta","error")}finally{_.value=!1}},x=async r=>{if(a.value)try{const t={status:r};r==="sent"&&!a.value.sent_date&&(t.sent_date=new Date().toISOString().split("T")[0]),await w.updateProposal(a.value.id,t),a.value={...a.value,...t},m({type:"success",title:"Stato aggiornato",message:{sent:"Proposta inviata al cliente con successo",accepted:"Proposta marcata come accettata",rejected:"Proposta marcata come rifiutata",negotiating:"Negoziazione avviata con successo",draft:"Proposta riportata in bozza"}[r]||"Stato proposta aggiornato con successo",duration:4e3})}catch(t){console.error("Error updating proposal status:",t),m({type:"error",title:"Errore aggiornamento",message:"Si è verificato un errore durante l'aggiornamento dello stato della proposta",duration:5e3})}},F=async()=>{if(a.value)try{const r={client_id:a.value.client_id,title:`${a.value.title} (Copia)`,description:a.value.description,value:a.value.value,status:"draft"},t=await w.createProposal(r);m("Proposta duplicata con successo","success"),I.push(`/app/crm/proposals/${t.id}`)}catch(r){console.error("Error duplicating proposal:",r),m("Errore nella duplicazione della proposta","error")}},j=r=>new Intl.NumberFormat("it-IT").format(r||0),z=r=>r?new Date(r).toLocaleDateString("it-IT"):"N/A",V=r=>r?new Date(r).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}):"N/A",H=r=>({technology:"Tecnologia",healthcare:"Sanità",finance:"Finanza",manufacturing:"Manifatturiero",retail:"Retail",consulting:"Consulenza",education:"Educazione",government:"Pubblico",other:"Altro"})[r]||r||"Non specificato",q=async()=>{var r;if(a.value){if(!n.value.contract_type){m("Seleziona il tipo di contratto","error");return}if(n.value.contract_type==="fixed"&&(!n.value.budget_amount||n.value.budget_amount<=0)){m("Inserisci un importo fisso valido","error");return}if(n.value.contract_type==="hourly"&&(!n.value.hourly_rate||n.value.hourly_rate<=0)){m("Inserisci una tariffa oraria valida","error");return}try{D.value=!0;const t={contract_type:n.value.contract_type,start_date:n.value.start_date,end_date:n.value.end_date||null,budget_amount:n.value.budget_amount,hourly_rate:n.value.hourly_rate},i=await w.createContractFromProposal(a.value.id,t);C.value=!1,(r=i==null?void 0:i.contract)!=null&&r.id?I.push(`/app/crm/contracts/${i.contract.id}`):I.push("/app/crm/contracts")}catch(t){console.error("Error creating contract from proposal:",t)}finally{D.value=!1}}},O=()=>{a.value&&(n.value={contract_type:"",start_date:new Date().toISOString().split("T")[0],end_date:"",budget_amount:a.value.value||null,hourly_rate:null},C.value=!0)};return Q(()=>{R()}),(r,t)=>{var v,y,f,b,$,E;const i=G("router-link");return s(),o("div",ot,[u(at,{title:((v=a.value)==null?void 0:v.title)||"Caricamento...",subtitle:"Dettaglio proposta commerciale",breadcrumbs:[{name:"CRM",href:"/app/crm"},{name:"Proposte",href:"/app/crm/proposals"},{name:((y=a.value)==null?void 0:y.title)||"Dettaglio",href:"#",current:!0}],loading:_.value},{actions:M(()=>[a.value?(s(),o("div",st,[u(i,{to:`/app/crm/proposals/${a.value.id}/edit`,class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},{default:M(()=>[u(p,{name:"pencil",class:"w-4 h-4 mr-2"}),t[12]||(t[12]=g(" Modifica Proposta "))]),_:1,__:[12]},8,["to"]),a.value.status==="draft"?(s(),o("button",{key:0,onClick:t[0]||(t[0]=d=>x("sent")),class:"btn-primary"},[u(p,{name:"paper-airplane",class:"w-4 h-4 mr-2"}),t[13]||(t[13]=g(" Invia Proposta "))])):a.value.status==="sent"?(s(),o("button",{key:1,onClick:t[1]||(t[1]=d=>x("accepted")),class:"btn-primary"},[u(p,{name:"check",class:"w-4 h-4 mr-2"}),t[14]||(t[14]=g(" Accetta Proposta "))])):l("",!0)])):l("",!0)]),_:1},8,["title","breadcrumbs","loading"]),e("div",nt,[_.value?(s(),o("div",lt,t[15]||(t[15]=[e("div",{class:"flex justify-center"},[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})],-1)]))):a.value?(s(),o("div",it,[u(rt,{stats:L.value},null,8,["stats"]),e("div",dt,[e("div",ut,[e("div",ct,[u(et,{status:a.value.status,type:"proposal",size:"large"},null,8,["status"]),e("div",gt,[a.value.value?(s(),o("div",pt," €"+c(j(a.value.value)),1)):l("",!0),t[16]||(t[16]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Valore Proposta",-1))])]),e("div",mt,[e("div",null,[t[17]||(t[17]=e("div",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Data Creazione",-1)),e("div",vt,c(z(a.value.created_at)),1)]),a.value.sent_date?(s(),o("div",yt,[t[18]||(t[18]=e("div",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Data Invio",-1)),e("div",bt,c(z(a.value.sent_date)),1)])):l("",!0),a.value.expiry_date?(s(),o("div",xt,[t[19]||(t[19]=e("div",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Data Scadenza",-1)),e("div",{class:W(["text-lg font-medium",T.value?"text-red-600 dark:text-red-400":"text-gray-900 dark:text-white"])},[g(c(z(a.value.expiry_date))+" ",1),T.value?(s(),o("span",ft,"(Scaduta)")):N.value?(s(),o("span",kt,"(In scadenza)")):l("",!0)],2)])):l("",!0)])])]),e("div",ht,[e("div",wt,[e("div",_t,[t[22]||(t[22]=e("div",{class:"p-6 border-b border-gray-200 dark:border-gray-600"},[e("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Dettagli Proposta")],-1)),e("div",Ct,[e("dl",Dt,[e("div",null,[t[20]||(t[20]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Titolo",-1)),e("dd",zt,c(a.value.title),1)]),a.value.description?(s(),o("div",St,[t[21]||(t[21]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Descrizione",-1)),e("dd",Mt,c(a.value.description),1)])):l("",!0)])])]),e("div",Pt,[t[27]||(t[27]=e("div",{class:"p-6 border-b border-gray-200 dark:border-gray-600"},[e("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Timeline Attività")],-1)),e("div",It,[e("div",jt,[e("ul",Tt,[e("li",Vt,[e("div",$t,[t[24]||(t[24]=e("div",null,[e("span",{class:"h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white dark:ring-gray-800"},[e("svg",{class:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"})])])],-1)),e("div",Et,[e("div",null,[e("div",At,[e("span",Bt,c((f=a.value.creator)==null?void 0:f.first_name)+" "+c((b=a.value.creator)==null?void 0:b.last_name),1),t[23]||(t[23]=g(" ha creato la proposta "))]),e("div",Nt,c(V(a.value.created_at)),1)])])])]),a.value.sent_date?(s(),o("li",Lt,[e("div",Rt,[t[26]||(t[26]=e("div",null,[e("span",{class:"h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white dark:ring-gray-800"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})])])],-1)),e("div",Ft,[e("div",null,[t[25]||(t[25]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"}," Proposta inviata al cliente ",-1)),e("div",Ht,c(V(a.value.sent_date)),1)])])])])):l("",!0)])])])])]),e("div",qt,[e("div",Ot,[t[32]||(t[32]=e("div",{class:"p-6 border-b border-gray-200 dark:border-gray-600"},[e("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Cliente")],-1)),e("div",Ut,[a.value.client?(s(),o("div",Qt,[e("div",Wt,[t[28]||(t[28]=e("div",{class:"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H9m12 0a2 2 0 01-2 2H5a2 2 0 01-2-2m0 0V9a2 2 0 012-2h2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10M7 11h10M7 15h10"})])],-1)),e("div",null,[e("div",Gt,c(a.value.client.name),1),a.value.client.industry?(s(),o("div",Jt,c(H(a.value.client.industry)),1)):l("",!0)])]),e("div",Kt,[a.value.client.website?(s(),o("div",Xt,[t[29]||(t[29]=e("svg",{class:"w-4 h-4 mr-2 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})],-1)),e("a",{href:a.value.client.website,target:"_blank",class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"},c(a.value.client.website),9,Yt)])):l("",!0),a.value.client.address?(s(),o("div",Zt,[t[30]||(t[30]=e("svg",{class:"w-4 h-4 mr-2 mt-0.5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),e("span",te,c(a.value.client.address),1)])):l("",!0)]),e("div",ee,[u(i,{to:`/app/crm/clients/${a.value.client.id}`,class:"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},{default:M(()=>t[31]||(t[31]=[g(" Visualizza Cliente ")])),_:1,__:[31]},8,["to"])])])):l("",!0)])]),e("div",ae,[t[39]||(t[39]=e("div",{class:"p-6 border-b border-gray-200 dark:border-gray-600"},[e("h2",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Azioni Rapide")],-1)),e("div",re,[a.value.status==="draft"?(s(),o("button",{key:0,onClick:t[2]||(t[2]=d=>x("sent")),class:"w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors"},[u(p,{name:"paper-airplane",class:"w-4 h-4 mr-2"}),t[33]||(t[33]=g(" Invia al Cliente "))])):l("",!0),["sent","negotiating"].includes(a.value.status)?(s(),o("button",{key:1,onClick:t[3]||(t[3]=d=>x("accepted")),class:"w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 transition-colors"},[u(p,{name:"check",class:"w-4 h-4 mr-2"}),t[34]||(t[34]=g(" Segna Accettata "))])):l("",!0),["sent","negotiating"].includes(a.value.status)?(s(),o("button",{key:2,onClick:t[4]||(t[4]=d=>x("rejected")),class:"w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[u(p,{name:"x-mark",class:"w-4 h-4 mr-2"}),t[35]||(t[35]=g(" Segna Rifiutata "))])):l("",!0),a.value.status==="sent"?(s(),o("button",{key:3,onClick:t[5]||(t[5]=d=>x("negotiating")),class:"w-full inline-flex items-center justify-center px-4 py-2 border border-yellow-300 dark:border-yellow-600 text-sm font-medium rounded-md text-yellow-700 dark:text-yellow-300 bg-yellow-50 dark:bg-yellow-900/30 hover:bg-yellow-100 dark:hover:bg-yellow-900/50 transition-colors"},[u(p,{name:"clock",class:"w-4 h-4 mr-2"}),t[36]||(t[36]=g(" Avvia Negoziazione "))])):l("",!0),a.value.status==="accepted"?(s(),o("button",{key:4,onClick:O,class:"w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 transition-colors"},[u(p,{name:"document-text",class:"w-4 h-4 mr-2"}),t[37]||(t[37]=g(" Crea Contratto "))])):l("",!0),e("button",{onClick:F,class:"w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[u(p,{name:"document-duplicate",class:"w-4 h-4 mr-2"}),t[38]||(t[38]=g(" Duplica Proposta "))])])])])])])):(s(),o("div",oe,[e("div",se,[t[41]||(t[41]=e("svg",{class:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),t[42]||(t[42]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Proposta non trovata",-1)),t[43]||(t[43]=e("p",{class:"text-gray-500 dark:text-gray-400 mb-4"},"La proposta richiesta non esiste o non è accessibile",-1)),u(i,{to:"/app/crm/proposals",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors"},{default:M(()=>t[40]||(t[40]=[g(" Torna alle Proposte ")])),_:1,__:[40]})])]))]),C.value?(s(),o("div",ne,[e("div",le,[e("div",ie,[t[52]||(t[52]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Crea Contratto da Proposta ",-1)),t[53]||(t[53]=e("div",{class:"mb-4"},[e("p",{class:"text-sm text-gray-600 dark:text-gray-400"}," Questa proposta accettata verrà convertita in un nuovo contratto. I dati principali saranno copiati automaticamente. ")],-1)),e("form",{onSubmit:J(q,["prevent"])},[e("div",de,[e("div",null,[t[45]||(t[45]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Tipo Contratto * ",-1)),h(e("select",{"onUpdate:modelValue":t[6]||(t[6]=d=>n.value.contract_type=d),required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"},t[44]||(t[44]=[X('<option value="">Seleziona tipo</option><option value="fixed">Prezzo Fisso</option><option value="hourly">Tariffa Oraria</option><option value="retainer">Retainer</option><option value="milestone">Milestone</option><option value="subscription">Abbonamento</option>',6)]),512),[[K,n.value.contract_type]])]),e("div",null,[t[46]||(t[46]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Data Inizio Contratto * ",-1)),h(e("input",{"onUpdate:modelValue":t[7]||(t[7]=d=>n.value.start_date=d),type:"date",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"},null,512),[[P,n.value.start_date]])]),e("div",null,[t[47]||(t[47]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Data Fine (opzionale) ",-1)),h(e("input",{"onUpdate:modelValue":t[8]||(t[8]=d=>n.value.end_date=d),type:"date",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"},null,512),[[P,n.value.end_date]]),t[48]||(t[48]=e("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Lascia vuoto per contratto a tempo indeterminato ",-1))]),n.value.contract_type==="fixed"?(s(),o("div",ue,[t[49]||(t[49]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Importo Fisso (€) * ",-1)),h(e("input",{"onUpdate:modelValue":t[9]||(t[9]=d=>n.value.budget_amount=d),type:"number",step:"0.01",min:"0",required:"",placeholder:($=a.value)!=null&&$.value?a.value.value.toString():"0.00",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"},null,8,ce),[[P,n.value.budget_amount,void 0,{number:!0}]]),e("p",ge," Valore proposta: €"+c((E=a.value)!=null&&E.value?j(a.value.value):"0"),1)])):l("",!0),n.value.contract_type==="hourly"?(s(),o("div",pe,[t[50]||(t[50]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Tariffa Oraria (€) * ",-1)),h(e("input",{"onUpdate:modelValue":t[10]||(t[10]=d=>n.value.hourly_rate=d),type:"number",step:"0.01",min:"0",required:"",placeholder:"0.00",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"},null,512),[[P,n.value.hourly_rate,void 0,{number:!0}]])])):l("",!0)]),e("div",me,[e("button",{type:"button",onClick:t[11]||(t[11]=d=>C.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"}," Annulla "),e("button",{type:"submit",disabled:D.value,class:"px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 disabled:opacity-50 dark:bg-green-700 dark:hover:bg-green-800 transition-colors"},[D.value?(s(),o("span",ye,t[51]||(t[51]=[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),g(" Creando Contratto... ")]))):(s(),o("span",be,"Crea Contratto"))],8,ve)])],32)])])])):l("",!0)])}}};export{De as default};
