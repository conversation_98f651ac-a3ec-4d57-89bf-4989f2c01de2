import{r as z,c as S,w as re,b as a,o as s,j as e,l as i,e as d,s as P,t as l,n as O,A as ae,h as H,E as X,F as B,p as M,B as ie,Q as de,k as J,x as ne,u as ue,q as ce}from"./vendor.js";import{u as me}from"./personnel.js";import{_ as F,d as ge,H as f,c as N,u as ve,h as ye}from"./app.js";import{S as R}from"./StandardInput.js";import{S as q}from"./StandardButton.js";import{T as xe}from"./TabContainer.js";import{P as pe}from"./Pagination.js";import{S as fe}from"./StatusBadge.js";/* empty css                                                             */const be={class:"space-y-6"},ke={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},he={class:"flex items-center justify-between mb-4"},_e={key:1,class:"flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},$e={class:"flex-1"},we={class:"text-sm font-medium text-gray-900 dark:text-white"},Ce={class:"text-xs text-gray-500 dark:text-gray-400"},je={key:0,class:"mt-1"},ze={class:"flex space-x-2"},Pe={key:2,class:"mt-4"},Te={class:"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2"},Se={class:"flex items-center"},Le={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Ie={key:0,class:"bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6"},Ee={class:"flex items-center justify-between mb-4"},Ae={class:"flex items-center"},De={class:"w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mr-3"},Ve={class:"flex items-center space-x-3"},Be={key:0,class:"mb-4"},Me={class:"text-sm text-purple-700 dark:text-purple-300 bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700"},Ue={key:1,class:"mb-4"},Je={class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-200"},Ne={key:2},Oe={class:"grid grid-cols-1 md:grid-cols-2 gap-3 mb-3"},Re={class:"flex items-center space-x-2"},qe={class:"text-sm font-medium text-gray-900 dark:text-white"},Fe={class:"flex items-center space-x-2"},He={key:0,class:"text-xs text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-2 py-1 rounded"},Qe={class:"text-xs text-gray-500 dark:text-gray-400"},Xe={key:0,class:"text-center"},Ye={class:"text-sm text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-3 py-1 rounded-full"},We={class:"mt-3"},Ge={class:"flex items-center justify-between mb-4"},Ke={class:"max-h-96 overflow-y-auto mb-4"},Ze={class:"space-y-2"},et=["id","value"],tt=["for"],st={class:"text-sm font-medium text-gray-900 dark:text-white"},at={class:"text-xs text-gray-500 dark:text-gray-400"},rt={key:0},lt={key:0,class:"text-xs text-gray-400 mt-1"},ot={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600"},nt=["disabled"],it={__name:"CVTab",props:{user:{type:Object,required:!0},canEdit:{type:Boolean,default:!1}},emits:["user-updated"],setup(r,{emit:h}){const _=r,c=h,{success:t,error:g,info:y}=ge(),b=z(!1),v=z(!1),n=z(0),w=z(!1),x=z(!1),L=z([]),$=z(!1),u=z(null),E=z(0),I=S(()=>{var k;if(!((k=_.user.profile)!=null&&k.cv_analysis_data))return null;try{return JSON.parse(_.user.profile.cv_analysis_data)}catch(m){return console.error("Error parsing CV analysis data:",m),null}}),j=k=>k?new Date(k).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",Q=k=>({beginner:"Principiante",intermediate:"Intermedio",advanced:"Avanzato",expert:"Esperto",1:"Principiante",2:"Base",3:"Intermedio",4:"Avanzato",5:"Esperto"})[k]||"Intermedio",Y=()=>{var k;(k=u.value)==null||k.click()},te=k=>{const m=k.target.files[0];m&&W(m)},se=k=>{k.preventDefault(),w.value=!1;const m=k.dataTransfer.files;m.length>0&&W(m[0])},W=async k=>{var D;if(!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/msword","text/plain"].includes(k.type)){g("Formato file non supportato","Usa PDF, DOCX, DOC o TXT.");return}if(k.size>10*1024*1024){g("File troppo grande","Dimensione massima: 10MB.");return}b.value=!0,n.value=0;try{const o=new FormData;o.append("cv_file",k),o.append("analyze_skills","true"),o.append("auto_add_skills","false");const p=setInterval(()=>{n.value<90&&(n.value+=Math.random()*15)},200),T=await N.post(`/api/personnel/users/${_.user.id}/cv/upload`,o,{headers:{"Content-Type":"multipart/form-data"}});clearInterval(p),n.value=100;const A=T.data;if(console.log("Upload response:",A),A.success){if(b.value=!1,c("user-updated",A.data),(D=A.data.profile)!=null&&D.cv_analysis_data)try{const C=JSON.parse(A.data.profile.cv_analysis_data);console.log("Analysis data:",C),C.skills&&C.skills.length>0?t("CV caricato e analizzato con successo!",`Trovate ${C.skills.length} competenze`):t("CV caricato con successo!","Nessuna competenza estratta dall'AI")}catch(C){console.error("Error parsing analysis data:",C),t("CV caricato con successo!","Errore nel parsing dei dati AI")}else t("CV caricato con successo!","Analisi AI non disponibile"),console.log("No AI analysis data found in response. Profile data:",A.data.profile);u.value&&(u.value.value="")}else throw new Error(A.message||"Errore durante il caricamento")}catch(o){console.error("Errore durante il caricamento del CV:",o),g("Errore durante il caricamento del CV",o.message)}finally{b.value=!1,n.value=0}},G=async()=>{try{const m=(await N.get(`/api/personnel/users/${_.user.id}/cv/download`,{responseType:"blob"})).data,D=window.URL.createObjectURL(m),o=document.createElement("a");o.href=D,o.download=`CV_${_.user.full_name}.pdf`,document.body.appendChild(o),o.click(),window.URL.revokeObjectURL(D),document.body.removeChild(o)}catch(k){console.error("Errore durante il download del CV:",k),alert("Errore durante il download del CV: "+k.message)}},K=async()=>{if(confirm("Sei sicuro di voler eliminare il CV? Questa azione non può essere annullata."))try{const m=(await N.delete(`/api/personnel/users/${_.user.id}/cv`)).data;if(m.success)c("user-updated",m.data),t("CV eliminato con successo");else throw new Error(m.message||"Errore durante l'eliminazione")}catch(k){console.error("Errore durante l'eliminazione del CV:",k),g("Errore durante l'eliminazione del CV",k.message)}},Z=async()=>{if(L.value.length!==0){$.value=!0;try{const k=L.value.map(o=>{const p=I.value.skills[o];return{...p,level:ee(p.level)}}),D=(await N.post(`/api/personnel/users/${_.user.id}/skills/from-cv`,{selected_skills:k})).data;if(D.success){const{total_added:o,total_skipped:p}=D.data;o>0&&t(`Aggiunte ${o} competenze al profilo!`),p>0&&y(`${p} competenze erano già presenti nel profilo`),x.value=!1,L.value=[],c("user-updated")}else throw new Error(D.message||"Errore durante l'aggiunta delle competenze")}catch(k){console.error("Errore durante l'aggiunta delle competenze:",k),g("Errore durante l'aggiunta delle competenze",k.message)}finally{$.value=!1}}},ee=k=>{const m={beginner:1,intermediate:3,advanced:4,expert:5};return typeof k=="number"?Math.max(1,Math.min(5,k)):typeof k=="string"&&m[k.toLowerCase()]||3};return re(b,k=>{k||(n.value=0)}),re(v,k=>{k||(E.value=0)}),(k,m)=>{var D,o,p,T,A;return s(),a("div",be,[e("div",ke,[e("div",he,[m[8]||(m[8]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"CV Attuale",-1)),r.canEdit&&!b.value?(s(),a("button",{key:0,onClick:Y,class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200"},[d(f,{name:"arrow-up-tray",size:"sm",class:"inline mr-2"}),P(" "+l((D=r.user.profile)!=null&&D.current_cv_path?"Aggiorna CV":"Carica CV"),1)])):i("",!0)]),!((o=r.user.profile)!=null&&o.current_cv_path)&&r.canEdit?(s(),a("div",{key:0,onDrop:se,onDragover:m[0]||(m[0]=ae(()=>{},["prevent"])),onDragenter:m[1]||(m[1]=ae(()=>{},["prevent"])),class:O(["border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200",w.value?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600"])},[d(f,{name:"cloud-arrow-up",size:"2xl",class:"mx-auto text-gray-400"}),m[10]||(m[10]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Carica il tuo CV",-1)),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},[m[9]||(m[9]=P(" Trascina qui il file o ")),e("button",{onClick:Y,class:"text-blue-600 hover:text-blue-500"},"sfoglia")]),m[11]||(m[11]=e("p",{class:"mt-1 text-xs text-gray-400"},"PDF, DOCX, DOC, TXT (max 10MB)",-1))],34)):(p=r.user.profile)!=null&&p.current_cv_path?(s(),a("div",_e,[d(f,{name:"document-text",size:"lg",class:"text-red-600 mr-3"}),e("div",$e,[e("p",we,"CV_"+l(r.user.full_name)+".pdf",1),e("p",Ce," Caricato il "+l(j(r.user.profile.cv_last_updated)),1),r.user.profile.cv_analysis_data?(s(),a("div",je,m[12]||(m[12]=[e("span",{class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"}," ✨ Analisi AI completata ",-1)]))):i("",!0)]),e("div",ze,[e("button",{onClick:G,class:"text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300",title:"Scarica CV"},[d(f,{name:"arrow-down-tray",size:"md"})]),r.canEdit?(s(),a("button",{key:0,onClick:K,class:"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300",title:"Elimina CV"},[d(f,{name:"trash",size:"md"})])):i("",!0)])])):i("",!0),b.value||v.value?(s(),a("div",Pe,[e("div",Te,[e("div",Se,[v.value?(s(),H(f,{key:0,name:"arrow-path",size:"sm",class:"animate-spin -ml-1 mr-2 text-blue-600"})):i("",!0),e("span",null,l(v.value?"Analisi AI in corso...":"Caricamento in corso..."),1)]),e("span",null,l(v.value?E.value:n.value)+"%",1)]),e("div",Le,[e("div",{class:O(["h-2 rounded-full transition-all duration-300",v.value?"bg-purple-600":"bg-blue-600"]),style:X({width:(v.value?E.value:n.value)+"%"})},null,6)])])):i("",!0)]),I.value?(s(),a("div",Ie,[e("div",Ee,[e("div",Ae,[e("div",De,[d(f,{name:"bolt",size:"sm",class:"text-purple-600 dark:text-purple-300"})]),m[13]||(m[13]=e("h3",{class:"text-lg font-medium text-purple-900 dark:text-purple-100"}," Analisi AI del CV ",-1))]),e("div",Ve,[r.canEdit&&((T=I.value.skills)==null?void 0:T.length)>0?(s(),a("button",{key:0,onClick:m[2]||(m[2]=C=>x.value=!0),class:"text-sm bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-md transition-colors duration-200 flex items-center"},[d(f,{name:"plus",size:"sm",class:"mr-1"}),m[14]||(m[14]=P(" Aggiungi Competenze "))])):i("",!0)])]),I.value.summary?(s(),a("div",Be,[m[15]||(m[15]=e("h4",{class:"text-sm font-medium text-purple-800 dark:text-purple-200 mb-2"},"Profilo Professionale",-1)),e("p",Me,l(I.value.summary),1)])):i("",!0),I.value.experience_years?(s(),a("div",Ue,[e("span",Je,[d(f,{name:"clock",size:"sm",class:"mr-1"}),P(" "+l(I.value.experience_years)+" anni di esperienza ",1)])])):i("",!0),((A=I.value.skills)==null?void 0:A.length)>0?(s(),a("div",Ne,[m[17]||(m[17]=e("h4",{class:"text-sm font-medium text-purple-800 dark:text-purple-200 mb-3"},"Competenze Estratte",-1)),e("div",Oe,[(s(!0),a(B,null,M(I.value.skills.slice(0,8),(C,V)=>(s(),a("div",{key:V,class:"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700"},[e("div",Re,[m[16]||(m[16]=e("div",{class:"w-2 h-2 bg-purple-500 rounded-full"},null,-1)),e("span",qe,l(C.name),1)]),e("div",Fe,[C.category?(s(),a("span",He,l(C.category),1)):i("",!0),e("span",Qe,l(Q(C.level)),1)])]))),128))]),I.value.skills.length>8?(s(),a("div",Xe,[e("span",Ye," +"+l(I.value.skills.length-8)+" altre competenze disponibili ",1)])):i("",!0)])):i("",!0)])):i("",!0),e("input",{ref_key:"fileInput",ref:u,type:"file",accept:".pdf,.docx,.doc,.txt",onChange:te,class:"hidden"},null,544),x.value?(s(),a("div",{key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:m[7]||(m[7]=C=>x.value=!1)},[e("div",{class:"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:m[6]||(m[6]=ae(()=>{},["stop"]))},[e("div",We,[e("div",Ge,[m[18]||(m[18]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Seleziona Competenze da Aggiungere ",-1)),e("button",{onClick:m[3]||(m[3]=C=>x.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[d(f,{name:"x-mark",size:"md"})])]),e("div",Ke,[e("div",Ze,[(s(!0),a(B,null,M(I.value.skills,(C,V)=>(s(),a("div",{key:V,class:"flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"},[ie(e("input",{type:"checkbox",id:`skill-${V}`,"onUpdate:modelValue":m[4]||(m[4]=U=>L.value=U),value:V,class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,8,et),[[de,L.value]]),e("label",{for:`skill-${V}`,class:"ml-3 flex-1 cursor-pointer"},[e("div",st,l(C.name),1),e("div",at,[P(l(C.category)+" • Livello "+l(C.level||3)+" ",1),C.years_experience?(s(),a("span",rt," • "+l(C.years_experience)+" anni",1)):i("",!0)]),C.context?(s(),a("div",lt,l(C.context),1)):i("",!0)],8,tt)]))),128))])]),e("div",ot,[e("button",{onClick:m[5]||(m[5]=C=>x.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors duration-200"}," Annulla "),e("button",{onClick:Z,disabled:L.value.length===0||$.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors duration-200"},l($.value?"Aggiungendo...":`Aggiungi ${L.value.length} competenze`),9,nt)])])])])):i("",!0)])}}},dt=F(it,[["__scopeId","data-v-70b5f306"]]),ut={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ct={class:"flex items-center justify-between mb-4"},mt={class:"text-lg font-medium text-gray-900 dark:text-white flex items-center"},gt={key:0,class:"space-y-4"},vt={class:"flex items-start justify-between"},yt={class:"flex-1"},xt={class:"flex items-center space-x-3"},pt={class:"text-xl font-semibold text-gray-900 dark:text-white"},ft={key:0,class:"text-sm text-gray-600 dark:text-gray-400 mt-1"},bt={key:1,class:"text-sm text-gray-600 dark:text-gray-400 mt-2"},kt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ht={key:0,class:"bg-green-50 dark:bg-green-900/20 p-3 rounded-lg"},_t={class:"flex items-center"},$t={class:"text-lg font-semibold text-green-800 dark:text-green-200"},wt={class:"bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg"},Ct={class:"flex items-center"},jt={class:"text-sm font-semibold text-blue-800 dark:text-blue-200"},zt={key:0,class:"bg-gray-50 dark:bg-gray-700 p-3 rounded-lg"},Pt={class:"flex items-center space-x-4"},Tt={key:0,class:"flex items-center"},St={class:"text-sm font-medium text-gray-900 dark:text-white"},Lt={key:1,class:"flex items-center"},It={class:"text-sm font-medium text-gray-900 dark:text-white"},Et={key:1,class:"border-t border-gray-200 dark:border-gray-700 pt-4"},At={class:"text-sm text-gray-600 dark:text-gray-400"},Dt={key:2,class:"border-t border-gray-200 dark:border-gray-700 pt-4"},Vt={class:"text-sm text-gray-600 dark:text-gray-400"},Bt={key:1,class:"text-center py-8"},Mt={key:2,class:"border-t border-gray-200 dark:border-gray-700 pt-4 mt-4"},Ut={class:"space-y-2"},Jt={class:"flex items-center"},Nt={class:"text-gray-900 dark:text-white"},Ot={class:"text-gray-500 dark:text-gray-400"},Rt={__name:"JobLevelCard",props:{currentJobLevel:{type:Object,default:null},jobLevelHistory:{type:Array,default:()=>[]},showHistory:{type:Boolean,default:!0},showCompactHistory:{type:Boolean,default:!0},canAssign:{type:Boolean,default:!1}},emits:["show-history","assign-job-level"],setup(r){const h=r,_=S(()=>h.jobLevelHistory&&h.jobLevelHistory.length>1),c=v=>({dirigente:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",quadro:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",impiegato:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",operaio:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"})[v]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",t=v=>({dirigente:"Dirigente",quadro:"Quadro",impiegato:"Impiegato",operaio:"Operaio"})[v]||v,g=v=>v?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(v):"",y=v=>v?new Date(v).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",b=v=>v?new Date(v).toLocaleDateString("it-IT",{year:"2-digit",month:"short"}):"";return(v,n)=>(s(),a("div",ut,[e("div",ct,[e("h3",mt,[d(f,{name:"academic-cap",size:"md",class:"text-blue-600 mr-2"}),n[2]||(n[2]=P(" Inquadramento "))]),r.showHistory&&_.value?(s(),a("button",{key:0,onClick:n[0]||(n[0]=w=>v.$emit("show-history")),class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"}," Storico ")):i("",!0)]),r.currentJobLevel?(s(),a("div",gt,[e("div",vt,[e("div",yt,[e("div",xt,[e("h4",pt,l(r.currentJobLevel.job_level.name),1),e("span",{class:O(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",c(r.currentJobLevel.job_level.category)])},l(t(r.currentJobLevel.job_level.category)),3)]),r.currentJobLevel.job_level.level?(s(),a("p",ft," Livello: "+l(r.currentJobLevel.job_level.level),1)):i("",!0),r.currentJobLevel.job_level.description?(s(),a("p",bt,l(r.currentJobLevel.job_level.description),1)):i("",!0)])]),e("div",kt,[r.currentJobLevel.current_salary?(s(),a("div",ht,[e("div",_t,[d(f,{name:"banknotes",size:"sm",class:"text-green-600 mr-2"}),e("div",null,[n[3]||(n[3]=e("p",{class:"text-xs text-green-600 dark:text-green-400 font-medium"},"RAL Attuale",-1)),e("p",$t,l(g(r.currentJobLevel.current_salary)),1)])])])):i("",!0),e("div",wt,[e("div",Ct,[d(f,{name:"calendar",size:"sm",class:"text-blue-600 mr-2"}),e("div",null,[n[4]||(n[4]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"Dal",-1)),e("p",jt,l(y(r.currentJobLevel.start_date)),1)])])])]),r.currentJobLevel.job_level.min_salary||r.currentJobLevel.job_level.max_salary?(s(),a("div",zt,[n[7]||(n[7]=e("p",{class:"text-xs text-gray-600 dark:text-gray-400 font-medium mb-2"},"Range Retributivo per questo Inquadramento",-1)),e("div",Pt,[r.currentJobLevel.job_level.min_salary?(s(),a("div",Tt,[n[5]||(n[5]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400 mr-1"},"Min:",-1)),e("span",St,l(g(r.currentJobLevel.job_level.min_salary)),1)])):i("",!0),r.currentJobLevel.job_level.max_salary?(s(),a("div",Lt,[n[6]||(n[6]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400 mr-1"},"Max:",-1)),e("span",It,l(g(r.currentJobLevel.job_level.max_salary)),1)])):i("",!0)])])):i("",!0),r.currentJobLevel.job_level.benefits?(s(),a("div",Et,[n[8]||(n[8]=e("h5",{class:"text-sm font-medium text-gray-900 dark:text-white mb-2"},"Benefits",-1)),e("p",At,l(r.currentJobLevel.job_level.benefits),1)])):i("",!0),r.currentJobLevel.notes?(s(),a("div",Dt,[n[9]||(n[9]=e("h5",{class:"text-sm font-medium text-gray-900 dark:text-white mb-2"},"Note",-1)),e("p",Vt,l(r.currentJobLevel.notes),1)])):i("",!0)])):(s(),a("div",Bt,[d(f,{name:"academic-cap",size:"xl",class:"mx-auto text-gray-400 mb-4"}),n[11]||(n[11]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun Inquadramento",-1)),n[12]||(n[12]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400"}," Questo dipendente non ha ancora un inquadramento assegnato. ",-1)),r.canAssign?(s(),a("button",{key:0,onClick:n[1]||(n[1]=w=>v.$emit("assign-job-level")),class:"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},[d(f,{name:"plus",size:"sm",class:"mr-2"}),n[10]||(n[10]=P(" Assegna Inquadramento "))])):i("",!0)])),r.showCompactHistory&&r.jobLevelHistory&&r.jobLevelHistory.length>1?(s(),a("div",Mt,[n[14]||(n[14]=e("h5",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"},"Storico Recente",-1)),e("div",Ut,[(s(!0),a(B,null,M(r.jobLevelHistory.slice(1,3),w=>(s(),a("div",{key:w.id,class:"flex items-center justify-between text-sm"},[e("div",Jt,[n[13]||(n[13]=e("div",{class:"w-2 h-2 bg-gray-400 rounded-full mr-3"},null,-1)),e("span",Nt,l(w.job_level_name),1)]),e("span",Ot,l(b(w.start_date))+" - "+l(w.end_date?b(w.end_date):"Presente"),1)]))),128))])])):i("",!0)]))}},qt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Ft={class:"flex items-center space-x-6"},Ht={class:"flex-shrink-0"},Qt={class:"w-24 h-24 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg border-2 border-white/30"},Xt=["src","alt"],Yt={key:1,class:"w-24 h-24 bg-white/10 rounded-full flex items-center justify-center"},Wt={class:"flex-1"},Gt={class:"text-3xl font-bold text-white"},Kt={class:"text-white/90 text-lg mt-1"},Zt={class:"flex items-center space-x-3 mt-3"},es={key:0,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 backdrop-blur-sm text-white border border-white/30"},ts={class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 backdrop-blur-sm text-white border border-white/30"},ss={class:"flex-shrink-0"},as={key:0,class:"px-6 py-4 bg-gray-50 dark:bg-gray-700"},rs={class:"flex items-center justify-between mb-2"},ls={class:"text-sm text-gray-500 dark:text-gray-400"},os={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},ns={__name:"ProfileHeader",props:{user:{type:Object,required:!0},canEdit:{type:Boolean,default:!1},editMode:{type:Boolean,default:!1}},emits:["toggle-edit"],setup(r){ve();const h=S(()=>({background:"linear-gradient(135deg, var(--brand-primary-500) 0%, var(--brand-secondary-500) 100%)"})),_=S(()=>({backgroundColor:"var(--brand-primary-500)"}));return(c,t)=>(s(),a("div",qt,[e("div",{class:"px-6 py-8 text-white",style:X(h.value)},[e("div",Ft,[e("div",Ht,[e("div",Qt,[r.user.profile_image?(s(),a("img",{key:0,src:r.user.profile_image,alt:r.user.full_name,class:"w-24 h-24 rounded-full object-cover"},null,8,Xt)):(s(),a("div",Yt,[d(f,{name:"user",size:"xl",class:"text-white/80"})]))])]),e("div",Wt,[e("h1",Gt,l(r.user.full_name),1),e("p",Kt,l(r.user.position||"Posizione non specificata"),1),e("div",Zt,[r.user.department_obj?(s(),a("span",es,[d(f,{name:"building-office",size:"sm",class:"mr-1"}),P(" "+l(r.user.department_obj.name),1)])):i("",!0),e("span",ts,[d(f,{name:"briefcase",size:"sm",class:"mr-1"}),P(" "+l(r.user.role),1)])])]),e("div",ss,[r.canEdit?(s(),a("button",{key:0,onClick:t[0]||(t[0]=g=>c.$emit("toggle-edit")),class:"bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white px-4 py-2 rounded-lg transition-all duration-200 border border-white/30 hover:border-white/50"},[d(f,{name:"pencil",size:"md",class:"inline mr-2"}),P(" "+l(r.editMode?"Annulla":"Modifica"),1)])):i("",!0)])])],4),r.user.profile&&r.user.profile.profile_completion!==void 0?(s(),a("div",as,[e("div",rs,[t[1]||(t[1]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Completamento Profilo",-1)),e("span",ls,l(r.user.profile.profile_completion)+"%",1)]),e("div",os,[e("div",{class:"h-2 rounded-full transition-all duration-300",style:X({width:r.user.profile.profile_completion+"%",..._.value})},null,4)])])):i("",!0)]))}},is={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ds={class:"flex items-center justify-between mb-4"},us={key:0,class:"space-y-3"},cs={class:"flex items-center"},ms={class:"text-gray-900 dark:text-white"},gs={key:0,class:"flex items-center"},vs={class:"text-gray-900 dark:text-white"},ys={key:1,class:"flex items-center"},xs={class:"text-gray-900 dark:text-white"},ps={key:1,class:"space-y-4"},fs={class:"flex space-x-2"},bs={__name:"ContactInfoCard",props:{user:{type:Object,required:!0},editMode:{type:Boolean,default:!1},editData:{type:Object,default:()=>({})},canEdit:{type:Boolean,default:!1},saving:{type:Boolean,default:!1}},emits:["toggle-edit","update-field","save","cancel"],setup(r){const h=_=>_?new Date(_).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"";return(_,c)=>(s(),a("div",is,[e("div",ds,[c[5]||(c[5]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni di Contatto",-1)),r.canEdit&&!r.editMode?(s(),a("button",{key:0,onClick:c[0]||(c[0]=t=>_.$emit("toggle-edit")),class:"text-brand-primary hover:text-brand-primary-600 transition-colors duration-200"},[d(f,{name:"pencil",size:"sm"})])):i("",!0)]),r.editMode?(s(),a("div",ps,[e("div",null,[c[6]||(c[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Telefono",-1)),d(R,{"model-value":r.editData.phone,"onUpdate:modelValue":c[1]||(c[1]=t=>_.$emit("update-field","phone",t)),type:"tel"},null,8,["model-value"])]),e("div",null,[c[7]||(c[7]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Bio",-1)),d(R,{"model-value":r.editData.bio,"onUpdate:modelValue":c[2]||(c[2]=t=>_.$emit("update-field","bio",t)),type:"textarea",rows:3},null,8,["model-value"])]),e("div",fs,[d(q,{variant:"primary",onClick:c[3]||(c[3]=t=>_.$emit("save")),disabled:r.saving,loading:r.saving,class:"flex-1"},{default:J(()=>c[8]||(c[8]=[P(" Salva ")])),_:1,__:[8]},8,["disabled","loading"]),d(q,{variant:"secondary",onClick:c[4]||(c[4]=t=>_.$emit("cancel")),class:"flex-1"},{default:J(()=>c[9]||(c[9]=[P(" Annulla ")])),_:1,__:[9]})])])):(s(),a("div",us,[e("div",cs,[d(f,{name:"envelope",size:"md",class:"text-gray-400 mr-3"}),e("span",ms,l(r.user.email),1)]),r.user.phone?(s(),a("div",gs,[d(f,{name:"phone",size:"md",class:"text-gray-400 mr-3"}),e("span",vs,l(r.user.phone),1)])):i("",!0),r.user.hire_date?(s(),a("div",ys,[d(f,{name:"calendar",size:"md",class:"text-gray-400 mr-3"}),e("span",xs,"Assunto il "+l(h(r.user.hire_date)),1)])):i("",!0)]))]))}},ks=F(bs,[["__scopeId","data-v-fff06dbf"]]),hs={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},_s={class:"flex items-center justify-between mb-4"},$s={key:0,class:"space-y-3"},ws={class:"text-sm font-medium text-gray-900 dark:text-white"},Cs={class:"flex items-center space-x-2"},js=["title"],zs={key:0,class:"certification-badge",title:"Competenza certificata"},Ps={key:0,class:"text-sm text-gray-500 dark:text-gray-400 text-center pt-2 border-t border-gray-100 dark:border-gray-700"},Ts={key:1,class:"empty-state"},Ss={class:"text-center py-8"},Ls={class:"w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600"},Is={__name:"SkillsOverviewCard",props:{skills:{type:Array,default:()=>[]},maxSkills:{type:Number,default:4},showViewAllButton:{type:Boolean,default:!0}},emits:["view-all"],setup(r){const h=r,_=S(()=>!h.skills||h.skills.length===0?[]:h.skills.slice(0,h.maxSkills));return(c,t)=>(s(),a("div",hs,[e("div",_s,[t[1]||(t[1]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Competenze Principali",-1)),r.showViewAllButton&&r.skills&&r.skills.length>r.maxSkills?(s(),a("button",{key:0,onClick:t[0]||(t[0]=g=>c.$emit("view-all")),class:"text-brand-primary hover:text-brand-primary-600 text-sm font-medium transition-colors duration-200"}," Vedi tutte ")):i("",!0)]),r.skills&&r.skills.length>0?(s(),a("div",$s,[(s(!0),a(B,null,M(_.value,g=>(s(),a("div",{key:g.id,class:"flex items-center justify-between group hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg p-2 -m-2 transition-colors duration-200"},[e("span",ws,l(g.name),1),e("div",Cs,[e("div",{class:"flex space-x-1",title:`Livello: ${g.proficiency_level}/5`},[(s(),a(B,null,M(5,y=>e("div",{key:y,class:O(["skill-dot transition-all duration-200",y<=g.proficiency_level?"skill-dot-active":"skill-dot-inactive"])},null,2)),64))],8,js),g.is_certified||g.certified?(s(),a("span",zs," ✓ ")):i("",!0)])]))),128)),r.skills.length>r.maxSkills?(s(),a("div",Ps," +"+l(r.skills.length-r.maxSkills)+" altre competenze ",1)):i("",!0)])):(s(),a("div",Ts,[e("div",Ss,[e("div",Ls,[d(f,{name:"star",size:"lg"})]),t[2]||(t[2]=e("p",{class:"text-gray-500 dark:text-gray-400 text-sm"},"Nessuna competenza registrata",-1)),t[3]||(t[3]=e("p",{class:"text-gray-400 dark:text-gray-500 text-xs mt-1"},"Le competenze verranno visualizzate qui una volta aggiunte",-1))])]))]))}},Es=F(Is,[["__scopeId","data-v-e3df80ed"]]),As={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},Ds={class:"flex items-center justify-between mb-6"},Vs={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},Bs={key:0,class:"hr-info-item"},Ms={class:"hr-info-value"},Us={key:1,class:"hr-info-item"},Js={class:"hr-info-value"},Ns={key:2,class:"hr-info-item"},Os={class:"hr-info-value"},Rs={key:3,class:"hr-info-item"},qs={class:"hr-info-value"},Fs={key:4,class:"hr-info-item"},Hs={class:"hr-info-value"},Qs={key:5,class:"hr-info-item"},Xs={class:"hr-info-value"},Ys={key:1,class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ws={class:"md:col-span-2 flex space-x-2"},Gs={__name:"HRInfoCard",props:{profile:{type:Object,required:!0},editMode:{type:Boolean,default:!1},editData:{type:Object,default:()=>({})},canEdit:{type:Boolean,default:!1},saving:{type:Boolean,default:!1}},emits:["toggle-edit","update-field","save","cancel"],setup(r){const h=c=>({full_time:"Tempo Pieno",part_time:"Part Time",contractor:"Consulente",intern:"Stagista"})[c]||c,_=c=>({office:"Ufficio",remote:"Remoto",hybrid:"Ibrido"})[c]||c;return(c,t)=>r.profile?(s(),a("div",As,[e("div",Ds,[t[9]||(t[9]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni HR",-1)),r.canEdit&&!r.editMode?(s(),a("button",{key:0,onClick:t[0]||(t[0]=g=>c.$emit("toggle-edit")),class:"text-brand-primary hover:text-brand-primary-600 transition-colors duration-200"},[d(f,{name:"pencil",size:"sm"})])):i("",!0)]),r.editMode?(s(),a("div",Ys,[e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"ID Dipendente",-1)),d(R,{"model-value":r.editData.employee_id,"onUpdate:modelValue":t[1]||(t[1]=g=>c.$emit("update-field","employee_id",g)),type:"text"},null,8,["model-value"])]),e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Titolo Lavoro",-1)),d(R,{"model-value":r.editData.job_title,"onUpdate:modelValue":t[2]||(t[2]=g=>c.$emit("update-field","job_title",g)),type:"text"},null,8,["model-value"])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Tipo Contratto",-1)),d(R,{"model-value":r.editData.employment_type,"onUpdate:modelValue":t[3]||(t[3]=g=>c.$emit("update-field","employment_type",g)),type:"select"},{default:J(()=>t[18]||(t[18]=[e("option",{value:""},"Seleziona tipo",-1),e("option",{value:"full_time"},"Tempo Pieno",-1),e("option",{value:"part_time"},"Part Time",-1),e("option",{value:"contractor"},"Consulente",-1),e("option",{value:"intern"},"Stagista",-1)])),_:1,__:[18]},8,["model-value"])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore Settimanali",-1)),d(R,{"model-value":r.editData.weekly_hours,"onUpdate:modelValue":t[4]||(t[4]=g=>c.$emit("update-field","weekly_hours",g)),type:"number",min:"1",max:"60",step:"0.5"},null,8,["model-value"])]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Modalità Lavoro",-1)),d(R,{"model-value":r.editData.work_location,"onUpdate:modelValue":t[5]||(t[5]=g=>c.$emit("update-field","work_location",g)),type:"select"},{default:J(()=>t[21]||(t[21]=[e("option",{value:""},"Seleziona modalità",-1),e("option",{value:"office"},"Ufficio",-1),e("option",{value:"remote"},"Remoto",-1),e("option",{value:"hybrid"},"Ibrido",-1)])),_:1,__:[21]},8,["model-value"])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Indirizzo",-1)),d(R,{"model-value":r.editData.address,"onUpdate:modelValue":t[6]||(t[6]=g=>c.$emit("update-field","address",g)),type:"text"},null,8,["model-value"])]),e("div",Ws,[d(q,{variant:"primary",onClick:t[7]||(t[7]=g=>c.$emit("save")),disabled:r.saving,loading:r.saving,class:"flex-1"},{default:J(()=>t[24]||(t[24]=[P(" Salva ")])),_:1,__:[24]},8,["disabled","loading"]),d(q,{variant:"secondary",onClick:t[8]||(t[8]=g=>c.$emit("cancel")),class:"flex-1"},{default:J(()=>t[25]||(t[25]=[P(" Annulla ")])),_:1,__:[25]})])])):(s(),a("div",Vs,[r.profile.employee_id?(s(),a("div",Bs,[d(f,{name:"identification",size:"md",class:"hr-info-icon"}),e("div",null,[t[10]||(t[10]=e("span",{class:"hr-info-label"},"ID Dipendente",-1)),e("p",Ms,l(r.profile.employee_id),1)])])):i("",!0),r.profile.job_title?(s(),a("div",Us,[d(f,{name:"briefcase",size:"md",class:"hr-info-icon"}),e("div",null,[t[11]||(t[11]=e("span",{class:"hr-info-label"},"Titolo",-1)),e("p",Js,l(r.profile.job_title),1)])])):i("",!0),r.profile.employment_type?(s(),a("div",Ns,[d(f,{name:"document-text",size:"md",class:"hr-info-icon"}),e("div",null,[t[12]||(t[12]=e("span",{class:"hr-info-label"},"Tipo Contratto",-1)),e("p",Os,l(h(r.profile.employment_type)),1)])])):i("",!0),r.profile.weekly_hours?(s(),a("div",Rs,[d(f,{name:"clock",size:"md",class:"hr-info-icon"}),e("div",null,[t[13]||(t[13]=e("span",{class:"hr-info-label"},"Ore Settimanali",-1)),e("p",qs,l(r.profile.weekly_hours)+"h",1)])])):i("",!0),r.profile.work_location?(s(),a("div",Fs,[d(f,{name:"map-pin",size:"md",class:"hr-info-icon"}),e("div",null,[t[14]||(t[14]=e("span",{class:"hr-info-label"},"Modalità Lavoro",-1)),e("p",Hs,l(_(r.profile.work_location)),1)])])):i("",!0),r.profile.address?(s(),a("div",Qs,[d(f,{name:"home",size:"md",class:"hr-info-icon"}),e("div",null,[t[15]||(t[15]=e("span",{class:"hr-info-label"},"Indirizzo",-1)),e("p",Xs,l(r.profile.address),1)])])):i("",!0)]))])):i("",!0)}},Ks=F(Gs,[["__scopeId","data-v-8c40ad15"]]),Zs={class:"space-y-6"},ea={key:0},ta={class:"flex items-center justify-between mb-4"},sa={class:"text-lg font-medium text-gray-900 dark:text-white"},aa={class:"text-sm text-gray-500 dark:text-gray-400"},ra={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},la=["onClick"],oa={class:"flex items-start justify-between mb-3"},na={class:"flex-1"},ia={class:"font-medium text-gray-900 dark:text-white mb-1"},da={class:"text-sm text-gray-500 dark:text-gray-400"},ua={class:"space-y-2 text-sm"},ca={key:0,class:"flex items-center text-gray-600 dark:text-gray-400"},ma={key:1,class:"flex items-center text-gray-600 dark:text-gray-400"},ga={key:2,class:"mt-3"},va={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1"},ya={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},xa={key:1,class:"empty-state"},pa={class:"text-center py-12"},fa={class:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600"},ba={__name:"ProjectsTabContent",props:{projects:{type:Array,default:()=>[]}},emits:["project-click"],setup(r){const h=r,_=S(()=>h.projects.filter(y=>y.status==="active").length),c=y=>y?new Date(y).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",t=y=>({active:"Attivo",completed:"Completato",on_hold:"In Pausa",cancelled:"Annullato"})[y]||y,g=y=>`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-2 ${{active:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",on_hold:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",cancelled:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"}[y]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"}`;return(y,b)=>(s(),a("div",Zs,[r.projects.length>0?(s(),a("div",ea,[e("div",ta,[e("h3",sa," Progetti Assegnati ("+l(r.projects.length)+") ",1),e("div",aa,l(_.value)+" attivi ",1)]),e("div",ra,[(s(!0),a(B,null,M(r.projects,v=>(s(),a("div",{key:v.id,class:"project-card",onClick:n=>y.$emit("project-click",v)},[e("div",oa,[e("div",na,[e("h4",ia,l(v.name),1),e("p",da,l(v.role||"Team Member"),1)]),e("span",{class:O(g(v.status))},l(t(v.status)),3)]),e("div",ua,[v.client?(s(),a("div",ca,[d(f,{name:"building-office",size:"sm",class:"mr-2"}),P(" "+l(v.client),1)])):i("",!0),v.deadline?(s(),a("div",ma,[d(f,{name:"calendar",size:"sm",class:"mr-2"}),P(" "+l(c(v.deadline)),1)])):i("",!0),v.progress!==void 0?(s(),a("div",ga,[e("div",va,[b[0]||(b[0]=e("span",null,"Progresso",-1)),e("span",null,l(v.progress)+"%",1)]),e("div",ya,[e("div",{class:"progress-bar h-2 rounded-full transition-all duration-300",style:X({width:v.progress+"%"})},null,4)])])):i("",!0)])],8,la))),128))])])):(s(),a("div",xa,[e("div",pa,[e("div",fa,[d(f,{name:"folder",size:"2xl"})]),b[1]||(b[1]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun progetto",-1)),b[2]||(b[2]=e("p",{class:"text-gray-500 dark:text-gray-400"},"L'utente non è assegnato a nessun progetto.",-1))])]))]))}},ka=F(ba,[["__scopeId","data-v-2c54eb20"]]),ha={class:"space-y-6"},_a={key:0},$a={class:"flex items-center justify-between mb-4"},wa={class:"flex items-center space-x-4"},Ca={class:"text-lg font-medium text-gray-900 dark:text-white"},ja={class:"text-sm text-gray-500 dark:text-gray-400"},za={class:"text-sm text-gray-500 dark:text-gray-400"},Pa={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},Ta=["onClick"],Sa={class:"flex items-center justify-between mb-2"},La={class:"font-medium text-gray-900 dark:text-white"},Ia={key:0,class:"certification-badge",title:"Competenza certificata"},Ea={key:0,class:"text-sm text-gray-500 dark:text-gray-400 mb-3"},Aa={class:"flex items-center justify-between"},Da={class:"flex items-center space-x-2"},Va={class:"flex space-x-1"},Ba={class:"text-xs text-gray-500 dark:text-gray-400"},Ma={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Ua={key:1,class:"mt-3 pt-3 border-t border-gray-100 dark:border-gray-700"},Ja={class:"text-xs text-gray-500 dark:text-gray-400 line-clamp-2"},Na={key:1,class:"empty-state"},Oa={class:"text-center py-12"},Ra={class:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600"},qa={__name:"SkillsTabContent",props:{skills:{type:Array,default:()=>[]},currentPage:{type:Number,default:1},perPage:{type:Number,default:6},canAddSkills:{type:Boolean,default:!1}},emits:["skill-click","page-change","add-skills"],setup(r){const h=r,_=S(()=>{if(!h.skills||h.skills.length===0)return[];const g=(h.currentPage-1)*h.perPage,y=g+h.perPage;return h.skills.slice(g,y)}),c=S(()=>h.skills?Math.ceil(h.skills.length/h.perPage):0),t=S(()=>h.skills?h.skills.filter(g=>g.is_certified||g.certified).length:0);return(g,y)=>(s(),a("div",ha,[r.skills&&r.skills.length>0?(s(),a("div",_a,[e("div",$a,[e("div",wa,[e("h3",Ca," Competenze ("+l(r.skills.length)+") ",1),e("span",ja," Pagina "+l(r.currentPage)+" di "+l(c.value),1)]),e("div",za,l(t.value)+" certificate ",1)]),e("div",Pa,[(s(!0),a(B,null,M(_.value,b=>(s(),a("div",{key:b.id,class:"skill-card",onClick:v=>g.$emit("skill-click",b)},[e("div",Sa,[e("h4",La,l(b.name),1),b.is_certified||b.certified?(s(),a("span",Ia," ✓ Certificato ")):i("",!0)]),b.category?(s(),a("p",Ea,l(b.category),1)):i("",!0),e("div",Aa,[e("div",Da,[y[2]||(y[2]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Livello:",-1)),e("div",Va,[(s(),a(B,null,M(5,v=>e("div",{key:v,class:O(["skill-dot",v<=b.proficiency_level?"skill-dot-active":"skill-dot-inactive"])},null,2)),64))]),e("span",Ba," ("+l(b.proficiency_level)+"/5) ",1)]),b.years_experience?(s(),a("span",Ma,l(b.years_experience)+l(b.years_experience===1?" anno":" anni"),1)):i("",!0)]),b.description?(s(),a("div",Ua,[e("p",Ja,l(b.description),1)])):i("",!0)],8,Ta))),128))]),c.value>1?(s(),H(pe,{key:0,"current-page":r.currentPage,"total-pages":c.value,total:r.skills.length,"per-page":r.perPage,"results-label":"competenze",onPageChange:y[0]||(y[0]=b=>g.$emit("page-change",b)),class:"border-t border-gray-200 dark:border-gray-700 pt-4"},null,8,["current-page","total-pages","total","per-page"])):i("",!0)])):(s(),a("div",Na,[e("div",Oa,[e("div",Ra,[d(f,{name:"star",size:"xl"})]),y[3]||(y[3]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessuna competenza",-1)),y[4]||(y[4]=e("p",{class:"text-gray-500 dark:text-gray-400"},"Non sono state registrate competenze per questo utente.",-1)),r.canAddSkills?(s(),a("button",{key:0,onClick:y[1]||(y[1]=b=>g.$emit("add-skills")),class:"mt-4 brand-button"}," Aggiungi Competenze ")):i("",!0)])]))]))}},Fa=F(qa,[["__scopeId","data-v-9e8a4c62"]]),Ha={class:"space-y-6"},Qa={key:0},Xa={class:"flex items-center justify-between mb-4"},Ya={class:"text-lg font-medium text-gray-900 dark:text-white"},Wa={class:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},Ga={class:"space-y-3"},Ka=["onClick"],Za={class:"flex items-start justify-between"},er={class:"flex-1"},tr={class:"flex items-center space-x-3 mb-2"},sr={class:"font-medium text-gray-900 dark:text-white"},ar={key:0,class:"text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2"},rr={class:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},lr={key:0,class:"flex items-center"},or={key:1,class:"flex items-center"},nr={key:2,class:"flex items-center"},ir={key:0,class:"ml-4 text-right min-w-0"},dr={class:"text-sm font-medium text-gray-900 dark:text-white mb-1"},ur={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},cr={key:0,class:"mt-3 flex flex-wrap gap-1"},mr={key:0,class:"task-tag-more"},gr={key:1,class:"empty-state"},vr={class:"text-center py-12"},yr={class:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600"},xr={__name:"TasksTabContent",props:{tasks:{type:Array,default:()=>[]}},emits:["task-click"],setup(r){const h=r,_=S(()=>h.tasks.filter(n=>n.status==="done"||n.status==="completed").length),c=S(()=>h.tasks.filter(n=>n.status==="in-progress"||n.status==="in_progress").length),t=n=>n?new Date(n).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"",g=n=>({todo:"Da Fare","in-progress":"In Corso",in_progress:"In Corso",review:"In Revisione",done:"Completato",completed:"Completato",blocked:"Bloccato",cancelled:"Annullato"})[n]||n,y=n=>{const w="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",x={todo:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200","in-progress":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",in_progress:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",review:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",done:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",completed:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",blocked:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",cancelled:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"};return`${w} ${x[n]||x.todo}`},b=n=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[n]||n,v=n=>{const w="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",x={low:"bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-200",medium:"bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-200",high:"bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-200",urgent:"bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-200"};return`${w} ${x[n]||x.medium}`};return(n,w)=>(s(),a("div",Ha,[r.tasks.length>0?(s(),a("div",Qa,[e("div",Xa,[e("h3",Ya," Task Assegnati ("+l(r.tasks.length)+") ",1),e("div",Wa,[e("span",null,l(_.value)+" completati",1),e("span",null,l(c.value)+" in corso",1)])]),e("div",Ga,[(s(!0),a(B,null,M(r.tasks,x=>(s(),a("div",{key:x.id,class:"task-card",onClick:L=>n.$emit("task-click",x)},[e("div",Za,[e("div",er,[e("div",tr,[e("h4",sr,l(x.name),1),e("span",{class:O(y(x.status))},l(g(x.status)),3),x.priority?(s(),a("span",{key:0,class:O(v(x.priority))},l(b(x.priority)),3)):i("",!0)]),x.description?(s(),a("p",ar,l(x.description),1)):i("",!0),e("div",rr,[x.project_name?(s(),a("div",lr,[d(f,{name:"folder",size:"sm",class:"mr-1"}),P(" "+l(x.project_name),1)])):i("",!0),x.due_date?(s(),a("div",or,[d(f,{name:"calendar",size:"sm",class:"mr-1"}),P(" Scadenza: "+l(t(x.due_date)),1)])):i("",!0),x.estimated_hours?(s(),a("div",nr,[d(f,{name:"clock",size:"sm",class:"mr-1"}),P(" "+l(x.estimated_hours)+"h stimati ",1)])):i("",!0)])]),x.progress!==void 0?(s(),a("div",ir,[e("div",dr,l(x.progress)+"% ",1),e("div",ur,[e("div",{class:"progress-bar h-2 rounded-full transition-all duration-300",style:X({width:x.progress+"%"})},null,4)])])):i("",!0)]),x.tags&&x.tags.length>0?(s(),a("div",cr,[(s(!0),a(B,null,M(x.tags.slice(0,3),L=>(s(),a("span",{key:L,class:"task-tag"},l(L),1))),128)),x.tags.length>3?(s(),a("span",mr," +"+l(x.tags.length-3),1)):i("",!0)])):i("",!0)],8,Ka))),128))])])):(s(),a("div",gr,[e("div",vr,[e("div",yr,[d(f,{name:"clipboard-list",size:"2xl"})]),w[0]||(w[0]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun task",-1)),w[1]||(w[1]=e("p",{class:"text-gray-500 dark:text-gray-400"},"Non sono stati assegnati task a questo utente.",-1))])]))]))}},pr=F(xr,[["__scopeId","data-v-16747482"]]),fr={class:"space-y-6"},br={key:0,class:"flex justify-center items-center h-32"},kr={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},hr={class:"flex"},_r={class:"text-sm text-red-700 dark:text-red-300 mt-1"},$r={key:2,class:"space-y-6"},wr={class:"flex items-center justify-between"},Cr={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},jr={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},zr={class:"flex items-center justify-between mb-4"},Pr={class:"text-md font-medium text-gray-900 dark:text-white"},Tr={key:0,class:"space-y-4"},Sr={class:"flex items-center justify-center"},Lr={class:"relative h-20 w-20"},Ir={class:"h-20 w-20 transform -rotate-90",viewBox:"0 0 100 100"},Er=["stroke-dasharray","stroke-dashoffset"],Ar={class:"absolute inset-0 flex items-center justify-center"},Dr={class:"text-sm font-semibold text-gray-900 dark:text-white"},Vr={class:"grid grid-cols-3 gap-4 text-center text-sm"},Br={class:"font-semibold text-gray-900 dark:text-white"},Mr={class:"font-semibold text-green-600"},Ur={class:"font-semibold text-blue-600"},Jr={class:"pt-3 border-t border-gray-200 dark:border-gray-600 space-y-2 text-sm"},Nr={class:"flex justify-between"},Or={class:"font-medium text-gray-900 dark:text-white"},Rr={key:0,class:"flex justify-between"},qr={class:"flex items-center space-x-1"},Fr={class:"font-medium text-yellow-600"},Hr={key:1,class:"text-center py-6"},Qr={class:"text-sm text-gray-600 dark:text-gray-400"},Xr={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Yr={class:"flex items-center justify-between mb-4"},Wr={class:"text-md font-medium text-gray-900 dark:text-white"},Gr={key:0,class:"space-y-4"},Kr={class:"grid grid-cols-2 gap-4 text-center"},Zr={class:"text-xl font-semibold text-gray-900 dark:text-white"},el={class:"text-xl font-semibold text-green-600"},tl={key:0,class:"text-center"},sl={class:"text-2xl font-semibold text-yellow-600 mb-1"},al={class:"flex items-center justify-center space-x-1 mb-1"},rl={key:1,class:"pt-3 border-t border-gray-200 dark:border-gray-600"},ll={class:"text-xs text-amber-600 dark:text-amber-400 text-center"},ol={key:1,class:"text-center py-6"},nl={class:"text-sm text-gray-600 dark:text-gray-400"},il={key:0},dl={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ul={class:"flex items-center justify-between mb-4"},cl={class:"space-y-3"},ml={class:"flex-1"},gl={class:"text-sm font-medium text-gray-900 dark:text-white"},vl={class:"flex items-center space-x-2 text-xs text-gray-600 dark:text-gray-400 mt-1"},yl={key:0},xl={key:1},pl={class:"text-right"},fl={class:"text-sm font-medium text-gray-900 dark:text-white"},bl={key:0,class:"flex items-center justify-end space-x-1"},kl={class:"text-xs text-yellow-600"},hl={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},_l={class:"space-y-3"},$l=["onClick"],wl={class:"flex items-center space-x-3"},Cl={class:"font-medium text-gray-900 dark:text-white"},jl={key:0,class:"text-xs text-primary-600 bg-primary-100 dark:bg-primary-900 dark:text-primary-200 px-2 py-1 rounded-full"},zl={class:"flex items-center space-x-4 text-sm"},Pl={__name:"PerformanceTabContent",props:{user:{type:Object,required:!0},canEdit:{type:Boolean,default:!1}},emits:["navigate-to-performance"],setup(r){const h=r,_=z(!1),c=z(null),t=z(null),g=z([]),y=S(()=>new Date().getFullYear()),b=S(()=>{var $,u;return(u=($=t.value)==null?void 0:$.goals_stats)!=null&&u.total?Math.round(t.value.goals_stats.completed/t.value.goals_stats.total*100):0}),v=S(()=>2*Math.PI*35),n=S(()=>{const $=b.value;return v.value-$/100*v.value}),w=async()=>{var $;if(($=h.user)!=null&&$.id){_.value=!0,c.value=null;try{const u=await N.get(`/api/performance/employees/${h.user.id}/summary/${y.value}`);u.data.success&&(t.value=u.data.data);const E=await N.get(`/api/performance/employees/${h.user.id}/goals/${y.value}`);E.data.success&&(g.value=E.data.data.recent_goals||E.data.data.goals||[])}catch(u){console.error("Error loading performance data:",u),c.value="Errore nel caricamento dei dati performance"}finally{_.value=!1}}},x=$=>$?new Date($).toLocaleDateString("it-IT"):"",L=$=>({technical:"Tecniche",soft_skills:"Soft Skills",business:"Business",career_development:"Sviluppo Carriera"})[$]||$;return ne(()=>{w()}),($,u)=>{var E,I;return s(),a("div",fr,[_.value?(s(),a("div",br,u[5]||(u[5]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):c.value?(s(),a("div",kr,[e("div",hr,[d(f,{name:"x-circle",size:"md",class:"text-red-400 mr-2 mt-0.5"}),e("div",null,[u[6]||(u[6]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento",-1)),e("p",_r,l(c.value),1)])])])):(s(),a("div",$r,[e("div",wr,[u[8]||(u[8]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Performance Overview "),e("p",{class:"text-sm text-gray-600 dark:text-gray-400 mt-1"}," Riepilogo delle performance e obiettivi recenti ")],-1)),d(q,{variant:"primary",icon:"chart-line",onClick:u[0]||(u[0]=j=>$.$emit("navigate-to-performance","view-full"))},{default:J(()=>u[7]||(u[7]=[P(" Visualizza Tutto ")])),_:1,__:[7]})]),e("div",Cr,[e("div",jr,[e("div",zr,[e("h4",Pr," Obiettivi "+l(y.value),1),d(q,{variant:"ghost",size:"sm",icon:"eye",onClick:u[1]||(u[1]=j=>$.$emit("navigate-to-performance","view-year",{year:y.value}))},{default:J(()=>u[9]||(u[9]=[P(" Dettagli ")])),_:1,__:[9]})]),(E=t.value)!=null&&E.goals_stats?(s(),a("div",Tr,[e("div",Sr,[e("div",Lr,[(s(),a("svg",Ir,[u[10]||(u[10]=e("circle",{cx:"50",cy:"50",r:"35","stroke-width":"8",stroke:"currentColor",fill:"transparent",class:"text-gray-200 dark:text-gray-600"},null,-1)),e("circle",{cx:"50",cy:"50",r:"35","stroke-width":"8",stroke:"currentColor",fill:"transparent","stroke-dasharray":v.value,"stroke-dashoffset":n.value,class:"text-green-500 transition-all duration-500","stroke-linecap":"round"},null,8,Er)])),e("div",Ar,[e("span",Dr,l(b.value)+"% ",1)])])]),e("div",Vr,[e("div",null,[e("div",Br,l(t.value.goals_stats.total),1),u[11]||(u[11]=e("div",{class:"text-gray-600 dark:text-gray-400"},"Totali",-1))]),e("div",null,[e("div",Mr,l(t.value.goals_stats.completed),1),u[12]||(u[12]=e("div",{class:"text-gray-600 dark:text-gray-400"},"Completati",-1))]),e("div",null,[e("div",Ur,l(t.value.goals_stats.in_progress),1),u[13]||(u[13]=e("div",{class:"text-gray-600 dark:text-gray-400"},"In Corso",-1))])]),e("div",Jr,[e("div",Nr,[u[14]||(u[14]=e("span",{class:"text-gray-600 dark:text-gray-400"},"Progresso Medio",-1)),e("span",Or,l(t.value.goals_stats.avg_progress)+"%",1)]),t.value.goals_stats.avg_rating?(s(),a("div",Rr,[u[15]||(u[15]=e("span",{class:"text-gray-600 dark:text-gray-400"},"Rating Medio",-1)),e("div",qr,[e("span",Fr,l(t.value.goals_stats.avg_rating),1),d(f,{name:"star",size:"xs",class:"text-yellow-500"})])])):i("",!0)])])):(s(),a("div",Hr,[d(f,{name:"target",size:"xl",class:"text-gray-300 dark:text-gray-600 mx-auto mb-2"}),e("p",Qr,"Nessun obiettivo definito per "+l(y.value),1)]))]),e("div",Xr,[e("div",Yr,[e("h4",Wr," Valutazioni "+l(y.value),1),d(q,{variant:"ghost",size:"sm",icon:"eye",onClick:u[2]||(u[2]=j=>$.$emit("navigate-to-performance","view-year",{year:y.value}))},{default:J(()=>u[16]||(u[16]=[P(" Dettagli ")])),_:1,__:[16]})]),(I=t.value)!=null&&I.reviews_stats?(s(),a("div",Gr,[e("div",Kr,[e("div",null,[e("div",Zr,l(t.value.reviews_stats.total),1),u[17]||(u[17]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Totali",-1))]),e("div",null,[e("div",el,l(t.value.reviews_stats.completed),1),u[18]||(u[18]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Completate",-1))])]),t.value.reviews_stats.avg_overall_rating?(s(),a("div",tl,[e("div",sl,l(t.value.reviews_stats.avg_overall_rating),1),e("div",al,[(s(),a(B,null,M(5,j=>d(f,{key:j,name:"star",size:"sm",class:O(j<=t.value.reviews_stats.avg_overall_rating?"text-yellow-500":"text-gray-300 dark:text-gray-600")},null,8,["class"])),64))]),u[19]||(u[19]=e("div",{class:"text-xs text-gray-600 dark:text-gray-400"},"Rating Complessivo",-1))])):i("",!0),t.value.reviews_stats.in_progress>0||t.value.reviews_stats.pending>0?(s(),a("div",rl,[e("div",ll,l(t.value.reviews_stats.in_progress+t.value.reviews_stats.pending)+" valutazioni in attesa ",1)])):i("",!0)])):(s(),a("div",ol,[d(f,{name:"star",size:"xl",class:"text-gray-300 dark:text-gray-600 mx-auto mb-2"}),e("p",nl,"Nessuna valutazione per "+l(y.value),1)]))])]),g.value.length>0?(s(),a("div",il,[e("div",dl,[e("div",ul,[u[21]||(u[21]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white"}," Obiettivi Recenti ",-1)),d(q,{variant:"ghost",size:"sm",icon:"arrow-right",onClick:u[3]||(u[3]=j=>$.$emit("navigate-to-performance","view-full"))},{default:J(()=>u[20]||(u[20]=[P(" Vedi Tutti ")])),_:1,__:[20]})]),e("div",cl,[(s(!0),a(B,null,M(g.value.slice(0,5),j=>(s(),a("div",{key:j.id,class:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer",onClick:u[4]||(u[4]=Q=>$.$emit("navigate-to-performance","view-full"))},[e("div",ml,[e("h5",gl,l(j.title),1),e("div",vl,[d(fe,{status:j.status,type:"goal",size:"xs"},null,8,["status"]),j.category?(s(),a("span",yl,l(L(j.category)),1)):i("",!0),j.target_date?(s(),a("span",xl,l(x(j.target_date)),1)):i("",!0)])]),e("div",pl,[e("div",fl,l(j.progress_percentage||0)+"% ",1),j.achievement_rating?(s(),a("div",bl,[e("span",kl,l(j.achievement_rating),1),d(f,{name:"star",size:"xs",class:"text-yellow-500"})])):i("",!0)])]))),128))])])])):i("",!0),e("div",hl,[u[23]||(u[23]=e("div",{class:"flex items-center justify-between mb-4"},[e("h4",{class:"text-md font-medium text-gray-900 dark:text-white"}," Trend Performance "),e("div",{class:"text-sm text-gray-600 dark:text-gray-400"}," Ultimi 3 anni ")],-1)),e("div",_l,[(s(!0),a(B,null,M([y.value,y.value-1,y.value-2],j=>(s(),a("div",{key:j,class:"flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer",onClick:Q=>$.$emit("navigate-to-performance","view-year",{year:j})},[e("div",wl,[e("div",Cl,l(j),1),j===y.value?(s(),a("div",jl," Corrente ")):i("",!0)]),e("div",zl,[u[22]||(u[22]=e("div",{class:"text-gray-600 dark:text-gray-400"}," Caricamento... ",-1)),d(f,{name:"chevron-right",size:"sm",class:"text-gray-400"})])],8,$l))),128))])])]))])}}},Tl=F(Pl,[["__scopeId","data-v-ae7ed3a5"]]),Sl={class:"personnel-profile"},Ll={key:0,class:"flex justify-center items-center h-64"},Il={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},El={class:"flex"},Al={class:"text-sm text-red-700 dark:text-red-300 mt-1"},Dl={key:2,class:"space-y-6"},Vl={class:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6"},Bl={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},Ml={key:0,class:"text-gray-700 dark:text-gray-300 text-sm leading-relaxed"},Ul={key:3},Jl={key:4},Wl={__name:"PersonnelProfile",setup(r){const h=ue(),_=ce();me();const{hasPermission:c}=ye(),t=z(null),g=z([]),y=z([]),b=z(!1),v=z(null),n=z(!1),w=z(!1),x=z("projects"),L=z(1),$=z(6),u=z({phone:"",bio:"",employee_id:"",job_title:"",employment_type:"",work_location:"",weekly_hours:40,address:"",emergency_contact_name:"",emergency_contact_phone:"",emergency_contact_relationship:""}),E=S(()=>{if(!t.value)return!1;try{return c.value&&typeof c.value=="function"?c.value("edit_personnel_data"):!1}catch(o){return console.warn("Permission check failed:",o),!1}});S(()=>{var T;if(!((T=t.value)!=null&&T.skills))return[];const o=(L.value-1)*$.value,p=o+$.value;return t.value.skills.slice(o,p)}),S(()=>{var o;return(o=t.value)!=null&&o.skills?Math.ceil(t.value.skills.length/$.value):0});const I=S(()=>{var o,p;return[{id:"projects",name:"Progetti",count:g.value.length},{id:"tasks",name:"Task",count:y.value.length},{id:"skills",name:"Competenze",count:((p=(o=t.value)==null?void 0:o.skills)==null?void 0:p.length)||0},{id:"performance",name:"Performance",icon:"chart-line"},{id:"cv",name:"CV"}]}),j=o=>{x.value=o,o==="skills"&&(L.value=1)},Q=o=>{L.value=o},Y=o=>{console.log("Project clicked:",o)},te=o=>{console.log("Task clicked:",o)},se=o=>{console.log("Skill clicked:",o)},W=(o,p)=>{o==="view-full"?_.push(`/app/personnel/${t.value.id}/performance/${new Date().getFullYear()}`):o==="view-year"&&_.push(`/app/personnel/${t.value.id}/performance/${p.year}`)},G=()=>{var o,p,T,A,C,V,U,le,oe;t.value&&(u.value={phone:t.value.phone||"",bio:t.value.bio||"",employee_id:((o=t.value.profile)==null?void 0:o.employee_id)||"",job_title:((p=t.value.profile)==null?void 0:p.job_title)||"",employment_type:((T=t.value.profile)==null?void 0:T.employment_type)||"",work_location:((A=t.value.profile)==null?void 0:A.work_location)||"",weekly_hours:((C=t.value.profile)==null?void 0:C.weekly_hours)||40,address:((V=t.value.profile)==null?void 0:V.address)||"",emergency_contact_name:((U=t.value.profile)==null?void 0:U.emergency_contact_name)||"",emergency_contact_phone:((le=t.value.profile)==null?void 0:le.emergency_contact_phone)||"",emergency_contact_relationship:((oe=t.value.profile)==null?void 0:oe.emergency_contact_relationship)||""})},K=()=>{n.value=!1,G()},Z=(o,p)=>{u.value[o]=p},ee=async()=>{if(t.value){w.value=!0;try{const p=(await N.put(`/api/personnel/users/${t.value.id}`,u.value)).data;if(p.success)t.value=p.data.user,n.value=!1,console.log("Profilo aggiornato con successo");else throw new Error(p.message||"Errore durante il salvataggio")}catch(o){console.error("Errore durante il salvataggio:",o),v.value=o.message}finally{w.value=!1}}},k=async o=>{var p,T,A,C;b.value=!0,v.value=null;try{const U=(await N.get(`/api/personnel/users/${o}/profile`)).data;if(console.log("🔍 API Response:",U),console.log("🔍 User data:",U.data),console.log("🔍 Current job level:",(T=(p=U.data)==null?void 0:p.user)==null?void 0:T.current_job_level),console.log("🔍 Job level history:",(C=(A=U.data)==null?void 0:A.user)==null?void 0:C.job_level_history),U.success)t.value=U.data.user,G();else throw new Error(U.message||"Errore nel caricamento del profilo")}catch(V){console.error("Error fetching user profile:",V),v.value=V.message}finally{b.value=!1}},m=async o=>{try{const T=(await N.get(`/api/tasks?assignee_id=${o}&limit=20`)).data;T.success&&(y.value=T.data.tasks||[])}catch(p){console.error("Error fetching user tasks:",p)}},D=async o=>{o?o.id&&o.profile?t.value=o:(o.cv_path&&(t.value.profile.current_cv_path=o.cv_path),o.cv_last_updated!==void 0&&(t.value.profile.cv_last_updated=o.cv_last_updated),o.analysis&&(t.value.profile.cv_analysis_data=JSON.stringify(o.analysis)),o.profile_completion!==void 0&&(t.value.profile.profile_completion=o.profile_completion),o.cv_path===null&&(t.value.profile.current_cv_path=null,t.value.profile.cv_last_updated=null,t.value.profile.cv_analysis_data=null)):await k(t.value.id)};return ne(async()=>{const o=h.params.id;if(!o){v.value="ID utente non specificato";return}await k(o),t.value&&(g.value=t.value.projects||[],await m(o))}),re(()=>h.params.id,async o=>{o&&(await k(o),t.value&&(g.value=t.value.projects||[],await m(o)))}),(o,p)=>(s(),a("div",Sl,[b.value?(s(),a("div",Ll,p[4]||(p[4]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"},null,-1)]))):v.value?(s(),a("div",Il,[e("div",El,[d(f,{name:"x-circle",size:"md",class:"text-red-400 mr-2 mt-0.5"}),e("div",null,[p[5]||(p[5]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento del profilo",-1)),e("p",Al,l(v.value),1)])])])):t.value?(s(),a("div",Dl,[d(ns,{user:t.value,"can-edit":E.value,"edit-mode":n.value,onToggleEdit:p[0]||(p[0]=T=>n.value=!n.value)},null,8,["user","can-edit","edit-mode"]),e("div",Vl,[d(ks,{user:t.value,"edit-mode":n.value,"edit-data":u.value,"can-edit":E.value,saving:w.value,onToggleEdit:p[1]||(p[1]=T=>n.value=!n.value),onUpdateField:Z,onSave:ee,onCancel:K},null,8,["user","edit-mode","edit-data","can-edit","saving"]),d(Es,{skills:t.value.skills,"max-skills":4,"show-view-all-button":!0,onViewAll:p[2]||(p[2]=T=>j("skills"))},null,8,["skills"]),t.value.current_job_level||t.value.job_level_history?(s(),H(Rt,{key:0,"current-job-level":t.value.current_job_level,"job-level-history":t.value.job_level_history,"show-history":!1,class:"h-full"},null,8,["current-job-level","job-level-history"])):i("",!0)]),d(Ks,{profile:t.value.profile,"edit-mode":n.value,"edit-data":u.value,"can-edit":E.value,saving:w.value,onToggleEdit:p[3]||(p[3]=T=>n.value=!n.value),onUpdateField:Z,onSave:ee,onCancel:K},null,8,["profile","edit-mode","edit-data","can-edit","saving"]),t.value.bio||n.value?(s(),a("div",Bl,[p[6]||(p[6]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Bio",-1)),n.value?i("",!0):(s(),a("p",Ml,l(t.value.bio||"Nessuna bio disponibile"),1))])):i("",!0),d(xe,{tabs:I.value,"active-tab":x.value,onTabChange:j},{default:J(()=>[x.value==="projects"?(s(),H(ka,{key:0,projects:g.value,onProjectClick:Y},null,8,["projects"])):i("",!0),x.value==="tasks"?(s(),H(pr,{key:1,tasks:y.value,onTaskClick:te},null,8,["tasks"])):i("",!0),x.value==="skills"?(s(),H(Fa,{key:2,skills:t.value.skills,"current-page":L.value,"per-page":$.value,onSkillClick:se,onPageChange:Q},null,8,["skills","current-page","per-page"])):i("",!0),x.value==="performance"?(s(),a("div",Ul,[d(Tl,{user:t.value,"can-edit":E.value,onNavigateToPerformance:W},null,8,["user","can-edit"])])):i("",!0),x.value==="cv"?(s(),a("div",Jl,[d(dt,{user:t.value,"can-edit":E.value,onUserUpdated:D},null,8,["user","can-edit"])])):i("",!0)]),_:1},8,["tabs","active-tab"])])):i("",!0)]))}};export{Wl as default};
