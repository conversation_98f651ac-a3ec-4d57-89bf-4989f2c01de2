import{_ as k,H as u}from"./app.js";/* empty css                                                             */import{Y as h,c as i,b as a,l as d,j as p,n as g,s as v,t as c,h as m,e as f,m as w,i as I,o as t}from"./vendor.js";const S={class:"space-y-1"},V=["for"],B={key:0,class:"text-red-500"},C={class:"relative"},z={key:0,class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},N={key:1,class:"absolute inset-y-0 right-0 pr-3 flex items-center"},q={key:2,class:"text-sm text-red-600 dark:text-red-400"},$={__name:"StandardInput",props:{modelValue:{type:[String,Number],default:""},label:{type:String,default:null},type:{type:String,default:"text",validator:e=>["text","email","password","number","tel","url","search","date","datetime-local","time","month","week","textarea"].includes(e)},placeholder:{type:String,default:null},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},required:{type:Boolean,default:!1},error:{type:String,default:null},helper:{type:String,default:null},leftIcon:{type:String,default:null},rightIcon:{type:String,default:null},clearable:{type:Boolean,default:!1},size:{type:String,default:"md",validator:e=>["sm","md","lg"].includes(e)},variant:{type:String,default:"default",validator:e=>["default","success","warning","danger"].includes(e)},rows:{type:Number,default:3}},emits:["update:modelValue"],setup(e,{emit:j}){const b=h(),l=e,y=i(()=>b.id||`input-${Math.random().toString(36).substr(2,9)}`),o=i(()=>l.type),x=i(()=>{const r=["block w-full rounded-md border-gray-300 dark:border-gray-600","bg-white dark:bg-gray-700 text-gray-900 dark:text-white","placeholder-gray-500 dark:placeholder-gray-400","focus:outline-none focus:ring-2 focus:ring-offset-0","disabled:opacity-50 disabled:cursor-not-allowed","readonly:bg-gray-50 dark:readonly:bg-gray-800","transition-colors duration-200"],s={sm:"px-3 py-2 text-sm",md:"px-3 py-2.5 text-sm",lg:"px-4 py-3 text-base"};r.push(s[l.size]),l.leftIcon&&r.push("pl-10"),(l.rightIcon||l.clearable)&&r.push("pr-10");const n={default:l.error?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-primary-500 focus:border-primary-500",success:"border-green-300 dark:border-green-600 focus:ring-green-500 focus:border-green-500",warning:"border-yellow-300 dark:border-yellow-600 focus:ring-yellow-500 focus:border-yellow-500",danger:"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500"};return r.push(n[l.variant]),r.join(" ")});return(r,s)=>(t(),a("div",S,[e.label?(t(),a("label",{key:0,for:y.value,class:g(["block text-sm font-medium text-gray-700 dark:text-gray-300",{"text-red-700 dark:text-red-400":e.error}])},[v(c(e.label)+" ",1),e.required?(t(),a("span",B,"*")):d("",!0)],10,V)):d("",!0),p("div",C,[e.leftIcon?(t(),a("div",z,[f(u,{name:e.leftIcon,size:"sm",class:"text-gray-400 dark:text-gray-500"},null,8,["name"])])):d("",!0),(t(),m(I(o.value==="textarea"?"textarea":"input"),w({id:y.value,type:o.value!=="textarea"?o.value:void 0,value:e.modelValue,onInput:s[0]||(s[0]=n=>r.$emit("update:modelValue",n.target.value)),disabled:e.disabled,readonly:e.readonly,required:e.required,placeholder:e.placeholder,rows:o.value==="textarea"?e.rows:void 0,class:x.value},r.$attrs),null,16,["id","type","value","disabled","readonly","required","placeholder","rows","class"])),e.rightIcon||e.clearable&&e.modelValue?(t(),a("div",N,[e.clearable&&e.modelValue&&!e.disabled?(t(),a("button",{key:0,onClick:s[1]||(s[1]=n=>r.$emit("update:modelValue","")),type:"button",class:"text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-400 focus:outline-none"},[f(u,{name:"x-mark",size:"sm"})])):e.rightIcon?(t(),m(u,{key:1,name:e.rightIcon,size:"sm",class:"text-gray-400 dark:text-gray-500"},null,8,["name"])):d("",!0)])):d("",!0)]),e.helper?(t(),a("p",{key:1,class:g(["text-sm text-gray-500 dark:text-gray-400",{"text-red-600 dark:text-red-400":e.error}])},c(e.helper),3)):d("",!0),e.error?(t(),a("p",q,c(e.error),1)):d("",!0)]))}},A=k($,[["__scopeId","data-v-06ea9c1e"]]);export{A as S};
