import{r as g,c as T,u as J,x as K,b as o,e as l,j as t,l as m,k as x,t as i,f as Q,n as W,v as $,F as N,p as V,A as X,B as j,C as D,s as y,q as Y,o as n}from"./vendor.js";import{u as Z}from"./crm.js";import{H as _,d as tt}from"./app.js";import{S as z}from"./StatusBadge.js";import{_ as et}from"./PageHeader.js";import{_ as at}from"./StatsGrid.js";import{a as rt,g as st}from"./contractTypes.js";const ot={class:"min-h-screen bg-gray-50 dark:bg-gray-900"},nt={key:0,class:"flex space-x-4"},dt={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},it={key:0,class:"bg-white dark:bg-gray-800 rounded-lg shadow p-8"},lt={key:1,class:"space-y-6"},ut={class:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden"},ct={class:"p-6"},gt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},mt={class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},yt={class:"space-y-2 text-sm"},pt={class:"flex justify-between"},vt={class:"flex justify-between"},xt={class:"font-medium text-gray-900 dark:text-white"},bt={class:"flex justify-between"},ft={class:"flex justify-between"},_t={class:"space-y-2 text-sm"},kt={key:0},ht={class:"flex justify-between"},wt={class:"font-medium text-gray-900 dark:text-white"},Ct={key:0,class:"flex justify-between"},jt={class:"font-medium text-gray-900 dark:text-white"},Dt={key:1,class:"flex justify-between"},Tt={class:"font-medium text-gray-900 dark:text-white"},zt={class:"flex justify-between"},It={class:"font-medium text-gray-900 dark:text-white"},St={class:"flex justify-between"},$t={class:"font-medium text-gray-900 dark:text-white"},Nt={key:0,class:"mt-6"},Vt={class:"text-gray-700 dark:text-gray-300 whitespace-pre-wrap"},Pt={class:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden"},Bt={class:"px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600"},Ft={class:"text-lg font-medium text-gray-900 dark:text-white"},Mt={class:"p-6"},Rt={key:0,class:"space-y-4"},qt={class:"flex justify-between items-start"},Et={class:"font-medium text-gray-900 dark:text-white"},Lt={key:0,class:"text-sm text-gray-600 dark:text-gray-400 mt-1"},Ot={class:"mt-2"},At={key:1,class:"text-center py-8 text-gray-500 dark:text-gray-400"},Ut={class:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden"},Ht={class:"px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600"},Gt={class:"text-lg font-medium text-gray-900 dark:text-white"},Jt={class:"p-6"},Kt={key:0,class:"space-y-4"},Qt={class:"flex justify-between items-start"},Wt={class:"font-medium text-gray-900 dark:text-white"},Xt={class:"text-sm text-gray-600 dark:text-gray-400"},Yt={class:"mt-2 flex items-center space-x-4"},Zt={class:"text-sm font-medium text-gray-900 dark:text-white"},te={key:1,class:"text-center py-8 text-gray-500 dark:text-gray-400"},ee={key:2,class:"bg-white dark:bg-gray-800 rounded-lg shadow p-8"},ae={class:"text-center"},re={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 dark:bg-gray-900 dark:bg-opacity-70 overflow-y-auto h-full w-full z-50"},se={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800 dark:border-gray-600"},oe={class:"mt-3"},ne=["placeholder"],de=["placeholder"],ie={class:"grid grid-cols-2 gap-4"},le={class:"flex justify-end space-x-3 pt-4"},ue=["disabled"],ce={key:0,class:"flex items-center"},ge={key:1},ke={__name:"ContractView",setup(me){const P=J(),B=Y(),{showToast:F}=tt(),M=Z(),k=g(!1),h=g(!1),a=g(null),p=g([]),b=g([]),w=g(!1),d=g({name:"",description:"",start_date:"",end_date:""}),R=T(()=>parseInt(P.params.id)),q=T(()=>a.value&&!p.value.length),E=T(()=>{if(!a.value)return[];const s=[];if(a.value.contract_type==="hourly"?(s.push({name:"Tariffa Oraria",value:`€${f(a.value.hourly_rate)}`,subtitle:"per ora",icon:"clock"}),a.value.budget_hours&&s.push({name:"Budget Ore",value:a.value.budget_hours,subtitle:"ore totali",icon:"calendar"})):a.value.budget_amount&&s.push({name:"Budget Totale",value:`€${f(a.value.budget_amount)}`,subtitle:"valore contratto",icon:"banknotes"}),s.push({name:"Progetti",value:p.value.length,subtitle:"collegati",icon:"folder"}),s.push({name:"Fatture",value:b.value.length,subtitle:"generate",icon:"document-text"}),a.value.start_date){const e=new Date(a.value.start_date),u=a.value.end_date?new Date(a.value.end_date):new Date,c=Math.abs(u-e),v=Math.ceil(c/(1e3*60*60*24));s.push({name:"Durata",value:v,subtitle:"giorni",icon:"calendar-days"})}return s}),L=async()=>{try{k.value=!0;const s=await fetch(`/api/contracts/${R.value}`);if(s.ok){const u=(await s.json()).data.contract;a.value={...u,budget_amount:u.total_budget},a.value&&(d.value.name=a.value.title,d.value.description=a.value.description||"",d.value.start_date=a.value.start_date||new Date().toISOString().split("T")[0],d.value.end_date=a.value.end_date||"")}else throw new Error("Contratto non trovato")}catch(s){console.error("Error loading contract:",s),F("Errore nel caricamento del contratto","error")}finally{k.value=!1}},O=async()=>{var s,e,u;try{(s=a.value)!=null&&s.projects&&(p.value=a.value.projects);const c=await fetch(`/api/invoices/?client_id=${(e=a.value)==null?void 0:e.client_id}&limit=5`);if(c.ok){const v=await c.json();b.value=((u=v.data)==null?void 0:u.invoices)||[]}}catch(c){console.error("Error loading related data:",c)}},A=async()=>{try{h.value=!0;const s={name:d.value.name,description:d.value.description,start_date:d.value.start_date,end_date:d.value.end_date,status:"planning"},e=await M.convertContractToProject(a.value.id,s);w.value=!1,B.push(`/app/projects/${e.project.id}`)}catch(s){console.error("Error converting to project:",s)}finally{h.value=!1}},U=()=>{w.value=!1,a.value&&(d.value.name=a.value.title,d.value.description=a.value.description||"",d.value.start_date=a.value.start_date||new Date().toISOString().split("T")[0],d.value.end_date=a.value.end_date||"")},f=s=>new Intl.NumberFormat("it-IT").format(s||0),C=s=>s?new Date(s).toLocaleDateString("it-IT"):"N/A",H=st,G=rt;return K(async()=>{await L(),a.value&&await O()}),(s,e)=>{var c,v,I,S;const u=Q("router-link");return n(),o("div",ot,[l(et,{title:((c=a.value)==null?void 0:c.contract_number)||"Caricamento...",subtitle:"Dettaglio contratto",breadcrumbs:[{name:"CRM",href:"/app/crm"},{name:"Contratti",href:"/app/crm/contracts"},{name:((v=a.value)==null?void 0:v.contract_number)||"Dettaglio",href:"#",current:!0}],loading:k.value},{actions:x(()=>[a.value?(n(),o("div",nt,[l(u,{to:`/app/crm/contracts/${a.value.id}/edit`,class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},{default:x(()=>[l(_,{name:"pencil",size:"sm",class:"mr-2"}),e[5]||(e[5]=y(" Modifica "))]),_:1,__:[5]},8,["to"]),a.value.status==="active"&&q.value?(n(),o("button",{key:0,onClick:e[0]||(e[0]=r=>w.value=!0),class:"btn-primary"},[l(_,{name:"arrow-up-tray",size:"sm",class:"mr-2"}),e[6]||(e[6]=y(" Converti in Progetto "))])):m("",!0)])):m("",!0)]),_:1},8,["title","breadcrumbs","loading"]),t("div",dt,[k.value?(n(),o("div",it,e[7]||(e[7]=[t("div",{class:"flex justify-center"},[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"})],-1)]))):a.value?(n(),o("div",lt,[l(at,{stats:E.value},null,8,["stats"]),t("div",ut,[e[19]||(e[19]=t("div",{class:"px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600"},[t("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni Generali")],-1)),t("div",ct,[t("div",gt,[t("div",null,[t("h3",mt,i(a.value.title),1),t("div",yt,[t("div",pt,[e[8]||(e[8]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Cliente:",-1)),l(u,{to:`/app/crm/clients/${a.value.client_id}`,class:"font-medium text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300 transition-colors"},{default:x(()=>{var r;return[y(i(((r=a.value.client)==null?void 0:r.name)||"N/A"),1)]}),_:1},8,["to"])]),t("div",vt,[e[9]||(e[9]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Numero Contratto:",-1)),t("span",xt,i(a.value.contract_number),1)]),t("div",bt,[e[10]||(e[10]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Tipo:",-1)),t("span",{class:W(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",$(G)(a.value.contract_type)])},i($(H)(a.value.contract_type)),3)]),t("div",ft,[e[11]||(e[11]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Stato:",-1)),l(z,{status:a.value.status,type:"contract"},null,8,["status"])])])]),t("div",null,[e[17]||(e[17]=t("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-3"},"Dettagli Finanziari",-1)),t("div",_t,[a.value.contract_type==="hourly"?(n(),o("div",kt,[t("div",ht,[e[12]||(e[12]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Tariffa Oraria:",-1)),t("span",wt,"€"+i(f(a.value.hourly_rate))+"/ora",1)]),a.value.budget_hours?(n(),o("div",Ct,[e[13]||(e[13]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Budget Ore:",-1)),t("span",jt,i(a.value.budget_hours)+" ore",1)])):m("",!0)])):a.value.budget_amount?(n(),o("div",Dt,[e[14]||(e[14]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Budget Totale:",-1)),t("span",Tt,"€"+i(f(a.value.budget_amount)),1)])):m("",!0),t("div",zt,[e[15]||(e[15]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Data Inizio:",-1)),t("span",It,i(C(a.value.start_date)),1)]),t("div",St,[e[16]||(e[16]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Data Fine:",-1)),t("span",$t,i(a.value.end_date?C(a.value.end_date):"Indeterminata"),1)])])])]),a.value.description?(n(),o("div",Nt,[e[18]||(e[18]=t("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-2"},"Descrizione",-1)),t("p",Vt,i(a.value.description),1)])):m("",!0)])]),t("div",Pt,[t("div",Bt,[t("h2",Ft,"Progetti Collegati ("+i(p.value.length)+")",1)]),t("div",Mt,[p.value.length>0?(n(),o("div",Rt,[(n(!0),o(N,null,V(p.value,r=>(n(),o("div",{key:r.id,class:"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[t("div",qt,[t("div",null,[t("h4",Et,i(r.name),1),r.description?(n(),o("p",Lt,i(r.description),1)):m("",!0),t("div",Ot,[l(z,{status:r.status,type:"project"},null,8,["status"])])]),l(u,{to:`/app/projects/${r.id}`,class:"text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300 text-sm font-medium transition-colors"},{default:x(()=>e[20]||(e[20]=[y(" Visualizza ")])),_:2,__:[20]},1032,["to"])])]))),128))])):(n(),o("div",At,[l(_,{name:"briefcase",size:"lg",class:"mx-auto mb-4 text-gray-300 dark:text-gray-600"}),e[21]||(e[21]=t("p",null,"Nessun progetto collegato",-1)),e[22]||(e[22]=t("p",{class:"text-sm"},"I progetti creati da questo contratto appariranno qui",-1))]))])]),t("div",Ut,[t("div",Ht,[t("h2",Gt,"Fatture Recenti ("+i(b.value.length)+")",1)]),t("div",Jt,[b.value.length>0?(n(),o("div",Kt,[(n(!0),o(N,null,V(b.value,r=>(n(),o("div",{key:r.id,class:"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[t("div",Qt,[t("div",null,[t("h4",Wt,i(r.invoice_number),1),t("p",Xt,i(C(r.issue_date))+" - "+i(C(r.due_date)),1),t("div",Yt,[l(z,{status:r.status,type:"invoice"},null,8,["status"]),t("span",Zt,"€"+i(f(r.total_amount)),1)])]),l(u,{to:`/app/invoices/${r.id}`,class:"text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300 text-sm font-medium transition-colors"},{default:x(()=>e[23]||(e[23]=[y(" Visualizza ")])),_:2,__:[23]},1032,["to"])])]))),128))])):(n(),o("div",te,[l(_,{name:"document-text",size:"lg",class:"mx-auto mb-4 text-gray-300 dark:text-gray-600"}),e[24]||(e[24]=t("p",null,"Nessuna fattura",-1)),e[25]||(e[25]=t("p",{class:"text-sm"},"Le fatture generate per questo contratto appariranno qui",-1))]))])])])):(n(),o("div",ee,[t("div",ae,[l(_,{name:"exclamation-circle",size:"xl",class:"mx-auto mb-4 text-gray-300 dark:text-gray-600"}),e[27]||(e[27]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Contratto non trovato",-1)),e[28]||(e[28]=t("p",{class:"text-gray-500 dark:text-gray-400 mb-4"},"Il contratto richiesto non esiste o non è accessibile",-1)),l(u,{to:"/app/crm/contracts",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 dark:bg-primary-700 dark:hover:bg-primary-800 transition-colors"},{default:x(()=>e[26]||(e[26]=[y(" Torna alla Lista Contratti ")])),_:1,__:[26]})])]))]),w.value?(n(),o("div",re,[t("div",se,[t("div",oe,[e[34]||(e[34]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Converti in Progetto",-1)),t("form",{onSubmit:X(A,["prevent"]),class:"space-y-4"},[t("div",null,[e[29]||(e[29]=t("label",{for:"project_name",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome Progetto * ",-1)),j(t("input",{id:"project_name","onUpdate:modelValue":e[1]||(e[1]=r=>d.value.name=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white",placeholder:((I=a.value)==null?void 0:I.title)||""},null,8,ne),[[D,d.value.name]])]),t("div",null,[e[30]||(e[30]=t("label",{for:"project_description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),j(t("textarea",{id:"project_description","onUpdate:modelValue":e[2]||(e[2]=r=>d.value.description=r),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white",placeholder:((S=a.value)==null?void 0:S.description)||""},null,8,de),[[D,d.value.description]])]),t("div",ie,[t("div",null,[e[31]||(e[31]=t("label",{for:"project_start",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Inizio ",-1)),j(t("input",{id:"project_start","onUpdate:modelValue":e[3]||(e[3]=r=>d.value.start_date=r),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[D,d.value.start_date]])]),t("div",null,[e[32]||(e[32]=t("label",{for:"project_end",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Fine ",-1)),j(t("input",{id:"project_end","onUpdate:modelValue":e[4]||(e[4]=r=>d.value.end_date=r),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[D,d.value.end_date]])])]),t("div",le,[t("button",{type:"button",onClick:U,class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"}," Annulla "),t("button",{type:"submit",disabled:h.value,class:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 disabled:opacity-50 dark:bg-green-700 dark:hover:bg-green-800 transition-colors"},[h.value?(n(),o("span",ce,e[33]||(e[33]=[t("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),y(" Conversione... ")]))):(n(),o("span",ge,"Crea Progetto"))],8,ue)])],32)])])])):m("",!0)])}}};export{ke as default};
