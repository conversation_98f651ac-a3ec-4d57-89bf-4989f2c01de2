import{r as p,c as k,x as G,b as n,e as g,l as S,k as _,j as e,t as i,A as W,B as c,C as x,H as T,F as C,p as V,s as A,f as J,o as d}from"./vendor.js";import{u as K}from"./crm.js";import{d as O,H as z}from"./app.js";import{_ as Q}from"./ListPageTemplate.js";import{A as X}from"./ActionButtonGroup.js";import"./Pagination.js";import"./StandardButton.js";const Y={class:"flex space-x-4"},Z=["value"],ee={class:"overflow-x-auto"},te={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ae={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},oe={class:"px-6 py-4 whitespace-nowrap"},se={class:"flex items-center"},le={class:"flex-shrink-0 h-10 w-10"},re={class:"h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center"},ie={class:"text-sm font-medium text-blue-600 dark:text-blue-400"},ne={class:"ml-4"},de={class:"text-sm font-medium text-gray-900 dark:text-white"},ue={class:"text-sm text-gray-500 dark:text-gray-400"},ce={class:"px-6 py-4 whitespace-nowrap"},me={class:"text-sm text-gray-900 dark:text-white"},ge={class:"px-6 py-4 whitespace-nowrap"},pe={class:"text-sm text-gray-900 dark:text-white"},xe={class:"px-6 py-4 whitespace-nowrap"},ye={class:"text-sm text-gray-900 dark:text-white"},be={key:0,class:"flex items-center mb-1"},fe={class:"text-sm"},ve={class:"flex items-center"},ke=["href"],he={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},we={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},_e={class:"text-center py-12"},Ce={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},ze={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},Ne={class:"mt-3"},Ve={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},Ae={class:"grid grid-cols-2 gap-4"},De=["value"],Se={class:"flex justify-end space-x-3 pt-4"},Te=["disabled"],Ue={key:0,class:"flex items-center"},$e={key:1},Re={__name:"ContactsList",setup(Ee){const u=K(),{showToast:y}=O(),b=p(""),f=p(""),h=p(!1),m=p(null),w=p(!1),s=p({first_name:"",last_name:"",email:"",phone:"",position:"",client_id:""}),U=[{key:"full_name",label:"Contatto"},{key:"client",label:"Cliente"},{key:"position",label:"Ruolo"},{key:"contact_info",label:"Contatti"},{key:"created_at",label:"Data Creazione"},{key:"actions",label:"Azioni"}],$=k(()=>{const a=[...new Set(v.value.map(r=>r.client_id))].length,t=v.value.filter(r=>r.phone).length;return[{label:"Totale Contatti",value:v.value.length,icon:"user",iconClass:"text-blue-500"},{label:"Clienti Coinvolti",value:a,icon:"building-office",iconClass:"text-green-500"},{label:"Con Telefono",value:t,icon:"phone",iconClass:"text-purple-500"},{label:"Con Ruolo",value:v.value.filter(r=>r.position).length,icon:"identification",iconClass:"text-orange-500"}]}),E=k(()=>u.loading),v=k(()=>u.contacts),N=k(()=>u.clients),F=k(()=>{let a=v.value;if(b.value&&(a=a.filter(t=>t.client_id==b.value)),f.value){const t=f.value.toLowerCase();a=a.filter(r=>r.position&&r.position.toLowerCase().includes(t))}return a}),M=async()=>{await Promise.all([u.fetchContacts(),u.fetchClients()])},B=a=>a?a.split(" ").map(t=>t.charAt(0)).join("").toUpperCase().slice(0,2):"N/A",L=a=>{const t=N.value.find(r=>r.id==a);return t?t.name:"Cliente sconosciuto"},P=a=>{m.value=a,s.value={first_name:a.first_name||"",last_name:a.last_name||"",email:a.email||"",phone:a.phone||"",position:a.position||"",client_id:a.client_id||""}},D=()=>{h.value=!1,m.value=null,s.value={first_name:"",last_name:"",email:"",phone:"",position:"",client_id:""}},q=async()=>{w.value=!0;try{m.value?(await u.updateContact(m.value.id,s.value),y("Contatto aggiornato con successo","success")):(await u.createContact(s.value),y("Contatto creato con successo","success")),D()}catch{y("Errore nel salvataggio del contatto","error")}finally{w.value=!1}},I=async a=>{if(confirm("Sei sicuro di voler eliminare questo contatto?"))try{await u.deleteContact(a),y("Contatto eliminato con successo","success")}catch{y("Errore nell'eliminazione del contatto","error")}},R=()=>{b.value="",f.value=""},j=a=>a?new Date(a).toLocaleDateString("it-IT"):"N/A";return G(()=>{M()}),(a,t)=>{const r=J("router-link");return d(),n(C,null,[g(Q,{title:"Contatti",subtitle:"Gestione contatti aziendali e persone di riferimento",data:F.value,columns:U,stats:$.value,loading:E.value,"can-create":!0,"create-label":"Nuovo Contatto","search-placeholder":"Nome, email o posizione...","empty-message":"Inizia creando il tuo primo contatto","results-label":"contatti",onCreate:t[3]||(t[3]=o=>h.value=!0)},{filters:_(()=>[e("div",Y,[e("div",null,[t[11]||(t[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Cliente",-1)),c(e("select",{"onUpdate:modelValue":t[0]||(t[0]=o=>b.value=o),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[10]||(t[10]=e("option",{value:""},"Tutti i clienti",-1)),(d(!0),n(C,null,V(N.value,o=>(d(),n("option",{key:o.id,value:o.id},i(o.name),9,Z))),128))],512),[[T,b.value]])]),e("div",null,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ruolo",-1)),c(e("input",{"onUpdate:modelValue":t[1]||(t[1]=o=>f.value=o),type:"text",placeholder:"Filtra per ruolo...",class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"},null,512),[[x,f.value]])]),e("div",{class:"flex items-end"},[e("button",{onClick:R,class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md"}," Pulisci Filtri ")])])]),content:_(({data:o})=>[e("div",ee,[e("table",te,[t[13]||(t[13]=e("thead",{class:"bg-gray-50 dark:bg-gray-800"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Contatto "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Cliente "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Ruolo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Contatti "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Data Creazione "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",ae,[(d(!0),n(C,null,V(o,l=>(d(),n("tr",{key:l.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",oe,[e("div",se,[e("div",le,[e("div",re,[e("span",ie,i(B(l.full_name)),1)])]),e("div",ne,[e("div",de,i(l.full_name),1),e("div",ue,i(l.email),1)])])]),e("td",ce,[e("div",me,[g(r,{to:`/app/crm/clients/${l.client_id}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"},{default:_(()=>[A(i(L(l.client_id)),1)]),_:2},1032,["to"])])]),e("td",ge,[e("span",pe,i(l.position||"N/A"),1)]),e("td",xe,[e("div",ye,[l.phone?(d(),n("div",be,[g(z,{name:"phone",class:"w-4 h-4 mr-1 text-gray-400 dark:text-gray-500"}),e("span",fe,i(l.phone),1)])):S("",!0),e("div",ve,[g(z,{name:"envelope",class:"w-4 h-4 mr-1 text-gray-400 dark:text-gray-500"}),e("a",{href:`mailto:${l.email}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm"},i(l.email),9,ke)])])]),e("td",he,i(j(l.created_at)),1),e("td",we,[g(X,{"show-view":!1,onEdit:H=>P(l),onDelete:H=>I(l.id),"delete-message":`Sei sicuro di voler eliminare il contatto '${l.name}'?`},null,8,["onEdit","onDelete","delete-message"])])]))),128))])])])]),"empty-state":_(()=>[e("div",_e,[g(z,{name:"user",class:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600"}),t[15]||(t[15]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun contatto trovato",-1)),t[16]||(t[16]=e("p",{class:"text-gray-500 dark:text-gray-400 mb-4"},"Inizia creando il tuo primo contatto",-1)),e("button",{onClick:t[2]||(t[2]=o=>h.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[g(z,{name:"plus",class:"w-4 h-4 mr-2"}),t[14]||(t[14]=A(" Crea Primo Contatto "))])])]),_:1},8,["data","stats","loading"]),h.value||m.value?(d(),n("div",Ce,[e("div",ze,[e("div",Ne,[e("h3",Ve,i(m.value?"Modifica Contatto":"Nuovo Contatto"),1),e("form",{onSubmit:W(q,["prevent"]),class:"space-y-4"},[e("div",Ae,[e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Nome *",-1)),c(e("input",{"onUpdate:modelValue":t[4]||(t[4]=o=>s.value.first_name=o),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Nome"},null,512),[[x,s.value.first_name]])]),e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Cognome *",-1)),c(e("input",{"onUpdate:modelValue":t[5]||(t[5]=o=>s.value.last_name=o),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Cognome"},null,512),[[x,s.value.last_name]])])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Email *",-1)),c(e("input",{"onUpdate:modelValue":t[6]||(t[6]=o=>s.value.email=o),type:"email",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"<EMAIL>"},null,512),[[x,s.value.email]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Telefono",-1)),c(e("input",{"onUpdate:modelValue":t[7]||(t[7]=o=>s.value.phone=o),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"+39 ************"},null,512),[[x,s.value.phone]])]),e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Posizione/Ruolo",-1)),c(e("input",{"onUpdate:modelValue":t[8]||(t[8]=o=>s.value.position=o),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"es. Direttore Commerciale"},null,512),[[x,s.value.position]])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Cliente *",-1)),c(e("select",{"onUpdate:modelValue":t[9]||(t[9]=o=>s.value.client_id=o),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[22]||(t[22]=e("option",{value:""},"Seleziona cliente",-1)),(d(!0),n(C,null,V(N.value,o=>(d(),n("option",{key:o.id,value:o.id},i(o.name),9,De))),128))],512),[[T,s.value.client_id]])]),e("div",Se,[e("button",{type:"button",onClick:D,class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:w.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 rounded-md"},[w.value?(d(),n("span",Ue,t[24]||(t[24]=[e("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),A(" Salvataggio... ")]))):(d(),n("span",$e,i(m.value?"Aggiorna Contatto":"Crea Contatto"),1))],8,Te)])],32)])])])):S("",!0)],64)}}};export{Re as default};
