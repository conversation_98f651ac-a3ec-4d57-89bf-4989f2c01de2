import{c as x,r as M,w as W,b as g,l as z,j as t,t as d,e as p,A as Y,B as m,C as b,H as f,I as F,s as v,o as u,h as Z,f as D,F as $,p as tt,x as et,n as A}from"./vendor.js";import{H as U,_ as ot}from"./app.js";import{u as B}from"./governance.js";const it={key:0,class:"fixed inset-0 bg-gray-50 bg-opacity-75 flex items-center justify-center z-50"},st={class:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-screen overflow-y-auto m-4"},lt={class:"px-6 py-4 border-b border-gray-200"},at={class:"flex items-center justify-between"},rt={class:"text-lg font-medium text-gray-900"},nt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},dt={class:"md:col-span-2"},mt={class:"md:col-span-2"},ut={class:"space-y-4"},gt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},pt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ct={class:"md:col-span-2"},bt={class:"md:col-span-2"},yt={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200"},vt=["disabled"],xt={key:0,class:"flex items-center"},ft={key:1},wt={__name:"RiskFormModal",props:{isOpen:{type:Boolean,default:!1},risk:{type:Object,default:null}},emits:["close","saved"],setup(y,{emit:e}){const c=y,i=e,w=B(),_=x(()=>w.loading),s=M({title:"",description:"",category:"",risk_type:"",probability:"",impact:"",status:"identified",compliance_framework:"",mitigation_strategy:"",mitigation_actions:"",mitigation_deadline:"",mitigation_cost:"",responsible_department:"",next_review_date:"",regulatory_requirements:"",tags:""}),h=x(()=>{var a;return!!((a=c.risk)!=null&&a.id)});W(()=>c.isOpen,a=>{console.log("🔥 RiskFormModal isOpen changed:",a),a&&(l(),c.risk&&C(c.risk))});const l=()=>{s.value={title:"",description:"",category:"",risk_type:"",probability:"",impact:"",status:"identified",compliance_framework:"",mitigation_strategy:"",mitigation_actions:"",mitigation_deadline:"",mitigation_cost:"",responsible_department:"",next_review_date:"",regulatory_requirements:"",tags:""}},C=a=>{s.value={title:a.title||"",description:a.description||"",category:a.category||"",risk_type:a.risk_type||"",probability:a.probability||"",impact:a.impact||"",status:a.status||"identified",compliance_framework:a.compliance_framework||"",mitigation_strategy:a.mitigation_strategy||"",mitigation_actions:a.mitigation_actions||"",mitigation_deadline:a.mitigation_deadline?a.mitigation_deadline.split("T")[0]:"",mitigation_cost:a.mitigation_cost||"",responsible_department:a.responsible_department||"",next_review_date:a.next_review_date?a.next_review_date.split("T")[0]:"",regulatory_requirements:a.regulatory_requirements||"",tags:Array.isArray(a.tags)?a.tags.join(", "):a.tags||""}},V=async()=>{try{const a={...s.value};a.tags?a.tags=a.tags.split(",").map(k=>k.trim()).filter(k=>k):a.tags=[],["risk_type","compliance_framework","mitigation_strategy","mitigation_actions","mitigation_deadline","mitigation_cost","responsible_department","next_review_date","regulatory_requirements"].forEach(k=>{a[k]===""&&(a[k]=null)});let n;h.value?n=await w.updateRisk(c.risk.id,a):n=await w.createRisk(a),i("saved",n),S()}catch(a){console.error("Error saving risk:",a)}},S=()=>{i("close")};return(a,o)=>y.isOpen?(u(),g("div",it,[t("div",st,[t("div",lt,[t("div",at,[t("h3",rt,d(h.value?"Modifica Rischio":"Nuovo Rischio"),1),t("button",{onClick:S,class:"text-gray-400 hover:text-gray-600"},[p(U,{name:"x-mark",size:"md"})])])]),t("form",{onSubmit:Y(V,["prevent"]),class:"p-6 space-y-6"},[t("div",nt,[t("div",dt,[o[16]||(o[16]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Titolo Rischio * ",-1)),m(t("input",{"onUpdate:modelValue":o[0]||(o[0]=n=>s.value.title=n),type:"text",required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Es. Rischio di violazione dati"},null,512),[[b,s.value.title]])]),t("div",mt,[o[17]||(o[17]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Descrizione * ",-1)),m(t("textarea",{"onUpdate:modelValue":o[1]||(o[1]=n=>s.value.description=n),rows:"3",required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Descrizione dettagliata del rischio"},null,512),[[b,s.value.description]])]),t("div",null,[o[19]||(o[19]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Categoria * ",-1)),m(t("select",{"onUpdate:modelValue":o[2]||(o[2]=n=>s.value.category=n),required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},o[18]||(o[18]=[F('<option value="">Seleziona categoria</option><option value="security">Sicurezza</option><option value="operational">Operativo</option><option value="financial">Finanziario</option><option value="compliance">Compliance</option><option value="strategic">Strategico</option>',6)]),512),[[f,s.value.category]])]),t("div",null,[o[20]||(o[20]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Tipo Rischio ",-1)),m(t("input",{"onUpdate:modelValue":o[3]||(o[3]=n=>s.value.risk_type=n),type:"text",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Es. Cyber Security, Data Protection"},null,512),[[b,s.value.risk_type]])]),t("div",null,[o[22]||(o[22]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Probabilità * ",-1)),m(t("select",{"onUpdate:modelValue":o[4]||(o[4]=n=>s.value.probability=n),required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},o[21]||(o[21]=[t("option",{value:""},"Seleziona probabilità",-1),t("option",{value:1},"1 - Molto Bassa",-1),t("option",{value:2},"2 - Bassa",-1),t("option",{value:3},"3 - Media",-1),t("option",{value:4},"4 - Alta",-1),t("option",{value:5},"5 - Molto Alta",-1)]),512),[[f,s.value.probability,void 0,{number:!0}]])]),t("div",null,[o[24]||(o[24]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Impatto * ",-1)),m(t("select",{"onUpdate:modelValue":o[5]||(o[5]=n=>s.value.impact=n),required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},o[23]||(o[23]=[t("option",{value:""},"Seleziona impatto",-1),t("option",{value:1},"1 - Molto Basso",-1),t("option",{value:2},"2 - Basso",-1),t("option",{value:3},"3 - Medio",-1),t("option",{value:4},"4 - Alto",-1),t("option",{value:5},"5 - Molto Alto",-1)]),512),[[f,s.value.impact,void 0,{number:!0}]])]),t("div",null,[o[26]||(o[26]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Status ",-1)),m(t("select",{"onUpdate:modelValue":o[6]||(o[6]=n=>s.value.status=n),class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},o[25]||(o[25]=[t("option",{value:"identified"},"Identificato",-1),t("option",{value:"under_review"},"In Revisione",-1),t("option",{value:"mitigated"},"Mitigato",-1),t("option",{value:"accepted"},"Accettato",-1)]),512),[[f,s.value.status]])]),t("div",null,[o[28]||(o[28]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Framework Compliance ",-1)),m(t("select",{"onUpdate:modelValue":o[7]||(o[7]=n=>s.value.compliance_framework=n),class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},o[27]||(o[27]=[F('<option value="">Seleziona framework</option><option value="GDPR">GDPR</option><option value="ISO27001">ISO 27001</option><option value="SOX">SOX</option><option value="HIPAA">HIPAA</option>',5)]),512),[[f,s.value.compliance_framework]])])]),t("div",ut,[t("div",null,[o[29]||(o[29]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Strategia di Mitigazione ",-1)),m(t("textarea",{"onUpdate:modelValue":o[8]||(o[8]=n=>s.value.mitigation_strategy=n),rows:"3",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Strategia per mitigare il rischio..."},null,512),[[b,s.value.mitigation_strategy]])]),t("div",null,[o[30]||(o[30]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Azioni di Mitigazione ",-1)),m(t("textarea",{"onUpdate:modelValue":o[9]||(o[9]=n=>s.value.mitigation_actions=n),rows:"3",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Azioni specifiche da intraprendere..."},null,512),[[b,s.value.mitigation_actions]])]),t("div",gt,[t("div",null,[o[31]||(o[31]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Scadenza Mitigazione ",-1)),m(t("input",{"onUpdate:modelValue":o[10]||(o[10]=n=>s.value.mitigation_deadline=n),type:"date",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[b,s.value.mitigation_deadline]])]),t("div",null,[o[32]||(o[32]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Costo Mitigazione (€) ",-1)),m(t("input",{"onUpdate:modelValue":o[11]||(o[11]=n=>s.value.mitigation_cost=n),type:"number",min:"0",step:"0.01",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"0.00"},null,512),[[b,s.value.mitigation_cost,void 0,{number:!0}]])])])]),t("div",pt,[t("div",null,[o[33]||(o[33]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Dipartimento Responsabile ",-1)),m(t("input",{"onUpdate:modelValue":o[12]||(o[12]=n=>s.value.responsible_department=n),type:"text",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Es. IT, HR, Finanza"},null,512),[[b,s.value.responsible_department]])]),t("div",null,[o[34]||(o[34]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Prossima Revisione ",-1)),m(t("input",{"onUpdate:modelValue":o[13]||(o[13]=n=>s.value.next_review_date=n),type:"date",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[b,s.value.next_review_date]])]),t("div",ct,[o[35]||(o[35]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Requisiti Normativi ",-1)),m(t("textarea",{"onUpdate:modelValue":o[14]||(o[14]=n=>s.value.regulatory_requirements=n),rows:"2",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Eventuali requisiti normativi correlati..."},null,512),[[b,s.value.regulatory_requirements]])]),t("div",bt,[o[36]||(o[36]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Tag (separati da virgola) ",-1)),m(t("input",{"onUpdate:modelValue":o[15]||(o[15]=n=>s.value.tags=n),type:"text",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Es. cyber, gdpr, backup"},null,512),[[b,s.value.tags]])])]),t("div",yt,[t("button",{type:"button",onClick:S,class:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"}," Annulla "),t("button",{type:"submit",disabled:_.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[_.value?(u(),g("span",xt,o[37]||(o[37]=[t("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),v(" Salvando... ")]))):(u(),g("span",ft,d(h.value?"Aggiorna Rischio":"Crea Rischio"),1))],8,vt)])],32)])])):z("",!0)}},kt={name:"RiskAssessment",components:{HeroIcon:U,RiskFormModal:wt},setup(){const y=B(),e=M(null),c=M(!1),i=M(!1),w=M(null),_=M({category:"",risk_level:"",status:"",owner:""}),s=x(()=>y.risks||[]),h=x(()=>y.loading),l=x(()=>y.error),C=x(()=>s.value.filter(r=>r.risk_level==="critical").length),V=x(()=>s.value.filter(r=>r.risk_level==="high").length),S=x(()=>s.value.filter(r=>r.risk_level==="medium").length),a=x(()=>s.value.filter(r=>r.risk_level==="low").length),o=async()=>{try{await y.fetchRisks()}catch(r){console.error("Error loading risks:",r)}},n=async()=>{try{const r=Object.fromEntries(Object.entries(_.value).filter(([R,Q])=>Q));await y.fetchRisks(r)}catch(r){console.error("Error applying filters:",r)}},k=()=>{_.value={category:"",risk_level:"",status:"",owner:""},o()},I=()=>{e.value=null,c.value=!0},L=r=>{e.value=r,c.value=!0},q=()=>{c.value=!1,e.value=null},O=()=>{o()},T=r=>{w.value=r,i.value=!0},E=()=>{i.value=!1,w.value=null},j=async r=>{try{await y.mitigateRisk(r.id,"Rischio mitigato"),o()}catch(R){console.error("Error mitigating risk:",R)}},P=r=>({operational:"Operativo",financial:"Finanziario",strategic:"Strategico",compliance:"Compliance",reputational:"Reputazionale"})[r]||r,N=r=>({operational:"bg-blue-100 text-blue-800",financial:"bg-green-100 text-green-800",strategic:"bg-purple-100 text-purple-800",compliance:"bg-orange-100 text-orange-800",reputational:"bg-pink-100 text-pink-800"})[r]||"bg-gray-100 text-gray-800",H=r=>({low:"Basso",medium:"Medio",high:"Alto",critical:"Critico"})[r]||r,G=r=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",critical:"bg-red-100 text-red-800"})[r]||"bg-gray-100 text-gray-800",X=r=>({identified:"Identificato",assessed:"Valutato",mitigated:"Mitigato",monitored:"Monitorato",closed:"Chiuso"})[r]||r,J=r=>({identified:"bg-red-100 text-red-800",assessed:"bg-yellow-100 text-yellow-800",mitigated:"bg-blue-100 text-blue-800",monitored:"bg-purple-100 text-purple-800",closed:"bg-green-100 text-green-800"})[r]||"bg-gray-100 text-gray-800",K=r=>r?r.length>80?r.substring(0,80)+"...":r:"-";return et(()=>{o()}),{selectedRisk:e,showRiskModal:c,showDetailModal:i,viewedRisk:w,filters:_,risks:s,loading:h,error:l,criticalRisks:C,highRisks:V,mediumRisks:S,lowRisks:a,loadRisks:o,applyFilters:n,resetFilters:k,openCreateRisk:I,editRisk:L,closeRiskModal:q,onRiskSaved:O,viewRisk:T,closeDetailModal:E,mitigateRisk:j,getCategoryLabel:P,getCategoryBadgeClass:N,getRiskLevelLabel:H,getRiskLevelBadgeClass:G,getStatusLabel:X,getStatusBadgeClass:J,truncateDescription:K}}},Rt={class:"risk-assessment"},_t={class:"bg-white rounded-lg shadow-sm p-6 mb-6"},ht={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6"},Ct={class:"mt-4 lg:mt-0"},Mt={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},St={class:"bg-red-50 rounded-lg p-4"},zt={class:"flex items-center"},Ft={class:"ml-3"},Vt={class:"text-2xl font-bold text-red-900"},At={class:"bg-orange-50 rounded-lg p-4"},Dt={class:"flex items-center"},Ut={class:"ml-3"},Bt={class:"text-2xl font-bold text-orange-900"},It={class:"bg-yellow-50 rounded-lg p-4"},Lt={class:"flex items-center"},qt={class:"ml-3"},Ot={class:"text-2xl font-bold text-yellow-900"},Tt={class:"bg-green-50 rounded-lg p-4"},Et={class:"flex items-center"},jt={class:"ml-3"},Pt={class:"text-2xl font-bold text-green-900"},Nt={class:"grid grid-cols-1 md:grid-cols-5 gap-4"},Ht={class:"flex items-end"},Gt={class:"bg-white rounded-lg shadow-sm"},Xt={key:0,class:"flex justify-center py-12"},Jt={key:1,class:"p-6 text-center text-red-600"},Kt={key:2,class:"p-12 text-center text-gray-500"},Qt={key:3},Wt={class:"overflow-x-auto"},Yt={class:"min-w-full divide-y divide-gray-200"},Zt={class:"bg-white divide-y divide-gray-200"},$t={class:"px-6 py-4"},te={class:"text-sm font-medium text-gray-900"},ee={class:"text-sm text-gray-500"},oe={class:"px-6 py-4 whitespace-nowrap"},ie={class:"px-6 py-4 whitespace-nowrap"},se={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},le={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ae={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},re={class:"px-6 py-4 whitespace-nowrap"},ne={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},de=["onClick"],me=["onClick"],ue=["onClick"],ge={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},pe={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},ce={class:"mt-3"},be={class:"flex items-center justify-between mb-4"},ye={key:0,class:"space-y-3"},ve={class:"mt-4"};function xe(y,e,c,i,w,_){const s=D("HeroIcon"),h=D("RiskFormModal");return u(),g("div",Rt,[t("div",_t,[t("div",ht,[e[15]||(e[15]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900"},"Risk Assessment"),t("p",{class:"text-gray-600 mt-1"},"Gestione e monitoraggio dei rischi aziendali")],-1)),t("div",Ct,[t("button",{onClick:e[0]||(e[0]=(...l)=>i.openCreateRisk&&i.openCreateRisk(...l)),class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"},[p(s,{name:"plus",class:"h-5 w-5 mr-2"}),e[14]||(e[14]=v(" Nuovo Rischio "))])])]),t("div",Mt,[t("div",St,[t("div",zt,[p(s,{name:"exclamation-triangle",class:"h-8 w-8 text-red-600"}),t("div",Ft,[e[16]||(e[16]=t("p",{class:"text-sm text-red-600 font-medium"},"Rischi Critici",-1)),t("p",Vt,d(i.criticalRisks),1)])])]),t("div",At,[t("div",Dt,[p(s,{name:"shield-exclamation",class:"h-8 w-8 text-orange-600"}),t("div",Ut,[e[17]||(e[17]=t("p",{class:"text-sm text-orange-600 font-medium"},"Rischi Alti",-1)),t("p",Bt,d(i.highRisks),1)])])]),t("div",It,[t("div",Lt,[p(s,{name:"exclamation-circle",class:"h-8 w-8 text-yellow-600"}),t("div",qt,[e[18]||(e[18]=t("p",{class:"text-sm text-yellow-600 font-medium"},"Rischi Medi",-1)),t("p",Ot,d(i.mediumRisks),1)])])]),t("div",Tt,[t("div",Et,[p(s,{name:"check-circle",class:"h-8 w-8 text-green-600"}),t("div",jt,[e[19]||(e[19]=t("p",{class:"text-sm text-green-600 font-medium"},"Rischi Bassi",-1)),t("p",Pt,d(i.lowRisks),1)])])])]),t("div",Nt,[t("div",null,[e[21]||(e[21]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Categoria",-1)),m(t("select",{"onUpdate:modelValue":e[1]||(e[1]=l=>i.filters.category=l),onChange:e[2]||(e[2]=(...l)=>i.applyFilters&&i.applyFilters(...l)),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},e[20]||(e[20]=[F('<option value="">Tutte</option><option value="operational">Operativo</option><option value="financial">Finanziario</option><option value="strategic">Strategico</option><option value="compliance">Compliance</option><option value="reputational">Reputazionale</option>',6)]),544),[[f,i.filters.category]])]),t("div",null,[e[23]||(e[23]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Livello Rischio",-1)),m(t("select",{"onUpdate:modelValue":e[3]||(e[3]=l=>i.filters.risk_level=l),onChange:e[4]||(e[4]=(...l)=>i.applyFilters&&i.applyFilters(...l)),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},e[22]||(e[22]=[F('<option value="">Tutti</option><option value="low">Basso</option><option value="medium">Medio</option><option value="high">Alto</option><option value="critical">Critico</option>',5)]),544),[[f,i.filters.risk_level]])]),t("div",null,[e[25]||(e[25]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Status",-1)),m(t("select",{"onUpdate:modelValue":e[5]||(e[5]=l=>i.filters.status=l),onChange:e[6]||(e[6]=(...l)=>i.applyFilters&&i.applyFilters(...l)),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},e[24]||(e[24]=[F('<option value="">Tutti</option><option value="identified">Identificato</option><option value="assessed">Valutato</option><option value="mitigated">Mitigato</option><option value="monitored">Monitorato</option><option value="closed">Chiuso</option>',6)]),544),[[f,i.filters.status]])]),t("div",null,[e[27]||(e[27]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Responsabile",-1)),m(t("select",{"onUpdate:modelValue":e[7]||(e[7]=l=>i.filters.owner=l),onChange:e[8]||(e[8]=(...l)=>i.applyFilters&&i.applyFilters(...l)),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},e[26]||(e[26]=[t("option",{value:""},"Tutti",-1),t("option",{value:"admin"},"Admin",-1),t("option",{value:"manager"},"Manager",-1)]),544),[[f,i.filters.owner]])]),t("div",Ht,[t("button",{onClick:e[9]||(e[9]=(...l)=>i.resetFilters&&i.resetFilters(...l)),class:"w-full bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"}," Reset Filtri ")])])]),t("div",Gt,[i.loading?(u(),g("div",Xt,e[28]||(e[28]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)]))):i.error?(u(),g("div",Jt,[p(s,{name:"exclamation-triangle",class:"h-8 w-8 mx-auto mb-2"}),t("p",null,d(i.error),1),t("button",{onClick:e[10]||(e[10]=(...l)=>i.loadRisks&&i.loadRisks(...l)),class:"mt-3 text-blue-600 hover:text-blue-800 font-medium"}," Riprova ")])):i.risks.length===0?(u(),g("div",Kt,[p(s,{name:"shield-exclamation",class:"h-12 w-12 mx-auto mb-4 text-gray-300"}),e[29]||(e[29]=t("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Nessun rischio trovato",-1)),e[30]||(e[30]=t("p",{class:"text-gray-600 mb-6"},"Inizia identificando il primo rischio aziendale",-1)),t("button",{onClick:e[11]||(e[11]=(...l)=>i.openCreateRisk&&i.openCreateRisk(...l)),class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"}," Identifica Primo Rischio ")])):(u(),g("div",Qt,[t("div",Wt,[t("table",Yt,[e[31]||(e[31]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Rischio "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Categoria "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Livello "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Probabilità "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Impatto "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Responsabile "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Azioni ")])],-1)),t("tbody",Zt,[(u(!0),g($,null,tt(i.risks,l=>(u(),g("tr",{key:l.id,class:"hover:bg-gray-50"},[t("td",$t,[t("div",te,d(l.title),1),t("div",ee,d(i.truncateDescription(l.description)),1)]),t("td",oe,[t("span",{class:A(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",i.getCategoryBadgeClass(l.category)])},d(i.getCategoryLabel(l.category)),3)]),t("td",ie,[t("span",{class:A(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",i.getRiskLevelBadgeClass(l.risk_level)])},d(i.getRiskLevelLabel(l.risk_level)),3)]),t("td",se,d(l.probability||"-")+"/10 ",1),t("td",le,d(l.impact||"-")+"/10 ",1),t("td",ae,d(l.owner||"-"),1),t("td",re,[t("span",{class:A(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",i.getStatusBadgeClass(l.status)])},d(i.getStatusLabel(l.status)),3)]),t("td",ne,[t("button",{onClick:C=>i.editRisk(l),class:"text-indigo-600 hover:text-indigo-900 mr-3"},[p(s,{name:"pencil-square",class:"h-4 w-4"})],8,de),t("button",{onClick:C=>i.viewRisk(l),class:"text-gray-600 hover:text-gray-900 mr-3"},[p(s,{name:"eye",class:"h-4 w-4"})],8,me),l.status!=="closed"?(u(),g("button",{key:0,onClick:C=>i.mitigateRisk(l),class:"text-green-600 hover:text-green-900"},[p(s,{name:"shield-check",class:"h-4 w-4"})],8,ue)):z("",!0)])]))),128))])])])]))]),i.showDetailModal?(u(),g("div",ge,[t("div",pe,[t("div",ce,[t("div",be,[e[32]||(e[32]=t("h3",{class:"text-lg font-medium text-gray-900"},"Dettaglio Rischio",-1)),t("button",{onClick:e[12]||(e[12]=(...l)=>i.closeDetailModal&&i.closeDetailModal(...l)),class:"text-gray-400 hover:text-gray-600"},[p(s,{name:"x-mark",class:"w-5 h-5"})])]),i.viewedRisk?(u(),g("div",ye,[t("div",null,[e[33]||(e[33]=t("strong",null,"Titolo:",-1)),v(" "+d(i.viewedRisk.title),1)]),t("div",null,[e[34]||(e[34]=t("strong",null,"Categoria:",-1)),v(" "+d(i.getCategoryLabel(i.viewedRisk.category)),1)]),t("div",null,[e[35]||(e[35]=t("strong",null,"Livello:",-1)),v(" "+d(i.getRiskLevelLabel(i.viewedRisk.risk_level)),1)]),t("div",null,[e[36]||(e[36]=t("strong",null,"Descrizione:",-1)),v(" "+d(i.viewedRisk.description),1)]),t("div",null,[e[37]||(e[37]=t("strong",null,"Probabilità:",-1)),v(" "+d(i.viewedRisk.probability||"-")+"/10",1)]),t("div",null,[e[38]||(e[38]=t("strong",null,"Impatto:",-1)),v(" "+d(i.viewedRisk.impact||"-")+"/10",1)]),t("div",null,[e[39]||(e[39]=t("strong",null,"Responsabile:",-1)),v(" "+d(i.viewedRisk.owner||"-"),1)]),t("div",null,[e[40]||(e[40]=t("strong",null,"Status:",-1)),v(" "+d(i.getStatusLabel(i.viewedRisk.status)),1)])])):z("",!0),t("div",ve,[t("button",{onClick:e[13]||(e[13]=(...l)=>i.closeDetailModal&&i.closeDetailModal(...l)),class:"w-full px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-md text-sm font-medium"}," Chiudi ")])])])])):z("",!0),i.showRiskModal?(u(),Z(h,{key:1,"is-open":i.showRiskModal,risk:i.selectedRisk,onClose:i.closeRiskModal,onSaved:i.onRiskSaved},null,8,["is-open","risk","onClose","onSaved"])):z("",!0)])}const Re=ot(kt,[["render",xe]]);export{Re as default};
