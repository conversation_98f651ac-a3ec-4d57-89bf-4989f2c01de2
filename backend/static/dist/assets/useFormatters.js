import{f as m,a as l,b as i,c as u,d as c,r as y,e as d,g as x}from"./formatters.js";function M(){return{formatHours:u,formatPercentage:i,formatCurrency:l,formatNumber:m,formatValue:x,formatDate:d,formatTimeAgo:r=>{if(!r)return"";try{const t=new Date,o=new Date(r),e=t-o,n=Math.floor(e/1e3),s=Math.floor(n/60),f=Math.floor(s/60),a=Math.floor(f/24);return n<60?"Ora":s<60?`${s} min fa`:f<24?`${f} ore fa`:a<7?`${a} giorni fa`:a<30?`${Math.floor(a/7)} settimane fa`:a<365?`${Math.floor(a/30)} mesi fa`:`${Math.floor(a/365)} anni fa`}catch(t){return console.error("Error formatting time ago:",t),d(r)}},roundNumber:y,calculations:c,formatUtilization:(r,t,o=!0)=>{const e=c.utilization(r,t,0);return i(e,o)},formatVariance:(r,t,o=!0)=>{const e=c.variance(r,t,1),n=e>0?"+":"";return o?`${n}${u(e)}`:`${n}${e}`},getUtilizationClass:r=>r>100?"text-red-600 dark:text-red-400":r>=90?"text-yellow-600 dark:text-yellow-400":r>=70?"text-green-600 dark:text-green-400":"text-gray-600 dark:text-gray-400",getVarianceClass:r=>r>0?"text-red-600 dark:text-red-400":r<0?"text-green-600 dark:text-green-400":"text-gray-600 dark:text-gray-400",displayHours:r=>u(r,!0),displayPercentage:r=>i(r,!0,0),displayCurrency:r=>l(r),displayNumber:(r,t=0)=>m(r,"it-IT",t)}}export{M as u};
