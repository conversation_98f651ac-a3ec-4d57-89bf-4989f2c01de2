import{r as n,c as E,x as A,b as s,j as a,l as d,B as u,H as f,F as T,p as z,I as P,t as l,A as U,C as k,o as i}from"./vendor.js";import{a as B,c}from"./app.js";const q={class:"flex justify-between items-center mb-6"},F={class:"flex space-x-3"},L={class:"mb-6"},O={class:"flex space-x-4 mb-3"},W=["value"],H={key:0,class:"flex justify-center py-8"},Q={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4 mb-6"},G={class:"text-red-600"},J={key:2,class:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md"},X={class:"divide-y divide-gray-200 dark:divide-gray-700"},Y={class:"flex items-center justify-between"},Z={class:"flex-1"},ee={class:"flex items-center"},te={class:"text-sm font-medium text-gray-900 dark:text-white"},ae={key:0,class:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"},oe={class:"mt-1 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},re={key:0},se={key:0,class:"mt-1 text-sm text-gray-600 dark:text-gray-300"},ie={class:"flex items-center space-x-2"},le=["onClick"],ne=["onClick"],de=["onClick"],ue={key:0,class:"text-center py-12"},me={class:"mt-3"},pe={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},ve={class:"mb-4"},ge=["value"],ye={class:"mb-4"},ce={class:"grid grid-cols-2 gap-4 mb-4"},xe={class:"mb-4"},fe={class:"mb-4"},be={class:"mb-4"},ke={class:"flex justify-end space-x-3"},_e={type:"submit",class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md"},Te={__name:"KPITemplates",setup(we){B();const j=n([]),b=n(!1),m=n(""),v=n(""),g=n(""),_=n(!1),y=n(!1),w=n(null),r=n({project_type:"",kpi_name:"",target_min:null,target_max:null,warning_threshold:null,unit:"%",description:""}),h=n([{key:"service",name:"Servizio"},{key:"license",name:"Licenza"},{key:"consulting",name:"Consulenza"},{key:"product",name:"Prodotto"},{key:"rd",name:"R&D"},{key:"internal",name:"Interno"}]),M=E(()=>{let o=j.value;return v.value&&(o=o.filter(e=>e.project_type===v.value)),g.value&&(o=o.filter(e=>e.kpi_name===g.value)),o}),x=async()=>{var o,e;b.value=!0,m.value="";try{const t=await c.get("/api/admin/kpi-templates");j.value=t.data.data||t.data}catch(t){m.value=((e=(o=t.response)==null?void 0:o.data)==null?void 0:e.message)||t.message||"Errore nel caricamento dei template"}finally{b.value=!1}},V=async()=>{var o,e;try{y.value?await c.put(`/api/admin/kpi-templates/${w.value.id}`,r.value):await c.post("/api/admin/kpi-templates",r.value),await x(),C()}catch(t){m.value=((e=(o=t.response)==null?void 0:o.data)==null?void 0:e.message)||t.message||"Errore nel salvataggio del template"}},S=o=>{w.value=o,r.value={...o},y.value=!0},$=async o=>{var e,t;try{await c.put(`/api/admin/kpi-templates/${o.id}/toggle`),await x()}catch(p){m.value=((t=(e=p.response)==null?void 0:e.data)==null?void 0:t.message)||p.message||"Errore nel toggle del template"}},K=async o=>{var e,t;if(confirm("Sei sicuro di voler eliminare questo template?"))try{await c.delete(`/api/admin/kpi-templates/${o.id}`),await x()}catch(p){m.value=((t=(e=p.response)==null?void 0:e.data)==null?void 0:t.message)||p.message||"Errore nell'eliminazione del template"}},N=async()=>{var o,e;if(confirm("Sei sicuro di voler ripristinare i template di default? Questa azione eliminerà tutti i template personalizzati."))try{await c.post("/api/admin/kpi-templates/reset"),await x()}catch(t){m.value=((e=(o=t.response)==null?void 0:o.data)==null?void 0:e.message)||t.message||"Errore nel reset dei template"}},I=()=>{},R=()=>{v.value="",g.value=""},C=()=>{_.value=!1,y.value=!1,w.value=null,r.value={project_type:"",kpi_name:"",target_min:null,target_max:null,warning_threshold:null,unit:"%",description:""}},D=o=>{const e=h.value.find(t=>t.key===o);return e?e.name:o};return A(()=>{x()}),(o,e)=>(i(),s("div",null,[a("div",q,[e[15]||(e[15]=a("div",null,[a("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Template KPI"),a("p",{class:"text-gray-600 dark:text-gray-400"},"Configura KPI di default per tipologie progetto")],-1)),a("div",F,[a("button",{onClick:e[0]||(e[0]=t=>_.value=!0),class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," + Nuovo Template "),a("button",{onClick:e[1]||(e[1]=t=>N()),class:"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," 🔄 Reset Default ")])]),a("div",L,[a("div",O,[u(a("select",{"onUpdate:modelValue":e[2]||(e[2]=t=>v.value=t),onChange:e[3]||(e[3]=t=>I()),class:"border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"},[e[16]||(e[16]=a("option",{value:""},"Tutte le tipologie",-1)),(i(!0),s(T,null,z(h.value,t=>(i(),s("option",{key:t.key,value:t.key},l(t.name),9,W))),128))],544),[[f,v.value]]),u(a("select",{"onUpdate:modelValue":e[4]||(e[4]=t=>g.value=t),onChange:e[5]||(e[5]=t=>I()),class:"border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"},e[17]||(e[17]=[P('<option value="">Tutti i KPI</option><option value="margin_percentage">Margine Netto</option><option value="utilization_rate">Utilization Rate</option><option value="cost_per_hour">Costo per Ora</option><option value="cost_revenue_ratio">Rapporto C/R</option>',5)]),544),[[f,g.value]]),v.value||g.value?(i(),s("button",{key:0,onClick:e[6]||(e[6]=t=>R()),class:"px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50"}," 🗑️ Reset ")):d("",!0)])]),b.value?(i(),s("div",H,e[18]||(e[18]=[a("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):d("",!0),m.value?(i(),s("div",Q,[a("p",G,l(m.value),1)])):d("",!0),b.value?d("",!0):(i(),s("div",J,[a("ul",X,[(i(!0),s(T,null,z(M.value,t=>(i(),s("li",{key:t.id,class:"px-6 py-4"},[a("div",Y,[a("div",Z,[a("div",ee,[a("h3",te,l(D(t.project_type))+" - "+l(t.display_name),1),t.is_active?d("",!0):(i(),s("span",ae," Inattivo "))]),a("div",oe,[a("span",null,"Target: "+l(t.target_min)+" - "+l(t.target_max)+" "+l(t.unit),1),t.warning_threshold?(i(),s("span",re,"Warning: "+l(t.warning_threshold)+" "+l(t.unit),1)):d("",!0)]),t.description?(i(),s("p",se,l(t.description),1)):d("",!0)]),a("div",ie,[a("button",{onClick:p=>S(t),class:"text-primary-600 hover:text-primary-900 text-sm font-medium"}," Modifica ",8,le),a("button",{onClick:p=>$(t),class:"text-gray-600 hover:text-gray-900 text-sm font-medium"},l(t.is_active?"Disattiva":"Attiva"),9,ne),a("button",{onClick:p=>K(t),class:"text-red-600 hover:text-red-900 text-sm font-medium"}," Elimina ",8,de)])])]))),128))]),M.value.length===0?(i(),s("div",ue,e[19]||(e[19]=[a("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun template trovato",-1)]))):d("",!0)])),_.value||y.value?(i(),s("div",{key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:C},[a("div",{class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:e[14]||(e[14]=U(()=>{},["stop"]))},[a("div",me,[a("h3",pe,l(y.value?"Modifica Template KPI":"Nuovo Template KPI"),1),a("form",{onSubmit:U(V,["prevent"])},[a("div",ve,[e[21]||(e[21]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tipologia Progetto ",-1)),u(a("select",{"onUpdate:modelValue":e[7]||(e[7]=t=>r.value.project_type=t),required:"",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"},[e[20]||(e[20]=a("option",{value:""},"Seleziona tipologia",-1)),(i(!0),s(T,null,z(h.value,t=>(i(),s("option",{key:t.key,value:t.key},l(t.name),9,ge))),128))],512),[[f,r.value.project_type]])]),a("div",ye,[e[23]||(e[23]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," KPI ",-1)),u(a("select",{"onUpdate:modelValue":e[8]||(e[8]=t=>r.value.kpi_name=t),required:"",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"},e[22]||(e[22]=[P('<option value="">Seleziona KPI</option><option value="margin_percentage">Margine Netto</option><option value="utilization_rate">Utilization Rate</option><option value="cost_per_hour">Costo per Ora</option><option value="cost_revenue_ratio">Rapporto C/R</option>',5)]),512),[[f,r.value.kpi_name]])]),a("div",ce,[a("div",null,[e[24]||(e[24]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Target Min ",-1)),u(a("input",{"onUpdate:modelValue":e[9]||(e[9]=t=>r.value.target_min=t),type:"number",step:"0.01",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"},null,512),[[k,r.value.target_min]])]),a("div",null,[e[25]||(e[25]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Target Max ",-1)),u(a("input",{"onUpdate:modelValue":e[10]||(e[10]=t=>r.value.target_max=t),type:"number",step:"0.01",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"},null,512),[[k,r.value.target_max]])])]),a("div",xe,[e[26]||(e[26]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Soglia Warning ",-1)),u(a("input",{"onUpdate:modelValue":e[11]||(e[11]=t=>r.value.warning_threshold=t),type:"number",step:"0.01",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"},null,512),[[k,r.value.warning_threshold]])]),a("div",fe,[e[28]||(e[28]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Unità ",-1)),u(a("select",{"onUpdate:modelValue":e[12]||(e[12]=t=>r.value.unit=t),class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"},e[27]||(e[27]=[a("option",{value:"%"},"%",-1),a("option",{value:"€"},"€",-1),a("option",{value:"ratio"},"ratio",-1),a("option",{value:"giorni"},"giorni",-1)]),512),[[f,r.value.unit]])]),a("div",be,[e[29]||(e[29]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Descrizione ",-1)),u(a("textarea",{"onUpdate:modelValue":e[13]||(e[13]=t=>r.value.description=t),rows:"3",class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"},null,512),[[k,r.value.description]])]),a("div",ke,[a("button",{type:"button",onClick:C,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),a("button",_e,l(y.value?"Aggiorna":"Crea"),1)])],32)])])])):d("",!0)]))}};export{Te as default};
