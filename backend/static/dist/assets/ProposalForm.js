import{r as b,c as z,u as te,x as oe,b as r,e as S,l as n,j as t,k as L,t as d,F as C,p as D,B as c,H as w,I as h,C as y,f as ae,s as P,A as W,q as le,o as s,h as H}from"./vendor.js";import{u as se}from"./crm.js";import{H as V,d as re}from"./app.js";import{_ as ie}from"./PageHeader.js";const ne={class:"proposal-form"},de={key:0},ue=["disabled"],pe={key:0,class:"bg-purple-50 dark:bg-purple-900/20 rounded-lg shadow border border-purple-200 dark:border-purple-700 mb-6"},me={class:"px-6 py-4 border-b border-purple-200 dark:border-purple-700"},ve={class:"flex items-start justify-between"},ce={class:"flex items-start space-x-3"},ge={class:"flex-shrink-0"},be={class:"flex-1"},xe={class:"text-xs text-purple-700 dark:text-purple-300 mt-1"},fe={class:"px-6 py-4 space-y-4 max-h-96 overflow-y-auto"},ye={class:"text-sm text-purple-700 dark:text-purple-300 font-medium"},ke={class:"text-sm text-purple-700 dark:text-purple-300 bg-white dark:bg-gray-800 p-3 rounded border max-h-32 overflow-y-auto"},_e={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},we={key:0},he={class:"text-sm text-purple-700 dark:text-purple-300 font-semibold"},Ae={key:1},Ie={class:"text-sm text-purple-700 dark:text-purple-300 font-semibold"},ze={key:0},Se={class:"text-sm text-purple-700 dark:text-purple-300 list-disc list-inside space-y-1"},Ce={key:1},De={class:"text-sm text-purple-700 dark:text-purple-300 list-disc list-inside space-y-1"},Pe={class:"border-t border-purple-200 dark:border-purple-700 pt-4"},Ve={class:"bg-white rounded-lg shadow p-6"},je={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ne=["value"],Ue={key:0,class:"mt-1 text-sm text-red-600"},qe={class:"md:col-span-2"},Ee={key:0,class:"mt-1 text-sm text-red-600"},Te={class:"flex items-center space-x-4"},Be={class:"flex-1"},Fe={class:"flex-1"},Re={key:0,class:"mt-1 text-sm text-red-600"},$e={class:"mt-6"},Me={key:0,class:"bg-white rounded-lg shadow p-6"},Ge={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},Le={class:"flex justify-end space-x-4"},We=["disabled"],He=["disabled"],Oe={key:0,class:"flex items-center"},Je={key:1},Ke={key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Qe={class:"relative top-10 mx-auto p-6 border max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800"},Xe={class:"mt-3"},Ye={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ze={class:"md:col-span-2"},et={class:"flex justify-end space-x-3 mt-6"},tt=["disabled"],ot={key:2,class:"fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50"},nt={__name:"ProposalForm",setup(at){const A=te(),j=le(),x=se(),{showToast:g}=re(),k=b(!1),I=b(!1),p=b({}),_=b(!1),f=b(!1),i=b(null),u=b({project_type:"",requirements:"",budget_range:"",timeline:""}),o=b({client_id:"",title:"",description:"",value:null,status:"draft",sent_date:"",expiry_date:""}),m=z(()=>A.params.id&&A.params.id!=="new"),N=z(()=>m.value?parseInt(A.params.id):null),O=z(()=>x.clients),J=async()=>{if(m.value)try{I.value=!0;const l=await x.getProposal(N.value);l&&(o.value={client_id:l.client_id||"",title:l.title||"",description:l.description||"",value:l.value||null,status:l.status||"draft",sent_date:l.sent_date||"",expiry_date:l.expiry_date||""})}catch{g("Errore nel caricamento della proposta","error"),j.push("/app/crm/proposals")}finally{I.value=!1}},K=async()=>{x.clients.length===0&&await x.fetchClients()},Q=()=>{if(p.value={},o.value.client_id||(p.value.client_id="Seleziona un cliente"),o.value.title.trim()||(p.value.title="Il titolo della proposta è obbligatorio"),o.value.sent_date&&o.value.expiry_date){const l=new Date(o.value.sent_date),e=new Date(o.value.expiry_date);l>e&&(p.value.dates="La data di invio non può essere successiva alla data di scadenza")}return Object.keys(p.value).length===0},X=()=>{o.value.sent_date||(o.value.sent_date=new Date().toISOString().split("T")[0])},Y=async()=>{o.value.status="draft",await U()},U=async()=>{if(!Q()){g("Correggi gli errori nel form","error");return}try{k.value=!0;const l={client_id:parseInt(o.value.client_id),title:o.value.title,description:o.value.description,value:o.value.value||null,status:o.value.status,sent_date:o.value.sent_date||null,expiry_date:o.value.expiry_date||null};m.value?(await x.updateProposal(N.value,l),g("Proposta aggiornata con successo","success")):(await x.createProposal(l),g("Proposta creata con successo","success")),j.push("/app/crm/proposals")}catch(l){console.error("Error saving proposal:",l),g(m.value?"Errore nell'aggiornamento della proposta":"Errore nella creazione della proposta","error")}finally{k.value=!1}},Z=async()=>{if(!o.value.client_id){g("Seleziona prima un cliente","error");return}try{f.value=!0;const l={client_id:parseInt(o.value.client_id),project_type:u.value.project_type,requirements:u.value.requirements,budget_range:u.value.budget_range,timeline:u.value.timeline},e=await x.generateProposalWithAI(l);console.log("AI Generation Result:",e),i.value=e,_.value=!1,u.value={project_type:"",requirements:"",budget_range:"",timeline:""}}catch(l){console.error("Error generating proposal with AI:",l),g("Errore nella generazione AI","error")}finally{f.value=!1}},ee=()=>{if(!i.value){console.error("No AI content to apply");return}console.log("Applying AI content:",i.value);const l=i.value.generated_proposal,e=i.value.suggested_dates;if(!l){console.error("No generated_proposal in AI content"),g("Errore: contenuto AI non valido","error");return}let v=[];l.title&&(o.value.title=l.title,v.push("titolo")),l.description&&(o.value.description=l.description,v.push("descrizione")),l.value&&(o.value.value=l.value,v.push("valore")),e!=null&&e.sent_date&&(o.value.sent_date=e.sent_date,v.push("data invio")),e!=null&&e.expiry_date&&(o.value.expiry_date=e.expiry_date,v.push("data scadenza")),v.length>0?(g({type:"success",title:"Contenuto AI applicato con successo",message:`I seguenti campi sono stati popolati: ${v.join(", ")}`,duration:6e3}),i.value=null):g({type:"warning",title:"Nessun campo applicabile",message:"Non sono stati trovati dati validi da applicare al form"})};return oe(async()=>{await K();const l=new URLSearchParams(window.location.search),e=l.get("client_id"),v=l.get("ai");e&&(o.value.client_id=e),m.value?await J():v==="true"&&e&&(_.value=!0)}),(l,e)=>{var q,E,T,B,F,R,$,M,G;const v=ae("router-link");return s(),r("div",ne,[S(ie,{title:m.value?"Modifica Proposta":"Nuova Proposta",description:m.value?"Aggiorna i dettagli della proposta":"Crea una nuova proposta commerciale","back-to":"/app/crm/proposals"},{actions:L(()=>[m.value?n("",!0):(s(),r("div",de,[t("button",{onClick:e[0]||(e[0]=a=>_.value=!0),disabled:f.value||!o.value.client_id,type:"button",class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"},[f.value?(s(),H(V,{key:1,name:"arrow-path",class:"animate-spin w-4 h-4 mr-2"})):(s(),H(V,{key:0,name:"sparkles",class:"w-4 h-4 mr-2"})),P(" "+d(f.value?"Generando...":"✨ Genera con AI"),1)],8,ue)]))]),_:1},8,["title","description"]),i.value?(s(),r("div",pe,[t("div",me,[t("div",ve,[t("div",ce,[t("div",ge,[S(V,{name:"sparkles",class:"w-5 h-5 text-purple-600 dark:text-purple-400"})]),t("div",be,[e[18]||(e[18]=t("h4",{class:"text-sm font-medium text-purple-900 dark:text-purple-100"}," Proposta generata con AI ",-1)),t("p",xe,d(i.value.similar_projects_used)+" progetti simili utilizzati come riferimento ",1)])]),t("button",{onClick:ee,class:"inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-purple-600 rounded hover:bg-purple-700"}," Applica al Form ")])]),t("div",fe,[t("div",null,[e[19]||(e[19]=t("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"},"Titolo generato:",-1)),t("p",ye,d((q=i.value.generated_proposal)==null?void 0:q.title),1)]),t("div",null,[e[20]||(e[20]=t("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"},"Descrizione generata:",-1)),t("div",ke,d((E=i.value.generated_proposal)==null?void 0:E.description),1)]),t("div",_e,[(T=i.value.generated_proposal)!=null&&T.value?(s(),r("div",we,[e[21]||(e[21]=t("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"},"Valore stimato:",-1)),t("p",he,"€"+d((B=i.value.generated_proposal.value)==null?void 0:B.toLocaleString()),1)])):n("",!0),(F=i.value.generated_proposal)!=null&&F.timeline_days?(s(),r("div",Ae,[e[22]||(e[22]=t("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"},"Timeline stimata:",-1)),t("p",Ie,d(i.value.generated_proposal.timeline_days)+" giorni",1)])):n("",!0)]),($=(R=i.value.generated_proposal)==null?void 0:R.key_deliverables)!=null&&$.length?(s(),r("div",ze,[e[23]||(e[23]=t("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"},"Deliverable chiave:",-1)),t("ul",Se,[(s(!0),r(C,null,D(i.value.generated_proposal.key_deliverables,a=>(s(),r("li",{key:a},d(a),1))),128))])])):n("",!0),(G=(M=i.value.generated_proposal)==null?void 0:M.value_proposition)!=null&&G.length?(s(),r("div",Ce,[e[24]||(e[24]=t("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"},"Punti di forza:",-1)),t("ul",De,[(s(!0),r(C,null,D(i.value.generated_proposal.value_proposition,a=>(s(),r("li",{key:a},d(a),1))),128))])])):n("",!0),t("div",Pe,[t("button",{onClick:e[1]||(e[1]=a=>i.value=null),class:"text-xs text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-200"}," Nascondere suggerimenti AI ")])])])):n("",!0),t("form",{onSubmit:W(U,["prevent"]),class:"space-y-6"},[t("div",Ve,[e[34]||(e[34]=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Informazioni Base",-1)),t("div",je,[t("div",null,[e[26]||(e[26]=t("label",{for:"client_id",class:"block text-sm font-medium text-gray-700 mb-2"}," Cliente * ",-1)),c(t("select",{id:"client_id","onUpdate:modelValue":e[2]||(e[2]=a=>o.value.client_id=a),required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"},[e[25]||(e[25]=t("option",{value:""},"Seleziona cliente",-1)),(s(!0),r(C,null,D(O.value,a=>(s(),r("option",{key:a.id,value:a.id},d(a.name),9,Ne))),128))],512),[[w,o.value.client_id]]),p.value.client_id?(s(),r("p",Ue,d(p.value.client_id),1)):n("",!0)]),t("div",null,[e[28]||(e[28]=t("label",{for:"status",class:"block text-sm font-medium text-gray-700 mb-2"}," Stato ",-1)),c(t("select",{id:"status","onUpdate:modelValue":e[3]||(e[3]=a=>o.value.status=a),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"},e[27]||(e[27]=[h('<option value="draft">Bozza</option><option value="sent">Inviata</option><option value="negotiating">Negoziazione</option><option value="accepted">Accettata</option><option value="rejected">Rifiutata</option>',5)]),512),[[w,o.value.status]])]),t("div",qe,[e[29]||(e[29]=t("label",{for:"title",class:"block text-sm font-medium text-gray-700 mb-2"}," Titolo Proposta * ",-1)),c(t("input",{id:"title","onUpdate:modelValue":e[4]||(e[4]=a=>o.value.title=a),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Es. Sviluppo sito web aziendale"},null,512),[[y,o.value.title]]),p.value.title?(s(),r("p",Ee,d(p.value.title),1)):n("",!0)]),t("div",null,[e[30]||(e[30]=t("label",{for:"value",class:"block text-sm font-medium text-gray-700 mb-2"}," Valore (€) ",-1)),c(t("input",{id:"value","onUpdate:modelValue":e[5]||(e[5]=a=>o.value.value=a),type:"number",step:"0.01",min:"0",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"0.00"},null,512),[[y,o.value.value,void 0,{number:!0}]])]),t("div",null,[t("div",Te,[t("div",Be,[e[31]||(e[31]=t("label",{for:"sent_date",class:"block text-sm font-medium text-gray-700 mb-2"}," Data Invio ",-1)),c(t("input",{id:"sent_date","onUpdate:modelValue":e[6]||(e[6]=a=>o.value.sent_date=a),type:"date",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"},null,512),[[y,o.value.sent_date]])]),t("div",Fe,[e[32]||(e[32]=t("label",{for:"expiry_date",class:"block text-sm font-medium text-gray-700 mb-2"}," Data Scadenza ",-1)),c(t("input",{id:"expiry_date","onUpdate:modelValue":e[7]||(e[7]=a=>o.value.expiry_date=a),type:"date",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"},null,512),[[y,o.value.expiry_date]])])]),p.value.dates?(s(),r("p",Re,d(p.value.dates),1)):n("",!0)])]),t("div",$e,[e[33]||(e[33]=t("label",{for:"description",class:"block text-sm font-medium text-gray-700 mb-2"}," Descrizione ",-1)),c(t("textarea",{id:"description","onUpdate:modelValue":e[8]||(e[8]=a=>o.value.description=a),rows:"6",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Descrivi i dettagli della proposta, deliverable, tempistiche, etc..."},null,512),[[y,o.value.description]])])]),m.value?(s(),r("div",Me,[e[35]||(e[35]=t("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Azioni Rapide",-1)),t("div",Ge,[o.value.status==="draft"?(s(),r("button",{key:0,type:"button",onClick:e[9]||(e[9]=a=>{o.value.status="sent",X()}),class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"}," Invia Proposta ")):n("",!0),["sent","negotiating"].includes(o.value.status)?(s(),r("button",{key:1,type:"button",onClick:e[10]||(e[10]=a=>o.value.status="accepted"),class:"px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700"}," Segna Accettata ")):n("",!0),["sent","negotiating"].includes(o.value.status)?(s(),r("button",{key:2,type:"button",onClick:e[11]||(e[11]=a=>o.value.status="rejected"),class:"px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700"}," Segna Rifiutata ")):n("",!0),o.value.status==="sent"?(s(),r("button",{key:3,type:"button",onClick:e[12]||(e[12]=a=>o.value.status="negotiating"),class:"px-4 py-2 text-sm font-medium text-white bg-yellow-600 rounded-md hover:bg-yellow-700"}," Avvia Negoziazione ")):n("",!0)])])):n("",!0),t("div",Le,[S(v,{to:"/app/crm/proposals",class:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"},{default:L(()=>e[36]||(e[36]=[P(" Annulla ")])),_:1,__:[36]}),m.value?n("",!0):(s(),r("button",{key:0,type:"button",onClick:Y,disabled:k.value,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 disabled:opacity-50"}," Salva Bozza ",8,We)),t("button",{type:"submit",disabled:k.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"},[k.value?(s(),r("span",Oe,e[37]||(e[37]=[t("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),P(" Salvataggio... ")]))):(s(),r("span",Je,d(m.value?"Aggiorna Proposta":"Crea Proposta"),1))],8,He)])],32),_.value?(s(),r("div",Ke,[t("div",Qe,[t("div",Xe,[e[46]||(e[46]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," ✨ Genera Proposta con AI ",-1)),t("form",{onSubmit:W(Z,["prevent"])},[t("div",Ye,[t("div",null,[e[39]||(e[39]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Tipo di Progetto * ",-1)),c(t("select",{"onUpdate:modelValue":e[13]||(e[13]=a=>u.value.project_type=a),required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"},e[38]||(e[38]=[h('<option value="">Seleziona tipo...</option><option value="web_development">Sviluppo Web</option><option value="mobile_app">App Mobile</option><option value="e_commerce">E-commerce</option><option value="consulting">Consulenza</option><option value="design">Design &amp; Branding</option><option value="digital_marketing">Digital Marketing</option><option value="software_development">Sviluppo Software</option><option value="data_analysis">Analisi Dati</option>',9)]),512),[[w,u.value.project_type]])]),t("div",Ze,[e[40]||(e[40]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Requisiti del Progetto * ",-1)),c(t("textarea",{"onUpdate:modelValue":e[14]||(e[14]=a=>u.value.requirements=a),required:"",rows:"8",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white",placeholder:"Descrivi in dettaglio le esigenze, obiettivi, funzionalità richieste, target audience, vincoli tecnici, deliverable desiderati, etc..."},null,512),[[y,u.value.requirements]]),e[41]||(e[41]=t("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Più dettagli fornisci, più precisa sarà la proposta generata dall'AI ",-1))]),t("div",null,[e[43]||(e[43]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Budget Range ",-1)),c(t("select",{"onUpdate:modelValue":e[15]||(e[15]=a=>u.value.budget_range=a),class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"},e[42]||(e[42]=[h('<option value="">Non specificato</option><option value="5k-15k">€5.000 - €15.000</option><option value="15k-30k">€15.000 - €30.000</option><option value="30k-50k">€30.000 - €50.000</option><option value="50k-100k">€50.000 - €100.000</option><option value="100k+">€100.000+</option>',6)]),512),[[w,u.value.budget_range]])]),t("div",null,[e[45]||(e[45]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Timeline Desiderata ",-1)),c(t("select",{"onUpdate:modelValue":e[16]||(e[16]=a=>u.value.timeline=a),class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"},e[44]||(e[44]=[h('<option value="">Non specificata</option><option value="1-2 settimane">1-2 settimane</option><option value="1 mese">1 mese</option><option value="2-3 mesi">2-3 mesi</option><option value="3-6 mesi">3-6 mesi</option><option value="6+ mesi">6+ mesi</option>',6)]),512),[[w,u.value.timeline]])])]),t("div",et,[t("button",{type:"button",onClick:e[17]||(e[17]=a=>_.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500"}," Annulla "),t("button",{type:"submit",disabled:f.value,class:"px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 disabled:opacity-50"},d(f.value?"Generando...":"✨ Genera"),9,tt)])],32)])])])):n("",!0),I.value?(s(),r("div",ot,e[47]||(e[47]=[t("div",{class:"bg-white rounded-lg p-6"},[t("div",{class:"flex items-center"},[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4"}),t("span",{class:"text-lg font-medium"},"Caricamento...")])],-1)]))):n("",!0)])}}};export{nt as default};
