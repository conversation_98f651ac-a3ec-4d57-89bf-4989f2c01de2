import{b as c,e as p,j as e,f as I,B as v,H as _,I as M,C as D,k as z,t as g,F as E,p as N,Q as U,r as y,x as B,o as u,s as A,n as P,l as F}from"./vendor.js";import{_ as j}from"./PageHeader.js";import{_ as q,H as G,c as T,d as L}from"./app.js";import{S as Q}from"./StandardButton.js";const K={name:"ResearchConfig",components:{PageHeader:j,HeroIcon:G,StandardButton:Q},setup(){const{showToast:l}=L(),a=y(!1),m=y(!1),o=y("Software/Technology"),x=y(null),d=y([]),f=y({name:"",industry:"Software/Technology",size:"small",mission:"",vision:""}),i=y({response_style:"executive",detail_level:"strategic",include_action_items:!0,confidence_threshold:.7,max_response_length:1500}),k=async()=>{a.value=!0;try{const t=await T.get("/api/ceo/config");if(t.data.success){const{company:s,research_config:b}=t.data.data;f.value={name:s.name||"",industry:s.industry||"Software/Technology",size:"small",mission:s.mission||"",vision:s.vision||""},o.value=f.value.industry,b.ai_settings&&(i.value={...i.value,...b.ai_settings});const n=t.data.data.user_preferences;n&&(n.ai_settings&&(i.value={...i.value,...n.ai_settings}),n.industry&&(o.value=n.industry)),x.value=b,await r(),n&&n.topics&&n.topics.forEach(w=>{const S=d.value.find(C=>C.id===w.id);S&&(S.enabled=w.enabled)})}else throw new Error("Failed to load config")}catch(t){console.error("Error loading configuration:",t),l("Error loading configuration","error"),x.value={research_categories:{},ai_settings:i.value}}finally{a.value=!1}},r=()=>{var s;if(!((s=x.value)!=null&&s.research_categories)){d.value=[];return}const t=x.value.research_categories[o.value];t&&t.topics?d.value=[...t.topics]:d.value=[]},h=t=>{t.enabled=!t.enabled,l(`${t.title} ${t.enabled?"abilitato":"disabilitato"}`,"success")},R=t=>{const s={high:"bg-red-100 text-red-800",medium:"bg-yellow-100 text-yellow-800",low:"bg-gray-100 text-gray-800"};return s[t]||s.medium},V=async()=>{m.value=!0;try{const t={...f.value,industry:o.value,updated_at:new Date().toISOString()};localStorage.setItem("ceo-company-profile",JSON.stringify(t)),await r(),l("Profilo aziendale salvato con successo","success")}catch(t){console.error("Error saving profile:",t),l("Errore nel salvataggio del profilo","error")}finally{m.value=!1}},O=async()=>{m.value=!0;try{const t={research_preferences:{industry:o.value,topics:d.value,updated_at:new Date().toISOString()}};if((await T.post("/api/ceo/config",t)).data.success)l("Configurazione ricerca salvata con successo","success"),localStorage.setItem("ceo-research-config",JSON.stringify(t.research_preferences));else throw new Error("Server response unsuccessful")}catch(t){console.error("Error saving research config:",t),l("Errore nel salvataggio della configurazione ricerca","error");try{const s={industry:o.value,topics:d.value,updated_at:new Date().toISOString()};localStorage.setItem("ceo-research-config",JSON.stringify(s)),l("Configurazione salvata localmente come fallback","warning")}catch(s){console.error("Fallback save failed:",s)}}finally{m.value=!1}},H=async()=>{m.value=!0;try{if((await T.post("/api/ceo/config",{ai_settings:i.value})).data.success)l("Impostazioni AI salvate con successo","success"),localStorage.setItem("ceo-ai-settings",JSON.stringify(i.value));else throw new Error("Server response unsuccessful")}catch(t){console.error("Error saving AI settings:",t),l("Errore nel salvataggio delle impostazioni AI","error");try{localStorage.setItem("ceo-ai-settings",JSON.stringify(i.value)),l("Impostazioni AI salvate localmente come fallback","warning")}catch(s){console.error("Fallback save failed:",s)}}finally{m.value=!1}},J=()=>{try{const t=localStorage.getItem("ceo-company-profile");if(t){const n=JSON.parse(t);f.value={...f.value,...n},o.value=n.industry||o.value}const s=localStorage.getItem("ceo-ai-settings");s&&(i.value={...i.value,...JSON.parse(s)});const b=localStorage.getItem("ceo-research-config");if(b){const n=JSON.parse(b);n.topics&&n.industry===o.value&&n.topics.forEach(w=>{const S=d.value.find(C=>C.id===w.id);S&&(S.enabled=w.enabled)})}}catch(t){console.error("Error loading saved settings:",t)}};return B(async()=>{await k(),J()}),{loading:a,saving:m,selectedIndustry:o,researchTopics:d,companyProfile:f,aiSettings:i,loadResearchTopics:r,toggleTopic:h,getPriorityClass:R,saveProfile:V,saveResearchConfig:O,saveAISettings:H}}},W={class:"max-w-4xl mx-auto p-6"},X={class:"bg-white rounded-lg shadow-sm p-6 mb-6"},Y={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Z={class:"md:col-span-2"},$={class:"md:col-span-2"},ee={class:"mt-4"},ae={class:"bg-white rounded-lg shadow-sm p-6"},oe={class:"flex items-center justify-between mb-4"},te={class:"text-sm text-gray-500"},re={key:0,class:"text-center py-8"},se={key:1,class:"space-y-4"},ie={class:"flex items-center justify-between mb-3"},ne={class:"font-medium text-gray-900"},le={class:"flex items-center gap-2"},de=["onClick"],ce={class:"text-sm text-gray-600 mb-3"},ge={class:"text-xs text-gray-500"},ue={class:"mt-1 flex flex-wrap gap-1"},me={key:0,class:"inline-block text-gray-400"},fe={key:2,class:"text-center py-8 text-gray-500"},pe={class:"mt-6 p-4 bg-primary-50 dark:bg-primary-900/20 rounded-lg"},ve={class:"flex items-start gap-3"},ye={class:"mt-4 flex justify-end"},be={class:"bg-white rounded-lg shadow-sm p-6 mt-6"},xe={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ke={class:"md:col-span-2"},he={class:"flex items-center"},we={class:"mt-4"};function Se(l,a,m,o,x,d){const f=I("PageHeader"),i=I("StandardButton"),k=I("HeroIcon");return u(),c("div",W,[p(f,{title:"Configurazione Ricerca",subtitle:"Configura gli argomenti di ricerca AI per il tuo settore"}),e("div",X,[a[14]||(a[14]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Profilo Aziendale",-1)),e("div",Y,[e("div",null,[a[9]||(a[9]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Settore",-1)),v(e("select",{"onUpdate:modelValue":a[0]||(a[0]=r=>o.selectedIndustry=r),onChange:a[1]||(a[1]=(...r)=>o.loadResearchTopics&&o.loadResearchTopics(...r)),class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:focus:ring-primary-400"},a[8]||(a[8]=[M('<option value="Software/Technology" data-v-b2cabcfe>Software/Technology</option><option value="Manufacturing" data-v-b2cabcfe>Manufacturing</option><option value="Services" data-v-b2cabcfe>Services</option><option value="Consulting" data-v-b2cabcfe>Consulting</option><option value="Technical Documentation" data-v-b2cabcfe>Technical Documentation</option><option value="Sports Association" data-v-b2cabcfe>Sports Association</option><option value="Cultural Association" data-v-b2cabcfe>Cultural Association</option>',7)]),544),[[_,o.selectedIndustry]])]),e("div",null,[a[11]||(a[11]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Dimensione Aziendale",-1)),v(e("select",{"onUpdate:modelValue":a[2]||(a[2]=r=>o.companyProfile.size=r),class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:focus:ring-primary-400"},a[10]||(a[10]=[e("option",{value:"startup"},"Startup (1-10)",-1),e("option",{value:"small"},"Piccola (11-50)",-1),e("option",{value:"medium"},"Media (51-250)",-1),e("option",{value:"large"},"Grande (250+)",-1)]),512),[[_,o.companyProfile.size]])]),e("div",Z,[a[12]||(a[12]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Mission Aziendale",-1)),v(e("textarea",{"onUpdate:modelValue":a[3]||(a[3]=r=>o.companyProfile.mission=r),rows:"3",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:focus:ring-primary-400",placeholder:"Descrivi la mission e gli obiettivi della tua azienda..."},null,512),[[D,o.companyProfile.mission]])]),e("div",$,[a[13]||(a[13]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Vision Aziendale",-1)),v(e("textarea",{"onUpdate:modelValue":a[4]||(a[4]=r=>o.companyProfile.vision=r),rows:"2",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:focus:ring-primary-400",placeholder:"Descrivi la vision della tua azienda..."},null,512),[[D,o.companyProfile.vision]])])]),e("div",ee,[p(i,{onClick:o.saveProfile,variant:"primary",disabled:o.saving,loading:o.saving},{default:z(()=>[A(g(o.saving?"Salvataggio...":"Salva Profilo"),1)]),_:1},8,["onClick","disabled","loading"])])]),e("div",ae,[e("div",oe,[a[15]||(a[15]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Argomenti di Ricerca Disponibili",-1)),e("span",te,"Settore: "+g(o.selectedIndustry),1)]),o.loading?(u(),c("div",re,[p(k,{name:"arrow-path",size:"lg",class:"mx-auto text-gray-400 animate-spin mb-3"}),a[16]||(a[16]=e("p",{class:"text-gray-500"},"Caricamento configurazione ricerca...",-1))])):o.researchTopics.length>0?(u(),c("div",se,[(u(!0),c(E,null,N(o.researchTopics,r=>(u(),c("div",{key:r.id,class:"border border-gray-200 rounded-lg p-4"},[e("div",ie,[e("h4",ne,g(r.title),1),e("div",le,[e("span",{class:P(["px-2 py-1 text-xs rounded-full",o.getPriorityClass(r.priority)])},g(r.priority.toUpperCase()),3),e("button",{onClick:h=>o.toggleTopic(r),class:P(["relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:ring-offset-2",r.enabled?"bg-primary-600 dark:bg-primary-500":"bg-gray-200 dark:bg-gray-600"])},[e("span",{class:P(["pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out",r.enabled?"translate-x-5":"translate-x-0"])},null,2)],10,de)])]),e("p",ce,g(r.description),1),e("div",ge,[a[17]||(a[17]=e("strong",null,"Query di esempio:",-1)),e("div",ue,[(u(!0),c(E,null,N(r.sample_queries.slice(0,3),h=>(u(),c("span",{key:h,class:"inline-block bg-gray-100 rounded px-2 py-1"},g(h),1))),128)),r.sample_queries.length>3?(u(),c("span",me," +"+g(r.sample_queries.length-3)+" altri ",1)):F("",!0)])])]))),128))])):(u(),c("div",fe,[p(k,{name:"exclamation-triangle",size:"lg",class:"mx-auto mb-3"}),a[18]||(a[18]=e("p",null,"Nessun argomento di ricerca disponibile per questo settore.",-1))])),e("div",pe,[e("div",ve,[p(k,{name:"information-circle",size:"sm",class:"text-primary-600 dark:text-primary-400 mt-0.5"}),a[19]||(a[19]=e("div",null,[e("h4",{class:"font-medium text-primary-900 dark:text-primary-100"},"Configurazione Argomenti di Ricerca"),e("p",{class:"text-sm text-primary-700 dark:text-primary-300 mt-1"}," Gli argomenti di ricerca sono configurati automaticamente in base alla selezione del settore. Attiva/disattiva gli argomenti per personalizzare le capacità del tuo assistente AI. ")],-1))])]),e("div",ye,[p(i,{onClick:o.saveResearchConfig,variant:"secondary",disabled:o.saving,loading:o.saving},{default:z(()=>[A(g(o.saving?"Salvataggio...":"Salva Configurazione Ricerca"),1)]),_:1},8,["onClick","disabled","loading"])])]),e("div",be,[a[25]||(a[25]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Impostazioni Assistente AI",-1)),e("div",xe,[e("div",null,[a[21]||(a[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Stile di Risposta",-1)),v(e("select",{"onUpdate:modelValue":a[5]||(a[5]=r=>o.aiSettings.response_style=r),class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:focus:ring-primary-400"},a[20]||(a[20]=[e("option",{value:"executive"},"Esecutivo (conciso, strategico)",-1),e("option",{value:"detailed"},"Dettagliato (analisi approfondita)",-1),e("option",{value:"conversational"},"Colloquiale (amichevole, informale)",-1)]),512),[[_,o.aiSettings.response_style]])]),e("div",null,[a[23]||(a[23]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Livello di Dettaglio",-1)),v(e("select",{"onUpdate:modelValue":a[6]||(a[6]=r=>o.aiSettings.detail_level=r),class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:focus:ring-primary-400"},a[22]||(a[22]=[e("option",{value:"strategic"},"Panoramica Strategica",-1),e("option",{value:"tactical"},"Dettagli Tattici",-1),e("option",{value:"operational"},"Focus Operativo",-1)]),512),[[_,o.aiSettings.detail_level]])]),e("div",ke,[e("label",he,[v(e("input",{"onUpdate:modelValue":a[7]||(a[7]=r=>o.aiSettings.include_action_items=r),type:"checkbox",class:"rounded border-gray-300 dark:border-gray-600 text-primary-600 dark:text-primary-400 shadow-sm focus:border-primary-300 dark:focus:border-primary-400 focus:ring focus:ring-primary-200 dark:focus:ring-primary-800 focus:ring-opacity-50"},null,512),[[U,o.aiSettings.include_action_items]]),a[24]||(a[24]=e("span",{class:"ml-2 text-sm text-gray-700"},"Includi elementi d'azione nelle risposte",-1))])])]),e("div",we,[p(i,{onClick:o.saveAISettings,variant:"secondary",disabled:o.saving,loading:o.saving},{default:z(()=>[A(g(o.saving?"Salvataggio...":"Salva Impostazioni AI"),1)]),_:1},8,["onClick","disabled","loading"])])])])}const Ae=q(K,[["render",Se],["__scopeId","data-v-b2cabcfe"]]);export{Ae as default};
