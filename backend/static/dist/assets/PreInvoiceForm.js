import{r as p,c as V,x as L,u as R,b as d,e as j,j as t,k as N,A as $,l as F,B as m,H,F as S,p as z,C as f,h as C,s as I,t as o,q as O,f as Y,o as l,Q}from"./vendor.js";import{d as J,H as y,c as D}from"./app.js";import{_ as K}from"./PageHeader.js";const W={class:"py-6"},X={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Z={class:"lg:col-span-2"},ee={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},te=["value"],ae={key:0},re={class:"space-y-2 max-h-40 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-3"},se=["value"],oe={class:"ml-2 text-sm text-gray-900 dark:text-white"},ie={class:"text-gray-500 dark:text-gray-400"},le={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ne={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},de={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700"},ue=["disabled"],ce=["disabled"],me={class:"lg:col-span-1"},ge={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 sticky top-6"},pe={key:0,class:"flex items-center justify-center py-8"},ye={key:1,class:"space-y-4"},ve={class:"text-sm text-gray-900 dark:text-white"},_e={class:"text-sm text-gray-900 dark:text-white"},be={key:0},xe={class:"space-y-2"},fe={class:"font-medium text-sm text-gray-900 dark:text-white"},ke={class:"text-xs text-gray-500 dark:text-gray-400"},we={class:"text-sm font-medium text-gray-900 dark:text-white"},he={key:1,class:"border-t border-gray-200 dark:border-gray-700 pt-4"},je={class:"space-y-2 text-sm"},Ce={class:"flex justify-between"},Ie={class:"text-gray-900 dark:text-white"},De={class:"flex justify-between"},Pe={class:"text-gray-600 dark:text-gray-400"},Ve={class:"text-gray-900 dark:text-white"},Se={class:"flex justify-between"},ze={class:"text-gray-600 dark:text-gray-400"},Te={class:"text-red-600 dark:text-red-400"},qe={class:"flex justify-between font-bold text-base border-t border-gray-200 dark:border-gray-700 pt-2"},Ee={class:"text-gray-900 dark:text-white"},Ne={key:2,class:"space-y-2 text-sm"},Fe={class:"flex justify-between"},Me={class:"text-gray-900 dark:text-white"},Ue={class:"flex justify-between"},Ae={class:"text-gray-900 dark:text-white"},Be={class:"flex justify-between font-bold"},Ge={class:"text-gray-900 dark:text-white"},Le={key:2,class:"text-center py-8"},Ye={__name:"PreInvoiceForm",setup(Re){const M=O(),U=R(),{showToast:v}=J(),k=p([]),w=p([]),i=p(null),_=p(!1),b=p(!1),a=p({client_id:"",project_ids:[],billing_period_start:"",billing_period_end:"",vat_rate:null,retention_rate:null,notes:""}),A=V(()=>k.value.find(s=>s.id===a.value.client_id)),h=V(()=>a.value.client_id&&a.value.billing_period_start&&a.value.billing_period_end),T=V(()=>h.value&&i.value&&i.value.entries_count>0),c=s=>new Intl.NumberFormat("it-IT",{minimumFractionDigits:0,maximumFractionDigits:0}).format(s||0),q=s=>s?new Date(s).toLocaleDateString("it-IT"):"",B=async()=>{try{const s=await D.get("/api/clients/");s.data.success&&(k.value=s.data.data.clients||[])}catch(s){console.error("Errore nel caricamento clienti:",s),v({type:"error",title:"Errore",message:"Impossibile caricare i clienti",duration:4e3})}},E=async()=>{if(a.value.project_ids=[],w.value=[],i.value=null,!!a.value.client_id){try{const s=await D.get(`/api/projects/?client_id=${a.value.client_id}`);s.data.success&&(w.value=s.data.data.projects||[])}catch(s){console.error("Errore nel caricamento progetti:",s)}h.value&&g()}},g=async()=>{var s;if(h.value){_.value=!0;try{const e={client_id:a.value.client_id,billing_period_start:a.value.billing_period_start,billing_period_end:a.value.billing_period_end};a.value.project_ids.length>0&&(e.project_ids=a.value.project_ids),a.value.vat_rate!==null&&a.value.vat_rate!==""&&(e.vat_rate=a.value.vat_rate),a.value.retention_rate!==null&&a.value.retention_rate!==""&&(e.retention_rate=a.value.retention_rate);const n=await D.post("/api/pre-invoices/preview",e);n.data.success&&(i.value=n.data.data)}catch(e){console.error("Errore nell'anteprima:",e),i.value=null,((s=e.response)==null?void 0:s.status)!==400&&v({type:"error",title:"Errore",message:"Impossibile caricare l'anteprima",duration:4e3})}finally{_.value=!1}}},G=async()=>{var s,e;if(T.value){b.value=!0;try{const n={client_id:a.value.client_id,billing_period_start:a.value.billing_period_start,billing_period_end:a.value.billing_period_end};a.value.project_ids.length>0&&(n.project_ids=a.value.project_ids),a.value.vat_rate!==null&&a.value.vat_rate!==""&&(n.vat_rate=a.value.vat_rate),a.value.retention_rate!==null&&a.value.retention_rate!==""&&(n.retention_rate=a.value.retention_rate),a.value.notes&&(n.notes=a.value.notes);const u=await D.post("/api/pre-invoices/generate",n);u.data.success&&(v({type:"success",title:"Successo",message:u.data.message,duration:4e3}),M.push(`/app/invoicing/pre-invoices/${u.data.data.id}`))}catch(n){console.error("Errore nella generazione:",n),v({type:"error",title:"Errore",message:((e=(s=n.response)==null?void 0:s.data)==null?void 0:e.message)||"Impossibile generare la pre-fattura",duration:4e3})}finally{b.value=!1}}};return L(async()=>{await B();const s=new Date,e=new Date(s.getFullYear(),s.getMonth(),1),n=new Date(s.getFullYear(),s.getMonth()+1,0);a.value.billing_period_start=e.toISOString().split("T")[0],a.value.billing_period_end=n.toISOString().split("T")[0];const u=U.query.client_id;if(u){const r=parseInt(u),x=k.value.find(P=>P.id===r);x&&(a.value.client_id=r,await E(),v({type:"info",title:"Cliente preselezionato",message:`Cliente "${x.name}" già selezionato`,duration:3e3}))}}),(s,e)=>{var u;const n=Y("router-link");return l(),d("div",W,[j(K,{title:"Nuova Pre-fattura",subtitle:"Genera una pre-fattura da timesheet entries fatturabili",icon:"document-text","icon-color":"text-purple-600"},{actions:N(()=>[j(n,{to:"/app/invoicing/pre-invoices",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:N(()=>[j(y,{name:"arrow-left",class:"w-4 h-4 mr-2"}),e[7]||(e[7]=I(" Torna alla lista "))]),_:1,__:[7]})]),_:1}),t("div",X,[t("div",Z,[t("div",ee,[e[19]||(e[19]=t("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-6"}," Dati Pre-fattura ",-1)),t("form",{onSubmit:$(G,["prevent"]),class:"space-y-6"},[t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Cliente * ",-1)),m(t("select",{"onUpdate:modelValue":e[0]||(e[0]=r=>a.value.client_id=r),onChange:E,required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[e[8]||(e[8]=t("option",{value:""},"Seleziona un cliente",-1)),(l(!0),d(S,null,z(k.value,r=>(l(),d("option",{key:r.id,value:r.id},o(r.name),9,te))),128))],544),[[H,a.value.client_id]])]),w.value.length>0?(l(),d("div",ae,[e[10]||(e[10]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Progetti (opzionale - lascia vuoto per tutti) ",-1)),t("div",re,[(l(!0),d(S,null,z(w.value,r=>{var x;return l(),d("label",{key:r.id,class:"flex items-center"},[m(t("input",{type:"checkbox",value:r.id,"onUpdate:modelValue":e[1]||(e[1]=P=>a.value.project_ids=P),class:"rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,8,se),[[Q,a.value.project_ids]]),t("span",oe,[I(o(r.name)+" ",1),t("span",ie," ("+o((x=r.contract)==null?void 0:x.contract_number)+") ",1)])])}),128))])])):F("",!0),t("div",le,[t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data inizio periodo * ",-1)),m(t("input",{"onUpdate:modelValue":e[2]||(e[2]=r=>a.value.billing_period_start=r),onChange:g,type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,544),[[f,a.value.billing_period_start]])]),t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data fine periodo * ",-1)),m(t("input",{"onUpdate:modelValue":e[3]||(e[3]=r=>a.value.billing_period_end=r),onChange:g,type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,544),[[f,a.value.billing_period_end]])])]),t("div",ne,[t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Aliquota IVA (%) ",-1)),m(t("input",{"onUpdate:modelValue":e[4]||(e[4]=r=>a.value.vat_rate=r),onInput:g,type:"number",step:"0.01",min:"0",max:"100",placeholder:"22.0",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,544),[[f,a.value.vat_rate,void 0,{number:!0}]]),e[14]||(e[14]=t("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Lascia vuoto per usare l'aliquota di default (22%) ",-1))]),t("div",null,[e[15]||(e[15]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Ritenuta d'acconto (%) ",-1)),m(t("input",{"onUpdate:modelValue":e[5]||(e[5]=r=>a.value.retention_rate=r),onInput:g,type:"number",step:"0.01",min:"0",max:"100",placeholder:"20.0",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,544),[[f,a.value.retention_rate,void 0,{number:!0}]]),e[16]||(e[16]=t("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Lascia vuoto per usare la ritenuta di default (20%) ",-1))])]),t("div",null,[e[17]||(e[17]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Note (opzionale) ",-1)),m(t("textarea",{"onUpdate:modelValue":e[6]||(e[6]=r=>a.value.notes=r),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Note aggiuntive per la pre-fattura..."},null,512),[[f,a.value.notes]])]),t("div",de,[t("button",{type:"button",onClick:g,disabled:!h.value||_.value,class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[_.value?(l(),C(y,{key:0,name:"arrow-path",class:"animate-spin -ml-1 mr-2 h-4 w-4"})):(l(),C(y,{key:1,name:"eye",class:"w-4 h-4 mr-2"})),e[18]||(e[18]=I(" Anteprima "))],8,ue),t("button",{type:"submit",disabled:!T.value||b.value,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[b.value?(l(),C(y,{key:0,name:"arrow-path",class:"animate-spin -ml-1 mr-2 h-4 w-4"})):(l(),C(y,{key:1,name:"plus",class:"w-4 h-4 mr-2"})),I(" "+o(b.value?"Generazione...":"Genera Pre-fattura"),1)],8,ce)])],32)])]),t("div",me,[t("div",ge,[e[30]||(e[30]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Anteprima Pre-fattura ",-1)),_.value?(l(),d("div",pe,e[20]||(e[20]=[t("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"},null,-1),t("span",{class:"ml-2 text-sm text-gray-600 dark:text-gray-400"},"Caricamento anteprima...",-1)]))):i.value?(l(),d("div",ye,[t("div",null,[e[21]||(e[21]=t("div",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Cliente",-1)),t("div",ve,o(((u=A.value)==null?void 0:u.name)||"Nessun cliente selezionato"),1)]),t("div",null,[e[22]||(e[22]=t("div",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Periodo",-1)),t("div",_e,o(q(a.value.billing_period_start))+" - "+o(q(a.value.billing_period_end)),1)]),i.value.projects_summary&&i.value.projects_summary.length>0?(l(),d("div",be,[e[23]||(e[23]=t("div",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Progetti inclusi",-1)),t("div",xe,[(l(!0),d(S,null,z(i.value.projects_summary,r=>(l(),d("div",{key:r.project.id,class:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md"},[t("div",fe,o(r.project.name),1),t("div",ke,o(r.total_hours)+"h × €"+o(c(r.total_amount/r.total_hours))+"/h ",1),t("div",we," €"+o(c(r.total_amount)),1)]))),128))])])):F("",!0),i.value.fiscal_calculations?(l(),d("div",he,[t("div",je,[t("div",Ce,[e[24]||(e[24]=t("span",{class:"text-gray-600 dark:text-gray-400"},"Subtotale",-1)),t("span",Ie,"€"+o(c(i.value.fiscal_calculations.subtotal)),1)]),t("div",De,[t("span",Pe," IVA ("+o(i.value.fiscal_calculations.vat_rate)+"%) ",1),t("span",Ve,"€"+o(c(i.value.fiscal_calculations.vat_amount)),1)]),t("div",Se,[t("span",ze," Ritenuta ("+o(i.value.fiscal_calculations.retention_rate)+"%) ",1),t("span",Te,"-€"+o(c(i.value.fiscal_calculations.retention_amount)),1)]),t("div",qe,[e[25]||(e[25]=t("span",{class:"text-gray-900 dark:text-white"},"Totale",-1)),t("span",Ee,"€"+o(c(i.value.fiscal_calculations.total_amount)),1)])])])):(l(),d("div",Ne,[t("div",Fe,[e[26]||(e[26]=t("span",{class:"text-gray-600 dark:text-gray-400"},"Ore totali",-1)),t("span",Me,o(i.value.total_hours)+"h",1)]),t("div",Ue,[e[27]||(e[27]=t("span",{class:"text-gray-600 dark:text-gray-400"},"Entries",-1)),t("span",Ae,o(i.value.entries_count),1)]),t("div",Be,[e[28]||(e[28]=t("span",{class:"text-gray-900 dark:text-white"},"Importo base",-1)),t("span",Ge,"€"+o(c(i.value.total_amount)),1)])]))])):(l(),d("div",Le,[j(y,{name:"eye",class:"mx-auto h-8 w-8 text-gray-400"}),e[29]||(e[29]=t("p",{class:"mt-2 text-sm text-gray-500 dark:text-gray-400"}," Seleziona cliente e periodo per vedere l'anteprima ",-1))]))])])])])}}};export{Ye as default};
