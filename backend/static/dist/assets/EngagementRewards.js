import{b as a,j as e,l as c,e as g,f as K,t as i,B as _,H as C,I as T,F as D,p as M,s as h,C as W,n as F,r as y,c as x,x as X,o as r,E as Y}from"./vendor.js";import{u as Z}from"./engagement.js";import{_ as $,H as ee}from"./app.js";const te={name:"EngagementRewards",components:{HeroIcon:ee},setup(){const l=Z(),t=y(null),f=y({reward_type:"",max_cost:"",campaign_id:""}),s=x(()=>l.rewards),P=x(()=>l.campaigns),j=x(()=>{var n;return((n=l.userProfile)==null?void 0:n.available_points)||0}),d=x(()=>{var n;return((n=l.userProfile)==null?void 0:n.total_points_spent)||0}),v=x(()=>{var n;return((n=l.userProfile)==null?void 0:n.redemptions)||[]}),o=x(()=>l.loading),p=y(null),w=y(""),b=y(!1),u=async(n=1)=>{try{const m={page:n.toString(),per_page:"12",...Object.fromEntries(Object.entries(f.value).filter(([G,R])=>R))},k=await l.fetchRewards(m);t.value=k.pagination}catch(m){console.error("Error fetching rewards:",m)}},I=async()=>{try{await l.fetchDashboard()}catch(n){console.error("Error fetching user data:",n)}},N=async()=>{try{await l.fetchCampaigns({status:"active"})}catch(n){console.error("Error fetching campaigns:",n)}},S=()=>{f.value={reward_type:"",max_cost:"",campaign_id:""},u()},z=()=>{var n;(n=t.value)!=null&&n.has_prev&&u(t.value.page-1)},V=()=>{var n;(n=t.value)!=null&&n.has_next&&u(t.value.page+1)},U=()=>{if(!t.value)return"0-0";const n=(t.value.page-1)*t.value.per_page+1,m=Math.min(t.value.page*t.value.per_page,t.value.total);return`${n}-${m}`},B=n=>{p.value=n,w.value=""},E=()=>{p.value=null,w.value="",b.value=!1},H=async()=>{if(!(!p.value||b.value)){b.value=!0;try{await l.redeemReward(p.value.id),E(),await Promise.all([I(),u()])}catch(n){console.error("Error redeeming reward:",n)}finally{b.value=!1}}},L=n=>{window.open(n,"_blank")},O=(n,m="")=>{const k={premium_gadget:"device-phone-mobile",vip_parking:"map-pin",spotify_premium:"musical-note",innovator_badge:"light-bulb",amazon_25:"gift",online_course:"academic-cap",team_lunch:"users",smart_working:"home-modern"};for(const[R,J]of Object.entries(k))if(m.toLowerCase().includes(R))return J;return{physical:"cube",digital:"computer-desktop",experience:"star",badge:"shield-check"}[n]||"gift"},A=n=>({physical:"bg-blue-100 text-blue-800",digital:"bg-green-100 text-green-800",experience:"bg-purple-100 text-purple-800",badge:"bg-yellow-100 text-yellow-800"})[n]||"bg-gray-100 text-gray-800",q=n=>({physical:"Fisico",digital:"Digitale",experience:"Esperienza",badge:"Badge"})[n]||n,Q=n=>n?new Date(n).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"";return X(async()=>{await Promise.all([I(),N(),u()])}),{pagination:t,filters:f,selectedReward:p,redemptionNotes:w,redeeming:b,rewards:s,campaigns:P,userPoints:j,totalPointsSpent:d,userRedemptions:v,loading:o,fetchRewards:u,resetFilters:S,previousPage:z,nextPage:V,getItemRange:U,openRedeemModal:B,closeRedeemModal:E,redeemReward:H,openExternalUrl:L,getRewardTypeIcon:O,getRewardTypeClass:A,getRewardTypeLabel:q,formatDate:Q}}},se={class:"engagement-rewards"},oe={class:"bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-lg p-6 text-white mb-6"},ne={class:"flex items-center justify-between"},ie={class:"flex items-center space-x-4"},ae={class:"bg-white bg-opacity-20 rounded-full p-3"},re={class:"text-xl font-bold"},le={class:"text-purple-100"},de={class:"text-right"},me={class:"text-2xl font-bold"},ce={class:"bg-white shadow rounded-lg mb-6 p-6"},ge={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},pe=["value"],ue={class:"flex items-end"},xe={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6"},be={class:"h-48 bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-lg flex items-center justify-center"},ye={class:"p-6"},fe={class:"flex items-start justify-between mb-3"},ve={class:"text-lg font-semibold text-gray-900"},_e={class:"flex flex-col items-end"},he={class:"text-lg font-bold text-primary-600"},we={class:"text-sm text-gray-600 mb-4 line-clamp-3"},ke={class:"space-y-2 mb-4"},Re={class:"flex items-center justify-between text-sm"},Ce={key:0,class:"flex items-center justify-between text-sm"},Pe={class:"text-gray-900 font-medium"},je={key:1,class:"flex items-center justify-between text-sm"},Ie={class:"text-gray-900"},Ee={key:0,class:"mb-4"},Te={class:"flex justify-between text-sm text-gray-600 mb-1"},De={class:"w-full bg-gray-200 rounded-full h-2"},Me={key:1,class:"mb-4 p-2 bg-blue-50 rounded-lg"},Fe={class:"flex items-center text-sm text-blue-800"},Ne={class:"flex space-x-2"},Se=["onClick"],ze={key:1,disabled:"",class:"flex-1 bg-gray-300 text-gray-500 px-4 py-2 rounded-md text-sm font-medium cursor-not-allowed"},Ve={key:2,disabled:"",class:"flex-1 bg-gray-300 text-gray-500 px-4 py-2 rounded-md text-sm font-medium cursor-not-allowed"},Ue={key:3,disabled:"",class:"flex-1 bg-gray-300 text-gray-500 px-4 py-2 rounded-md text-sm font-medium cursor-not-allowed"},Be=["onClick"],He={key:1,class:"text-center py-12"},Le={key:2,class:"bg-white px-6 py-3 flex items-center justify-between border-t border-gray-200 rounded-lg shadow"},Oe={class:"flex-1 flex justify-between sm:hidden"},Ae=["disabled"],qe=["disabled"],Qe={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Ge={class:"text-sm text-gray-700"},Je={class:"font-medium"},Ke={class:"font-medium"},We={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},Xe=["disabled"],Ye={class:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"},Ze=["disabled"],$e={key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},et={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},tt={class:"mt-3"},st={class:"flex items-center justify-between mb-4"},ot={class:"mb-4"},nt={class:"font-medium text-gray-900"},it={class:"text-sm text-gray-600 mt-1"},at={class:"mt-3 p-3 bg-gray-50 rounded-lg"},rt={class:"flex justify-between text-sm"},lt={class:"font-medium"},dt={class:"flex justify-between text-sm"},mt={class:"font-medium"},ct={class:"flex justify-between text-sm border-t border-gray-200 pt-2 mt-2"},gt={class:"font-medium"},pt={class:"mb-4"},ut={class:"flex space-x-3"},xt=["disabled"],bt={key:0},yt={key:1},ft={key:4,class:"fixed inset-0 bg-gray-50 bg-opacity-75 flex items-center justify-center z-50"};function vt(l,t,f,s,P,j){var v;const d=K("HeroIcon");return r(),a("div",se,[t[38]||(t[38]=e("div",{class:"mb-6"},[e("h1",{class:"text-2xl font-bold text-gray-900"},"Catalogo Premi"),e("p",{class:"mt-1 text-sm text-gray-600"}," Riscatta i tuoi punti con premi esclusivi ")],-1)),e("div",oe,[e("div",ne,[e("div",ie,[e("div",ae,[g(d,{name:"gift",class:"w-8 h-8"})]),e("div",null,[e("h2",re,i(s.userPoints)+" Punti Disponibili",1),e("p",le,"Spesi "+i(s.totalPointsSpent)+" punti finora",1)])]),e("div",de,[t[13]||(t[13]=e("div",{class:"text-sm text-purple-100"},"I Tuoi Riscatti",-1)),e("div",me,i(((v=s.userRedemptions)==null?void 0:v.length)||0),1)])])]),e("div",ce,[t[21]||(t[21]=e("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Filtri",-1)),e("div",ge,[e("div",null,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700"},"Tipo Premio",-1)),_(e("select",{"onUpdate:modelValue":t[0]||(t[0]=o=>s.filters.reward_type=o),class:"mt-1 block w-full rounded-md border-gray-300"},t[14]||(t[14]=[T('<option value="" data-v-3c146565>Tutti</option><option value="physical" data-v-3c146565>Fisico</option><option value="digital" data-v-3c146565>Digitale</option><option value="experience" data-v-3c146565>Esperienza</option><option value="badge" data-v-3c146565>Badge</option>',5)]),512),[[C,s.filters.reward_type]])]),e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700"},"Costo Massimo",-1)),_(e("select",{"onUpdate:modelValue":t[1]||(t[1]=o=>s.filters.max_cost=o),class:"mt-1 block w-full rounded-md border-gray-300"},t[16]||(t[16]=[T('<option value="" data-v-3c146565>Qualsiasi</option><option value="50" data-v-3c146565>Fino a 50 punti</option><option value="100" data-v-3c146565>Fino a 100 punti</option><option value="200" data-v-3c146565>Fino a 200 punti</option><option value="500" data-v-3c146565>Fino a 500 punti</option>',5)]),512),[[C,s.filters.max_cost]])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700"},"Campagna",-1)),_(e("select",{"onUpdate:modelValue":t[2]||(t[2]=o=>s.filters.campaign_id=o),class:"mt-1 block w-full rounded-md border-gray-300"},[t[18]||(t[18]=e("option",{value:""},"Tutte",-1)),(r(!0),a(D,null,M(s.campaigns,o=>(r(),a("option",{key:o.id,value:o.id},i(o.name),9,pe))),128))],512),[[C,s.filters.campaign_id]])]),e("div",ue,[e("button",{onClick:t[3]||(t[3]=(...o)=>s.fetchRewards&&s.fetchRewards(...o)),class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium mr-2"},[g(d,{name:"search",class:"w-4 h-4 inline mr-2"}),t[20]||(t[20]=h(" Cerca "))]),e("button",{onClick:t[4]||(t[4]=(...o)=>s.resetFilters&&s.resetFilters(...o)),class:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"}," Reset ")])])]),s.rewards.length>0?(r(),a("div",xe,[(r(!0),a(D,null,M(s.rewards,o=>(r(),a("div",{key:o.id,class:"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow"},[e("div",be,[g(d,{name:s.getRewardTypeIcon(o.reward_type,o.name),class:"w-16 h-16 text-gray-400"},null,8,["name"])]),e("div",ye,[e("div",fe,[e("h3",ve,i(o.name),1),e("div",_e,[e("span",he,i(o.points_cost),1),t[22]||(t[22]=e("span",{class:"text-xs text-gray-500"},"punti",-1))])]),e("p",we,i(o.description),1),e("div",ke,[e("div",Re,[t[23]||(t[23]=e("span",{class:"text-gray-500"},"Tipo:",-1)),e("span",{class:F([s.getRewardTypeClass(o.reward_type),"px-2 py-1 rounded-full text-xs font-medium"])},i(s.getRewardTypeLabel(o.reward_type)),3)]),o.campaign_name?(r(),a("div",Ce,[t[24]||(t[24]=e("span",{class:"text-gray-500"},"Campagna:",-1)),e("span",Pe,i(o.campaign_name),1)])):c("",!0),o.available_until?(r(),a("div",je,[t[25]||(t[25]=e("span",{class:"text-gray-500"},"Disponibile fino:",-1)),e("span",Ie,i(s.formatDate(o.available_until)),1)])):c("",!0)]),o.max_redemptions?(r(),a("div",Ee,[e("div",Te,[t[26]||(t[26]=e("span",null,"Stock:",-1)),e("span",null,i(o.stock_remaining)+"/"+i(o.max_redemptions),1)]),e("div",De,[e("div",{class:"bg-primary-600 h-2 rounded-full transition-all duration-300",style:Y({width:`${(o.max_redemptions-o.stock_remaining)/o.max_redemptions*100}%`})},null,4)])])):c("",!0),o.user_redemptions>0?(r(),a("div",Me,[e("div",Fe,[g(d,{name:"check-circle",class:"w-4 h-4 mr-2"}),e("span",null,"Hai già riscattato "+i(o.user_redemptions||0)+" volta"+i((o.user_redemptions||0)>1?"e":""),1)])])):c("",!0),e("div",Ne,[o.can_redeem?(r(),a("button",{key:0,onClick:p=>s.openRedeemModal(o),class:"flex-1 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Riscatta ",8,Se)):o.is_available?s.userPoints<o.points_cost?(r(),a("button",Ve," Punti Insufficienti ")):(r(),a("button",Ue," Limite Raggiunto ")):(r(),a("button",ze," Non Disponibile ")),o.external_url?(r(),a("button",{key:4,onClick:p=>s.openExternalUrl(o.external_url),class:"px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"},[g(d,{name:"arrow-top-right-on-square",class:"w-4 h-4"})],8,Be)):c("",!0)])])]))),128))])):s.loading?c("",!0):(r(),a("div",He,[g(d,{name:"gift",class:"mx-auto h-12 w-12 text-gray-400"}),t[27]||(t[27]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"Nessun premio trovato",-1)),t[28]||(t[28]=e("p",{class:"mt-1 text-sm text-gray-500"}," Non ci sono premi che corrispondono ai filtri selezionati. ",-1))])),s.pagination&&s.pagination.pages>1?(r(),a("div",Le,[e("div",Oe,[e("button",{onClick:t[5]||(t[5]=(...o)=>s.previousPage&&s.previousPage(...o)),disabled:!s.pagination.has_prev,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Precedente ",8,Ae),e("button",{onClick:t[6]||(t[6]=(...o)=>s.nextPage&&s.nextPage(...o)),disabled:!s.pagination.has_next,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Successivo ",8,qe)]),e("div",Qe,[e("div",null,[e("p",Ge,[t[29]||(t[29]=h(" Mostrando ")),e("span",Je,i(s.getItemRange()),1),t[30]||(t[30]=h(" di ")),e("span",Ke,i(s.pagination.total),1),t[31]||(t[31]=h(" premi "))])]),e("div",null,[e("nav",We,[e("button",{onClick:t[7]||(t[7]=(...o)=>s.previousPage&&s.previousPage(...o)),disabled:!s.pagination.has_prev,class:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"},[g(d,{name:"chevron-left",class:"h-5 w-5"})],8,Xe),e("span",Ye,i(s.pagination.page)+" / "+i(s.pagination.pages),1),e("button",{onClick:t[8]||(t[8]=(...o)=>s.nextPage&&s.nextPage(...o)),disabled:!s.pagination.has_next,class:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"},[g(d,{name:"chevron-right",class:"h-5 w-5"})],8,Ze)])])])])):c("",!0),s.selectedReward?(r(),a("div",$e,[e("div",et,[e("div",tt,[e("div",st,[t[32]||(t[32]=e("h3",{class:"text-lg font-medium text-gray-900"},"Riscatta Premio",-1)),e("button",{onClick:t[9]||(t[9]=(...o)=>s.closeRedeemModal&&s.closeRedeemModal(...o)),class:"text-gray-400 hover:text-gray-600"},[g(d,{name:"x-mark",class:"w-5 h-5"})])]),e("div",ot,[e("h4",nt,i(s.selectedReward.name),1),e("p",it,i(s.selectedReward.description),1),e("div",at,[e("div",rt,[t[33]||(t[33]=e("span",null,"Costo:",-1)),e("span",lt,i(s.selectedReward.points_cost)+" punti",1)]),e("div",dt,[t[34]||(t[34]=e("span",null,"Punti disponibili:",-1)),e("span",mt,i(s.userPoints)+" punti",1)]),e("div",ct,[t[35]||(t[35]=e("span",null,"Punti dopo riscatto:",-1)),e("span",gt,i(s.userPoints-s.selectedReward.points_cost)+" punti",1)])])]),e("div",pt,[t[36]||(t[36]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Note (opzionale) ",-1)),_(e("textarea",{"onUpdate:modelValue":t[10]||(t[10]=o=>s.redemptionNotes=o),rows:"3",class:"block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Aggiungi note per il riscatto..."},null,512),[[W,s.redemptionNotes]])]),e("div",ut,[e("button",{onClick:t[11]||(t[11]=(...o)=>s.redeemReward&&s.redeemReward(...o)),disabled:s.redeeming,class:F(["flex-1 px-4 py-2 rounded-md text-sm font-medium",s.redeeming?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-primary-600 text-white hover:bg-primary-700"])},[s.redeeming?(r(),a("span",bt,"Riscattando...")):(r(),a("span",yt,"Conferma Riscatto"))],10,xt),e("button",{onClick:t[12]||(t[12]=(...o)=>s.closeRedeemModal&&s.closeRedeemModal(...o)),class:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"}," Annulla ")])])])])):c("",!0),s.loading?(r(),a("div",ft,t[37]||(t[37]=[e("div",{class:"bg-white p-6 rounded-lg shadow-lg flex items-center space-x-3"},[e("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"}),e("span",{class:"text-gray-900"},"Caricamento premi...")],-1)]))):c("",!0)])}const kt=$(te,[["render",vt],["__scopeId","data-v-3c146565"]]);export{kt as default};
