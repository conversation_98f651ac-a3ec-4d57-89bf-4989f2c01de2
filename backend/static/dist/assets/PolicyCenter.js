import{c as C,r as F,w as K,b as y,l as p,j as e,t as u,e as g,A as Q,B as x,C as w,H as D,I as M,s as V,o as m,h as U,k as O,n as z,f as T,F as W,p as Y,x as Z}from"./vendor.js";import{H as S,_ as ee}from"./app.js";import{u as R}from"./governance.js";import{_ as te}from"./BaseModal.js";import{S as oe}from"./StandardButton.js";const re={key:0,class:"fixed inset-0 bg-gray-50 bg-opacity-75 flex items-center justify-center z-50"},se={class:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-screen overflow-y-auto m-4"},ae={class:"px-6 py-4 border-b border-gray-200"},ie={class:"flex items-center justify-between"},le={class:"text-lg font-medium text-gray-900"},ne={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},de={class:"md:col-span-2"},ce={class:"md:col-span-2"},ue={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},me={class:"space-y-4"},ye={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},pe={class:"flex justify-end space-x-3 pt-4 border-t border-gray-200"},xe=["disabled"],ge={key:0,class:"flex items-center"},ve={key:1},fe={__name:"PolicyFormModal",props:{isOpen:{type:Boolean,default:!1},policy:{type:Object,default:null}},emits:["close","saved"],setup(i,{emit:r}){const f=i,s=r,k=R(),_=C(()=>k.loading),l=F({name:"",description:"",policy_type:"",framework:"",version:"1.0",status:"draft",content:"",compliance_requirements:"",violation_penalties:"",effective_date:"",expiry_date:"",next_review_date:"",owner:"",approver:""}),h=C(()=>{var n;return!!((n=f.policy)!=null&&n.id)});K(()=>f.isOpen,n=>{n&&(b(),f.policy&&o(f.policy))});const b=()=>{l.value={name:"",description:"",policy_type:"",framework:"",version:"1.0",status:"draft",content:"",compliance_requirements:"",violation_penalties:"",effective_date:"",expiry_date:"",next_review_date:"",owner:"",approver:""}},o=n=>{l.value={name:n.name||"",description:n.description||"",policy_type:n.policy_type||"",framework:n.framework||"",version:n.version||"1.0",status:n.status||"draft",content:n.content||"",compliance_requirements:n.compliance_requirements||"",violation_penalties:n.violation_penalties||"",effective_date:n.effective_date?n.effective_date.split("T")[0]:"",expiry_date:n.expiry_date?n.expiry_date.split("T")[0]:"",next_review_date:n.next_review_date?n.next_review_date.split("T")[0]:"",owner:n.owner||"",approver:n.approver||""}},a=async()=>{try{const n={...l.value};["framework","expiry_date","next_review_date","owner","approver","compliance_requirements","violation_penalties"].forEach(A=>{n[A]===""&&(n[A]=null)});let d;h.value?d=await k.updatePolicy(f.policy.id,n):d=await k.createPolicy(n),d&&(s("saved",d),v())}catch(n){console.error("Error saving policy:",n)}},v=()=>{s("close")};return(n,t)=>i.isOpen?(m(),y("div",re,[e("div",se,[e("div",ae,[e("div",ie,[e("h3",le,u(h.value?"Modifica Policy":"Nuova Policy"),1),e("button",{onClick:v,class:"text-gray-400 hover:text-gray-600"},[g(S,{name:"x-mark",size:"md"})])])]),e("form",{onSubmit:Q(a,["prevent"]),class:"p-6 space-y-6"},[e("div",ne,[e("div",de,[t[14]||(t[14]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Nome Policy * ",-1)),x(e("input",{"onUpdate:modelValue":t[0]||(t[0]=d=>l.value.name=d),type:"text",required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Es. Politica di Sicurezza Informatica"},null,512),[[w,l.value.name]])]),e("div",ce,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Descrizione * ",-1)),x(e("textarea",{"onUpdate:modelValue":t[1]||(t[1]=d=>l.value.description=d),rows:"3",required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Descrizione della policy e del suo scopo"},null,512),[[w,l.value.description]])]),e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Tipo Policy * ",-1)),x(e("select",{"onUpdate:modelValue":t[2]||(t[2]=d=>l.value.policy_type=d),required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[16]||(t[16]=[M('<option value="">Seleziona tipo</option><option value="security">Sicurezza</option><option value="privacy">Privacy</option><option value="operational">Operativa</option><option value="hr">Risorse Umane</option><option value="financial">Finanziaria</option>',6)]),512),[[D,l.value.policy_type]])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Framework di Riferimento ",-1)),x(e("select",{"onUpdate:modelValue":t[3]||(t[3]=d=>l.value.framework=d),class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[18]||(t[18]=[M('<option value="">Nessun framework</option><option value="GDPR">GDPR</option><option value="ISO27001">ISO 27001</option><option value="SOX">SOX</option><option value="HIPAA">HIPAA</option><option value="ISO9001">ISO 9001</option><option value="COBIT">COBIT</option>',7)]),512),[[D,l.value.framework]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Versione * ",-1)),x(e("input",{"onUpdate:modelValue":t[4]||(t[4]=d=>l.value.version=d),type:"text",required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"1.0"},null,512),[[w,l.value.version]])]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Status * ",-1)),x(e("select",{"onUpdate:modelValue":t[5]||(t[5]=d=>l.value.status=d),required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[21]||(t[21]=[e("option",{value:"draft"},"Bozza",-1),e("option",{value:"review"},"In Revisione",-1),e("option",{value:"active"},"Attiva",-1),e("option",{value:"expired"},"Scaduta",-1)]),512),[[D,l.value.status]])])]),e("div",ue,[e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Data Effettiva * ",-1)),x(e("input",{"onUpdate:modelValue":t[6]||(t[6]=d=>l.value.effective_date=d),type:"date",required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[w,l.value.effective_date]])]),e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Data Scadenza ",-1)),x(e("input",{"onUpdate:modelValue":t[7]||(t[7]=d=>l.value.expiry_date=d),type:"date",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[w,l.value.expiry_date]])]),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Prossima Revisione ",-1)),x(e("input",{"onUpdate:modelValue":t[8]||(t[8]=d=>l.value.next_review_date=d),type:"date",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[w,l.value.next_review_date]])])]),e("div",me,[e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Contenuto Policy * ",-1)),x(e("textarea",{"onUpdate:modelValue":t[9]||(t[9]=d=>l.value.content=d),rows:"8",required:"",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Contenuto dettagliato della policy, procedure e requisiti..."},null,512),[[w,l.value.content]])]),e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Requisiti di Conformità ",-1)),x(e("textarea",{"onUpdate:modelValue":t[10]||(t[10]=d=>l.value.compliance_requirements=d),rows:"4",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Requisiti specifici per essere in conformità con questa policy..."},null,512),[[w,l.value.compliance_requirements]])]),e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Sanzioni per Violazione ",-1)),x(e("textarea",{"onUpdate:modelValue":t[11]||(t[11]=d=>l.value.violation_penalties=d),rows:"3",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Descrizione delle sanzioni in caso di violazione..."},null,512),[[w,l.value.violation_penalties]])])]),e("div",ye,[e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Proprietario Policy ",-1)),x(e("input",{"onUpdate:modelValue":t[12]||(t[12]=d=>l.value.owner=d),type:"text",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Nome del responsabile"},null,512),[[w,l.value.owner]])]),e("div",null,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Approvatore ",-1)),x(e("input",{"onUpdate:modelValue":t[13]||(t[13]=d=>l.value.approver=d),type:"text",class:"w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Nome dell'approvatore"},null,512),[[w,l.value.approver]])])]),e("div",pe,[e("button",{type:"button",onClick:v,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"}," Annulla "),e("button",{type:"submit",disabled:_.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[_.value?(m(),y("div",ge,t[31]||(t[31]=[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),V(" Salvando... ")]))):(m(),y("span",ve,u(h.value?"Aggiorna Policy":"Crea Policy"),1))],8,xe)])],32)])])):p("",!0)}},be={key:0,class:"space-y-6"},we={class:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4"},ke={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},_e={class:"text-sm text-gray-900 dark:text-gray-100"},Pe={class:"space-y-4"},he={key:0},Ce={class:"text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded-md"},Se={key:1},ze={class:"flex items-center"},De={class:"text-sm text-gray-900 dark:text-gray-100"},Fe={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Me={key:0},Ve={class:"flex items-center"},Ae={class:"text-sm text-gray-900 dark:text-gray-100"},Ie={key:1},Oe={class:"flex items-center"},Te={key:2},Ue={class:"flex items-center"},Ne={class:"text-sm text-gray-900 dark:text-gray-100"},Re={key:0},qe={class:"bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4 max-h-60 overflow-y-auto"},Be={class:"text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap"},$e={key:1},Ee={class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"},je={class:"text-sm text-blue-700 dark:text-blue-300 whitespace-pre-wrap"},He={key:2},Le={class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},Ge={class:"text-sm text-red-700 dark:text-red-300 whitespace-pre-wrap"},Xe={key:3,class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Je={key:0},Ke={class:"flex items-center"},Qe={class:"text-sm text-gray-900 dark:text-gray-100"},We={key:1},Ye={class:"flex items-center"},Ze={class:"text-sm text-gray-900 dark:text-gray-100"},et={class:"pt-4 border-t border-gray-200 dark:border-gray-700"},tt={class:"grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-500 dark:text-gray-400"},ot={key:0},rt={key:1},st={__name:"PolicyDetailModal",props:{isOpen:{type:Boolean,default:!1},policy:{type:Object,default:null}},emits:["close"],setup(i,{emit:r}){const f=o=>o?{security:"Sicurezza",privacy:"Privacy",operational:"Operativa",hr:"Risorse Umane",financial:"Finanziaria"}[o]||o:"N/A",s=o=>({security:"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-200",privacy:"bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-200",operational:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-200",hr:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200",financial:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200"})[o]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-200",k=o=>o?{active:"Attiva",draft:"Bozza",review:"In Revisione",expired:"Scaduta"}[o]||o:"N/A",_=o=>({active:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200",draft:"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-200",review:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200",expired:"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-200"})[o]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-200",l=o=>{const a=new Date,v=new Date(o),n=Math.ceil((v-a)/(1e3*60*60*24));return n<0?"text-red-600 dark:text-red-400 font-medium":n<=30?"text-orange-600 dark:text-orange-400 font-medium":"text-gray-900 dark:text-gray-100"},h=o=>{const a=new Date,v=new Date(o),n=Math.ceil((v-a)/(1e3*60*60*24));return n<0?"text-red-600 dark:text-red-400 mr-2":n<=30?"text-orange-600 dark:text-orange-400 mr-2":"text-gray-600 dark:text-gray-400 mr-2"},b=o=>{if(!o)return"-";try{const a=new Date(o);return isNaN(a.getTime())?"-":a.toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"})}catch{return"-"}};return(o,a)=>{var v;return m(),U(te,{"is-open":i.isOpen,title:((v=i.policy)==null?void 0:v.name)||"Dettaglio Policy",size:"xl",onClose:a[1]||(a[1]=n=>o.$emit("close"))},{actions:O(()=>[g(oe,{variant:"secondary",onClick:a[0]||(a[0]=n=>o.$emit("close"))},{default:O(()=>a[17]||(a[17]=[V(" Chiudi ")])),_:1,__:[17]})]),default:O(()=>[i.policy?(m(),y("div",be,[e("div",we,[e("div",ke,[e("div",null,[a[2]||(a[2]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Policy ",-1)),e("span",{class:z(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s(i.policy.policy_type)])},u(f(i.policy.policy_type)),3)]),e("div",null,[a[3]||(a[3]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Status ",-1)),e("span",{class:z(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",_(i.policy.status)])},u(k(i.policy.status)),3)]),e("div",null,[a[4]||(a[4]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Versione ",-1)),e("span",_e," v"+u(i.policy.version||"1.0"),1)])])]),e("div",Pe,[i.policy.description?(m(),y("div",he,[a[5]||(a[5]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Descrizione ",-1)),e("p",Ce,u(i.policy.description),1)])):p("",!0),i.policy.framework?(m(),y("div",Se,[a[6]||(a[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Framework di Riferimento ",-1)),e("div",ze,[g(S,{name:"shield-check",size:"sm",class:"text-blue-600 dark:text-blue-400 mr-2"}),e("span",De,u(i.policy.framework),1)])])):p("",!0)]),e("div",Fe,[i.policy.effective_date?(m(),y("div",Me,[a[7]||(a[7]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data Effettiva ",-1)),e("div",Ve,[g(S,{name:"calendar",size:"sm",class:"text-green-600 dark:text-green-400 mr-2"}),e("span",Ae,u(b(i.policy.effective_date)),1)])])):p("",!0),i.policy.expiry_date?(m(),y("div",Ie,[a[8]||(a[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data Scadenza ",-1)),e("div",Oe,[g(S,{name:"calendar",size:"sm",class:z(h(i.policy.expiry_date))},null,8,["class"]),e("span",{class:z(["text-sm ml-2",l(i.policy.expiry_date)])},u(b(i.policy.expiry_date)),3)])])):p("",!0),i.policy.next_review_date?(m(),y("div",Te,[a[9]||(a[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Prossima Revisione ",-1)),e("div",Ue,[g(S,{name:"calendar",size:"sm",class:"text-orange-600 dark:text-orange-400 mr-2"}),e("span",Ne,u(b(i.policy.next_review_date)),1)])])):p("",!0)]),i.policy.content?(m(),y("div",Re,[a[10]||(a[10]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Contenuto Policy ",-1)),e("div",qe,[e("pre",Be,u(i.policy.content),1)])])):p("",!0),i.policy.compliance_requirements?(m(),y("div",$e,[a[11]||(a[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Requisiti di Conformità ",-1)),e("div",Ee,[e("pre",je,u(i.policy.compliance_requirements),1)])])):p("",!0),i.policy.violation_penalties?(m(),y("div",He,[a[12]||(a[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Sanzioni per Violazione ",-1)),e("div",Le,[e("pre",Ge,u(i.policy.violation_penalties),1)])])):p("",!0),i.policy.owner||i.policy.approver?(m(),y("div",Xe,[i.policy.owner?(m(),y("div",Je,[a[13]||(a[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Proprietario Policy ",-1)),e("div",Ke,[g(S,{name:"user",size:"sm",class:"text-purple-600 dark:text-purple-400 mr-2"}),e("span",Qe,u(i.policy.owner),1)])])):p("",!0),i.policy.approver?(m(),y("div",We,[a[14]||(a[14]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Approvatore ",-1)),e("div",Ye,[g(S,{name:"check-circle",size:"sm",class:"text-green-600 dark:text-green-400 mr-2"}),e("span",Ze,u(i.policy.approver),1)])])):p("",!0)])):p("",!0),e("div",et,[e("div",tt,[i.policy.created_at?(m(),y("div",ot,[a[15]||(a[15]=e("span",{class:"font-medium"},"Creato:",-1)),V(" "+u(b(i.policy.created_at)),1)])):p("",!0),i.policy.updated_at?(m(),y("div",rt,[a[16]||(a[16]=e("span",{class:"font-medium"},"Aggiornato:",-1)),V(" "+u(b(i.policy.updated_at)),1)])):p("",!0)])])])):p("",!0)]),_:1},8,["is-open","title"])}}},at={name:"PolicyCenter",components:{HeroIcon:S,PolicyFormModal:fe,PolicyDetailModal:st},setup(){const i=R(),r=F(!1),f=F(!1),s=F(null),k=F({policy_type:"",framework:"",status:""}),_=C(()=>i.policies||[]),l=C(()=>i.loading),h=C(()=>i.error),b=C(()=>_.value.filter(c=>c.status==="active").length),o=C(()=>_.value.filter(c=>c.status==="review").length),a=C(()=>_.value.filter(c=>c.status==="expired").length),v=async()=>{try{await i.fetchPolicies()}catch(c){console.error("Error loading policies:",c)}},n=async()=>{try{const c=Object.fromEntries(Object.entries(k.value).filter(([P,I])=>I));await i.fetchPolicies(c)}catch(c){console.error("Error applying filters:",c)}},t=()=>{k.value={policy_type:"",framework:"",status:""},v()},d=()=>{s.value=null,r.value=!0},A=c=>{s.value=c,r.value=!0},q=()=>{r.value=!1,s.value=null},B=()=>{v()},$=c=>{s.value=c,f.value=!0},E=()=>{f.value=!1,s.value=null},j=c=>c?{security:"Sicurezza",privacy:"Privacy",operational:"Operativa",hr:"Risorse Umane",financial:"Finanziaria"}[c]||c:"N/A",H=c=>({security:"bg-red-100 text-red-800",privacy:"bg-purple-100 text-purple-800",operational:"bg-blue-100 text-blue-800",hr:"bg-green-100 text-green-800",financial:"bg-yellow-100 text-yellow-800"})[c]||"bg-gray-100 text-gray-800",L=c=>c?{active:"Attiva",draft:"Bozza",review:"In Revisione",expired:"Scaduta"}[c]||c:"N/A",G=c=>({active:"bg-green-100 text-green-800",draft:"bg-gray-100 text-gray-800",review:"bg-yellow-100 text-yellow-800",expired:"bg-red-100 text-red-800"})[c]||"bg-gray-100 text-gray-800",X=c=>{const P=new Date,I=new Date(c),N=Math.ceil((I-P)/(1e3*60*60*24));return N<0?"text-red-600 font-medium":N<=30?"text-orange-600 font-medium":"text-gray-900"},J=c=>{if(!c)return"-";try{const P=new Date(c);return isNaN(P.getTime())?"-":P.toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"})}catch{return"-"}};return Z(()=>{v()}),{showPolicyModal:r,showDetailModal:f,selectedPolicy:s,filters:k,policies:_,loading:l,error:h,activePolicies:b,reviewPolicies:o,expiredPolicies:a,loadPolicies:v,applyFilters:n,resetFilters:t,openCreatePolicy:d,editPolicy:A,closePolicyModal:q,onPolicySaved:B,viewPolicy:$,closeDetailModal:E,getPolicyTypeLabel:j,getPolicyTypeBadgeClass:H,getStatusLabel:L,getStatusBadgeClass:G,getExpiryClass:X,formatDate:J}}},it={class:"policy-center"},lt={class:"bg-white rounded-lg shadow-sm p-6 mb-6"},nt={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6"},dt={class:"mt-4 lg:mt-0"},ct={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},ut={class:"bg-blue-50 rounded-lg p-4"},mt={class:"flex items-center"},yt={class:"ml-3"},pt={class:"text-2xl font-bold text-blue-900"},xt={class:"bg-yellow-50 rounded-lg p-4"},gt={class:"flex items-center"},vt={class:"ml-3"},ft={class:"text-2xl font-bold text-yellow-900"},bt={class:"bg-red-50 rounded-lg p-4"},wt={class:"flex items-center"},kt={class:"ml-3"},_t={class:"text-2xl font-bold text-red-900"},Pt={class:"bg-green-50 rounded-lg p-4"},ht={class:"flex items-center"},Ct={class:"ml-3"},St={class:"text-2xl font-bold text-green-900"},zt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Dt={class:"flex items-end"},Ft={class:"bg-white rounded-lg shadow-sm"},Mt={key:0,class:"flex justify-center py-12"},Vt={key:1,class:"p-6 text-center text-red-600"},At={key:2,class:"p-12 text-center text-gray-500"},It={key:3},Ot={class:"overflow-x-auto"},Tt={class:"min-w-full divide-y divide-gray-200"},Ut={class:"bg-white divide-y divide-gray-200"},Nt={class:"px-6 py-4"},Rt={class:"text-sm font-medium text-gray-900"},qt={class:"text-sm text-gray-500"},Bt={class:"px-6 py-4 whitespace-nowrap"},$t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Et={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},jt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Ht={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Lt={key:1,class:"text-gray-400"},Gt={class:"px-6 py-4 whitespace-nowrap"},Xt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium"},Jt=["onClick"],Kt=["onClick"];function Qt(i,r,f,s,k,_){const l=T("HeroIcon"),h=T("PolicyFormModal"),b=T("PolicyDetailModal");return m(),y("div",it,[e("div",lt,[e("div",nt,[r[11]||(r[11]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Policy Center"),e("p",{class:"text-gray-600 mt-1"},"Gestione policy e regole di compliance")],-1)),e("div",dt,[e("button",{onClick:r[0]||(r[0]=(...o)=>s.openCreatePolicy&&s.openCreatePolicy(...o)),class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"},[g(l,{name:"plus",class:"h-5 w-5 mr-2"}),r[10]||(r[10]=V(" Nuova Policy "))])])]),e("div",ct,[e("div",ut,[e("div",mt,[g(l,{name:"document-text",class:"h-8 w-8 text-blue-600"}),e("div",yt,[r[12]||(r[12]=e("p",{class:"text-sm text-blue-600 font-medium"},"Policy Attive",-1)),e("p",pt,u(s.activePolicies),1)])])]),e("div",xt,[e("div",gt,[g(l,{name:"clock",class:"h-8 w-8 text-yellow-600"}),e("div",vt,[r[13]||(r[13]=e("p",{class:"text-sm text-yellow-600 font-medium"},"In Revisione",-1)),e("p",ft,u(s.reviewPolicies),1)])])]),e("div",bt,[e("div",wt,[g(l,{name:"exclamation-triangle",class:"h-8 w-8 text-red-600"}),e("div",kt,[r[14]||(r[14]=e("p",{class:"text-sm text-red-600 font-medium"},"Scadute",-1)),e("p",_t,u(s.expiredPolicies),1)])])]),e("div",Pt,[e("div",ht,[g(l,{name:"check-circle",class:"h-8 w-8 text-green-600"}),e("div",Ct,[r[15]||(r[15]=e("p",{class:"text-sm text-green-600 font-medium"},"Totali",-1)),e("p",St,u(s.policies.length),1)])])])]),e("div",zt,[e("div",null,[r[17]||(r[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Tipo Policy",-1)),x(e("select",{"onUpdate:modelValue":r[1]||(r[1]=o=>s.filters.policy_type=o),onChange:r[2]||(r[2]=(...o)=>s.applyFilters&&s.applyFilters(...o)),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},r[16]||(r[16]=[M('<option value="">Tutte</option><option value="security">Sicurezza</option><option value="privacy">Privacy</option><option value="operational">Operativa</option><option value="hr">Risorse Umane</option><option value="financial">Finanziaria</option>',6)]),544),[[D,s.filters.policy_type]])]),e("div",null,[r[19]||(r[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Framework",-1)),x(e("select",{"onUpdate:modelValue":r[3]||(r[3]=o=>s.filters.framework=o),onChange:r[4]||(r[4]=(...o)=>s.applyFilters&&s.applyFilters(...o)),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},r[18]||(r[18]=[M('<option value="">Tutti</option><option value="GDPR">GDPR</option><option value="ISO27001">ISO 27001</option><option value="SOX">SOX</option><option value="HIPAA">HIPAA</option>',5)]),544),[[D,s.filters.framework]])]),e("div",null,[r[21]||(r[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Status",-1)),x(e("select",{"onUpdate:modelValue":r[5]||(r[5]=o=>s.filters.status=o),onChange:r[6]||(r[6]=(...o)=>s.applyFilters&&s.applyFilters(...o)),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},r[20]||(r[20]=[M('<option value="">Tutti</option><option value="active">Attiva</option><option value="draft">Bozza</option><option value="review">In Revisione</option><option value="expired">Scaduta</option>',5)]),544),[[D,s.filters.status]])]),e("div",Dt,[e("button",{onClick:r[7]||(r[7]=(...o)=>s.resetFilters&&s.resetFilters(...o)),class:"w-full bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"}," Reset Filtri ")])])]),e("div",Ft,[s.loading?(m(),y("div",Mt,r[22]||(r[22]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)]))):s.error?(m(),y("div",Vt,[g(l,{name:"exclamation-triangle",class:"h-8 w-8 mx-auto mb-2"}),e("p",null,u(s.error),1),e("button",{onClick:r[8]||(r[8]=(...o)=>s.loadPolicies&&s.loadPolicies(...o)),class:"mt-3 text-blue-600 hover:text-blue-800 font-medium"}," Riprova ")])):s.policies.length===0?(m(),y("div",At,[g(l,{name:"document-text",class:"h-12 w-12 mx-auto mb-4 text-gray-300"}),r[23]||(r[23]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Nessuna policy trovata",-1)),r[24]||(r[24]=e("p",{class:"text-gray-600 mb-6"},"Inizia aggiungendo la prima policy aziendale",-1)),e("button",{onClick:r[9]||(r[9]=(...o)=>s.openCreatePolicy&&s.openCreatePolicy(...o)),class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"}," Crea Prima Policy ")])):(m(),y("div",It,[e("div",Ot,[e("table",Tt,[r[25]||(r[25]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Policy "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Tipo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Framework "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Versione "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Data Effettiva "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Scadenza "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",Ut,[(m(!0),y(W,null,Y(s.policies,o=>(m(),y("tr",{key:o.id,class:"hover:bg-gray-50"},[e("td",Nt,[e("div",Rt,u(o.name||"N/A"),1),e("div",qt,u(o.description||"N/A"),1)]),e("td",Bt,[e("span",{class:z(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s.getPolicyTypeBadgeClass(o.policy_type)])},u(s.getPolicyTypeLabel(o.policy_type)),3)]),e("td",$t,u(o.framework||"-"),1),e("td",Et," v"+u(o.version||"1.0"),1),e("td",jt,u(s.formatDate(o.effective_date)),1),e("td",Ht,[o.expiry_date?(m(),y("span",{key:0,class:z(s.getExpiryClass(o.expiry_date))},u(s.formatDate(o.expiry_date)),3)):(m(),y("span",Lt,"-"))]),e("td",Gt,[e("span",{class:z(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s.getStatusBadgeClass(o.status)])},u(s.getStatusLabel(o.status)),3)]),e("td",Xt,[e("button",{onClick:a=>s.editPolicy(o),class:"text-indigo-600 hover:text-indigo-900 mr-3"},[g(l,{name:"pencil",class:"h-4 w-4"})],8,Jt),e("button",{onClick:a=>s.viewPolicy(o),class:"text-gray-600 hover:text-gray-900"},[g(l,{name:"eye",class:"h-4 w-4"})],8,Kt)])]))),128))])])])]))]),s.showPolicyModal?(m(),U(h,{key:0,"is-open":s.showPolicyModal,policy:s.selectedPolicy,onClose:s.closePolicyModal,onSaved:s.onPolicySaved},null,8,["is-open","policy","onClose","onSaved"])):p("",!0),s.showDetailModal?(m(),U(b,{key:1,"is-open":s.showDetailModal,policy:s.selectedPolicy,onClose:s.closeDetailModal},null,8,["is-open","policy","onClose"])):p("",!0)])}const oo=ee(at,[["render",Qt]]);export{oo as default};
