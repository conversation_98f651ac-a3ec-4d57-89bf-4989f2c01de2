import{u as T}from"./governance.js";import{_ as j,H as A}from"./app.js";import{b as i,j as e,l as H,e as c,f as V,t as l,s as u,F as f,p as w,c as R,r as F,x as P,o as n,n as g}from"./vendor.js";const G={name:"ComplianceDashboard",components:{HeroIcon:A},setup(){const m=T(),s=R(()=>m.dashboardData),p=R(()=>m.loading),t=F(null),v=async()=>{t.value=null;try{await m.fetchDashboard(30)}catch(a){console.error("Error fetching compliance dashboard:",a),t.value="Errore caricamento dashboard compliance"}},k=a=>({low:"bg-green-400",medium:"bg-yellow-400",high:"bg-orange-400",critical:"bg-red-400"})[a]||"bg-gray-400",r=a=>({info:"border-blue-200 bg-blue-50",warning:"border-yellow-200 bg-yellow-50",critical:"border-red-200 bg-red-50",emergency:"border-red-300 bg-red-100"})[a]||"border-gray-200 bg-gray-50",h=a=>({info:"information-circle",warning:"exclamation-triangle",critical:"exclamation-circle",emergency:"x-circle"})[a]||"information-circle",x=a=>({info:"text-blue-400",warning:"text-yellow-400",critical:"text-red-400",emergency:"text-red-600"})[a]||"text-gray-400",_=a=>({info:"bg-blue-100 text-blue-800",warning:"bg-yellow-100 text-yellow-800",critical:"bg-red-100 text-red-800",emergency:"bg-red-200 text-red-900"})[a]||"bg-gray-100 text-gray-800",b=a=>({open:"bg-yellow-100 text-yellow-800",investigating:"bg-blue-100 text-blue-800",resolved:"bg-green-100 text-green-800",false_positive:"bg-gray-100 text-gray-800"})[a]||"bg-gray-100 text-gray-800",y=a=>new Date(a).toLocaleString("it-IT",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return P(()=>{v()}),{error:t,dashboardData:s,loading:p,fetchDashboardData:v,getRiskLevelColor:k,getEventSeverityClass:r,getEventIcon:h,getEventIconColor:x,getSeverityBadgeClass:_,getStatusBadgeClass:b,formatDateTime:y}}},M={class:"governance-compliance-dashboard"},q={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},J={class:"bg-white overflow-hidden shadow rounded-lg"},K={class:"p-5"},O={class:"flex items-center"},Q={class:"flex-shrink-0"},U={class:"ml-5 w-0 flex-1"},W={class:"text-lg font-medium text-gray-900"},X={class:"bg-white overflow-hidden shadow rounded-lg"},Y={class:"p-5"},Z={class:"flex items-center"},$={class:"flex-shrink-0"},ee={class:"ml-5 w-0 flex-1"},te={class:"text-lg font-medium text-gray-900"},se={class:"bg-white overflow-hidden shadow rounded-lg"},oe={class:"p-5"},ae={class:"flex items-center"},ie={class:"flex-shrink-0"},ne={class:"ml-5 w-0 flex-1"},le={class:"text-lg font-medium text-gray-900"},re={class:"bg-white overflow-hidden shadow rounded-lg"},de={class:"p-5"},ce={class:"flex items-center"},ge={class:"flex-shrink-0"},me={class:"ml-5 w-0 flex-1"},he={class:"text-lg font-medium text-gray-900"},xe={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},_e={class:"bg-white shadow rounded-lg p-6"},be={class:"text-lg font-medium text-gray-900 mb-4"},ye={key:0,class:"space-y-3"},ve={class:"text-sm text-gray-600 capitalize"},ue={class:"text-sm font-medium text-gray-900"},fe={key:1,class:"text-sm text-gray-500"},we={class:"bg-white shadow rounded-lg p-6"},pe={class:"text-lg font-medium text-gray-900 mb-4"},ke={key:0,class:"space-y-3"},De={class:"flex items-center"},Ce={class:"text-sm text-gray-600 capitalize"},Ee={class:"text-sm font-medium text-gray-900"},Se={key:1,class:"text-sm text-gray-500"},Ie={class:"bg-white shadow rounded-lg"},Be={class:"px-6 py-4 border-b border-gray-200"},Ne={class:"text-lg font-medium text-gray-900"},ze={class:"p-6"},Le={key:0,class:"space-y-4"},Re={class:"flex-shrink-0"},Te={class:"flex-1 min-w-0"},je={class:"text-sm font-medium text-gray-900"},Ae={class:"text-sm text-gray-600 mt-1"},He={class:"flex items-center mt-2 space-x-2"},Ve={key:1,class:"text-sm text-gray-500 text-center py-8"},Fe={key:0,class:"fixed inset-0 bg-gray-50 bg-opacity-75 flex items-center justify-center z-50"};function Pe(m,s,p,t,v,k){var h,x,_,b,y,a,d,D,C,E,S,I,B,N,z,L;const r=V("HeroIcon");return n(),i("div",M,[s[8]||(s[8]=e("div",{class:"mb-6"},[e("h1",{class:"text-2xl font-bold text-gray-900"},"Compliance Dashboard"),e("p",{class:"mt-1 text-sm text-gray-600"}," Panoramica compliance, audit trail e monitoraggio policy ")],-1)),e("div",q,[e("div",J,[e("div",K,[e("div",O,[e("div",Q,[c(r,{name:"document-text",class:"h-6 w-6 text-blue-400"})]),e("div",U,[e("dl",null,[s[0]||(s[0]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"}," Attività Totali ",-1)),e("dd",W,l(((x=(h=t.dashboardData)==null?void 0:h.metrics)==null?void 0:x.total_activities)||0),1)])])])])]),e("div",X,[e("div",Y,[e("div",Z,[e("div",$,[c(r,{name:"exclamation-triangle",class:"h-6 w-6 text-red-400"})]),e("div",ee,[e("dl",null,[s[1]||(s[1]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"}," Alto Rischio ",-1)),e("dd",te,l(((b=(_=t.dashboardData)==null?void 0:_.metrics)==null?void 0:b.high_risk_activities)||0),1)])])])])]),e("div",se,[e("div",oe,[e("div",ae,[e("div",ie,[c(r,{name:"bell",class:"h-6 w-6 text-yellow-400"})]),e("div",ne,[e("dl",null,[s[2]||(s[2]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"}," Eventi Compliance ",-1)),e("dd",le,l(((a=(y=t.dashboardData)==null?void 0:y.metrics)==null?void 0:a.total_events)||0),1)])])])])]),e("div",re,[e("div",de,[e("div",ce,[e("div",ge,[c(r,{name:"book-open",class:"h-6 w-6 text-green-400"})]),e("div",me,[e("dl",null,[s[3]||(s[3]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"}," Policy Attive ",-1)),e("dd",he,l(((D=(d=t.dashboardData)==null?void 0:d.metrics)==null?void 0:D.active_policies)||0),1)])])])])])]),e("div",xe,[e("div",_e,[e("h3",be,[c(r,{name:"chart-bar",class:"h-5 w-5 inline mr-2"}),s[4]||(s[4]=u(" Breakdown Attività "))]),(S=(E=(C=t.dashboardData)==null?void 0:C.breakdowns)==null?void 0:E.actions)!=null&&S.length?(n(),i("div",ye,[(n(!0),i(f,null,w(t.dashboardData.breakdowns.actions,o=>(n(),i("div",{key:o.type,class:"flex items-center justify-between"},[e("span",ve,l(o.type),1),e("span",ue,l(o.count),1)]))),128))])):(n(),i("div",fe,"Nessun dato disponibile"))]),e("div",we,[e("h3",pe,[c(r,{name:"shield-check",class:"h-5 w-5 inline mr-2"}),s[5]||(s[5]=u(" Distribuzione Livelli Rischio "))]),(N=(B=(I=t.dashboardData)==null?void 0:I.breakdowns)==null?void 0:B.risk_levels)!=null&&N.length?(n(),i("div",ke,[(n(!0),i(f,null,w(t.dashboardData.breakdowns.risk_levels,o=>(n(),i("div",{key:o.level,class:"flex items-center justify-between"},[e("div",De,[e("div",{class:g([t.getRiskLevelColor(o.level),"w-3 h-3 rounded-full mr-2"])},null,2),e("span",Ce,l(o.level),1)]),e("span",Ee,l(o.count),1)]))),128))])):(n(),i("div",Se,"Nessun dato disponibile"))])]),e("div",Ie,[e("div",Be,[e("h3",Ne,[c(r,{name:"bell",class:"h-5 w-5 inline mr-2"}),s[6]||(s[6]=u(" Eventi Recenti "))])]),e("div",ze,[(L=(z=t.dashboardData)==null?void 0:z.recent_events)!=null&&L.length?(n(),i("div",Le,[(n(!0),i(f,null,w(t.dashboardData.recent_events,o=>(n(),i("div",{key:o.id,class:g(["flex items-start space-x-3 p-4 rounded-lg border",t.getEventSeverityClass(o.severity)])},[e("div",Re,[c(r,{name:t.getEventIcon(o.severity),class:g(["h-5 w-5",t.getEventIconColor(o.severity)])},null,8,["name","class"])]),e("div",Te,[e("p",je,l(o.title),1),e("p",Ae,l(t.formatDateTime(o.created_at)),1),e("div",He,[e("span",{class:g(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",t.getSeverityBadgeClass(o.severity)])},l(o.severity),3),e("span",{class:g(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",t.getStatusBadgeClass(o.status)])},l(o.status),3)])])],2))),128))])):(n(),i("div",Ve," Nessun evento recente "))])]),t.loading?(n(),i("div",Fe,s[7]||(s[7]=[e("div",{class:"bg-white p-6 rounded-lg shadow-lg flex items-center space-x-3"},[e("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"}),e("span",{class:"text-gray-900"},"Caricamento dashboard...")],-1)]))):H("",!0)])}const Je=j(G,[["render",Pe],["__scopeId","data-v-0921126c"]]);export{Je as default};
