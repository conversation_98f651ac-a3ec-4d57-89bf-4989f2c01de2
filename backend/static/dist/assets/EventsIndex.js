import{b as d,l as h,o as a,j as e,t as c,A as j,B as x,C as k,H as L,I as be,Q as q,F as I,p as F,s as S,r as D,c as w,x as ye,e as v,h as P,k as T,v as A,q as he,n as V}from"./vendor.js";import{_ as W,e as J,a as we,H as y}from"./app.js";import{u as ke}from"./useFormatters.js";import{_ as De}from"./ListPageTemplate.js";import{S as H}from"./StandardButton.js";import{S as _e}from"./StatusBadge.js";import{E as Ce}from"./EventEditModal.js";import{C as Se}from"./ConfirmationModal.js";import"./formatters.js";import"./Pagination.js";const ze={name:"CreateEventModal",props:{isOpen:{type:Boolean,default:!1},event:{type:Object,default:null}},emits:["close","created","updated"],data(){return{isSubmitting:!1,tagsInput:"",form:{title:"",description:"",category:"",priority:"medium",startDate:"",endDate:"",location:"",maxParticipants:null,requiresRegistration:!0,registrationDeadline:"",isPublic:!0,sendReminders:!0,allowComments:!0,tags:[]}}},computed:{isEditing(){return!!this.event},isFormValid(){const g=this.form.title.trim()&&this.form.description.trim()&&this.form.category&&this.form.startDate&&this.form.location.trim(),t=new Date(this.form.startDate)>new Date&&(!this.form.endDate||new Date(this.form.endDate)>new Date(this.form.startDate)),p=!this.form.requiresRegistration||!this.form.registrationDeadline||new Date(this.form.registrationDeadline)<new Date(this.form.startDate);return g&&t&&p},minDateTime(){const g=new Date;return g.setHours(g.getHours()+1),g.toISOString().slice(0,16)},parsedTags(){return this.tagsInput.trim()?this.tagsInput.split(",").map(g=>g.trim()).filter(g=>g.length>0).slice(0,5):[]}},watch:{isOpen(g){g&&this.initializeForm()},event:{handler(){this.isOpen&&this.initializeForm()},immediate:!0},parsedTags(g){this.form.tags=g}},methods:{initializeForm(){this.isEditing&&this.event?(this.form={title:this.event.title||"",description:this.event.description||"",category:this.event.category||"",priority:this.event.priority||"medium",startDate:this.event.startDate?new Date(this.event.startDate).toISOString().slice(0,16):"",endDate:this.event.endDate?new Date(this.event.endDate).toISOString().slice(0,16):"",location:this.event.location||"",maxParticipants:this.event.maxParticipants||null,requiresRegistration:this.event.requiresRegistration!==void 0?this.event.requiresRegistration:!0,registrationDeadline:this.event.registrationDeadline?new Date(this.event.registrationDeadline).toISOString().slice(0,16):"",isPublic:this.event.isPublic!==void 0?this.event.isPublic:!0,sendReminders:this.event.sendReminders!==void 0?this.event.sendReminders:!0,allowComments:this.event.allowComments!==void 0?this.event.allowComments:!0,tags:this.event.tags||[]},this.tagsInput=this.event.tags?this.event.tags.join(", "):""):(this.form={title:"",description:"",category:"",priority:"medium",startDate:"",endDate:"",location:"",maxParticipants:null,requiresRegistration:!0,registrationDeadline:"",isPublic:!0,sendReminders:!0,allowComments:!0,tags:[]},this.tagsInput="")},async submitEvent(){if(!(!this.isFormValid||this.isSubmitting)){this.isSubmitting=!0;try{const g={...this.form,tags:this.parsedTags,startDate:new Date(this.form.startDate).toISOString(),endDate:this.form.endDate?new Date(this.form.endDate).toISOString():null,registrationDeadline:this.form.registrationDeadline?new Date(this.form.registrationDeadline).toISOString():null},t=J();let p;this.isEditing?(p=await t.updateEvent({id:this.event.id,data:g}),this.$emit("updated",p)):(p=await t.createEvent(g),this.$emit("created",p)),this.closeModal()}catch(g){console.error("Errore durante il salvataggio dell'evento:",g)}finally{this.isSubmitting=!1}}},closeModal(){this.$emit("close"),setTimeout(()=>{this.initializeForm(),this.isSubmitting=!1},300)}}},Ee={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Me={class:"bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto"},Te={class:"flex items-center justify-between p-6 border-b"},Ie={class:"text-lg font-semibold text-gray-900"},Re={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Pe={class:"md:col-span-2"},Ve={class:"text-right text-xs text-gray-500 mt-1"},Fe={class:"text-right text-xs text-gray-500 mt-1"},Oe={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ue=["min"],Ne=["min"],qe={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ae={class:"space-y-3"},je={class:"flex items-center"},Le={class:"flex items-center"},Be={class:"flex items-center"},Ye={class:"flex items-center"},He={key:0},Ge=["min","max"],$e={key:0,class:"flex flex-wrap gap-2 mt-2"},Qe={class:"flex justify-end space-x-3 pt-4 border-t"},We=["disabled"],Je={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"};function Ke(g,t,p,B,n,f){return p.isOpen?(a(),d("div",Ee,[e("div",Me,[e("div",Te,[e("h3",Ie,c(f.isEditing?"Modifica Evento":"Nuovo Evento"),1),e("button",{onClick:t[0]||(t[0]=(...r)=>f.closeModal&&f.closeModal(...r)),class:"text-gray-400 hover:text-gray-600 transition-colors"},t[17]||(t[17]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("form",{onSubmit:t[16]||(t[16]=j((...r)=>f.submitEvent&&f.submitEvent(...r),["prevent"])),class:"p-6 space-y-6"},[e("div",Re,[e("div",Pe,[t[18]||(t[18]=e("label",{for:"title",class:"block text-sm font-medium text-gray-700 mb-2"}," Titolo * ",-1)),x(e("input",{id:"title","onUpdate:modelValue":t[1]||(t[1]=r=>n.form.title=r),type:"text",required:"",maxlength:"200",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Inserisci il titolo dell'evento"},null,512),[[k,n.form.title]]),e("div",Ve,c(n.form.title.length)+"/200 ",1)]),e("div",null,[t[20]||(t[20]=e("label",{for:"category",class:"block text-sm font-medium text-gray-700 mb-2"}," Categoria * ",-1)),x(e("select",{id:"category","onUpdate:modelValue":t[2]||(t[2]=r=>n.form.category=r),required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},t[19]||(t[19]=[be('<option value="" data-v-63cb66d9>Seleziona una categoria</option><option value="meeting" data-v-63cb66d9>Meeting</option><option value="training" data-v-63cb66d9>Formazione</option><option value="social" data-v-63cb66d9>Evento sociale</option><option value="conference" data-v-63cb66d9>Conferenza</option><option value="workshop" data-v-63cb66d9>Workshop</option><option value="team-building" data-v-63cb66d9>Team Building</option><option value="presentation" data-v-63cb66d9>Presentazione</option><option value="celebration" data-v-63cb66d9>Celebrazione</option><option value="other" data-v-63cb66d9>Altro</option>',10)]),512),[[L,n.form.category]])]),e("div",null,[t[22]||(t[22]=e("label",{for:"priority",class:"block text-sm font-medium text-gray-700 mb-2"}," Priorità ",-1)),x(e("select",{id:"priority","onUpdate:modelValue":t[3]||(t[3]=r=>n.form.priority=r),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},t[21]||(t[21]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"urgent"},"Urgente",-1)]),512),[[L,n.form.priority]])])]),e("div",null,[t[23]||(t[23]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 mb-2"}," Descrizione * ",-1)),x(e("textarea",{id:"description","onUpdate:modelValue":t[4]||(t[4]=r=>n.form.description=r),required:"",rows:"4",maxlength:"2000",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical",placeholder:"Descrivi l'evento, l'agenda e le informazioni importanti"},null,512),[[k,n.form.description]]),e("div",Fe,c(n.form.description.length)+"/2000 ",1)]),e("div",Oe,[e("div",null,[t[24]||(t[24]=e("label",{for:"startDate",class:"block text-sm font-medium text-gray-700 mb-2"}," Data e ora inizio * ",-1)),x(e("input",{id:"startDate","onUpdate:modelValue":t[5]||(t[5]=r=>n.form.startDate=r),type:"datetime-local",required:"",min:f.minDateTime,class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,8,Ue),[[k,n.form.startDate]])]),e("div",null,[t[25]||(t[25]=e("label",{for:"endDate",class:"block text-sm font-medium text-gray-700 mb-2"}," Data e ora fine ",-1)),x(e("input",{id:"endDate","onUpdate:modelValue":t[6]||(t[6]=r=>n.form.endDate=r),type:"datetime-local",min:n.form.startDate,class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,8,Ne),[[k,n.form.endDate]])])]),e("div",qe,[e("div",null,[t[26]||(t[26]=e("label",{for:"location",class:"block text-sm font-medium text-gray-700 mb-2"}," Luogo * ",-1)),x(e("input",{id:"location","onUpdate:modelValue":t[7]||(t[7]=r=>n.form.location=r),type:"text",required:"",maxlength:"200",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Sala riunioni, indirizzo, link online, etc."},null,512),[[k,n.form.location]])]),e("div",null,[t[27]||(t[27]=e("label",{for:"maxParticipants",class:"block text-sm font-medium text-gray-700 mb-2"}," Numero massimo partecipanti ",-1)),x(e("input",{id:"maxParticipants","onUpdate:modelValue":t[8]||(t[8]=r=>n.form.maxParticipants=r),type:"number",min:"1",max:"1000",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Lascia vuoto per illimitato"},null,512),[[k,n.form.maxParticipants,void 0,{number:!0}]])])]),e("div",Ae,[t[32]||(t[32]=e("h4",{class:"text-sm font-medium text-gray-700"},"Opzioni evento",-1)),e("div",je,[x(e("input",{id:"requiresRegistration","onUpdate:modelValue":t[9]||(t[9]=r=>n.form.requiresRegistration=r),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[q,n.form.requiresRegistration]]),t[28]||(t[28]=e("label",{for:"requiresRegistration",class:"ml-2 block text-sm text-gray-700"}," Richiede registrazione ",-1))]),e("div",Le,[x(e("input",{id:"isPublic","onUpdate:modelValue":t[10]||(t[10]=r=>n.form.isPublic=r),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[q,n.form.isPublic]]),t[29]||(t[29]=e("label",{for:"isPublic",class:"ml-2 block text-sm text-gray-700"}," Evento pubblico (visibile a tutti) ",-1))]),e("div",Be,[x(e("input",{id:"sendReminders","onUpdate:modelValue":t[11]||(t[11]=r=>n.form.sendReminders=r),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[q,n.form.sendReminders]]),t[30]||(t[30]=e("label",{for:"sendReminders",class:"ml-2 block text-sm text-gray-700"}," Invia promemoria automatici ",-1))]),e("div",Ye,[x(e("input",{id:"allowComments","onUpdate:modelValue":t[12]||(t[12]=r=>n.form.allowComments=r),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[q,n.form.allowComments]]),t[31]||(t[31]=e("label",{for:"allowComments",class:"ml-2 block text-sm text-gray-700"}," Permetti commenti ",-1))])]),n.form.requiresRegistration?(a(),d("div",He,[t[33]||(t[33]=e("label",{for:"registrationDeadline",class:"block text-sm font-medium text-gray-700 mb-2"}," Scadenza registrazione ",-1)),x(e("input",{id:"registrationDeadline","onUpdate:modelValue":t[13]||(t[13]=r=>n.form.registrationDeadline=r),type:"datetime-local",min:f.minDateTime,max:n.form.startDate,class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,8,Ge),[[k,n.form.registrationDeadline]]),t[34]||(t[34]=e("div",{class:"text-xs text-gray-500 mt-1"}," Lascia vuoto per permettere registrazioni fino all'inizio dell'evento ",-1))])):h("",!0),e("div",null,[t[35]||(t[35]=e("label",{for:"tags",class:"block text-sm font-medium text-gray-700 mb-2"}," Tags (opzionale) ",-1)),x(e("input",{id:"tags","onUpdate:modelValue":t[14]||(t[14]=r=>n.tagsInput=r),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Inserisci i tag separati da virgola (es: importante, team, formazione)"},null,512),[[k,n.tagsInput]]),t[36]||(t[36]=e("div",{class:"text-xs text-gray-500 mt-1"}," Separare i tag con virgole. Massimo 5 tag. ",-1)),f.parsedTags.length>0?(a(),d("div",$e,[(a(!0),d(I,null,F(f.parsedTags,r=>(a(),d("span",{key:r,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},c(r),1))),128))])):h("",!0)]),e("div",Qe,[e("button",{type:"button",onClick:t[15]||(t[15]=(...r)=>f.closeModal&&f.closeModal(...r)),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Annulla "),e("button",{type:"submit",disabled:n.isSubmitting||!f.isFormValid,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"},[n.isSubmitting?(a(),d("svg",Je,t[37]||(t[37]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):h("",!0),S(" "+c(n.isSubmitting?"Salvataggio...":f.isEditing?"Aggiorna":"Crea Evento"),1)],8,We)])],32)])])):h("",!0)}const Xe=W(ze,[["render",Ke],["__scopeId","data-v-63cb66d9"]]),Ze={class:"grid grid-cols-1 gap-5 sm:grid-cols-4"},et={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},tt={class:"p-5"},st={class:"flex items-center"},it={class:"flex-shrink-0"},ot={class:"ml-5 w-0 flex-1"},rt={class:"text-lg font-medium text-gray-900 dark:text-white"},nt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},at={class:"p-5"},lt={class:"flex items-center"},dt={class:"flex-shrink-0"},ut={class:"ml-5 w-0 flex-1"},mt={class:"text-lg font-medium text-gray-900 dark:text-white"},ct={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},gt={class:"p-5"},pt={class:"flex items-center"},ft={class:"flex-shrink-0"},vt={class:"ml-5 w-0 flex-1"},xt={class:"text-lg font-medium text-gray-900 dark:text-white"},bt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},yt={class:"p-5"},ht={class:"flex items-center"},wt={class:"flex-shrink-0"},kt={class:"ml-5 w-0 flex-1"},Dt={class:"text-lg font-medium text-gray-900 dark:text-white"},_t={class:"flex items-center justify-between"},Ct={class:"flex space-x-4"},St={class:"flex rounded-md shadow-sm"},zt={key:0,class:"flex justify-center items-center h-64"},Et={key:1,class:"text-center py-12"},Mt={key:2,class:"p-6"},Tt={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"},It=["onClick"],Rt={class:"p-6"},Pt={class:"flex items-start justify-between mb-3"},Vt={class:"text-lg font-semibold text-gray-900 dark:text-white line-clamp-2"},Ft={class:"text-gray-600 dark:text-gray-400 text-sm line-clamp-2 mb-4"},Ot={class:"space-y-2 mb-4"},Ut={class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},Nt={key:0,class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},qt={class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},At={key:0},jt={key:0,class:"mb-4"},Lt={key:0,class:"inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full"},Bt={key:2,class:"inline-flex items-center px-3 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full"},Yt={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-4"},Ht={class:"flex items-center space-x-2"},Gt={class:"flex justify-end space-x-2"},$t={key:3,class:"p-6"},Qt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Wt={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Jt={class:"flex items-center justify-between"},Kt={class:"text-lg font-medium text-gray-900 dark:text-white"},Xt={class:"flex items-center space-x-1"},Zt={class:"p-6"},es={class:"grid grid-cols-7 gap-1 mb-2"},ts={class:"grid grid-cols-7 gap-1"},ss={class:"space-y-1"},is=["onClick","title"],os={__name:"EventsIndex",setup(g){const t=he(),p=J(),B=we(),{formatDate:n}=ke(),f=D(!1),r=D(!1),O=D(!1),_=D(null),U=D(""),N=D(""),z=D("list"),C=D(new Date),K=w(()=>B.hasPermission("PERMISSION_MANAGE_COMMUNICATION")),X=w(()=>B.hasPermission("PERMISSION_MANAGE_COMMUNICATION")),Z=w(()=>p.events.filter(i=>E(i)==="upcoming").length),ee=w(()=>p.events.filter(i=>E(i)==="completed").length),te=w(()=>p.events.reduce((i,s)=>i+(s.registered_count||0),0)),Y=w(()=>{let i=p.events;if(U.value){const s=new Date,l=new Date(s.getFullYear(),s.getMonth(),s.getDate()),u=new Date(l);u.setDate(l.getDate()-l.getDay());const b=new Date(l.getFullYear(),l.getMonth(),1);i=i.filter(o=>{const m=new Date(o.start_time||o.event_time),R=new Date(m.getFullYear(),m.getMonth(),m.getDate());switch(U.value){case"upcoming":return m>s;case"today":return R.getTime()===l.getTime();case"this_week":return m>=u&&m<new Date(u.getTime()+7*24*60*60*1e3);case"this_month":return m>=b&&m.getMonth()===s.getMonth();case"past":return m<s;default:return!0}})}return N.value&&(i=i.filter(s=>s.event_type===N.value)),i}),se=w(()=>C.value.toLocaleDateString("it-IT",{month:"long"})),ie=w(()=>C.value.getFullYear()),oe=w(()=>["Dom","Lun","Mar","Mer","Gio","Ven","Sab"]),re=w(()=>{const i=C.value.getFullYear(),s=C.value.getMonth(),l=new Date(i,s,1),u=new Date(i,s+1,0),b=new Date(l);b.setDate(b.getDate()-l.getDay());const o=new Date(u),m=6-u.getDay();o.setDate(o.getDate()+m);const R=[],M=new Date(b),xe=new Date;for(;M<=o;)R.push({date:new Date(M),isCurrentMonth:M.getMonth()===s,isToday:M.toDateString()===xe.toDateString()}),M.setDate(M.getDate()+1);return R}),E=i=>{const s=new Date,l=new Date(i.start_time||i.event_time);return l<s?"completed":l>s?"upcoming":"ongoing"},ne=i=>{const s=new Date(i.start_time||i.event_time),l=i.end_time?new Date(i.end_time):null,u=s.toLocaleDateString("it-IT",{weekday:"short",year:"numeric",month:"short",day:"numeric"}),b=s.toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit"});if(l&&l.toDateString()===s.toDateString()){const o=l.toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit"});return`${u} ${b} - ${o}`}return`${u} ${b}`},G=i=>p.getUserEventRegistration(i.id)!==null,ae=i=>G(i)?!1:i.max_participants?(i.registered_count||0)<i.max_participants:!0,le=async i=>{var s,l,u;try{await p.registerForEvent(i.id),alert("Registrazione completata con successo!")}catch(b){console.error("Errore nella registrazione all'evento:",b);let o="Errore nella registrazione all'evento";((s=b.response)==null?void 0:s.status)===400?o=((l=b.response.data)==null?void 0:l.message)||"Registrazione non valida":((u=b.response)==null?void 0:u.status)===500?o="Errore del server. Riprova più tardi.":navigator.onLine||(o="Connessione internet non disponibile"),alert(o)}},$=i=>{t.push(`/app/communications/events/${i.id}`)},de=i=>{_.value=i,r.value=!0},ue=i=>{_.value=i,O.value=!0},me=i=>{f.value=!1},ce=i=>{r.value=!1,_.value=null},ge=async()=>{try{await p.deleteEvent(_.value.id),O.value=!1,_.value=null}catch(i){console.error("Errore nell'eliminazione dell'evento:",i)}},Q=i=>{const s=new Date(C.value);s.setMonth(s.getMonth()+i),C.value=s},pe=()=>{C.value=new Date},fe=i=>Y.value.filter(s=>{const l=new Date(s.start_time||s.event_time);return l.getDate()===i.getDate()&&l.getMonth()===i.getMonth()&&l.getFullYear()===i.getFullYear()}),ve=i=>new Date(i.start_time||i.event_time).toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit"});return ye(async()=>{try{await p.fetchEvents()}catch(i){console.error("Errore nel caricamento degli eventi:",i)}}),(i,s)=>{var l;return a(),d(I,null,[v(De,{title:"Eventi Aziendali",subtitle:"Gestione e partecipazione agli eventi aziendali",data:A(p).events,loading:A(p).loading.events,"can-create":K.value,"create-label":"Nuovo Evento","search-placeholder":"Cerca eventi...","empty-message":"Nessun evento programmato","results-label":"eventi",onCreate:s[6]||(s[6]=u=>f.value=!0)},{stats:T(()=>[e("div",Ze,[e("div",et,[e("div",tt,[e("div",st,[e("div",it,[v(y,{name:"calendar-days",size:"lg",class:"text-blue-500"})]),e("div",ot,[e("dl",null,[s[10]||(s[10]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Totali",-1)),e("dd",rt,c(A(p).events.length),1)])])])])]),e("div",nt,[e("div",at,[e("div",lt,[e("div",dt,[v(y,{name:"clock",size:"lg",class:"text-green-500"})]),e("div",ut,[e("dl",null,[s[11]||(s[11]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Prossimi",-1)),e("dd",mt,c(Z.value),1)])])])])]),e("div",ct,[e("div",gt,[e("div",pt,[e("div",ft,[v(y,{name:"check-circle",size:"lg",class:"text-purple-500"})]),e("div",vt,[e("dl",null,[s[12]||(s[12]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Completati",-1)),e("dd",xt,c(ee.value),1)])])])])]),e("div",bt,[e("div",yt,[e("div",ht,[e("div",wt,[v(y,{name:"users",size:"lg",class:"text-yellow-500"})]),e("div",kt,[e("dl",null,[s[13]||(s[13]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Partecipanti",-1)),e("dd",Dt,c(te.value),1)])])])])])])]),filters:T(()=>[e("div",_t,[e("div",Ct,[x(e("select",{"onUpdate:modelValue":s[0]||(s[0]=u=>U.value=u),class:"block w-40 pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},s[14]||(s[14]=[e("option",{value:""},"Tutti i periodi",-1),e("option",{value:"upcoming"},"Prossimi",-1),e("option",{value:"today"},"Oggi",-1),e("option",{value:"this_week"},"Questa settimana",-1),e("option",{value:"this_month"},"Questo mese",-1),e("option",{value:"past"},"Passati",-1)]),512),[[L,U.value]]),x(e("select",{"onUpdate:modelValue":s[1]||(s[1]=u=>N.value=u),class:"block w-32 pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},s[15]||(s[15]=[e("option",{value:""},"Tutti i tipi",-1),e("option",{value:"meeting"},"Riunioni",-1),e("option",{value:"training"},"Formazione",-1),e("option",{value:"social"},"Sociale",-1),e("option",{value:"announcement"},"Annunci",-1)]),512),[[L,N.value]])]),e("div",St,[e("button",{onClick:s[2]||(s[2]=u=>z.value="list"),class:V([z.value==="list"?"bg-primary-600 text-white":"bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600","px-3 py-2 text-sm font-medium border border-gray-300 dark:border-gray-600 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500"])},[v(y,{name:"list-bullet",size:"sm",class:"mr-1"}),s[16]||(s[16]=S(" Lista "))],2),e("button",{onClick:s[3]||(s[3]=u=>z.value="calendar"),class:V([z.value==="calendar"?"bg-primary-600 text-white":"bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600","px-3 py-2 text-sm font-medium border border-l-0 border-gray-300 dark:border-gray-600 rounded-r-md focus:outline-none focus:ring-2 focus:ring-primary-500"])},[v(y,{name:"calendar",size:"sm",class:"mr-1"}),s[17]||(s[17]=S(" Calendario "))],2)])])]),content:T(({data:u,loading:b})=>[b?(a(),d("div",zt,s[18]||(s[18]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"},null,-1)]))):Y.value.length===0?(a(),d("div",Et,[v(y,{name:"calendar-days",size:"2xl",class:"mx-auto text-gray-400 mb-4"}),s[19]||(s[19]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Nessun evento trovato",-1)),s[20]||(s[20]=e("p",{class:"text-gray-500 dark:text-gray-400"},"Non ci sono eventi da visualizzare con i filtri selezionati.",-1))])):z.value==="list"?(a(),d("div",Mt,[e("div",Tt,[(a(!0),d(I,null,F(Y.value,o=>(a(),d("div",{key:o.id,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer",onClick:m=>$(o)},[e("div",Rt,[e("div",Pt,[e("h3",Vt,c(o.title),1),v(_e,{status:E(o),size:"sm"},null,8,["status"])]),e("p",Ft,c(o.description),1),e("div",Ot,[e("div",Ut,[v(y,{name:"calendar",size:"xs",class:"mr-2"}),e("span",null,c(ne(o)),1)]),o.location?(a(),d("div",Nt,[v(y,{name:"map-pin",size:"xs",class:"mr-2"}),e("span",null,c(o.location),1)])):h("",!0),e("div",qt,[v(y,{name:"users",size:"xs",class:"mr-2"}),e("span",null,c(o.registered_count||0)+" partecipanti",1),o.max_participants?(a(),d("span",At," / "+c(o.max_participants),1)):h("",!0)])]),E(o)==="upcoming"?(a(),d("div",jt,[G(o)?(a(),d("div",Lt,[v(y,{name:"check",size:"xs",class:"mr-1"}),s[21]||(s[21]=S(" Iscritto "))])):ae(o)?(a(),P(H,{key:1,variant:"success",icon:"plus",size:"sm",onClick:j(m=>le(o),["stop"])},{default:T(()=>s[22]||(s[22]=[S(" Iscriviti ")])),_:2,__:[22]},1032,["onClick"])):(a(),d("span",Bt," Completo "))])):h("",!0),e("div",Yt,[e("div",Ht,[v(y,{name:"user",size:"xs"}),e("span",null,c(o.organizer_name||"Organizzatore"),1)]),e("span",null,c(A(n)(o.created_at)),1)]),e("div",Gt,[v(H,{variant:"secondary",icon:"pencil",size:"sm",onClick:j(m=>de(o),["stop"])},{default:T(()=>s[23]||(s[23]=[S(" Modifica ")])),_:2,__:[23]},1032,["onClick"]),X.value?(a(),P(H,{key:0,variant:"danger",icon:"trash",size:"sm",onClick:j(m=>ue(o),["stop"])},{default:T(()=>s[24]||(s[24]=[S(" Elimina ")])),_:2,__:[24]},1032,["onClick"])):h("",!0)])])],8,It))),128))])])):z.value==="calendar"?(a(),d("div",$t,[e("div",Qt,[e("div",Wt,[e("div",Jt,[e("h3",Kt,c(se.value)+" "+c(ie.value),1),e("div",Xt,[e("button",{onClick:s[4]||(s[4]=o=>Q(-1)),class:"p-2 text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"},[v(y,{name:"chevron-left",size:"sm"})]),e("button",{onClick:pe,class:"px-3 py-1 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600"}," Oggi "),e("button",{onClick:s[5]||(s[5]=o=>Q(1)),class:"p-2 text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"},[v(y,{name:"chevron-right",size:"sm"})])])])]),e("div",Zt,[e("div",es,[(a(!0),d(I,null,F(oe.value,o=>(a(),d("div",{key:o,class:"p-2 text-sm font-medium text-gray-500 dark:text-gray-400 text-center"},c(o),1))),128))]),e("div",ts,[(a(!0),d(I,null,F(re.value,o=>(a(),d("div",{key:`${o.date.getTime()}-${o.isCurrentMonth}`,class:V(["min-h-[100px] p-1 border border-gray-200 dark:border-gray-700",o.isCurrentMonth?"bg-white dark:bg-gray-800":"bg-gray-50 dark:bg-gray-900",o.isToday?"ring-2 ring-primary-500":""])},[e("div",{class:V(["text-sm font-medium p-1",o.isCurrentMonth?"text-gray-900 dark:text-white":"text-gray-400 dark:text-gray-600",o.isToday?"text-primary-600 dark:text-primary-400 font-bold":""])},c(o.date.getDate()),3),e("div",ss,[(a(!0),d(I,null,F(fe(o.date),m=>(a(),d("div",{key:m.id,onClick:R=>$(m),class:V(["text-xs p-1 rounded cursor-pointer truncate",E(m)==="upcoming"?"bg-primary-100 text-primary-800 hover:bg-primary-200":E(m)==="ongoing"?"bg-green-100 text-green-800 hover:bg-green-200":"bg-gray-100 text-gray-800 hover:bg-gray-200"]),title:m.title},c(ve(m))+" "+c(m.title),11,is))),128))])],2))),128))])])])])):h("",!0)]),_:1},8,["data","loading","can-create"]),f.value?(a(),P(Xe,{key:0,onClose:s[7]||(s[7]=u=>f.value=!1),onCreated:me})):h("",!0),r.value?(a(),P(Ce,{key:1,event:_.value,onClose:s[8]||(s[8]=u=>r.value=!1),onUpdated:ce},null,8,["event"])):h("",!0),O.value?(a(),P(Se,{key:2,title:"Elimina Evento",message:`Sei sicuro di voler eliminare l'evento '${(l=_.value)==null?void 0:l.title}'?`,"confirm-text":"Elimina","confirm-class":"bg-red-600 hover:bg-red-700",onConfirm:ge,onCancel:s[9]||(s[9]=u=>O.value=!1)},null,8,["message"])):h("",!0)],64)}}},fs=W(os,[["__scopeId","data-v-310d1c36"]]);export{fs as default};
