import{_ as u,H as m}from"./app.js";import{b as c,h as o,l as i,n as d,K as y,f as p,o as n,s as b,t as f}from"./vendor.js";const h={name:"StandardButton",components:{HeroIcon:m},props:{variant:{type:String,default:"primary",validator:e=>["primary","secondary","danger","warning","success","outline-primary","outline-secondary","outline-danger","ghost","link"].includes(e)},size:{type:String,default:"md",validator:e=>["xs","sm","md","lg","xl"].includes(e)},type:{type:String,default:"button"},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},icon:{type:[String,Object],default:null},iconPosition:{type:String,default:"left",validator:e=>["left","right"].includes(e)},text:{type:String,default:null},block:{type:<PERSON>olean,default:!1}},emits:["click"],computed:{computedIconName(){return this.icon?typeof this.icon=="string"?this.icon:typeof this.icon=="object"&&this.icon.name?this.icon.name:null:null},computedIconPosition(){return typeof this.icon=="object"&&this.icon.position?this.icon.position:this.iconPosition},buttonClasses(){const e=["inline-flex items-center justify-center border font-medium rounded-md","focus:outline-none focus:ring-2 focus:ring-offset-2","transition-colors duration-200","disabled:opacity-50 disabled:cursor-not-allowed"];this.block&&e.push("w-full");const a={xs:"px-2.5 py-1.5 text-xs",sm:"px-3 py-2 text-sm leading-4",md:"px-4 py-2 text-sm",lg:"px-4 py-2 text-base",xl:"px-6 py-3 text-base"};e.push(a[this.size]);const r={primary:["border-transparent text-white","bg-primary-600 hover:bg-primary-700","focus:ring-primary-500","dark:bg-primary-600 dark:hover:bg-primary-700"],secondary:["border-gray-300 text-gray-700","bg-white hover:bg-gray-50","focus:ring-primary-500","dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600","dark:hover:bg-gray-600"],danger:["border-transparent text-white","bg-red-600 hover:bg-red-700","focus:ring-red-500"],warning:["border-transparent text-white","bg-yellow-600 hover:bg-yellow-700","focus:ring-yellow-500"],success:["border-transparent text-white","bg-green-600 hover:bg-green-700","focus:ring-green-500"],"outline-primary":["border-primary-600 text-primary-600","bg-transparent hover:bg-primary-50","focus:ring-primary-500","dark:border-primary-400 dark:text-primary-400","dark:hover:bg-primary-900/20"],"outline-secondary":["border-gray-300 text-gray-700","bg-transparent hover:bg-gray-50","focus:ring-gray-500","dark:border-gray-600 dark:text-gray-300","dark:hover:bg-gray-700"],"outline-danger":["border-red-600 text-red-600","bg-transparent hover:bg-red-50","focus:ring-red-500","dark:border-red-400 dark:text-red-400","dark:hover:bg-red-900/20"],"outline-success":["border-green-600 text-green-600","bg-transparent hover:bg-green-50","focus:ring-green-500","dark:border-green-400 dark:text-green-400","dark:hover:bg-green-900/20"],ghost:["border-transparent text-gray-700","bg-transparent hover:bg-gray-100","focus:ring-gray-500","dark:text-gray-300 dark:hover:bg-gray-800"],link:["border-transparent text-primary-600","bg-transparent hover:text-primary-700 hover:underline","focus:ring-primary-500 focus:ring-offset-0","dark:text-primary-400 dark:hover:text-primary-300"]},l=r[this.variant]||r.primary;return e.push(...l),e.join(" ")},iconSize(){return{xs:"xs",sm:"sm",md:"sm",lg:"md",xl:"md"}[this.size]},iconClasses(){const e=[];return(this.text||this.$slots.default)&&(this.computedIconPosition==="left"?e.push("mr-2"):e.push("ml-2")),e.join(" ")}}},x=["type","disabled"],k={key:2};function v(e,a,r,l,z,t){const s=p("HeroIcon");return n(),c("button",{type:r.type,disabled:r.disabled||r.loading,class:d(t.buttonClasses),onClick:a[0]||(a[0]=g=>e.$emit("click",g))},[r.loading?(n(),o(s,{key:0,name:"spinner",size:t.iconSize,class:"animate-spin mr-2"},null,8,["size"])):r.icon&&t.computedIconPosition==="left"?(n(),o(s,{key:1,name:t.computedIconName,size:t.iconSize,class:d(t.iconClasses)},null,8,["name","size","class"])):i("",!0),e.$slots.default||r.text?(n(),c("span",k,[y(e.$slots,"default",{},()=>[b(f(r.text),1)])])):i("",!0),!r.loading&&r.icon&&t.computedIconPosition==="right"?(n(),o(s,{key:3,name:t.computedIconName,size:t.iconSize,class:d(t.iconClasses)},null,8,["name","size","class"])):i("",!0)],10,x)}const C=u(h,[["render",v]]);export{C as S};
