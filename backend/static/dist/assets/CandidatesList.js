import{b as y,o as c,j as e,e as w,F as q,p as R,t as p,v as I,c as E,n as z,l as T,s as ne,h as X,A as Y,r as A,w as oe,k as J,B as P,C as L,H as se,x as le,q as de}from"./vendor.js";import{u as ue}from"./recruiting.js";import{H as U,_ as ce,d as ge}from"./app.js";import{S as F}from"./StandardButton.js";import{u as pe}from"./useDebounce.js";import{A as me}from"./ActionButtonGroup.js";import{_ as ie}from"./BaseModal.js";const M={new:{label:"Nuovo",class:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",color:"bg-blue-500"},screening:{label:"Screening",class:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",color:"bg-yellow-500"},interviewing:{label:"In colloquio",class:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",color:"bg-purple-500"},offered:{label:"Offerta fatta",class:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",color:"bg-orange-500"},hired:{label:"Assunto",class:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",color:"bg-green-500"},rejected:{label:"Scartato",class:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",color:"bg-red-500"}},K={pending:{label:"In Attesa",class:"bg-warning-100 text-warning-800 dark:bg-warning-900/20 dark:text-warning-400"},in_progress:{label:"In Corso",class:"bg-brand-secondary-100 text-brand-secondary-800 dark:bg-brand-secondary-900/20 dark:text-brand-secondary-400"},completed:{label:"Completata",class:"bg-success-100 text-success-800 dark:bg-success-900/20 dark:text-success-400"},rejected:{label:"Rifiutata",class:"bg-danger-100 text-danger-800 dark:bg-danger-900/20 dark:text-danger-400"}},O={application_received:{label:"Ricevute",icon:"inbox-arrow-down",iconClass:"text-info-600"},screening:{label:"Screening",icon:"document-magnifying-glass",iconClass:"text-warning-600"},interview_1:{label:"Primo Colloquio",icon:"users",iconClass:"text-brand-secondary-600"},interview_2:{label:"Secondo Colloquio",icon:"chat-bubble-left-ellipsis",iconClass:"text-brand-primary-600"},offer:{label:"Offerta",icon:"check-circle",iconClass:"text-success-600"}},Q={website:{label:"Sito web",class:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"},linkedin:{label:"LinkedIn",class:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},referral:{label:"Referral",class:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"},agency:{label:"Agenzia",class:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"}},W={phone_screening:{label:"Telefono",class:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},video_technical:{label:"Video Tech",class:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"},onsite_cultural:{label:"In Sede",class:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"},final_executive:{label:"Finale",class:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"}};function H(){return{getCandidateStatusLabel:t=>{var r;return((r=M[t])==null?void 0:r.label)||t},getCandidateStatusClass:t=>{var r;return((r=M[t])==null?void 0:r.class)||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"},getCandidateStatusColor:t=>{var r;return((r=M[t])==null?void 0:r.color)||"bg-gray-500"},getApplicationStatusLabel:t=>{var r;return((r=K[t])==null?void 0:r.label)||t},getApplicationStatusClass:t=>{var r;return((r=K[t])==null?void 0:r.class)||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"},getPipelineStepLabel:t=>{var r;return((r=O[t])==null?void 0:r.label)||t},getPipelineStepIcon:t=>{var r;return((r=O[t])==null?void 0:r.icon)||"question-mark-circle"},getPipelineStepIconClass:t=>{var r;return((r=O[t])==null?void 0:r.iconClass)||"text-gray-500"},getSourceLabel:t=>{var r;return((r=Q[t])==null?void 0:r.label)||t},getSourceClass:t=>{var r;return((r=Q[t])==null?void 0:r.class)||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"},getInterviewTypeLabel:t=>{var r;return((r=W[t])==null?void 0:r.label)||t},getInterviewTypeClass:t=>{var r;return((r=W[t])==null?void 0:r.class)||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"},formatDate:t=>t?new Date(t).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"",formatDateTime:t=>{if(!t)return"";const r=new Date(t),V=new Date,j=new Date(V);j.setDate(j.getDate()+1);const D=r.toDateString()===V.toDateString(),G=r.toDateString()===j.toDateString(),l=r.toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit"});return D?`Oggi ${l}`:G?`Domani ${l}`:r.toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"})},formatShortDate:t=>t?new Date(t).toLocaleDateString("it-IT",{day:"numeric",month:"short"}):"",formatScore:t=>!t||isNaN(t)?"N/A":Number(t).toFixed(1),formatCurrency:t=>!t||isNaN(t)?"€ 0":new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(t),formatPhoneNumber:t=>{if(!t)return"";const r=t.replace(/\D/g,"");return r.length===10?r.replace(/(\d{3})(\d{3})(\d{4})/,"$1 $2 $3"):t},validateEmail:t=>t?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t):!1,validatePhone:t=>t?/^[\+]?[0-9\s\-\(\)]{6,}$/.test(t):!1,validateRequired:t=>t!=null&&t!=="",validateRange:(t,r,V)=>{const j=Number(t);return!isNaN(j)&&j>=r&&j<=V},calculateApplicationStats:t=>{const r={total:t.length,pending:0,in_progress:0,completed:0,rejected:0,average_score:0};let V=0,j=0;return t.forEach(D=>{r.hasOwnProperty(D.status)&&r[D.status]++,D.overall_score&&(V+=D.overall_score,j++)}),j>0&&(r.average_score=(V/j).toFixed(1)),r},calculateCandidateStats:t=>{const r={total:t.length,new:0,screening:0,interviewing:0,offered:0,hired:0,rejected:0};return t.forEach(V=>{r.hasOwnProperty(V.status)&&r[V.status]++}),r},candidateStatusConfig:M,applicationStatusConfig:K,pipelineStepConfig:O,sourceConfig:Q,interviewTypeConfig:W}}const be={class:"bg-white dark:bg-gray-800 rounded-lg shadow mb-6 p-6"},ye={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},xe={class:"relative"},ve=["value"],fe=["value"],ke=["value"],he=["value"],we=["value"],Ce={class:"flex items-end space-x-2"},_e={__name:"CandidateFiltersPanel",props:{modelValue:{type:Object,required:!0,default:()=>({search:"",status:"",source:""})}},emits:["update:modelValue","filter-change","clear-filters"],setup(n,{emit:S}){const{candidateStatusConfig:x,sourceConfig:k}=H(),m=n,g=S,C=pe(()=>{g("filter-change")},300),$=o=>{const a={...m.modelValue,search:o.target.value};g("update:modelValue",a),C()},s=o=>{const a={...m.modelValue,status:o.target.value};g("update:modelValue",a),g("filter-change")},u=o=>{const a={...m.modelValue,source:o.target.value};g("update:modelValue",a),g("filter-change")},_=()=>{g("update:modelValue",{search:"",status:"",source:""}),g("clear-filters")};return(o,a)=>(c(),y("div",be,[e("div",ye,[e("div",null,[a[0]||(a[0]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Cerca candidato ",-1)),e("div",xe,[w(U,{name:"magnifying-glass",size:"sm",class:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e("input",{value:n.modelValue.search,type:"text",placeholder:"Nome, email...",class:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",onInput:$},null,40,ve)])]),e("div",null,[a[2]||(a[2]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stato ",-1)),e("select",{value:n.modelValue.status,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",onChange:s},[a[1]||(a[1]=e("option",{value:""},"Tutti gli stati",-1)),(c(!0),y(q,null,R(I(x),(i,b)=>(c(),y("option",{key:b,value:b},p(i.label),9,ke))),128))],40,fe)]),e("div",null,[a[4]||(a[4]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Fonte ",-1)),e("select",{value:n.modelValue.source,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",onChange:u},[a[3]||(a[3]=e("option",{value:""},"Tutte le fonti",-1)),(c(!0),y(q,null,R(I(k),(i,b)=>(c(),y("option",{key:b,value:b},p(i.label),9,we))),128))],40,he)]),e("div",Ce,[w(F,{variant:"outline-secondary",icon:"funnel",text:"Pulisci Filtri",size:"sm",onClick:_})])])]))}},Se={class:"grid grid-cols-1 md:grid-cols-5 gap-6 mb-6"},$e={class:"flex items-center"},je={class:"text-sm font-medium text-gray-500 dark:text-gray-400"},Ve={class:"text-2xl font-semibold text-gray-900 dark:text-white"},Ae={__name:"CandidateStatsCards",props:{candidates:{type:Array,required:!0,default:()=>[]},loading:{type:Boolean,default:!1}},setup(n){const S=n,{getCandidateStatusLabel:x,getCandidateStatusColor:k,calculateCandidateStats:m}=H(),g=E(()=>S.loading||!S.candidates.length?{new:0,screening:0,interviewing:0,offered:0,hired:0}:m(S.candidates));return(C,$)=>(c(),y("div",Se,[(c(!0),y(q,null,R(g.value,(s,u)=>(c(),y("div",{key:u,class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},[e("div",$e,[e("div",{class:z(["w-3 h-3 rounded-full mr-3",I(k)(u)])},null,2),e("div",null,[e("p",je,p(I(x)(u)),1),e("p",Ve,p(s),1)])])]))),128))]))}},Ie={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},Fe={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},De={class:"text-lg font-medium text-gray-900 dark:text-white"},Pe={key:0,class:"text-sm font-normal text-gray-500 dark:text-gray-400"},ze={key:0,class:"flex justify-center py-12"},Te={key:1,class:"text-center py-12"},Ne={class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},Ee={class:"text-gray-500 dark:text-gray-400 mb-4"},Le={key:2,class:"overflow-x-auto"},qe={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Re={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Be=["onClick"],Me={class:"px-6 py-4 whitespace-nowrap"},Oe={class:"flex items-center"},Ue={class:"flex-shrink-0 h-10 w-10"},Je={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},He={class:"text-sm font-medium text-gray-700 dark:text-gray-300"},Ge={class:"ml-4"},Ke={class:"text-sm font-medium text-gray-900 dark:text-white"},Qe={class:"text-sm text-gray-500 dark:text-gray-400 flex items-center"},We={class:"px-6 py-4 whitespace-nowrap"},Xe={class:"text-sm text-gray-900 dark:text-white"},Ye={class:"text-sm text-gray-500 dark:text-gray-400"},Ze={class:"px-6 py-4 whitespace-nowrap"},et={class:"px-6 py-4 whitespace-nowrap"},tt={class:"px-6 py-4 whitespace-nowrap text-center"},at={key:0,class:"flex items-center justify-center"},rt={key:1,class:"flex items-center justify-center"},nt={class:"px-6 py-4 whitespace-nowrap text-center"},ot={class:"text-sm text-gray-900 dark:text-white"},st={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},it={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},lt={class:"flex items-center justify-end"},dt={key:3,class:"px-6 py-4 border-t border-gray-200 dark:border-gray-700"},ut={class:"flex items-center justify-between"},ct={class:"text-sm text-gray-700 dark:text-gray-300"},gt={class:"flex items-center space-x-2"},pt={class:"px-3 py-1 text-sm text-gray-700 dark:text-gray-300"},mt={__name:"CandidateDataTable",props:{candidates:{type:Array,required:!0,default:()=>[]},loading:{type:Boolean,default:!1},pagination:{type:Object,required:!0,default:()=>({page:1,per_page:20,total:0,pages:0})},hasActiveFilters:{type:Boolean,default:!1}},emits:["view-candidate","edit-candidate","delete-candidate","upload-cv","view-applications","apply-to-job","open-linkedin","create-candidate","change-page"],setup(n,{emit:S}){const x=S,{getCandidateStatusLabel:k,getCandidateStatusClass:m,getSourceLabel:g,getSourceClass:C,formatDate:$,formatPhoneNumber:s}=H(),u=o=>{const a=[{key:"view-applications",label:`Candidature (${o.applications_count||0})`,icon:"document-text",danger:!1},{key:"apply-to-job",label:"Candida a posizione",icon:"plus-circle",danger:!1}];return o.linkedin_url&&a.push({key:"open-linkedin",label:"Apri LinkedIn",icon:"link",danger:!1}),a},_=(o,a)=>{switch(o){case"view-applications":x("view-applications",a);break;case"apply-to-job":x("apply-to-job",a);break;case"open-linkedin":x("open-linkedin",a.linkedin_url);break;default:console.log("Azione non riconosciuta:",o)}};return(o,a)=>(c(),y("div",Ie,[e("div",Fe,[e("h3",De,[a[3]||(a[3]=ne(" Candidati ")),n.pagination.total?(c(),y("span",Pe," ("+p(n.pagination.total)+" totali) ",1)):T("",!0)])]),n.loading?(c(),y("div",ze,a[4]||(a[4]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary-600"},null,-1)]))):n.candidates.length?(c(),y("div",Le,[e("table",qe,[a[5]||(a[5]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Candidato "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Contatto "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Fonte "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato "),e("th",{class:"px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," CV "),e("th",{class:"px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Candidature "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Data "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",Re,[(c(!0),y(q,null,R(n.candidates,i=>(c(),y("tr",{key:i.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",onClick:b=>o.$emit("view-candidate",i)},[e("td",Me,[e("div",Oe,[e("div",Ue,[e("div",Je,[e("span",He,p(i.first_name.charAt(0))+p(i.last_name.charAt(0)),1)])]),e("div",Ge,[e("div",Ke,p(i.full_name),1),e("div",Qe,[w(U,{name:"map-pin",size:"xs",class:"mr-1"}),ne(" "+p(i.location||"N/A"),1)])])])]),e("td",We,[e("div",Xe,p(i.email),1),e("div",Ye,p(I(s)(i.phone)||"N/A"),1)]),e("td",Ze,[e("span",{class:z(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",I(C)(i.source)])},p(I(g)(i.source)),3)]),e("td",et,[e("span",{class:z(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",I(m)(i.status)])},p(I(k)(i.status)),3)]),e("td",tt,[i.cv_path?(c(),y("div",at,[w(U,{name:"document-text",size:"sm",class:"text-green-600 dark:text-green-400"})])):(c(),y("div",rt,[w(F,{variant:"ghost",icon:"arrow-up-tray",size:"xs",title:"Carica CV",onClick:Y(b=>o.$emit("upload-cv",i),["stop"])},null,8,["onClick"])]))]),e("td",nt,[e("span",ot,p(i.applications_count||0),1)]),e("td",st,p(I($)(i.created_at)),1),e("td",it,[e("div",lt,[w(me,{onView:b=>o.$emit("view-candidate",i),onEdit:b=>o.$emit("edit-candidate",i),onDelete:b=>o.$emit("delete-candidate",i),"more-actions":u(i),onAction:b=>_(b,i),"delete-message":`Sei sicuro di voler eliminare il candidato ${i.first_name} ${i.last_name}?`},null,8,["onView","onEdit","onDelete","more-actions","onAction","delete-message"])])])],8,Be))),128))])])])):(c(),y("div",Te,[w(U,{name:"user-group",size:"lg",class:"mx-auto text-gray-400 mb-4"}),e("h3",Ne,p(n.hasActiveFilters?"Nessun candidato trovato":"Nessun candidato disponibile"),1),e("p",Ee,p(n.hasActiveFilters?"Prova a modificare i filtri di ricerca.":"Aggiungi il tuo primo candidato."),1),n.hasActiveFilters?T("",!0):(c(),X(F,{key:0,variant:"primary",icon:"plus",text:"Nuovo Candidato",onClick:a[0]||(a[0]=i=>o.$emit("create-candidate"))}))])),n.pagination.pages>1?(c(),y("div",dt,[e("div",ut,[e("div",ct," Mostrando "+p((n.pagination.page-1)*n.pagination.per_page+1)+" - "+p(Math.min(n.pagination.page*n.pagination.per_page,n.pagination.total))+" di "+p(n.pagination.total)+" risultati ",1),e("div",gt,[w(F,{variant:"outline-secondary",icon:"chevron-left",text:"Precedente",size:"sm",disabled:n.pagination.page<=1,onClick:a[1]||(a[1]=i=>o.$emit("change-page",n.pagination.page-1))},null,8,["disabled"]),e("span",pt," Pagina "+p(n.pagination.page)+" di "+p(n.pagination.pages),1),w(F,{variant:"outline-secondary",icon:"chevron-right",text:"Successiva",size:"sm",disabled:n.pagination.page>=n.pagination.pages,onClick:a[2]||(a[2]=i=>o.$emit("change-page",n.pagination.page+1))},null,8,["disabled"])])])])):T("",!0)]))}},bt={class:"space-y-4"},yt={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},xt={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},vt={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},ft={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},kt=["value"],ht={__name:"CreateCandidateModal",props:{show:{type:Boolean,default:!1},loading:{type:Boolean,default:!1}},emits:["close","submit"],setup(n,{emit:S}){const x=n,k=S,{validateEmail:m,validatePhone:g,validateRequired:C,sourceConfig:$}=H(),s=A({first_name:"",last_name:"",email:"",phone:"",location:"",source:"website"}),u=A({}),_=E(()=>C(s.value.first_name)&&C(s.value.last_name)&&C(s.value.email)&&m(s.value.email)&&(!s.value.phone||g(s.value.phone))),o=()=>{const f={};return C(s.value.first_name)||(f.first_name="Il nome è obbligatorio"),C(s.value.last_name)||(f.last_name="Il cognome è obbligatorio"),C(s.value.email)?m(s.value.email)||(f.email="Inserisci un'email valida"):f.email="L'email è obbligatoria",s.value.phone&&!g(s.value.phone)&&(f.phone="Inserisci un numero di telefono valido"),u.value=f,Object.keys(f).length===0},a=f=>{u.value[f]&&delete u.value[f]},i=()=>{s.value={first_name:"",last_name:"",email:"",phone:"",location:"",source:"website"},u.value={},k("close")},b=()=>{o()&&k("submit",{...s.value})};return oe(()=>x.show,f=>{f&&(s.value={first_name:"",last_name:"",email:"",phone:"",location:"",source:"website"},u.value={})}),(f,d)=>(c(),X(ie,{show:n.show,title:"Nuovo Candidato",size:"md",onClose:i},{footer:J(()=>[w(F,{variant:"secondary",text:"Annulla",onClick:i}),w(F,{variant:"primary",text:n.loading?"Creazione...":"Crea Candidato",loading:n.loading,disabled:!_.value,onClick:b},null,8,["text","loading","disabled"])]),default:J(()=>[e("form",{onSubmit:Y(b,["prevent"])},[e("div",bt,[e("div",null,[d[10]||(d[10]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome * ",-1)),P(e("input",{"onUpdate:modelValue":d[0]||(d[0]=h=>s.value.first_name=h),type:"text",required:"",class:z(["w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",u.value.first_name?"border-red-300 dark:border-red-600":"border-gray-300 dark:border-gray-600"]),onInput:d[1]||(d[1]=h=>a("first_name"))},null,34),[[L,s.value.first_name]]),u.value.first_name?(c(),y("p",yt,p(u.value.first_name),1)):T("",!0)]),e("div",null,[d[11]||(d[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Cognome * ",-1)),P(e("input",{"onUpdate:modelValue":d[2]||(d[2]=h=>s.value.last_name=h),type:"text",required:"",class:z(["w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",u.value.last_name?"border-red-300 dark:border-red-600":"border-gray-300 dark:border-gray-600"]),onInput:d[3]||(d[3]=h=>a("last_name"))},null,34),[[L,s.value.last_name]]),u.value.last_name?(c(),y("p",xt,p(u.value.last_name),1)):T("",!0)]),e("div",null,[d[12]||(d[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Email * ",-1)),P(e("input",{"onUpdate:modelValue":d[4]||(d[4]=h=>s.value.email=h),type:"email",required:"",class:z(["w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",u.value.email?"border-red-300 dark:border-red-600":"border-gray-300 dark:border-gray-600"]),onInput:d[5]||(d[5]=h=>a("email"))},null,34),[[L,s.value.email]]),u.value.email?(c(),y("p",vt,p(u.value.email),1)):T("",!0)]),e("div",null,[d[13]||(d[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Telefono ",-1)),P(e("input",{"onUpdate:modelValue":d[6]||(d[6]=h=>s.value.phone=h),type:"tel",class:z(["w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",u.value.phone?"border-red-300 dark:border-red-600":"border-gray-300 dark:border-gray-600"]),onInput:d[7]||(d[7]=h=>a("phone"))},null,34),[[L,s.value.phone]]),u.value.phone?(c(),y("p",ft,p(u.value.phone),1)):T("",!0)]),e("div",null,[d[14]||(d[14]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Città ",-1)),P(e("input",{"onUpdate:modelValue":d[8]||(d[8]=h=>s.value.location=h),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[L,s.value.location]])]),e("div",null,[d[15]||(d[15]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Fonte ",-1)),P(e("select",{"onUpdate:modelValue":d[9]||(d[9]=h=>s.value.source=h),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[(c(!0),y(q,null,R(I($),(h,B)=>(c(),y("option",{key:B,value:B},p(h.label),9,kt))),128))],512),[[se,s.value.source]])])])],32)]),_:1},8,["show"]))}},wt={class:"space-y-4"},Ct=["value"],_t={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},St={__name:"ApplyCandidateToJobModal",props:{show:{type:Boolean,default:!1},candidate:{type:Object,default:null},jobPostings:{type:Array,required:!0,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["close","submit"],setup(n,{emit:S}){const x=n,k=S,m=A({job_posting_id:"",cover_letter:""}),g=A({}),C=E(()=>m.value.job_posting_id!==""),$=()=>{const o={};return m.value.job_posting_id||(o.job_posting_id="Seleziona una posizione"),g.value=o,Object.keys(o).length===0},s=o=>{g.value[o]&&delete g.value[o]},u=()=>{m.value={job_posting_id:"",cover_letter:""},g.value={},k("close")},_=()=>{var o;$()&&k("submit",{candidate_id:(o=x.candidate)==null?void 0:o.id,job_posting_id:m.value.job_posting_id,cover_letter:m.value.cover_letter})};return oe(()=>x.show,o=>{o&&(m.value={job_posting_id:"",cover_letter:""},g.value={})}),(o,a)=>{var i;return c(),X(ie,{show:n.show,title:`Candida ${((i=n.candidate)==null?void 0:i.full_name)||""}`,size:"md",onClose:u},{footer:J(()=>[w(F,{variant:"secondary",text:"Annulla",onClick:u}),w(F,{variant:"primary",text:n.loading?"Creazione...":"Crea Candidatura",loading:n.loading,disabled:!C.value,onClick:_},null,8,["text","loading","disabled"])]),default:J(()=>[e("form",{onSubmit:Y(_,["prevent"])},[e("div",wt,[e("div",null,[a[4]||(a[4]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Posizione * ",-1)),P(e("select",{"onUpdate:modelValue":a[0]||(a[0]=b=>m.value.job_posting_id=b),required:"",class:z(["w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",g.value.job_posting_id?"border-red-300 dark:border-red-600":"border-gray-300 dark:border-gray-600"]),onChange:a[1]||(a[1]=b=>s("job_posting_id"))},[a[3]||(a[3]=e("option",{value:""},"Seleziona posizione",-1)),(c(!0),y(q,null,R(n.jobPostings,b=>(c(),y("option",{key:b.id,value:b.id},p(b.title)+" - "+p(b.location),9,Ct))),128))],34),[[se,m.value.job_posting_id]]),g.value.job_posting_id?(c(),y("p",_t,p(g.value.job_posting_id),1)):T("",!0)]),e("div",null,[a[5]||(a[5]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Lettera di Motivazione ",-1)),P(e("textarea",{"onUpdate:modelValue":a[2]||(a[2]=b=>m.value.cover_letter=b),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-brand-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Motivazione del candidato..."},null,512),[[L,m.value.cover_letter]])])])],32)]),_:1},8,["show","title"])}}},$t={class:"candidates-list"},jt={class:"mb-6 flex items-center justify-between"},Vt={__name:"CandidatesList",setup(n){const S=de(),x=ue(),k=ge(),m=A(!1),g=A(!1),C=A(null),$=A(null),s=A(null),u=A([]),_=A({search:"",status:"",source:""}),o=E(()=>x.loading),a=E(()=>x.candidates),i=E(()=>x.pagination),b=E(()=>_.value.search||_.value.status||_.value.source),f=async()=>{const l={..._.value};Object.keys(l).forEach(v=>{(l[v]===""||l[v]===null||l[v]===void 0)&&delete l[v]}),await x.fetchCandidates(l)},d=()=>{x.pagination.page=1,f()},h=()=>{_.value={search:"",status:"",source:""},d()},B=l=>{l>=1&&l<=i.value.pages&&(x.pagination.page=l,f())},Z=l=>{S.push(`/app/recruiting/candidates/${l.id}`)},ee=l=>{S.push(`/app/recruiting/candidates/${l.id}/edit`)},te=async l=>{try{await x.createCandidate(l),m.value=!1,k.success("Candidato creato con successo"),await f()}catch(v){console.error("Error creating candidate:",v),k.error("Errore nella creazione del candidato")}},ae=l=>{$.value=l,C.value.click()},re=async l=>{const v=l.target.files[0];if(!(!v||!$.value))try{await x.uploadCandidateCV($.value.id,v),k.success("CV caricato con successo"),await f()}catch(N){console.error("Error uploading CV:",N),k.error("Errore nel caricamento del CV")}finally{l.target.value="",$.value=null}},t=async l=>{if(confirm(`Eliminare il candidato "${l.full_name}"?`))try{await x.deleteCandidate(l.id),k.success("Candidato eliminato con successo"),await f()}catch(v){console.error("Error deleting candidate:",v),k.error("Errore nell'eliminazione del candidato")}},r=l=>{window.open(l,"_blank")},V=l=>{S.push(`/app/recruiting/applications?candidate_id=${l.id}`)},j=l=>{s.value=l,g.value=!0},D=async l=>{try{await x.applyCandidateToJobPosting(l.candidate_id,l.job_posting_id,l.cover_letter),g.value=!1,s.value=null,k.success("Candidatura creata con successo"),await f()}catch(v){console.error("Error creating application:",v),k.error("Errore nella creazione della candidatura")}},G=async()=>{try{await x.fetchJobPostings({status:"active"}),u.value=x.activeJobPostings||[]}catch(l){console.error("Error loading job postings:",l),u.value=[]}};return le(async()=>{await Promise.all([f(),G()])}),(l,v)=>(c(),y("div",$t,[e("div",jt,[v[5]||(v[5]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Candidati"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Gestione candidati e processo di selezione ")],-1)),w(F,{variant:"primary",icon:"plus",text:"Nuovo Candidato",onClick:v[0]||(v[0]=N=>m.value=!0)})]),w(_e,{modelValue:_.value,"onUpdate:modelValue":v[1]||(v[1]=N=>_.value=N),onFilterChange:d,onClearFilters:h},null,8,["modelValue"]),w(Ae,{candidates:a.value,loading:o.value},null,8,["candidates","loading"]),w(mt,{candidates:a.value,loading:o.value,pagination:i.value,"has-active-filters":b.value,onViewCandidate:Z,onEditCandidate:ee,onDeleteCandidate:t,onUploadCv:ae,onViewApplications:V,onApplyToJob:j,onOpenLinkedin:r,onCreateCandidate:v[2]||(v[2]=N=>m.value=!0),onChangePage:B},null,8,["candidates","loading","pagination","has-active-filters"]),w(ht,{show:m.value,loading:o.value,onClose:v[3]||(v[3]=N=>m.value=!1),onSubmit:te},null,8,["show","loading"]),w(St,{show:g.value,candidate:s.value,"job-postings":u.value,loading:o.value,onClose:v[4]||(v[4]=N=>g.value=!1),onSubmit:D},null,8,["show","candidate","job-postings","loading"]),e("input",{ref_key:"cvInput",ref:C,type:"file",accept:".pdf,.doc,.docx,.txt",style:{display:"none"},onChange:re},null,544)]))}},Nt=ce(Vt,[["__scopeId","data-v-23604877"]]);export{Nt as default};
