import{r as z,c as F,f as X,b as n,o as s,j as e,l as y,n as ee,t as v,F as O,p as I,h as Y,s as G,w as le,x as ne,z as K,y as oe,I as ie,e as H,k as ae,B as U,H as Z,q as de,C as ue}from"./vendor.js";import{_ as J,H as se}from"./app.js";import{_ as ce}from"./PageHeader.js";import{V as ge}from"./ViewModeToggle.js";import{_ as me}from"./StatsGrid.js";const pe={class:"department-node"},ve={class:"department-header bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4 mb-4 min-w-80"},be={class:"flex items-center justify-between"},ye={class:"flex-1"},xe={class:"flex items-center"},he={class:"flex items-center"},fe={class:"text-lg font-semibold text-gray-900 dark:text-white"},ke={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},we={class:"flex items-center mt-1 space-x-4 text-xs text-gray-500 dark:text-gray-400"},Ce={key:0},$e={key:0,class:"ml-4 flex items-center"},_e={class:"text-right mr-3"},Me={class:"text-sm font-medium text-gray-900 dark:text-white"},ze={key:0,class:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"},De={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3"},Ee=["onClick"],Le={class:"flex-1 min-w-0"},Se={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},Ae={class:"text-xs text-gray-500 dark:text-gray-400 truncate"},Be={key:0,class:"ml-2"},Ne={key:0,class:"mt-3 text-center"},je={key:0,class:"ml-8 space-y-4"},Te={class:"relative"},W=6,Ve={__name:"DepartmentNode",props:{department:{type:Object,required:!0},expanded:{type:Set,required:!0},searchQuery:{type:String,default:""}},emits:["toggle-node","employee-click"],setup(i,{emit:q}){const C=i,S=q,k=z(!1),p=F(()=>C.expanded.has(C.department.id)),l=F(()=>k.value||C.department.employees.length<=W?C.department.employees:C.department.employees.slice(0,W)),$=()=>{S("toggle-node",C.department.id)},m=w=>new Intl.NumberFormat("it-IT").format(w);return(w,u)=>{const T=X("DepartmentNode",!0);return s(),n("div",pe,[e("div",ve,[e("div",be,[e("div",ye,[e("div",xe,[i.department.subdepartments.length>0?(s(),n("button",{key:0,onClick:$,class:"mr-2 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"},[(s(),n("svg",{class:ee(["w-4 h-4 text-gray-500 transition-transform",p.value?"transform rotate-90":""]),fill:"currentColor",viewBox:"0 0 20 20"},u[3]||(u[3]=[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))])):y("",!0),e("div",he,[u[4]||(u[4]=e("div",{class:"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("h3",fe,v(i.department.name),1),i.department.description?(s(),n("p",ke,v(i.department.description),1)):y("",!0),e("div",we,[e("span",null,v(i.department.employee_count)+" dipendenti",1),i.department.budget>0?(s(),n("span",Ce,"Budget: €"+v(m(i.department.budget)),1)):y("",!0)])])])])]),i.department.manager?(s(),n("div",$e,[e("div",_e,[e("p",Me,v(i.department.manager.full_name),1),u[5]||(u[5]=e("p",{class:"text-xs text-gray-500 dark:text-gray-400"},"Manager",-1))]),u[6]||(u[6]=e("div",{class:"w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-purple-600 dark:text-purple-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1))])):y("",!0)]),i.department.employees.length>0?(s(),n("div",ze,[e("div",De,[(s(!0),n(O,null,I(l.value,o=>(s(),n("div",{key:o.id,onClick:V=>w.$emit("employee-click",o),class:"flex items-center p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"},[u[8]||(u[8]=e("div",{class:"w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3"},[e("svg",{class:"w-4 h-4 text-gray-500 dark:text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1)),e("div",Le,[e("p",Se,v(o.full_name),1),e("p",Ae,v(o.position||"Dipendente"),1)]),o.is_manager?(s(),n("div",Be,u[7]||(u[7]=[e("span",{class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"}," Manager ",-1)]))):y("",!0)],8,Ee))),128))]),i.department.employees.length>W?(s(),n("div",Ne,[e("button",{onClick:u[0]||(u[0]=o=>k.value=!k.value),class:"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"},v(k.value?"Mostra meno":`Mostra altri ${i.department.employees.length-W}`),1)])):y("",!0)])):y("",!0)]),p.value&&i.department.subdepartments.length>0?(s(),n("div",je,[e("div",Te,[u[9]||(u[9]=e("div",{class:"absolute -left-4 top-0 bottom-0 w-px bg-gray-300 dark:bg-gray-600"},null,-1)),u[10]||(u[10]=e("div",{class:"absolute -left-4 top-6 w-4 h-px bg-gray-300 dark:bg-gray-600"},null,-1)),(s(!0),n(O,null,I(i.department.subdepartments,o=>(s(),Y(T,{key:o.id,department:o,expanded:i.expanded,"search-query":i.searchQuery,onToggleNode:u[1]||(u[1]=V=>w.$emit("toggle-node",V)),onEmployeeClick:u[2]||(u[2]=V=>w.$emit("employee-click",V))},null,8,["department","expanded","search-query"]))),128))])])):y("",!0)])}}},He=J(Ve,[["__scopeId","data-v-31c5ce5d"]]),Fe={class:"department-list"},Oe={class:"p-6 border-b border-gray-200 dark:border-gray-700"},Ie={class:"flex items-center justify-between"},qe={class:"flex items-center"},Re={class:"text-xl font-semibold text-gray-900 dark:text-white"},Ge={key:0,class:"text-sm text-gray-500 dark:text-gray-400 mt-1"},Qe={class:"flex items-center mt-2 space-x-6 text-sm text-gray-500 dark:text-gray-400"},Ue={class:"flex items-center"},Pe={key:0,class:"flex items-center"},Ze={key:1,class:"flex items-center"},We={key:0,class:"flex items-center bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3"},Ye={class:"text-sm font-medium text-gray-900 dark:text-white"},Je={class:"text-xs text-gray-500 dark:text-gray-400"},Ke={key:0,class:"p-6"},Xe={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"},et=["onClick"],tt={class:"flex items-center"},rt={class:"flex-1 min-w-0"},at={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},st={class:"text-xs text-gray-500 dark:text-gray-400 truncate"},lt={class:"text-xs text-gray-500 dark:text-gray-400 truncate"},nt={class:"mt-3 flex items-center justify-between"},ot={class:"flex items-center space-x-2"},it={key:0,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"},dt={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},ut={key:1,class:"border-t border-gray-200 dark:border-gray-700"},ct={class:"p-6"},gt={class:"space-y-4"},mt={__name:"DepartmentList",props:{department:{type:Object,required:!0},searchQuery:{type:String,default:""}},emits:["employee-click"],setup(i,{emit:q}){const C=p=>new Intl.NumberFormat("it-IT").format(p),S=p=>p?new Date(p).toLocaleDateString("it-IT",{year:"numeric",month:"short"}):"",k=p=>({admin:"Admin",manager:"Manager",employee:"Dipendente"})[p]||p;return(p,l)=>{const $=X("DepartmentList",!0);return s(),n("div",Fe,[e("div",Oe,[e("div",Ie,[e("div",qe,[l[4]||(l[4]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-4"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("h3",Re,v(i.department.name),1),i.department.description?(s(),n("p",Ge,v(i.department.description),1)):y("",!0),e("div",Qe,[e("span",Ue,[l[1]||(l[1]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"})],-1)),G(" "+v(i.department.employee_count)+" dipendenti ",1)]),i.department.budget>0?(s(),n("span",Pe,[l[2]||(l[2]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}),e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.51-1.31c-.562-.649-1.413-1.076-2.353-1.253V5z","clip-rule":"evenodd"})],-1)),G(" Budget: €"+v(C(i.department.budget)),1)])):y("",!0),i.department.subdepartments.length>0?(s(),n("span",Ze,[l[3]||(l[3]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"})],-1)),G(" "+v(i.department.subdepartments.length)+" sotto-dipartimenti ",1)])):y("",!0)])])]),i.department.manager?(s(),n("div",We,[l[6]||(l[6]=e("div",{class:"w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mr-3"},[e("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("p",Ye,v(i.department.manager.full_name),1),l[5]||(l[5]=e("p",{class:"text-xs text-gray-500 dark:text-gray-400"},"Manager del Dipartimento",-1)),e("p",Je,v(i.department.manager.email),1)])])):y("",!0)])]),i.department.employees.length>0?(s(),n("div",Ke,[l[8]||(l[8]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Dipendenti",-1)),e("div",Xe,[(s(!0),n(O,null,I(i.department.employees,m=>(s(),n("div",{key:m.id,onClick:w=>p.$emit("employee-click",m),class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer transition-colors"},[e("div",tt,[l[7]||(l[7]=e("div",{class:"w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3"},[e("svg",{class:"w-5 h-5 text-gray-500 dark:text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1)),e("div",rt,[e("p",at,v(m.full_name),1),e("p",st,v(m.position||"Dipendente"),1),e("p",lt,v(m.email),1)])]),e("div",nt,[e("div",ot,[m.is_manager?(s(),n("span",it," Manager ")):y("",!0),e("span",{class:ee(["inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",m.role==="admin"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":m.role==="manager"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},v(k(m.role)),3)]),m.hire_date?(s(),n("div",dt,v(S(m.hire_date)),1)):y("",!0)])],8,et))),128))])])):y("",!0),i.department.subdepartments.length>0?(s(),n("div",ut,[e("div",ct,[l[9]||(l[9]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Sotto-dipartimenti",-1)),e("div",gt,[(s(!0),n(O,null,I(i.department.subdepartments,m=>(s(),Y($,{key:m.id,department:m,"search-query":i.searchQuery,onEmployeeClick:l[0]||(l[0]=w=>p.$emit("employee-click",w)),class:"ml-6 border-l-2 border-gray-200 dark:border-gray-700 pl-6"},null,8,["department","search-query"]))),128))])])])):y("",!0)])}}},pt=J(mt,[["__scopeId","data-v-d1eb3eb4"]]),vt={class:"org-chart-diagram"},bt={class:"mt-4 flex justify-center space-x-4"},yt={__name:"OrgChartDiagram",props:{orgData:{type:Array,required:!0},searchQuery:{type:String,default:""}},emits:["employee-click","department-click"],setup(i,{emit:q}){const C=i,S=q,k=z(null),p=z(!0);let l=null;const $=()=>{if(!k.value||!C.orgData.length)return;k.value.innerHTML="";const b=k.value,x=b.clientWidth,D=Math.max(600,b.clientHeight);l=document.createElementNS("http://www.w3.org/2000/svg","svg"),l.setAttribute("width",x),l.setAttribute("height",D),l.setAttribute("viewBox",`0 0 ${x} ${D}`),l.style.background="transparent";const E=document.createElementNS("http://www.w3.org/2000/svg","g");l.appendChild(E);const _=[],N=[],M=(d,h=null,f=0)=>{const g={id:`dept-${d.id}`,type:"department",name:d.name,description:d.description,level:f,employees:d.employees.length,budget:d.budget,manager:d.manager,x:0,y:0};_.push(g),h&&N.push({source:h.id,target:g.id}),d.employees.forEach((j,r)=>{const t={id:`emp-${j.id}`,type:"employee",name:j.full_name,position:j.position,isManager:j.is_manager,level:f+1,x:0,y:0,employee:j};_.push(t),N.push({source:g.id,target:t.id})}),d.subdepartments.forEach(j=>{M(j,g,f+1)})};C.orgData.forEach(d=>M(d)),m(_,x,D),N.forEach(d=>{const h=_.find(g=>g.id===d.source),f=_.find(g=>g.id===d.target);if(h&&f){const g=document.createElementNS("http://www.w3.org/2000/svg","line");g.setAttribute("x1",h.x),g.setAttribute("y1",h.y),g.setAttribute("x2",f.x),g.setAttribute("y2",f.y),g.setAttribute("stroke","#d1d5db"),g.setAttribute("stroke-width","2"),E.appendChild(g)}}),_.forEach(d=>{const h=document.createElementNS("http://www.w3.org/2000/svg","g");h.setAttribute("transform",`translate(${d.x}, ${d.y})`),h.style.cursor="pointer";const f=document.createElementNS("http://www.w3.org/2000/svg","circle");f.setAttribute("r",d.type==="department"?"25":"15"),f.setAttribute("fill",w(d)),f.setAttribute("stroke","#fff"),f.setAttribute("stroke-width","2"),h.appendChild(f);const g=document.createElementNS("http://www.w3.org/2000/svg","text");g.setAttribute("text-anchor","middle"),g.setAttribute("dy",d.type==="department"?"35":"25"),g.setAttribute("font-size","12"),g.setAttribute("fill","#374151"),g.textContent=u(d.name,15),h.appendChild(g),h.addEventListener("click",()=>{d.type==="department"?S("department-click",{id:d.id.replace("dept-",""),name:d.name}):S("employee-click",d.employee)}),h.addEventListener("mouseenter",()=>{f.setAttribute("stroke-width","3"),f.setAttribute("stroke","#3b82f6")}),h.addEventListener("mouseleave",()=>{f.setAttribute("stroke-width","2"),f.setAttribute("stroke","#fff")}),E.appendChild(h)}),b.appendChild(l)},m=(b,x,D)=>{const E={};b.forEach(_=>{E[_.level]||(E[_.level]=[]),E[_.level].push(_)}),Object.keys(E).forEach(_=>{const N=E[_],M=p.value?parseInt(_)*(D/Object.keys(E).length)+50:D/2;N.forEach((d,h)=>{p.value?(d.x=x/(N.length+1)*(h+1),d.y=M):(d.x=parseInt(_)*(x/Object.keys(E).length)+50,d.y=D/(N.length+1)*(h+1))})})},w=b=>b.type==="department"?"#3b82f6":b.isManager?"#8b5cf6":"#10b981",u=(b,x)=>b.length>x?b.substring(0,x)+"...":b,T=()=>{if(l){const b=P();A(b*1.2)}},o=()=>{if(l){const b=P();A(b*.8)}},V=()=>{l&&A(1)},P=()=>{const D=(l.querySelector("g").getAttribute("transform")||"").match(/scale\(([^)]+)\)/);return D?parseFloat(D[1]):1},A=b=>{l.querySelector("g").setAttribute("transform",`scale(${b})`)},B=()=>{p.value=!p.value,K(()=>{$()})};return le(()=>C.orgData,()=>{K(()=>{$()})},{deep:!0}),ne(()=>{K(()=>{$()}),window.addEventListener("resize",$)}),oe(()=>{window.removeEventListener("resize",$)}),(b,x)=>(s(),n("div",vt,[e("div",{ref_key:"chartContainer",ref:k,class:"chart-container bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 rounded-lg p-6 min-h-96"},null,512),e("div",bt,[e("button",{onClick:T,class:"px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"},x[0]||(x[0]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1)])),e("button",{onClick:o,class:"px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"},x[1]||(x[1]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 12H4"})],-1)])),e("button",{onClick:V,class:"px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"}," Reset "),e("button",{onClick:B,class:"px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"},v(p.value?"Layout Orizzontale":"Layout Verticale"),1)]),x[2]||(x[2]=ie('<div class="mt-4 bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700" data-v-c85a321c><h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2" data-v-c85a321c>Legenda</h4><div class="flex flex-wrap gap-4 text-xs" data-v-c85a321c><div class="flex items-center" data-v-c85a321c><div class="w-4 h-4 bg-blue-500 rounded mr-2" data-v-c85a321c></div><span class="text-gray-600 dark:text-gray-400" data-v-c85a321c>Dipartimento</span></div><div class="flex items-center" data-v-c85a321c><div class="w-4 h-4 bg-purple-500 rounded mr-2" data-v-c85a321c></div><span class="text-gray-600 dark:text-gray-400" data-v-c85a321c>Manager</span></div><div class="flex items-center" data-v-c85a321c><div class="w-4 h-4 bg-green-500 rounded mr-2" data-v-c85a321c></div><span class="text-gray-600 dark:text-gray-400" data-v-c85a321c>Dipendente</span></div></div></div>',1))]))}},xt=J(yt,[["__scopeId","data-v-c85a321c"]]),ht={class:"space-y-6"},ft={class:"flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3"},kt={class:"relative"},wt=["disabled"],Ct={key:0,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},$t={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},_t={key:0,class:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"},Mt={class:"flex flex-wrap gap-2"},zt=["onClick"],Dt={key:2,class:"flex justify-center items-center py-12"},Et={key:3,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},Lt={class:"flex"},St={class:"mt-1 text-sm text-red-700 dark:text-red-300"},At={key:4,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"},Bt={key:0,class:"p-6"},Nt={class:"orgchart-container overflow-x-auto"},jt={class:"orgchart-tree min-w-max"},Tt={key:1,class:"divide-y divide-gray-200 dark:divide-gray-700"},Vt={key:2,class:"p-6"},Ht={key:5,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12"},Ft={class:"text-center"},Ot={class:"mt-6"},It={__name:"PersonnelOrgChart",setup(i){const q=de(),C=z(!1),S=z(null),k=z([]),p=z(null),l=z(null),$=z(""),m=z("tree"),w=z(new Set),u=z(!1),T=z(!1),o=z({hierarchyLevel:"",departmentSize:"",budgetRange:"",hasManager:""}),V=F(()=>[{id:"tree",label:"Albero",icon:"bars-arrow-down"},{id:"list",label:"Lista",icon:"list-bullet"},{id:"chart",label:"Chart",icon:"chart-bar"}]),P=F(()=>{var r,t,c;return l.value?[{id:"employees",label:`Dipendenti ${B.value?"Filtrati":"Totali"}`,value:l.value.total_employees,format:"number",icon:"users",iconBgColor:"bg-blue-100 dark:bg-blue-900",iconColor:"text-blue-600 dark:text-blue-400",subtitle:B.value?`di ${((r=p.value)==null?void 0:r.total_employees)||0} totali`:null},{id:"departments",label:`Dipartimenti ${B.value?"Filtrati":"Totali"}`,value:l.value.total_departments,format:"number",icon:"building-office",iconBgColor:"bg-green-100 dark:bg-green-900",iconColor:"text-green-600 dark:text-green-400",subtitle:B.value?`di ${((t=p.value)==null?void 0:t.total_departments)||0} totali`:null},{id:"managers",label:`Manager ${B.value?"Filtrati":"Totali"}`,value:l.value.total_managers,format:"number",icon:"user-circle",iconBgColor:"bg-purple-100 dark:bg-purple-900",iconColor:"text-purple-600 dark:text-purple-400",subtitle:B.value?`di ${((c=p.value)==null?void 0:c.total_managers)||0} totali`:null}]:[]}),A=F(()=>{let r=k.value;if(B.value&&(r=r.map(t=>x(t)).filter(t=>t!==null)),$.value.trim()){const t=$.value.toLowerCase();r=r.map(c=>D(c,t)).filter(c=>c!==null)}return r}),B=F(()=>Object.values(o.value).some(r=>r!=="")),b=F(()=>{const r=[],t={hierarchyLevel:{managers:"Solo Manager",employees:"Solo Dipendenti",top_level:"Dirigenti"},departmentSize:{small:"Piccoli Dipartimenti",medium:"Medi Dipartimenti",large:"Grandi Dipartimenti"},budgetRange:{low:"Budget Basso",medium:"Budget Medio",high:"Budget Alto"},hasManager:{true:"Con Manager",false:"Senza Manager"}};return Object.entries(o.value).forEach(([c,a])=>{a&&t[c]&&t[c][a]&&r.push({key:c,label:t[c][a]})}),r}),x=r=>{if(o.value.departmentSize){const a=r.employee_count;if(o.value.departmentSize==="small"&&a>5||o.value.departmentSize==="medium"&&(a<6||a>15)||o.value.departmentSize==="large"&&a<16)return null}if(o.value.budgetRange&&r.budget){const a=r.budget;if(o.value.budgetRange==="low"&&a>=5e4||o.value.budgetRange==="medium"&&(a<5e4||a>2e5)||o.value.budgetRange==="high"&&a<=2e5)return null}if(o.value.hasManager){const a=r.manager_id!==null;if(o.value.hasManager==="true"&&!a||o.value.hasManager==="false"&&a)return null}let t=r.employees;o.value.hierarchyLevel&&(o.value.hierarchyLevel==="managers"?t=r.employees.filter(a=>a.is_manager||a.role==="manager"||a.role==="admin"):o.value.hierarchyLevel==="employees"?t=r.employees.filter(a=>!a.is_manager&&a.role==="employee"):o.value.hierarchyLevel==="top_level"&&(t=r.employees.filter(a=>a.role==="admin")));const c=r.subdepartments.map(a=>x(a)).filter(a=>a!==null);return t.length>0||c.length>0?{...r,employees:t,subdepartments:c,employee_count:t.length}:null},D=(r,t)=>{var Q;const c=((Q=r.name)==null?void 0:Q.toLowerCase().includes(t))||r.description&&r.description.toLowerCase().includes(t),a=r.employees.filter(L=>{var te,re;return((te=L.full_name)==null?void 0:te.toLowerCase().includes(t))||((re=L.email)==null?void 0:re.toLowerCase().includes(t))||L.position&&L.position.toLowerCase().includes(t)}),R=r.subdepartments.map(L=>D(L,t)).filter(L=>L!==null);return c||a.length>0||R.length>0?{...r,employees:a.length>0?a:r.employees,subdepartments:R}:null},E=async()=>{C.value=!0,S.value=null;try{const r=await fetch("/api/personnel/orgchart",{credentials:"include"});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const t=await r.json();if(t.success)k.value=t.data.orgchart||[],p.value=t.data.stats||{},M(),k.value.length>0&&k.value.forEach(c=>{w.value.add(c.id)});else throw new Error(t.message||"Errore nel caricamento dell'organigramma")}catch(r){console.error("Error loading org chart:",r),S.value=r.message}finally{C.value=!1}},_=r=>{w.value.has(r)?w.value.delete(r):w.value.add(r)},N=()=>{if(u.value)w.value.clear(),u.value=!1;else{const r=t=>{t.forEach(c=>{w.value.add(c.id),c.subdepartments&&c.subdepartments.length>0&&r(c.subdepartments)})};r(k.value),u.value=!0}},M=()=>{const r=t=>{let c=0,a=0,R=0;const Q=L=>{a++,c+=L.employees.length,L.manager_id&&R++,L.subdepartments.forEach(Q)};return t.forEach(Q),{total_employees:c,total_departments:a,total_managers:R}};l.value=r(A.value)},d=r=>{m.value=r,r==="chart"&&(u.value=!1)},h=()=>{o.value={hierarchyLevel:"",departmentSize:"",budgetRange:"",hasManager:""},M()},f=r=>{o.value[r]="",M()},g=r=>{q.push(`/app/personnel/${r.id}`)},j=r=>{console.log("Department clicked:",r.name)};return le(A,()=>{M()},{deep:!0}),ne(()=>{E()}),(r,t)=>{const c=X("router-link");return s(),n("div",ht,[H(ce,{title:"Organigramma Aziendale",subtitle:"Struttura organizzativa e gerarchia aziendale",icon:"building-office","icon-color":"text-blue-600"},{actions:ae(()=>[e("div",ft,[e("div",kt,[U(e("input",{"onUpdate:modelValue":t[0]||(t[0]=a=>$.value=a),onInput:M,type:"text",placeholder:"Cerca dipendente o dipartimento...",class:"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,544),[[ue,$.value]]),H(se,{name:"magnifying-glass",size:"sm",class:"absolute left-3 top-2.5 text-gray-400"})]),H(ge,{modes:V.value,"active-mode":m.value,onModeChange:d},null,8,["modes","active-mode"]),m.value==="tree"?(s(),n("button",{key:0,onClick:N,disabled:A.value.length===0,class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200"},v(u.value?"Comprimi Tutto":"Espandi Tutto"),9,wt)):y("",!0),e("button",{onClick:t[1]||(t[1]=a=>T.value=!T.value),class:ee(["px-4 py-2 rounded-lg transition-colors duration-200 flex items-center",T.value?"bg-green-600 hover:bg-green-700 text-white":"bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300"])},[H(se,{name:"adjustments-horizontal",size:"sm",class:"mr-2"}),t[6]||(t[6]=G(" Filtri "))],2)])]),_:1}),T.value?(s(),n("div",Ct,[e("div",{class:"flex items-center justify-between mb-4"},[t[7]||(t[7]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Filtri Avanzati",-1)),e("button",{onClick:h,class:"text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"}," Cancella tutti ")]),e("div",$t,[e("div",null,[t[9]||(t[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Livello Gerarchico",-1)),U(e("select",{"onUpdate:modelValue":t[2]||(t[2]=a=>o.value.hierarchyLevel=a),onChange:M,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[8]||(t[8]=[e("option",{value:""},"Tutti i livelli",-1),e("option",{value:"managers"},"Solo Manager",-1),e("option",{value:"employees"},"Solo Dipendenti",-1),e("option",{value:"top_level"},"Dirigenti",-1)]),544),[[Z,o.value.hierarchyLevel]])]),e("div",null,[t[11]||(t[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Dimensione Dipartimento",-1)),U(e("select",{"onUpdate:modelValue":t[3]||(t[3]=a=>o.value.departmentSize=a),onChange:M,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[10]||(t[10]=[e("option",{value:""},"Tutte le dimensioni",-1),e("option",{value:"small"},"Piccoli (1-5 dipendenti)",-1),e("option",{value:"medium"},"Medi (6-15 dipendenti)",-1),e("option",{value:"large"},"Grandi (16+ dipendenti)",-1)]),544),[[Z,o.value.departmentSize]])]),e("div",null,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Budget Dipartimento",-1)),U(e("select",{"onUpdate:modelValue":t[4]||(t[4]=a=>o.value.budgetRange=a),onChange:M,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[12]||(t[12]=[e("option",{value:""},"Tutti i budget",-1),e("option",{value:"low"},"Basso (< €50k)",-1),e("option",{value:"medium"},"Medio (€50k - €200k)",-1),e("option",{value:"high"},"Alto (> €200k)",-1)]),544),[[Z,o.value.budgetRange]])]),e("div",null,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Stato Manager",-1)),U(e("select",{"onUpdate:modelValue":t[5]||(t[5]=a=>o.value.hasManager=a),onChange:M,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[14]||(t[14]=[e("option",{value:""},"Tutti",-1),e("option",{value:"true"},"Con Manager",-1),e("option",{value:"false"},"Senza Manager",-1)]),544),[[Z,o.value.hasManager]])])]),B.value?(s(),n("div",_t,[e("div",Mt,[t[17]||(t[17]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400 mr-2"},"Filtri attivi:",-1)),(s(!0),n(O,null,I(b.value,a=>(s(),n("span",{key:a.key,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},[G(v(a.label)+" ",1),e("button",{onClick:R=>f(a.key),class:"ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"},t[16]||(t[16]=[e("svg",{class:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,zt)]))),128))])])):y("",!0)])):y("",!0),l.value?(s(),Y(me,{key:1,stats:P.value},null,8,["stats"])):y("",!0),C.value?(s(),n("div",Dt,t[18]||(t[18]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):S.value?(s(),n("div",Et,[e("div",Lt,[t[20]||(t[20]=e("svg",{class:"w-5 h-5 text-red-400 mr-3 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[19]||(t[19]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento",-1)),e("p",St,v(S.value),1)])])])):k.value.length>0?(s(),n("div",At,[m.value==="tree"?(s(),n("div",Bt,[e("div",Nt,[e("div",jt,[(s(!0),n(O,null,I(A.value,a=>(s(),Y(He,{key:a.id,department:a,expanded:w.value,"search-query":$.value,onToggleNode:_,onEmployeeClick:g},null,8,["department","expanded","search-query"]))),128))])])])):m.value==="list"?(s(),n("div",Tt,[(s(!0),n(O,null,I(A.value,a=>(s(),n("div",{key:a.id},[H(pt,{department:a,"search-query":$.value,onEmployeeClick:g},null,8,["department","search-query"])]))),128))])):m.value==="chart"?(s(),n("div",Vt,[H(xt,{"org-data":A.value,"search-query":$.value,onEmployeeClick:g,onDepartmentClick:j},null,8,["org-data","search-query"])])):y("",!0)])):C.value?y("",!0):(s(),n("div",Ht,[e("div",Ft,[t[22]||(t[22]=e("svg",{class:"mx-auto h-16 w-16 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),t[23]||(t[23]=e("h3",{class:"mt-4 text-lg font-medium text-gray-900 dark:text-white"},"Nessun dipartimento configurato",-1)),t[24]||(t[24]=e("p",{class:"mt-2 text-sm text-gray-500 dark:text-gray-400"}," Inizia creando la struttura organizzativa aziendale ",-1)),e("div",Ot,[H(c,{to:"/app/personnel/departments",class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},{default:ae(()=>t[21]||(t[21]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),G(" Gestisci Dipartimenti ")])),_:1,__:[21]})])])]))])}}},Pt=J(It,[["__scopeId","data-v-1ddeebaf"]]);export{Pt as default};
