import{r as c,x as H,b as r,I as V,l as d,j as e,t as n,F as N,p as q,s as p,A as C,B as v,C as x,o as i}from"./vendor.js";import{_ as R,c as L}from"./app.js";const U={class:"careers-page"},$={key:0,class:"flex justify-center py-12"},I={key:1,class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12"},J={class:"text-center"},F={class:"text-red-600"},S={key:2,class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12"},P={key:0,class:"text-center mb-12"},j={class:"text-lg text-gray-600"},K={key:1,class:"text-center py-16"},O={key:2,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},G={class:"p-6"},Q={class:"flex items-start justify-between mb-4"},W={class:"flex-1"},X={class:"text-lg font-semibold text-gray-900 mb-2"},Y={class:"flex items-center text-sm text-gray-600 mb-2"},Z={key:0,class:"ml-2 text-green-600"},ee={class:"space-y-3 mb-6"},te={class:"flex items-center text-sm text-gray-600"},se={key:0,class:"flex items-center text-sm text-gray-600"},oe={key:1,class:"flex items-center text-sm text-gray-600"},ae={key:0,class:"mb-6"},re={class:"text-sm text-gray-600 line-clamp-3"},ie={class:"flex space-x-3"},le=["onClick"],ne=["onClick"],de={class:"mt-3"},ue={class:"flex items-start justify-between mb-6"},ce={class:"flex-1"},me={class:"text-2xl font-bold text-gray-900 mb-2"},pe={class:"flex items-center space-x-4 text-sm text-gray-600"},ve={class:"flex items-center"},xe={key:0},ye={class:"max-h-96 overflow-y-auto space-y-6"},ge={key:0},be=["innerHTML"],fe={key:1},he=["innerHTML"],_e={key:2},ke=["innerHTML"],we={key:3},Ce={class:"text-gray-600"},Me={class:"flex justify-end space-x-3 mt-6 pt-6 border-t"},ze={class:"mt-3"},Te={class:"text-xl font-bold text-gray-900 mb-4"},Ve={class:"space-y-4"},Le={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ee={class:"flex justify-end space-x-3 mt-6"},Be=["disabled"],Ae={__name:"Careers",setup(De){const h=c(!0),y=c(null),m=c([]),a=c(null),_=c(!1),g=c(null),b=c(!1),u=c({first_name:"",last_name:"",email:"",phone:"",cover_letter:""}),E=async()=>{var o,t;h.value=!0,y.value=null;try{const l=await L.get("/api/recruiting/public/job-postings");if(l.data.success)m.value=l.data.data.job_postings||[];else throw new Error(l.data.message||"Errore nel caricamento posizioni")}catch(l){y.value=((t=(o=l.response)==null?void 0:o.data)==null?void 0:t.message)||l.message||"Errore nel caricamento delle posizioni disponibili",console.error("Error loading public job postings:",l)}finally{h.value=!1}},B=o=>{a.value=o},f=()=>{a.value=null},M=o=>{g.value=o,_.value=!0,f()},k=()=>{_.value=!1,g.value=null,u.value={first_name:"",last_name:"",email:"",phone:"",cover_letter:""}},A=async()=>{var o,t;b.value=!0;try{const l=await L.post("/api/recruiting/public/applications",{job_posting_id:g.value.id,...u.value});if(l.data.success)alert("Candidatura inviata con successo! Ti contatteremo presto."),k();else throw new Error(l.data.message||"Errore nell'invio della candidatura")}catch(l){alert(((t=(o=l.response)==null?void 0:o.data)==null?void 0:t.message)||l.message||"Errore nell'invio della candidatura"),console.error("Error submitting application:",l)}finally{b.value=!1}},z=o=>({full_time:"Tempo pieno",part_time:"Tempo parziale",contract:"Contratto",intern:"Stage"})[o]||o,T=(o,t,l="EUR")=>{if(!o&&!t)return"Da concordare";const s=new Intl.NumberFormat("it-IT",{style:"currency",currency:l});return o&&t?`${s.format(o)} - ${s.format(t)}`:o?`Da ${s.format(o)}`:`Fino a ${s.format(t)}`},w=o=>o.replace(/\n/g,"<br>");return H(()=>{E()}),(o,t)=>{var l;return i(),r("div",U,[t[27]||(t[27]=V('<section class="bg-gradient-to-r from-primary-600 to-primary-700 text-white py-16" data-v-72b55b3c><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" data-v-72b55b3c><div class="text-center" data-v-72b55b3c><h1 class="text-4xl font-bold mb-4" data-v-72b55b3c>Lavora con noi</h1><p class="text-xl text-primary-100 max-w-3xl mx-auto" data-v-72b55b3c> Unisciti al nostro team di professionisti e contribuisci alla crescita dell&#39;innovazione italiana </p></div></div></section>',1)),h.value?(i(),r("div",$,t[8]||(t[8]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):y.value?(i(),r("div",I,[e("div",J,[e("p",F,n(y.value),1)])])):(i(),r("section",S,[m.value.length>0?(i(),r("div",P,[t[9]||(t[9]=e("h2",{class:"text-3xl font-bold text-gray-900 mb-4"},"Posizioni Aperte",-1)),e("p",j,n(m.value.length)+" "+n(m.value.length===1?"posizione disponibile":"posizioni disponibili"),1)])):d("",!0),m.value.length===0?(i(),r("div",K,t[10]||(t[10]=[V('<div class="max-w-md mx-auto" data-v-72b55b3c><svg class="w-24 h-24 text-gray-400 mx-auto mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" data-v-72b55b3c><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6.5" data-v-72b55b3c></path></svg><h3 class="text-xl font-semibold text-gray-900 mb-2" data-v-72b55b3c> Nessuna posizione aperta al momento </h3><p class="text-gray-600 mb-6" data-v-72b55b3c> Non ci sono attualmente posizioni disponibili, ma continua a seguirci per future opportunità. </p><a href="/contact" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors" data-v-72b55b3c> Contattaci per candidature spontanee </a></div>',1)]))):d("",!0),m.value.length>0?(i(),r("div",O,[(i(!0),r(N,null,q(m.value,s=>(i(),r("div",{key:s.id,class:"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200"},[e("div",G,[e("div",Q,[e("div",W,[e("h3",X,n(s.title),1),e("div",Y,[t[11]||(t[11]=e("svg",{class:"w-4 h-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),p(" "+n(s.location||"Da definire")+" ",1),s.remote_allowed?(i(),r("span",Z,"• Remote OK")):d("",!0)])]),t[12]||(t[12]=e("span",{class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"}," Aperta ",-1))]),e("div",ee,[e("div",te,[t[13]||(t[13]=e("svg",{class:"w-4 h-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),p(" "+n(z(s.employment_type)),1)]),s.department?(i(),r("div",se,[t[14]||(t[14]=e("svg",{class:"w-4 h-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"})],-1)),p(" "+n(s.department.name),1)])):d("",!0),s.salary_min||s.salary_max?(i(),r("div",oe,[t[15]||(t[15]=e("svg",{class:"w-4 h-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1)),p(" "+n(T(s.salary_min,s.salary_max,s.salary_currency)),1)])):d("",!0)]),s.description?(i(),r("div",ae,[e("p",re,n(s.description.substring(0,150))+n(s.description.length>150?"...":""),1)])):d("",!0),e("div",ie,[e("button",{onClick:D=>B(s),class:"flex-1 bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700 transition-colors"}," Visualizza Dettagli ",8,le),e("button",{onClick:D=>M(s),class:"flex-1 bg-white text-primary-600 border border-primary-600 px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-50 transition-colors"}," Candidati ",8,ne)])])]))),128))])):d("",!0)])),a.value?(i(),r("div",{key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:f},[e("div",{class:"relative top-20 mx-auto p-5 border max-w-4xl shadow-lg rounded-md bg-white",onClick:t[1]||(t[1]=C(()=>{},["stop"]))},[e("div",de,[e("div",ue,[e("div",ce,[e("h3",me,n(a.value.title),1),e("div",pe,[e("span",ve,[t[16]||(t[16]=e("svg",{class:"w-4 h-4 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"})],-1)),p(" "+n(a.value.location||"Da definire"),1)]),e("span",null,n(z(a.value.employment_type)),1),a.value.department?(i(),r("span",xe,n(a.value.department.name),1)):d("",!0)])]),e("button",{onClick:f,class:"text-gray-400 hover:text-gray-600"},t[17]||(t[17]=[e("svg",{class:"w-6 h-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",ye,[a.value.description?(i(),r("div",ge,[t[18]||(t[18]=e("h4",{class:"text-lg font-semibold text-gray-900 mb-3"},"Descrizione",-1)),e("div",{class:"prose text-gray-600",innerHTML:w(a.value.description)},null,8,be)])):d("",!0),a.value.requirements?(i(),r("div",fe,[t[19]||(t[19]=e("h4",{class:"text-lg font-semibold text-gray-900 mb-3"},"Requisiti",-1)),e("div",{class:"prose text-gray-600",innerHTML:w(a.value.requirements)},null,8,he)])):d("",!0),a.value.responsibilities?(i(),r("div",_e,[t[20]||(t[20]=e("h4",{class:"text-lg font-semibold text-gray-900 mb-3"},"Responsabilità",-1)),e("div",{class:"prose text-gray-600",innerHTML:w(a.value.responsibilities)},null,8,ke)])):d("",!0),a.value.salary_min||a.value.salary_max?(i(),r("div",we,[t[21]||(t[21]=e("h4",{class:"text-lg font-semibold text-gray-900 mb-3"},"Retribuzione",-1)),e("p",Ce,n(T(a.value.salary_min,a.value.salary_max,a.value.salary_currency)),1)])):d("",!0)]),e("div",Me,[e("button",{onClick:f,class:"px-6 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"}," Chiudi "),e("button",{onClick:t[0]||(t[0]=s=>M(a.value)),class:"px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"}," Candidati ora ")])])])])):d("",!0),_.value?(i(),r("div",{key:4,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:k},[e("div",{class:"relative top-20 mx-auto p-5 border max-w-2xl shadow-lg rounded-md bg-white",onClick:t[7]||(t[7]=C(()=>{},["stop"]))},[e("div",ze,[e("h3",Te," Candidatura per: "+n((l=g.value)==null?void 0:l.title),1),e("form",{onSubmit:C(A,["prevent"])},[e("div",Ve,[e("div",Le,[e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Nome * ",-1)),v(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>u.value.first_name=s),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500"},null,512),[[x,u.value.first_name]])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Cognome * ",-1)),v(e("input",{"onUpdate:modelValue":t[3]||(t[3]=s=>u.value.last_name=s),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500"},null,512),[[x,u.value.last_name]])])]),e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Email * ",-1)),v(e("input",{"onUpdate:modelValue":t[4]||(t[4]=s=>u.value.email=s),type:"email",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500"},null,512),[[x,u.value.email]])]),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Telefono ",-1)),v(e("input",{"onUpdate:modelValue":t[5]||(t[5]=s=>u.value.phone=s),type:"tel",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500"},null,512),[[x,u.value.phone]])]),e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Lettera di motivazione ",-1)),v(e("textarea",{"onUpdate:modelValue":t[6]||(t[6]=s=>u.value.cover_letter=s),rows:"4",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500",placeholder:"Racconta perché sei interessato a questa posizione..."},null,512),[[x,u.value.cover_letter]])])]),e("div",Ee,[e("button",{type:"button",onClick:k,class:"px-6 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"}," Annulla "),e("button",{type:"submit",disabled:b.value,class:"px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors disabled:opacity-50"},n(b.value?"Invio...":"Invia Candidatura"),9,Be)])],32)])])])):d("",!0)])}}},qe=R(Ae,[["__scopeId","data-v-72b55b3c"]]);export{qe as default};
