import{_ as o,H as n}from"./app.js";import{b as a,o as s,F as i,p as c,n as d,e as l,s as g,t as m}from"./vendor.js";const u={class:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1"},y=["onClick"],p={__name:"ViewModeToggle",props:{modes:{type:Array,required:!0,validator:r=>r.every(e=>e.id&&e.label&&e.icon)},activeMode:{type:String,required:!0}},emits:["mode-change"],setup(r){return(e,x)=>(s(),a("div",u,[(s(!0),a(i,null,c(r.modes,t=>(s(),a("button",{key:t.id,onClick:_=>e.$emit("mode-change",t.id),class:d(["px-3 py-1 rounded-md text-sm font-medium transition-colors flex items-center",r.activeMode===t.id?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"])},[l(n,{name:t.icon,size:"sm",class:"mr-1"},null,8,["name"]),g(" "+m(t.label),1)],10,y))),128))]))}},v=o(p,[["__scopeId","data-v-15ec93c7"]]);export{v as V};
