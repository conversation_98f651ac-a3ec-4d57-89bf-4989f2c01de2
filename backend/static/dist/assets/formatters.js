function u(r,n=2,t=!0){if(r==null||isNaN(r))return 0;const e=Math.pow(10,n),i=Math.round(r*e)/e;return t?parseFloat(i.toFixed(n)):i}function o(r,n=!0){if(r==null||isNaN(r))return n?"0h":"0";const t=u(r,1),e=t%1===0?t.toString():t.toFixed(1);return n?`${e}h`:e}function a(r,n=!0,t=0){if(r==null||isNaN(r))return n?"0%":"0";const e=u(r,t),i=t===0?Math.round(e).toString():e.toFixed(t);return n?`${i}%`:i}function f(r,n="EUR",t="it-IT"){return r==null||isNaN(r)?new Intl.NumberFormat(t,{style:"currency",currency:n}).format(0):new Intl.NumberFormat(t,{style:"currency",currency:n}).format(r)}function c(r,n="it-IT",t=0){if(r==null||isNaN(r))return"0";const e=u(r,t);return new Intl.NumberFormat(n,{minimumFractionDigits:t,maximumFractionDigits:t}).format(e)}function m(r,n,t={}){switch(n){case"hours":return o(r,t.showUnit!==!1);case"percentage":return a(r,t.showUnit!==!1,t.decimals||0);case"currency":return f(r,t.currency,t.locale);case"number":return c(r,t.locale,t.decimals||0);default:return(r==null?void 0:r.toString())||"-"}}function d(r,n="short",t="it-IT"){if(!r)return"-";try{const e=typeof r=="string"?new Date(r):r;if(isNaN(e.getTime()))return"-";const i={short:{year:"numeric",month:"2-digit",day:"2-digit"},medium:{year:"numeric",month:"short",day:"numeric"},long:{weekday:"long",year:"numeric",month:"long",day:"numeric"},time:{hour:"2-digit",minute:"2-digit"},datetime:{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}};return new Intl.DateTimeFormat(t,i[n]||i.short).format(e)}catch(e){return console.error("Error formatting date:",e),"-"}}const s={percentage:(r,n,t=0)=>!n||n===0?0:u(r/n*100,t),utilization:(r,n,t=1)=>!n||n===0?0:u(r/n*100,t),variance:(r,n,t=1)=>u((r||0)-(n||0),t)};export{f as a,a as b,o as c,s as d,d as e,c as f,m as g,u as r};
