import{r as V,c as i,x as E,b as n,j as e,e as d,l,A as j,t as f,B as x,C as k,Q as F,s as m,I as S,h as A,k as p,f as H,q as P,o}from"./vendor.js";import{a as N,H as v}from"./app.js";import{S as z}from"./StandardButton.js";const I={class:"flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8"},L={class:"max-w-md w-full space-y-8"},U={class:"text-center"},q={class:"mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-primary-100 mb-6"},G={class:"bg-white py-8 px-6 shadow-lg rounded-lg border border-gray-200"},T={key:0,class:"rounded-md bg-red-50 p-4 border border-red-200"},D={class:"flex"},R={class:"text-sm text-red-700"},W={class:"space-y-4"},$={class:"flex items-center justify-between"},Q={class:"flex items-center"},J=["disabled"],K={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},X={key:0,class:"mt-6"},Y={key:1,class:"mt-6 space-y-3"},Z={key:2,class:"rounded-md bg-red-50 p-4 border border-red-200 mt-4"},ee={class:"flex"},se={class:"text-sm text-red-700"},te={class:"text-center"},re={class:"text-xs text-gray-500"},ne={__name:"Login",setup(oe){const M=P(),t=N(),r=V({username:"",password:"",remember:!1}),u=i(()=>t.loading),y=i(()=>t.error),b=i(()=>t.hasOAuthProviders),C=i(()=>t.isGoogleOAuthEnabled),B=i(()=>t.isMicrosoftOAuthEnabled),g=i(()=>t.oauthLoading),h=i(()=>t.oauthError);async function O(){(await t.login({username:r.value.username,password:r.value.password,remember:r.value.remember})).success&&M.push("/app/dashboard")}async function w(c){try{const s=await t.loginWithOAuth(c);s.success&&s.redirecting}catch(s){console.error("OAuth login error:",s)}}return E(async()=>{try{await t.initializeOAuth()}catch(c){console.warn("Failed to initialize OAuth providers:",c)}}),(c,s)=>{const _=H("router-link");return o(),n("div",I,[e("div",L,[e("div",U,[e("div",q,[d(v,{name:"lock-closed",size:"lg",color:"text-primary-600"})]),s[5]||(s[5]=e("h2",{class:"text-3xl font-bold text-gray-900 mb-2"}," Accedi al tuo account ",-1)),s[6]||(s[6]=e("p",{class:"text-sm text-gray-600"}," Accedi con le tue credenziali aziendali ",-1))]),e("div",G,[e("form",{onSubmit:j(O,["prevent"]),class:"space-y-6"},[y.value?(o(),n("div",T,[e("div",D,[d(v,{name:"exclamation-triangle",size:"sm",class:"text-red-400 mr-2 mt-0.5"}),e("div",R,f(y.value),1)])])):l("",!0),e("div",W,[e("div",null,[s[7]||(s[7]=e("label",{for:"username",class:"block text-sm font-medium text-gray-700 mb-1"}," Username ",-1)),x(e("input",{id:"username","onUpdate:modelValue":s[0]||(s[0]=a=>r.value.username=a),name:"username",type:"text",autocomplete:"username",required:"","data-testid":"username",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors",placeholder:"Inserisci il tuo username"},null,512),[[k,r.value.username]])]),e("div",null,[s[8]||(s[8]=e("label",{for:"password",class:"block text-sm font-medium text-gray-700 mb-1"}," Password ",-1)),x(e("input",{id:"password","onUpdate:modelValue":s[1]||(s[1]=a=>r.value.password=a),name:"password",type:"password",autocomplete:"current-password",required:"","data-testid":"password",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors",placeholder:"Inserisci la tua password"},null,512),[[k,r.value.password]])])]),e("div",$,[e("div",Q,[x(e("input",{id:"remember-me","onUpdate:modelValue":s[2]||(s[2]=a=>r.value.remember=a),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded transition-colors"},null,512),[[F,r.value.remember]]),s[9]||(s[9]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-700"}," Ricordami ",-1))]),s[10]||(s[10]=e("div",{class:"text-sm"},[e("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500 transition-colors"}," Password dimenticata? ")],-1))]),e("div",null,[e("button",{type:"submit",disabled:u.value,"data-testid":"login-button",class:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"},[u.value?(o(),n("span",K,[d(v,{name:"arrow-path",size:"sm",class:"text-primary-300 animate-spin"})])):l("",!0),m(" "+f(u.value?"Accesso in corso...":"Accedi"),1)],8,J)])],32),b.value?(o(),n("div",X,s[11]||(s[11]=[S('<div class="relative"><div class="absolute inset-0 flex items-center"><div class="w-full border-t border-gray-300"></div></div><div class="relative flex justify-center text-sm"><span class="px-2 bg-white text-gray-500">Oppure accedi con</span></div></div>',1)]))):l("",!0),b.value?(o(),n("div",Y,[C.value?(o(),A(z,{key:0,onClick:s[3]||(s[3]=a=>w("google")),variant:"secondary",size:"lg",class:"w-full",disabled:u.value||g.value},{default:p(()=>s[12]||(s[12]=[e("div",{class:"flex items-center justify-center"},[e("svg",{class:"w-5 h-5 mr-3 flex-shrink-0",viewBox:"0 0 24 24"},[e("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),e("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),e("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),e("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]),e("span",null,"Continua con Google")],-1)])),_:1,__:[12]},8,["disabled"])):l("",!0),B.value?(o(),A(z,{key:1,onClick:s[4]||(s[4]=a=>w("microsoft")),variant:"secondary",size:"lg",class:"w-full",disabled:u.value||g.value},{default:p(()=>s[13]||(s[13]=[e("div",{class:"flex items-center justify-center"},[e("svg",{class:"w-5 h-5 mr-3 flex-shrink-0",viewBox:"0 0 24 24"},[e("path",{fill:"#F25022",d:"M11.4 11.4H2.8V2.8h8.6v8.6z"}),e("path",{fill:"#00A4EF",d:"M21.2 11.4h-8.6V2.8h8.6v8.6z"}),e("path",{fill:"#7FBA00",d:"M11.4 21.2H2.8v-8.6h8.6v8.6z"}),e("path",{fill:"#FFB900",d:"M21.2 21.2h-8.6v-8.6h8.6v8.6z"})]),e("span",null,"Continua con Microsoft")],-1)])),_:1,__:[13]},8,["disabled"])):l("",!0),h.value?(o(),n("div",Z,[e("div",ee,[d(v,{name:"exclamation-triangle",size:"sm",class:"text-red-400 mr-2 mt-0.5"}),e("div",se,f(h.value),1)])])):l("",!0)])):l("",!0)]),e("div",te,[e("p",re,[s[16]||(s[16]=m(" Continuando, accetti i nostri ")),d(_,{to:"/privacy",class:"text-primary-600 hover:text-primary-500"},{default:p(()=>s[14]||(s[14]=[m(" Termini di Servizio ")])),_:1,__:[14]}),s[17]||(s[17]=m(" e la ")),d(_,{to:"/privacy",class:"text-primary-600 hover:text-primary-500"},{default:p(()=>s[15]||(s[15]=[m(" Privacy Policy ")])),_:1,__:[15]})])])])])}}};export{ne as default};
