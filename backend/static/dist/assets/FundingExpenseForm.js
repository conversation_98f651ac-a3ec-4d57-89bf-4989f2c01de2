import{r as g,c as f,u as C,G as M,x as B,b as h,e as D,j as a,A as U,B as u,C as b,H as T,I as F,Q as $,l as S,h as E,s as k,t as j,q as R,o as v}from"./vendor.js";import{u as G}from"./funding.js";import{d as H,i as V}from"./app.js";import{_ as L}from"./PageHeader.js";const O={class:"funding-expense-form"},P={class:"max-w-4xl mx-auto"},Q={class:"bg-white rounded-lg shadow-sm border p-6"},J={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},K={class:"md:col-span-2"},W={class:"flex items-center"},X={class:"md:col-span-2"},Y={class:"flex gap-3 pt-6 border-t"},Z=["disabled"],ee=["disabled"],ne={__name:"FundingExpenseForm",setup(ae){const n=C(),_=R(),p=G(),{showToast:l}=H(),c=g(!1),m=g(!1),r=g(null),d=g(null),i=f(()=>!!n.params.id),y=f(()=>n.params.id?parseInt(n.params.id):null),x=f(()=>n.query.applicationId?parseInt(n.query.applicationId):null),o=M({description:"",amount:"",expense_date:new Date().toISOString().split("T")[0],category:"other",is_eligible:!0,notes:"",application_id:x.value}),z=f(()=>{const t=[{name:"Funding",href:"/app/funding"},{name:"Spese",href:"/app/funding/expenses"}];return i.value?t.push({name:"Modifica",href:n.path}):t.push({name:"Nuova",href:n.path}),t});async function I(){if(i.value)try{r.value=await p.fetchExpense(y.value),o.description=r.value.description,o.amount=r.value.amount,o.expense_date=r.value.expense_date,o.category=r.value.category,o.is_eligible=r.value.is_eligible,o.notes=r.value.notes||"",o.application_id=r.value.application_id,d.value=r.value.application}catch(t){l("error","Errore nel caricamento della spesa"),console.error("Error loading expense:",t)}}async function N(){if(x.value)try{const t=await p.fetchApplications({id:x.value});t.applications&&t.applications.length>0&&(d.value=t.applications[0])}catch(t){console.error("Error loading application:",t)}}async function q(){c.value=!0;try{i.value?(await p.updateExpense(y.value,o),l("success","Spesa aggiornata con successo")):(await p.createExpense(o),l("success","Spesa creata con successo")),w()}catch(t){l("error",t.message||"Errore nel salvataggio della spesa"),console.error("Error saving expense:",t)}finally{c.value=!1}}async function A(){if(confirm("Sei sicuro di voler eliminare questa spesa?")){m.value=!0;try{await p.deleteExpense(y.value),l("success","Spesa eliminata con successo"),w()}catch(t){l("error",t.message||"Errore nell'eliminazione della spesa"),console.error("Error deleting expense:",t)}finally{m.value=!1}}}function w(){d.value?_.push(`/app/funding/reporting?applicationId=${d.value.id}`):_.push("/app/funding/expenses")}return B(async()=>{i.value?await I():await N()}),(t,e)=>(v(),h("div",O,[D(L,{title:i.value?"Modifica Spesa":"Nuova Spesa",subtitle:d.value?`Candidatura: ${d.value.project_title}`:"Aggiungi una spesa di finanziamento",breadcrumbs:z.value},null,8,["title","subtitle","breadcrumbs"]),a("div",P,[a("div",Q,[a("form",{onSubmit:U(q,["prevent"]),class:"space-y-6"},[a("div",J,[a("div",K,[e[6]||(e[6]=a("label",{for:"description",class:"block text-sm font-medium text-gray-700 mb-2"}," Descrizione * ",-1)),u(a("input",{id:"description","onUpdate:modelValue":e[0]||(e[0]=s=>o.description=s),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500",placeholder:"Descrizione della spesa..."},null,512),[[b,o.description]])]),a("div",null,[e[7]||(e[7]=a("label",{for:"amount",class:"block text-sm font-medium text-gray-700 mb-2"}," Importo (€) * ",-1)),u(a("input",{id:"amount","onUpdate:modelValue":e[1]||(e[1]=s=>o.amount=s),type:"number",step:"0.01",min:"0",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500",placeholder:"0.00"},null,512),[[b,o.amount]])]),a("div",null,[e[8]||(e[8]=a("label",{for:"expense_date",class:"block text-sm font-medium text-gray-700 mb-2"}," Data Spesa * ",-1)),u(a("input",{id:"expense_date","onUpdate:modelValue":e[2]||(e[2]=s=>o.expense_date=s),type:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500"},null,512),[[b,o.expense_date]])]),a("div",null,[e[10]||(e[10]=a("label",{for:"category",class:"block text-sm font-medium text-gray-700 mb-2"}," Categoria ",-1)),u(a("select",{id:"category","onUpdate:modelValue":e[3]||(e[3]=s=>o.category=s),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500"},e[9]||(e[9]=[F('<option value="other">Altro</option><option value="personnel">Personale</option><option value="equipment">Attrezzature</option><option value="travel">Viaggi e trasferte</option><option value="external">Servizi esterni</option><option value="materials">Materiali</option>',6)]),512),[[T,o.category]])]),a("div",null,[a("label",W,[u(a("input",{"onUpdate:modelValue":e[4]||(e[4]=s=>o.is_eligible=s),type:"checkbox",class:"rounded border-gray-300 text-brand-primary-600 focus:ring-brand-primary-500"},null,512),[[$,o.is_eligible]]),e[11]||(e[11]=a("span",{class:"ml-2 text-sm text-gray-700"},"Spesa eleggibile per il finanziamento",-1))])]),a("div",X,[e[12]||(e[12]=a("label",{for:"notes",class:"block text-sm font-medium text-gray-700 mb-2"}," Note ",-1)),u(a("textarea",{id:"notes","onUpdate:modelValue":e[5]||(e[5]=s=>o.notes=s),rows:"3",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500",placeholder:"Note aggiuntive..."},null,512),[[b,o.notes]])])]),a("div",Y,[a("button",{type:"submit",disabled:c.value,class:"px-4 py-2 bg-brand-primary-600 text-white rounded-md hover:bg-brand-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"},[c.value?(v(),E(V,{key:0,size:"sm",class:"inline mr-2"})):S("",!0),k(" "+j(i.value?"Aggiorna Spesa":"Crea Spesa"),1)],8,Z),a("button",{type:"button",onClick:w,class:"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"}," Annulla "),i.value&&r.value?(v(),h("button",{key:0,type:"button",onClick:A,disabled:m.value,class:"ml-auto px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"},[m.value?(v(),E(V,{key:0,size:"sm",class:"inline mr-2"})):S("",!0),e[13]||(e[13]=k(" Elimina "))],8,ee)):S("",!0)])],32)])])]))}};export{ne as default};
