import{r as h,c as w,x as A,b as n,j as e,l as b,B as S,H as U,e as o,s as E,n as g,t as d,F as T,p as H,q as I,u as P,o as i}from"./vendor.js";import{f as q,H as a,c as R}from"./app.js";const $={class:"py-6"},G={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},L={class:"mt-4 md:mt-0 flex space-x-3"},J=["disabled"],Q={key:0,class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},W={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},X={class:"p-5"},Y={class:"flex items-center"},Z={class:"flex-shrink-0"},K={class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},D={class:"ml-5 w-0 flex-1"},ee={class:"text-lg font-medium text-gray-900 dark:text-white"},te={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},se={class:"p-5"},re={class:"flex items-center"},oe={class:"flex-shrink-0"},de={class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},ae={class:"ml-5 w-0 flex-1"},le={class:"text-lg font-medium text-gray-900 dark:text-white"},ne={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},ie={class:"p-5"},ce={class:"flex items-center"},ue={class:"flex-shrink-0"},ge={class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},xe={class:"ml-5 w-0 flex-1"},me={class:"text-lg font-medium text-gray-900 dark:text-white"},be={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},ye={class:"p-5"},fe={class:"flex items-center"},pe={class:"flex-shrink-0"},he={class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},ve={class:"ml-5 w-0 flex-1"},_e={class:"text-lg font-medium text-gray-900 dark:text-white"},we={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},ke=["onClick"],ze={class:"p-6"},je={class:"flex items-center justify-between"},Me={class:"flex items-center"},Ce={class:"ml-4"},Fe={class:"text-lg font-medium text-gray-900 dark:text-white"},Ne={class:"text-sm text-gray-500 dark:text-gray-400"},Be={class:"text-right"},Oe={class:"text-sm"},Ve={key:2,class:"text-center py-12"},Ae={key:3,class:"text-center py-12"},Te={__name:"DraftsDashboard",setup(Se){const k=I();P();const z=q(),c=h(!1),y=h("30"),f=h({}),x=h(null),j={performance_reviews:"personnel_module",job_postings:"recruiting_module",proposals:"sales_module",pre_invoices:"invoicing_module",invoices:"invoicing_module",case_studies:"business_intelligence_module",technical_offers:"business_intelligence_module",timesheets:"timesheet_module",funding_applications:"funding_module",news:"communications_module"},u=w(()=>{if(!f.value)return{};const s={};return Object.entries(f.value).forEach(([t,r])=>{const l=j[t];(!l||z.isFeatureEnabled(l))&&(s[t]=r)}),s}),p=w(()=>{if(!x.value||!u.value)return x.value;const s=Object.values(u.value),t=s.reduce((l,m)=>l+m.count,0);let r=null;return t>0&&(r=s.reduce((m,_)=>_.count>m.count?_:m,{count:0}).module),{...x.value,total_drafts:t,most_used_module:r,average_per_module:s.length>0?Math.round(t/s.length*10)/10:0}}),v=async()=>{c.value=!0;try{const s=await R.get("/api/drafts/overview",{params:{days:y.value}});s.data.success&&(f.value=s.data.data.overview,x.value=s.data.data.stats)}catch(s){console.error("Error loading drafts overview:",s),f.value={},x.value=null}finally{c.value=!1}},M=(s,t)=>{k.push(`/app/drafts/module/${s}`)},C=()=>{v()},F=s=>{const t={blue:"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center",green:"w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center",purple:"w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center",yellow:"w-10 h-10 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center",red:"w-10 h-10 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center",indigo:"w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center",gray:"w-10 h-10 bg-gray-100 dark:bg-gray-900 rounded-lg flex items-center justify-center",emerald:"w-10 h-10 bg-emerald-100 dark:bg-emerald-900 rounded-lg flex items-center justify-center",orange:"w-10 h-10 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center",cyan:"w-10 h-10 bg-cyan-100 dark:bg-cyan-900 rounded-lg flex items-center justify-center"};return t[s]||t.blue},N=s=>{const t={blue:"w-6 h-6 text-blue-600 dark:text-blue-400",green:"w-6 h-6 text-green-600 dark:text-green-400",purple:"w-6 h-6 text-purple-600 dark:text-purple-400",yellow:"w-6 h-6 text-yellow-600 dark:text-yellow-400",red:"w-6 h-6 text-red-600 dark:text-red-400",indigo:"w-6 h-6 text-indigo-600 dark:text-indigo-400",gray:"w-6 h-6 text-gray-600 dark:text-gray-400",emerald:"w-6 h-6 text-emerald-600 dark:text-emerald-400",orange:"w-6 h-6 text-orange-600 dark:text-orange-400",cyan:"w-6 h-6 text-cyan-600 dark:text-cyan-400"};return t[s]||t.blue},B=s=>{const t={blue:"text-2xl font-bold text-blue-600 dark:text-blue-400",green:"text-2xl font-bold text-green-600 dark:text-green-400",purple:"text-2xl font-bold text-purple-600 dark:text-purple-400",yellow:"text-2xl font-bold text-yellow-600 dark:text-yellow-400",red:"text-2xl font-bold text-red-600 dark:text-red-400",indigo:"text-2xl font-bold text-indigo-600 dark:text-indigo-400",gray:"text-2xl font-bold text-gray-600 dark:text-gray-400",emerald:"text-2xl font-bold text-emerald-600 dark:text-emerald-400",orange:"text-2xl font-bold text-orange-600 dark:text-orange-400",cyan:"text-2xl font-bold text-cyan-600 dark:text-cyan-400"};return t[s]||t.blue},O=s=>{const t={blue:"bg-blue-50 dark:bg-blue-900/20 px-6 py-3",green:"bg-green-50 dark:bg-green-900/20 px-6 py-3",purple:"bg-purple-50 dark:bg-purple-900/20 px-6 py-3",yellow:"bg-yellow-50 dark:bg-yellow-900/20 px-6 py-3",red:"bg-red-50 dark:bg-red-900/20 px-6 py-3",indigo:"bg-indigo-50 dark:bg-indigo-900/20 px-6 py-3",gray:"bg-gray-50 dark:bg-gray-900/20 px-6 py-3",emerald:"bg-emerald-50 dark:bg-emerald-900/20 px-6 py-3",orange:"bg-orange-50 dark:bg-orange-900/20 px-6 py-3",cyan:"bg-cyan-50 dark:bg-cyan-900/20 px-6 py-3"};return t[s]||t.blue},V=s=>{const t={blue:"font-medium text-blue-600 dark:text-blue-400",green:"font-medium text-green-600 dark:text-green-400",purple:"font-medium text-purple-600 dark:text-purple-400",yellow:"font-medium text-yellow-600 dark:text-yellow-400",red:"font-medium text-red-600 dark:text-red-400",indigo:"font-medium text-indigo-600 dark:text-indigo-400",gray:"font-medium text-gray-600 dark:text-gray-400",emerald:"font-medium text-emerald-600 dark:text-emerald-400",orange:"font-medium text-orange-600 dark:text-orange-400",cyan:"font-medium text-cyan-600 dark:text-cyan-400"};return t[s]||t.blue};return A(()=>{v()}),(s,t)=>(i(),n("div",$,[e("div",G,[t[2]||(t[2]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Dashboard Bozze"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Gestione centralizzata di tutte le bozze nel sistema ")],-1)),e("div",L,[S(e("select",{"onUpdate:modelValue":t[0]||(t[0]=r=>y.value=r),onChange:v,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-brand-primary-500 focus:border-brand-primary-500"},t[1]||(t[1]=[e("option",{value:"7"},"Ultimi 7 giorni",-1),e("option",{value:"30"},"Ultimo mese",-1),e("option",{value:"90"},"Ultimi 3 mesi",-1)]),544),[[U,y.value]]),e("button",{onClick:C,disabled:c.value,type:"button",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-brand-primary-600 hover:bg-brand-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary-500 disabled:opacity-50"},[o(a,{name:"arrow-path",size:"sm",class:g([{"animate-spin":c.value},"mr-2"])},null,8,["class"]),E(" "+d(c.value?"Aggiornamento...":"Aggiorna"),1)],8,J)])]),p.value?(i(),n("div",Q,[e("div",W,[e("div",X,[e("div",Y,[e("div",Z,[e("div",K,[o(a,{name:"document-duplicate",size:"md",color:"text-white"})])]),e("div",D,[e("dl",null,[t[3]||(t[3]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Totale Bozze ",-1)),e("dd",ee,d(p.value.total_drafts),1)])])])])]),e("div",te,[e("div",se,[e("div",re,[e("div",oe,[e("div",de,[o(a,{name:"trending-up",size:"md",color:"text-white"})])]),e("div",ae,[e("dl",null,[t[4]||(t[4]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Modulo Più Attivo ",-1)),e("dd",le,d(p.value.most_used_module||"N/A"),1)])])])])]),e("div",ne,[e("div",ie,[e("div",ce,[e("div",ue,[e("div",ge,[o(a,{name:"calculator",size:"md",color:"text-white"})])]),e("div",xe,[e("dl",null,[t[5]||(t[5]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Media per Modulo ",-1)),e("dd",me,d(p.value.average_per_module),1)])])])])]),e("div",be,[e("div",ye,[e("div",fe,[e("div",pe,[e("div",he,[o(a,{name:"calendar",size:"md",color:"text-white"})])]),e("div",ve,[e("dl",null,[t[6]||(t[6]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Periodo ",-1)),e("dd",_e,d(y.value)+" giorni ",1)])])])])])])):b("",!0),u.value?(i(),n("div",we,[(i(!0),n(T,null,H(u.value,(r,l)=>(i(),n("div",{key:l,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer",onClick:m=>M(l)},[e("div",ze,[e("div",je,[e("div",Me,[e("div",{class:g(F(r.color))},[o(a,{name:r.icon,class:g(N(r.color))},null,8,["name","class"])],2),e("div",Ce,[e("h3",Fe,d(r.module),1),e("p",Ne,d(r.count)+" "+d(r.count===1?"bozza":"bozze"),1)])]),e("div",Be,[e("div",{class:g(B(r.color))},d(r.count),3),o(a,{name:"chevron-right",class:"w-5 h-5 text-gray-400 ml-2"})])])]),r.count>0?(i(),n("div",{key:0,class:g(O(r.color))},[e("div",Oe,[e("span",{class:g(V(r.color))}," Visualizza dettagli → ",2)])],2)):b("",!0)],8,ke))),128))])):b("",!0),!c.value&&u.value&&Object.keys(u.value).length===0?(i(),n("div",Ve,[o(a,{name:"document-check",class:"w-16 h-16 mx-auto text-green-400 mb-4"}),t[7]||(t[7]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessuna bozza trovata",-1)),t[8]||(t[8]=e("p",{class:"text-gray-500 dark:text-gray-400"}," Non ci sono bozze nel periodo selezionato. Ottimo lavoro! ",-1))])):b("",!0),c.value?(i(),n("div",Ae,[o(a,{name:"arrow-path",class:"w-12 h-12 mx-auto animate-spin text-gray-400 mb-4"}),t[9]||(t[9]=e("p",{class:"text-gray-500 dark:text-gray-400"},"Caricamento dashboard bozze...",-1))])):b("",!0)]))}};export{Te as default};
