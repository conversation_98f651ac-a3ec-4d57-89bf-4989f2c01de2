import{r as m,c as b,x as J,b as n,e as d,h as C,l as c,k as g,v as u,F as _,q as K,o as s,j as o,A as S,s as x,t as k,B as z,p as W,H as A}from"./vendor.js";import{u as X,R as Z,S as ee}from"./ReviewEditModal.js";import{u as te}from"./personnel.js";import{a as ae,H as h}from"./app.js";import{_ as re}from"./ListPageTemplate.js";import{S as oe}from"./StatusBadge.js";import{R as se}from"./ReviewDetailModal.js";import"./ConfirmationModal.js";import"./StandardButton.js";import"./Pagination.js";import"./StatsGrid.js";import"./MarkdownContent.js";import"./jspdf.es.min.js";const ne={key:0,class:"flex items-center gap-2"},le=["value"],ie={class:"flex items-center gap-2"},ue={key:0,class:"flex items-center"},de={class:"text-sm font-medium mr-2"},me={key:1,class:"text-gray-400"},ce={class:"flex items-center justify-end space-x-2"},ve=["onClick"],pe=["onClick"],Me={__name:"PersonnelPerformanceReviews",setup(ye){const r=X(),R=te(),M=ae(),D=K(),v=m(""),p=m(2025),y=m(!1),w=m(!1),l=m(!1),f=b(()=>{var e;const t=(e=M.user)==null?void 0:e.role;return t==="admin"||t==="hr"}),E=b(()=>f.value),N=[{key:"employee_name",label:"Dipendente",type:"text",sortable:!0},{key:"review_year",label:"Anno",type:"text",sortable:!0},{key:"status",label:"Stato",type:"custom",sortable:!0},{key:"overall_rating",label:"Rating",type:"custom",sortable:!0},{key:"due_date",label:"Scadenza",type:"date",sortable:!0},{key:"actions",label:"Azioni",type:"custom",sortable:!1}],$=b(()=>[{key:"status",label:"Stato",type:"select",options:[{value:"",label:"Tutti gli stati"},{value:"draft",label:"Bozza"},{value:"in_progress",label:"In corso"},{value:"pending",label:"In attesa"},{value:"completed",label:"Completata"},{value:"approved",label:"Approvata"}]},{key:"year",label:"Anno",type:"select",options:[{value:"",label:"Tutti gli anni"},{value:"2024",label:"2024"},{value:"2025",label:"2025"},{value:"2026",label:"2026"}]}]),V=b(()=>r.reviews.map(t=>{var e,a;return{...t,employee_name:`${(e=t.employee)==null?void 0:e.first_name} ${(a=t.employee)==null?void 0:a.last_name}`||"N/A"}})),B=async()=>{await i()},P=async t=>{const e={year:p.value,employee_id:v.value||void 0,...t};await r.fetchReviews(e)},F=async()=>{await i()},T=async()=>{await i()},i=async()=>{try{const t={year:p.value,employee_id:v.value||void 0};await r.fetchReviews(t)}catch(t){console.error("Error loading reviews:",t)}},H=t=>{D.push(`/app/personnel/performance/reviews/${t.id}`)},I=()=>{y.value=!0},U=t=>{r.setCurrentReview(t),l.value=!0},j=t=>f.value&&t&&t.status==="draft",L=async t=>{if(confirm(`Sei sicuro di voler eliminare la valutazione di ${t.employee_name} per l'anno ${t.review_year}? Questa azione non può essere annullata.`))try{await r.deleteReview(t.id),await i()}catch(e){console.error("Error deleting review:",e)}},O=()=>{w.value=!1,l.value=!0},Y=()=>{y.value=!1,l.value=!1,r.setCurrentReview(null)},q=async()=>{y.value=!1,l.value=!1,r.setCurrentReview(null),await i()},Q=async()=>{l.value=!1,r.setCurrentReview(null),await i()};return J(async()=>{f.value&&await R.fetchUsers(),await i()}),(t,e)=>(s(),n(_,null,[d(re,{title:"Valutazioni Performance",subtitle:"Lista completa delle valutazioni del personale",data:V.value,columns:N,filters:$.value,loading:u(r).loading,error:u(r).error,"empty-message":"Nessuna valutazione trovata",onRowClick:H,onFilterChange:P,onRefresh:B},{"header-actions":g(()=>[f.value?(s(),n("div",ne,[e[4]||(e[4]=o("label",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Dipendente:",-1)),z(o("select",{"onUpdate:modelValue":e[0]||(e[0]=a=>v.value=a),onChange:F,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-brand-primary-500 focus:border-brand-primary-500"},[e[3]||(e[3]=o("option",{value:""},"Tutti i dipendenti",-1)),(s(!0),n(_,null,W(u(R).users,a=>(s(),n("option",{key:a.id,value:a.id},k(a.first_name)+" "+k(a.last_name),9,le))),128))],544),[[A,v.value]])])):c("",!0),o("div",ie,[e[6]||(e[6]=o("label",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Anno:",-1)),z(o("select",{"onUpdate:modelValue":e[1]||(e[1]=a=>p.value=a),onChange:T,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-brand-primary-500 focus:border-brand-primary-500"},e[5]||(e[5]=[o("option",{value:"2024"},"2024",-1),o("option",{value:"2025"},"2025",-1),o("option",{value:"2026"},"2026",-1)]),544),[[A,p.value]])]),E.value?(s(),n("button",{key:1,onClick:I,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-brand-primary-600 hover:bg-brand-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary-500"},[d(h,{name:"plus",size:"sm",class:"mr-2"}),e[7]||(e[7]=x(" Nuova Valutazione "))])):c("",!0)]),"cell-status":g(({value:a})=>[d(oe,{type:"performance",status:a},null,8,["status"])]),"cell-overall_rating":g(({value:a})=>[a?(s(),n("div",ue,[o("span",de,k(a.toFixed(1)),1),d(ee,{rating:a,size:"sm",readonly:""},null,8,["rating"])])):(s(),n("span",me,"N/A"))]),"cell-actions":g(({row:a})=>[o("div",ce,[o("button",{onClick:S(G=>U(a),["stop"]),class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 dark:text-blue-400 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 transition-colors"},[d(h,{name:"pencil",class:"w-3 h-3 mr-1"}),e[8]||(e[8]=x(" Modifica "))],8,ve),j(a)?(s(),n("button",{key:0,onClick:S(G=>L(a),["stop"]),class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 dark:text-red-400 dark:bg-red-900/20 dark:hover:bg-red-900/30 transition-colors"},[d(h,{name:"trash",class:"w-3 h-3 mr-1"}),e[9]||(e[9]=x(" Elimina "))],8,pe)):c("",!0)])]),_:1},8,["data","filters","loading","error"]),w.value?(s(),C(se,{key:0,review:u(r).currentReview,loading:u(r).loading,onClose:e[2]||(e[2]=a=>w.value=!1),onEdit:O},null,8,["review","loading"])):c("",!0),y.value||l.value?(s(),C(Z,{key:1,review:l.value?u(r).currentReview:null,loading:u(r).loading,onClose:Y,onSave:q,onDelete:Q},null,8,["review","loading"])):c("",!0)],64))}};export{Me as default};
