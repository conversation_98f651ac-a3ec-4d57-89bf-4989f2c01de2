import{_ as N}from"./BaseModal.js";import{S as b}from"./StandardButton.js";import{_ as j,H as o,d as G}from"./app.js";import{M as U}from"./MarkdownContent.js";import{u as E}from"./useFormatters.js";import{h as P,o as r,k as u,j as e,b as m,l as p,e as n,t as l,n as D,v as F,s as x,F as V,p as O,E as L,r as B}from"./vendor.js";const Q={class:"space-y-6"},H={class:"bg-gray-50 rounded-lg p-4"},R={class:"flex items-start justify-between"},q={class:"flex-1"},J={class:"flex items-center space-x-2 mb-2"},K={class:"text-sm font-medium text-gray-600"},W={class:"text-sm text-gray-900 mb-2"},X={class:"flex items-center space-x-4 text-xs text-gray-500"},Y={key:0,class:"text-center py-8"},Z={key:1,class:"text-center py-8"},ee={class:"animate-spin mx-auto mb-4"},te={key:2,class:"space-y-6"},se={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},ae={class:"flex items-start space-x-3"},ne={class:"text-blue-800 text-sm"},le={key:0,class:"bg-red-50 border border-red-200 rounded-lg p-4"},ie={class:"flex items-start space-x-3"},oe={class:"text-red-800 text-sm"},re={key:1,class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},de={class:"flex items-start space-x-3"},ce={class:"text-yellow-800 text-sm"},me={key:2,class:"bg-green-50 border border-green-200 rounded-lg p-4"},ue={class:"flex items-start space-x-3"},pe={class:"flex-1"},xe={class:"space-y-2"},ge={class:"flex-shrink-0 w-5 h-5 bg-green-100 text-green-800 rounded-full flex items-center justify-center text-xs font-medium"},fe={class:"text-green-800 text-sm"},be={key:3,class:"bg-gray-50 border border-gray-200 rounded-lg p-4"},ye={class:"flex items-start space-x-3"},ve={class:"text-gray-700 text-sm"},he={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ce={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},ke={class:"flex items-center space-x-3"},$e={class:"flex items-center space-x-2 mt-1"},_e={class:"flex-1 bg-blue-200 rounded-full h-2"},we={class:"text-sm font-medium text-blue-700"},ze={class:"bg-purple-50 border border-purple-200 rounded-lg p-4"},Pe={class:"flex items-center space-x-3"},Ae={__name:"PatternAnalysisModal",props:{show:{type:Boolean,default:!1},pattern:{type:Object,default:null},analysis:{type:Object,default:null},analyzingPattern:{type:Boolean,default:!1},generatingPrompt:{type:Boolean,default:!1}},emits:["close","analyze","generate-prompt"],setup(s,{emit:T}){const{formatTimeAgo:C}=E();function k(d){return{critical:"bg-red-100 text-red-800",high:"bg-orange-100 text-orange-800",medium:"bg-yellow-100 text-yellow-800",low:"bg-green-100 text-green-800"}[d]||"bg-gray-100 text-gray-800"}function $(d){return{urgent:"bg-red-100 text-red-800",high:"bg-orange-100 text-orange-800",normal:"bg-blue-100 text-blue-800",low:"bg-gray-100 text-gray-800"}[d]||"bg-blue-100 text-blue-800"}function v(d,a="Non disponibile"){return d==null||d===""||d==="Unknown"?a:d}return(d,a)=>(r(),P(N,{show:s.show,title:"🔍 Analisi AI Pattern Errore",size:"xl",onClose:a[3]||(a[3]=g=>d.$emit("close"))},{footer:u(()=>[n(b,{variant:"outline",onClick:a[1]||(a[1]=g=>d.$emit("close"))},{default:u(()=>a[16]||(a[16]=[x(" Chiudi ")])),_:1,__:[16]}),s.analysis&&!s.generatingPrompt?(r(),P(b,{key:0,variant:"primary",icon:"document-text",onClick:a[2]||(a[2]=g=>{var y;return d.$emit("generate-prompt",(y=s.pattern)==null?void 0:y.id)})},{default:u(()=>a[17]||(a[17]=[x(" Genera Prompt Claude ")])),_:1,__:[17]})):s.generatingPrompt?(r(),P(b,{key:1,variant:"primary",icon:"arrow-path",loading:"",disabled:""},{default:u(()=>a[18]||(a[18]=[x(" Generando... ")])),_:1,__:[18]})):p("",!0)]),default:u(()=>{var g,y,A,_,i,t,c,h,w;return[e("div",Q,[e("div",H,[e("div",R,[e("div",q,[e("div",J,[e("span",K,l(v((g=s.pattern)==null?void 0:g.error_type,"Tipo errore sconosciuto")),1),e("span",{class:D(["px-2 py-1 text-xs font-medium rounded",k((y=s.pattern)==null?void 0:y.severity)])},l((_=(A=s.pattern)==null?void 0:A.severity)==null?void 0:_.toUpperCase()),3)]),e("p",W,l(v((i=s.pattern)==null?void 0:i.message_preview,"Messaggio non disponibile")),1),e("div",X,[e("span",null,l((t=s.pattern)==null?void 0:t.occurrence_count)+" occorrenze",1),e("span",null,l(F(C)((c=s.pattern)==null?void 0:c.last_seen)),1)])]),n(o,{name:"cpu-chip",size:"lg",class:"text-blue-500 flex-shrink-0"})])]),!s.analysis&&!s.analyzingPattern?(r(),m("div",Y,[n(o,{name:"exclamation-triangle",size:"xl",class:"text-yellow-500 mx-auto mb-4"}),a[5]||(a[5]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Analisi AI Non Disponibile",-1)),a[6]||(a[6]=e("p",{class:"text-gray-600 mb-4"}," Questo pattern non è ancora stato analizzato dall'AI. Avvia l'analisi per ottenere insights dettagliati. ",-1)),n(b,{variant:"primary",icon:"cpu-chip",loading:s.analyzingPattern,onClick:a[0]||(a[0]=z=>{var f;return d.$emit("analyze",(f=s.pattern)==null?void 0:f.id)})},{default:u(()=>a[4]||(a[4]=[x(" Avvia Analisi AI ")])),_:1,__:[4]},8,["loading"])])):s.analyzingPattern?(r(),m("div",Z,[e("div",ee,[n(o,{name:"cpu-chip",size:"xl",class:"text-blue-500"})]),a[7]||(a[7]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Analisi in Corso...",-1)),a[8]||(a[8]=e("p",{class:"text-gray-600"}," L'AI sta analizzando il pattern per identificare cause e soluzioni potenziali. ",-1))])):s.analysis?(r(),m("div",te,[e("div",se,[e("div",ae,[n(o,{name:"light-bulb",class:"text-blue-600 flex-shrink-0 mt-1"}),e("div",null,[a[9]||(a[9]=e("h4",{class:"font-medium text-blue-900 mb-2"},"Analisi Principale",-1)),e("p",ne,l(s.analysis.summary),1)])])]),s.analysis.root_cause?(r(),m("div",le,[e("div",ie,[n(o,{name:"exclamation-circle",class:"text-red-600 flex-shrink-0 mt-1"}),e("div",null,[a[10]||(a[10]=e("h4",{class:"font-medium text-red-900 mb-2"},"Causa Principale",-1)),e("p",oe,l(s.analysis.root_cause),1)])])])):p("",!0),s.analysis.impact?(r(),m("div",re,[e("div",de,[n(o,{name:"chart-bar-square",class:"text-yellow-600 flex-shrink-0 mt-1"}),e("div",null,[a[11]||(a[11]=e("h4",{class:"font-medium text-yellow-900 mb-2"},"Impatto del Problema",-1)),e("p",ce,l(s.analysis.impact),1)])])])):p("",!0),(h=s.analysis.suggested_solutions)!=null&&h.length?(r(),m("div",me,[e("div",ue,[n(o,{name:"wrench-screwdriver",class:"text-green-600 flex-shrink-0 mt-1"}),e("div",pe,[a[12]||(a[12]=e("h4",{class:"font-medium text-green-900 mb-3"},"Soluzioni Suggerite",-1)),e("ul",xe,[(r(!0),m(V,null,O(s.analysis.suggested_solutions,(z,f)=>(r(),m("li",{key:f,class:"flex items-start space-x-2"},[e("span",ge,l(f+1),1),e("span",fe,l(z),1)]))),128))])])])])):p("",!0),s.analysis.technical_details?(r(),m("div",be,[e("div",ye,[n(o,{name:"code-bracket",class:"text-gray-600 flex-shrink-0 mt-1"}),e("div",null,[a[13]||(a[13]=e("h4",{class:"font-medium text-gray-900 mb-2"},"Dettagli Tecnici",-1)),e("div",ve,[n(U,{content:s.analysis.technical_details},null,8,["content"])])])])])):p("",!0),e("div",he,[e("div",Ce,[e("div",ke,[n(o,{name:"chart-pie",class:"text-blue-600"}),e("div",null,[a[14]||(a[14]=e("h4",{class:"font-medium text-blue-900"},"Confidenza Analisi",-1)),e("div",$e,[e("div",_e,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:L({width:`${(s.analysis.confidence||0)*100}%`})},null,4)]),e("span",we,l(Math.round((s.analysis.confidence||0)*100))+"% ",1)])])])]),e("div",ze,[e("div",Pe,[n(o,{name:"clock",class:"text-purple-600"}),e("div",null,[a[15]||(a[15]=e("h4",{class:"font-medium text-purple-900"},"Priorità Fix",-1)),e("span",{class:D(["inline-block mt-1 px-2 py-1 text-xs font-medium rounded",$(s.analysis.priority)])},l(((w=s.analysis.priority)==null?void 0:w.toUpperCase())||"NORMALE"),3)])])])])])):p("",!0)])]}),_:1},8,["show"]))}},xt=j(Ae,[["__scopeId","data-v-3a738c5a"]]),De={class:"space-y-6"},Te={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},Ie={class:"flex items-start space-x-3"},Me={key:0,class:"bg-gray-50 rounded-lg p-4"},Se={class:"font-medium text-gray-900 mb-3 flex items-center space-x-2"},Be={class:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm"},Ne={class:"font-medium ml-2"},je={class:"font-medium ml-2"},Ee={class:"font-medium ml-2 font-mono text-xs"},Fe={class:"space-y-4"},Ge={class:"flex items-center justify-between"},Ue={class:"font-medium text-gray-900 flex items-center space-x-2"},Ve={class:"flex items-center space-x-2"},Oe={class:"text-xs text-gray-500"},Le={class:"relative"},Qe=["value"],He={key:0,class:"absolute inset-0 bg-green-500 bg-opacity-10 border-2 border-green-500 rounded-lg flex items-center justify-center transition-opacity duration-300"},Re={class:"bg-green-500 text-white px-4 py-2 rounded-lg flex items-center space-x-2"},qe={class:"bg-green-50 border border-green-200 rounded-lg p-4"},Je={class:"font-medium text-green-900 mb-3 flex items-center space-x-2"},Ke={key:1,class:"grid grid-cols-1 md:grid-cols-3 gap-4"},We={class:"bg-blue-50 border border-blue-200 rounded-lg p-3"},Xe={class:"flex items-center space-x-2"},Ye={class:"text-blue-700 text-xs"},Ze={class:"bg-purple-50 border border-purple-200 rounded-lg p-3"},et={class:"flex items-center space-x-2"},tt={class:"text-purple-700 text-xs"},st={class:"bg-orange-50 border border-orange-200 rounded-lg p-3"},at={class:"flex items-center space-x-2"},nt={class:"text-orange-700 text-xs"},lt={key:2,class:"bg-red-50 border border-red-200 rounded-lg p-4"},it={class:"flex items-start space-x-3"},ot={__name:"ClaudePromptModal",props:{show:{type:Boolean,default:!1},promptData:{type:Object,default:null}},emits:["close","copy","mark-completed"],setup(s,{emit:T}){const C=s,k=T,$=B(null),v=B(!1),{formatters:d}=E(),{showToast:a}=G();async function g(){var i,t;try{const c=((i=C.promptData)==null?void 0:i.prompt)||"";if(!c){a("Nessun prompt da copiare","warning");return}await navigator.clipboard.writeText(c),v.value=!0,setTimeout(()=>{v.value=!1},2e3),a("Prompt copiato negli appunti!","success"),k("copy",c)}catch{$.value?($.value.select(),document.execCommand("copy"),a("Prompt copiato negli appunti!","success"),k("copy",(t=C.promptData)==null?void 0:t.prompt)):a("Errore durante la copia","error")}}function y(){var i;k("mark-completed",(i=C.promptData)==null?void 0:i.healing_session_id)}function A(i){return{critical:"bg-red-100 text-red-800",high:"bg-orange-100 text-orange-800",medium:"bg-yellow-100 text-yellow-800",low:"bg-green-100 text-green-800"}[i]||"bg-gray-100 text-gray-800"}function _(i,t="Non disponibile"){return i==null||i===""||i==="Unknown"?t:i}return(i,t)=>(r(),P(N,{show:s.show,title:"🤖 Prompt Claude Code Generato",size:"2xl",onClose:t[1]||(t[1]=c=>i.$emit("close"))},{footer:u(()=>{var c;return[n(b,{variant:"outline",onClick:t[0]||(t[0]=h=>i.$emit("close"))},{default:u(()=>t[17]||(t[17]=[x(" Chiudi ")])),_:1,__:[17]}),n(b,{variant:"primary",icon:"clipboard",onClick:g},{default:u(()=>t[18]||(t[18]=[x(" Copia Prompt ")])),_:1,__:[18]}),(c=s.promptData)!=null&&c.healing_session_id?(r(),P(b,{key:0,variant:"success",icon:"check-circle",onClick:y},{default:u(()=>t[19]||(t[19]=[x(" Marca Come Risolto ")])),_:1,__:[19]})):p("",!0)]}),default:u(()=>{var c,h,w,z,f,I,M,S;return[e("div",De,[e("div",Te,[e("div",Ie,[n(o,{name:"information-circle",class:"text-blue-600 flex-shrink-0 mt-1"}),t[2]||(t[2]=e("div",null,[e("h4",{class:"font-medium text-blue-900 mb-2"},"Prompt AI-Assistito Generato"),e("p",{class:"text-blue-800 text-sm"}," Questo prompt è stato generato automaticamente dall'AI per risolvere il pattern di errore rilevato. Copialo e utilizzalo con Claude Code per implementare automaticamente il fix. ")],-1))])]),(c=s.promptData)!=null&&c.pattern_info?(r(),m("div",Me,[e("h4",Se,[n(o,{name:"bug-ant",class:"text-gray-600"}),t[3]||(t[3]=e("span",null,"Contesto del Problema",-1))]),e("div",Be,[e("div",null,[t[4]||(t[4]=e("span",{class:"text-gray-600"},"Tipo:",-1)),e("span",Ne,l(_(s.promptData.pattern_info.error_type,"Tipo sconosciuto")),1)]),e("div",null,[t[5]||(t[5]=e("span",{class:"text-gray-600"},"Severità:",-1)),e("span",{class:D(["ml-2 px-2 py-1 text-xs font-medium rounded",A(s.promptData.pattern_info.severity)])},l((h=s.promptData.pattern_info.severity)==null?void 0:h.toUpperCase()),3)]),e("div",null,[t[6]||(t[6]=e("span",{class:"text-gray-600"},"Occorrenze:",-1)),e("span",je,l(s.promptData.pattern_info.occurrence_count),1)]),e("div",null,[t[7]||(t[7]=e("span",{class:"text-gray-600"},"File:",-1)),e("span",Ee,l(_(s.promptData.pattern_info.file_pattern,"File sconosciuto")),1)])])])):p("",!0),e("div",Fe,[e("div",Ge,[e("h4",Ue,[n(o,{name:"document-text",class:"text-gray-600"}),t[8]||(t[8]=e("span",null,"Prompt Claude Code",-1))]),e("div",Ve,[e("span",Oe,l(((z=(w=s.promptData)==null?void 0:w.prompt)==null?void 0:z.length)||0)+" caratteri",1),n(b,{variant:"outline",size:"sm",icon:"clipboard",onClick:g},{default:u(()=>t[9]||(t[9]=[x(" Copia ")])),_:1,__:[9]})])]),e("div",Le,[e("textarea",{ref_key:"promptTextarea",ref:$,value:((f=s.promptData)==null?void 0:f.prompt)||"",readonly:"",class:"w-full h-64 p-4 border border-gray-300 rounded-lg bg-gray-50 text-sm font-mono resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Prompt non ancora generato..."},null,8,Qe),v.value?(r(),m("div",He,[e("div",Re,[n(o,{name:"check",class:"text-white"}),t[10]||(t[10]=e("span",{class:"font-medium"},"Copiato negli appunti!",-1))])])):p("",!0)])]),e("div",qe,[e("h4",Je,[n(o,{name:"play",class:"text-green-600"}),t[11]||(t[11]=e("span",null,"Istruzioni per l'Uso",-1))]),t[12]||(t[12]=e("ol",{class:"list-decimal list-inside space-y-2 text-sm text-green-800"},[e("li",null,'Copia il prompt generato utilizzando il pulsante "Copia"'),e("li",null,"Apri il terminale nella directory del progetto DatPortal"),e("li",null,[x("Esegui il comando "),e("code",{class:"bg-green-100 px-1 rounded"},"claude-code")]),e("li",null,"Incolla il prompt e premi Enter"),e("li",null,"Segui le istruzioni di Claude Code per implementare automaticamente il fix"),e("li",null,"Testa la soluzione e verifica che il problema sia risolto")],-1))]),(I=s.promptData)!=null&&I.metadata?(r(),m("div",Ke,[e("div",We,[e("div",Xe,[n(o,{name:"cpu-chip",class:"text-blue-600"}),e("div",null,[t[13]||(t[13]=e("h5",{class:"font-medium text-blue-900 text-sm"},"Modello AI",-1)),e("p",Ye,l(s.promptData.metadata.ai_model||"GPT-4o-mini"),1)])])]),e("div",Ze,[e("div",et,[n(o,{name:"clock",class:"text-purple-600"}),e("div",null,[t[14]||(t[14]=e("h5",{class:"font-medium text-purple-900 text-sm"},"Generato",-1)),e("p",tt,l(F(d).timeAgo(s.promptData.metadata.generated_at)),1)])])]),e("div",st,[e("div",at,[n(o,{name:"star",class:"text-orange-600"}),e("div",null,[t[15]||(t[15]=e("h5",{class:"font-medium text-orange-900 text-sm"},"Confidenza",-1)),e("p",nt,l(Math.round((s.promptData.metadata.confidence||0)*100))+"%",1)])])])])):p("",!0),((S=(M=s.promptData)==null?void 0:M.pattern_info)==null?void 0:S.severity)==="critical"?(r(),m("div",lt,[e("div",it,[n(o,{name:"exclamation-triangle",class:"text-red-600 flex-shrink-0 mt-1"}),t[16]||(t[16]=e("div",null,[e("h4",{class:"font-medium text-red-900 mb-2"},"⚠️ Problema Critico"),e("p",{class:"text-red-800 text-sm mb-2"}," Questo errore è stato classificato come critico. Prima di applicare il fix automatico: "),e("ul",{class:"list-disc list-inside text-red-800 text-sm space-y-1"},[e("li",null,"Crea un backup del codice attuale"),e("li",null,"Testa il fix in ambiente di sviluppo"),e("li",null,"Considera di coinvolgere altri sviluppatori per review"),e("li",null,"Monitora attentamente dopo il deployment")])],-1))])])):p("",!0)])]}),_:1},8,["show"]))}},gt=j(ot,[["__scopeId","data-v-82861ee0"]]);export{gt as C,xt as P};
